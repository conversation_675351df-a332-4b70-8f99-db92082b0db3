// THIS FILE IS AUTO GENERATED
var GenIcon = require('../lib').GenIcon
module.exports.BiCloudRain = function BiCloudRain (props) {
  return GenIcon({"tag":"svg","attr":{"viewBox":"0 0 24 24"},"child":[{"tag":"path","attr":{"d":"M8 13H10V20H8zM11 15H13V22H11zM14 13H16V20H14z"}},{"tag":"path","attr":{"d":"M18.944,10.113C18.507,6.671,15.56,4.001,12,4.001c-2.756,0-5.15,1.611-6.243,4.15C3.609,8.793,2,10.82,2,13.001 c0,2.757,2.243,5,5,5v-2c-1.654,0-3-1.346-3-3c0-1.403,1.199-2.756,2.673-3.015l0.581-0.103l0.192-0.559 C8.149,7.274,9.895,6.001,12,6.001c2.757,0,5,2.243,5,5v1h1c1.103,0,2,0.897,2,2s-0.897,2-2,2h-1v2h1c2.206,0,4-1.794,4-4 C22,12.12,20.695,10.539,18.944,10.113z"}}]})(props);
};
