// THIS FILE IS AUTO GENERATED
var GenIcon = require('../lib').GenIcon
module.exports.BiGitMerge = function BiGitMerge (props) {
  return GenIcon({"tag":"svg","attr":{"viewBox":"0 0 24 24"},"child":[{"tag":"path","attr":{"d":"M2.5,18.5C2.5,20.43,4.07,22,6,22s3.5-1.57,3.5-3.5c0-1.58-1.06-2.903-2.5-3.337v-3.488 c0.244,0.273,0.509,0.527,0.813,0.744c1.18,0.844,2.617,1.098,3.918,1.098c0.966,0,1.853-0.14,2.506-0.281 c0.502,1.319,1.771,2.265,3.264,2.265c1.93,0,3.5-1.57,3.5-3.5s-1.57-3.5-3.5-3.5c-1.66,0-3.047,1.165-3.404,2.718 c-1.297,0.321-3.664,0.616-5.119-0.426c-0.666-0.477-1.09-1.239-1.306-2.236C8.755,7.96,9.5,6.821,9.5,5.5C9.5,3.57,7.93,2,6,2 S2.5,3.57,2.5,5.5c0,1.58,1.06,2.903,2.5,3.337v6.326C3.56,15.597,2.5,16.92,2.5,18.5z M17.5,10.5c0.827,0,1.5,0.673,1.5,1.5 s-0.673,1.5-1.5,1.5S16,12.827,16,12S16.673,10.5,17.5,10.5z M7.5,18.5C7.5,19.327,6.827,20,6,20s-1.5-0.673-1.5-1.5S5.173,17,6,17 S7.5,17.673,7.5,18.5z M4.5,5.5C4.5,4.673,5.173,4,6,4s1.5,0.673,1.5,1.5S6.827,7,6,7S4.5,6.327,4.5,5.5z"}}]})(props);
};
