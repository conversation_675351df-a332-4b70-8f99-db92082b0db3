// THIS FILE IS AUTO GENERATED
var GenIcon = require('../lib').GenIcon
module.exports.GiMite = function GiMite (props) {
  return GenIcon({"tag":"svg","attr":{"viewBox":"0 0 512 512"},"child":[{"tag":"path","attr":{"d":"M148.938 62.313c-40.392.64-77.533 26.18-91.063 58.406 29.237-14.9 62.01-26.24 102.063-14.157-12.28 34.587-12.97 73.268 9.5 105.593l.062-.03c-3.206 7.243-5.79 14.955-7.656 23.03-27.112-17-40.46-47.814-43.688-81.187-.018-.904-.115-1.828-.25-2.75-.065-.824-.134-1.645-.187-2.47l-.314.03c-3.127-12.508-16.047-24.964-31.75-19.093-44.4 16.605-70.46 64.092-65.437 103.72 17.878-27.532 40.594-53.727 81.343-63.282 6.42 34.787 24.056 67.457 57.218 84.78-.29 3.658-.436 7.37-.436 11.126 0 2.775.098 5.443.25 8.033-25.757-5.683-47.636-23.653-64.125-46.813-.43-.692-.92-1.372-1.44-2.03-.478-.697-.966-1.39-1.436-2.095l-.25.156c-8.968-9.26-26.375-13.552-37.03-.624-30.152 36.58-29.008 90.75-4.814 122.53 1.717-32.775 8.262-66.844 38.78-95.498 19.006 22.293 43.946 39.482 73.19 43.843 2.405 9.377 6.14 17.173 10.968 23.75-20.756 2.423-42.643-2.44-63.22-11.842-.75-.403-1.527-.772-2.343-1.094-.728-.347-1.463-.674-2.188-1.03l-.124.28c-12.4-3.55-29.64 1.43-32.407 17.97-7.82 46.748 20.25 93.07 57.094 108.498-14.903-29.244-26.225-61.995-14.125-102.062 23.854 8.464 49.654 11.385 73.97 5.03 6.886 4.576 14.892 8.144 23.75 10.75-10.733 9.146-17.345 21.658-17.345 35.47 0 9.626 3.232 18.615 8.813 26.28-10.362 1.9-21.63 12.622-22.5 28.376-1.944 35.11 30.045 49.124 54.5 42.375-19.484-9.088-28.823-40.074-14.782-55.436 9.844 5.736 21.805 9.125 34.72 9.125 13.986 0 26.857-3.965 37.125-10.595-.054.41-.118.852-.156 1.28 14.282 15.3 4.966 46.5-14.595 55.626 24.454 6.75 56.444-7.264 54.5-42.374-.935-16.886-13.816-28.01-24.72-28.656 5.453-7.603 8.595-16.494 8.595-26 0-13.772-6.607-26.237-17.28-35.375 8.89-2.584 16.95-6.102 23.874-10.656 24.12 6.127 49.674 3.168 73.312-5.22 12.1 40.067.778 72.818-14.125 102.063 36.846-15.428 64.916-61.75 57.095-108.5-2.765-16.538-20.008-21.52-32.406-17.97l-.126-.28c-.758.374-1.52.73-2.28 1.093-.774.31-1.538.65-2.252 1.03-20.298 9.278-41.87 14.135-62.375 11.94 4.882-6.628 8.647-14.494 11.063-23.97 28.83-4.583 53.447-21.648 72.25-43.687 30.505 28.65 37.065 62.696 38.78 95.468 24.195-31.782 25.37-85.953-4.78-122.532-10.66-12.93-28.097-8.64-37.063.625l-.25-.155c-.51.767-1.04 1.525-1.562 2.28-.477.62-.945 1.26-1.344 1.907-16.287 22.86-37.82 40.637-63.155 46.532.143-2.522.22-5.117.22-7.813 0-3.916-.154-7.784-.47-11.592 32.593-17.44 49.98-49.836 56.344-84.313 40.75 9.555 63.463 35.75 81.342 63.28 5.024-39.626-21.005-87.113-65.406-103.718-15.704-5.872-28.655 6.584-31.78 19.094l-.282-.03c-.052.794-.125 1.584-.188 2.375-.143.954-.23 1.91-.25 2.844-3.196 33.04-16.328 63.586-42.906 80.686-1.922-8.17-4.582-15.97-7.875-23.28 21.922-32.167 21.173-70.504 9-104.814 40.05-12.08 72.794-.74 102.03 14.157-15.462-36.83-61.747-64.916-108.5-57.095-16.534 2.77-21.515 20.01-17.967 32.406l-.282.126c.368.745.707 1.5 1.063 2.25.324.817.687 1.594 1.092 2.344 13.876 30.404 17.78 63.67 3.032 91.906-18.117-26.304-45.696-42.906-76.125-42.906-30.717 0-58.52 16.92-76.626 43.656-15.216-28.396-11.375-61.98 2.625-92.656.414-.77.793-1.568 1.124-2.406.346-.728.673-1.464 1.03-2.188l-.28-.125c3.55-12.396-1.432-29.635-17.97-32.405-5.843-.978-11.666-1.404-17.436-1.313z"}}]})(props);
};
