// THIS FILE IS AUTO GENERATED
var GenIcon = require('../lib').GenIcon
module.exports.GiNecklace = function Gi<PERSON><PERSON>lace (props) {
  return GenIcon({"tag":"svg","attr":{"viewBox":"0 0 512 512"},"child":[{"tag":"path","attr":{"d":"M256 17.12c-80.055 0-146.96 11.46-146.96 86.294 3.176 46.31 27.865 48.73 47.546 87.525 33.323 65.317 26.562 70.923 43.5 108.67 1.202 2.678 2.27 5.576 3.26 8.61 9.382 2.25 18.562 3.807 26.664 3.973l2.008-4.035-41.237-90.95c-23.33-59.194-65.534-81.684-65.303-112.478.303-40.477 28.157-69.41 129.843-68.84 105.162.587 130.883 27.404 132.082 68.973.612 21.207-44.356 58.01-64.32 115.63l-47.756 91.66c10.88-.37 23.584-3.116 36.098-6.678.71-1.71 1.687-3.282 2.213-4.96 12.574-40.15 12.044-45.83 43.636-107.757 21.512-38.208 40.718-48.8 45.686-89.344 0-74.833-66.905-86.295-146.96-86.295zm-3.834 277.737L237.834 324.6c-23.02-.476-55.97-13.1-74.252-19.473 1.806 12.728 15.387 42.465 16.162 58.176l-27.592 5.732 28.225 30.58c-.227 25.06-17.045 57.838-23.74 75.438 5.747-.733 31.057-9.59 37.44-10.155L191.3 494.88l49.374-27.892c-.48-1.364-.76-2.83-.76-4.36 0-6.958 5.38-12.63 12.25-13.14 6.87.51 12.248 6.182 12.248 13.14 0 1.532-.282 2.996-.758 4.36l49.375 27.893-2.776-29.982c6.384.567 31.694 9.422 37.44 10.155-6.694-17.6-23.5-50.38-23.74-75.438l28.224-30.58-27.592-5.732c.776-15.71 14.36-45.448 16.164-58.176-18.283 6.375-51.23 19-74.252 19.473l-14.332-29.743zm-75.957 24.247c15.53 6.215 39.34 14.43 56.194 14.992l-4.925 8.54c4.984 1.897 8.522 6.618 8.522 12.245 0 7.295-5.98 13.2-13.322 13.2-6.6 0-12.032-4.79-13.07-11.05l-19.952 4.18c-.7-11.076-8.67-30.437-13.45-42.106zm151.913 0c-4.78 11.67-12.75 31.03-13.45 42.107l-19.95-4.18c-1.04 6.26-6.472 11.05-13.07 11.05-7.343 0-13.323-5.905-13.323-13.2 0-5.626 3.538-10.345 8.524-12.243l-4.924-8.54c16.854-.562 40.66-8.778 56.193-14.993zm-75.955 35.242l10.922 18.93c-2.318.885-3.916 3.058-3.916 5.675 0 3.394 2.773 6.152 6.19 6.152 3.07 0 5.577-2.166 6.06-5.077l26.71 5.555-18.187 19.71c-1.13-1.54-2.988-2.448-5.05-2.448-3.416 0-6.188 2.7-6.188 6.092 0 3.392 2.772 6.152 6.186 6.152.7 0 1.332-.142 1.958-.36l3.093 26.758-22.92-10.392c.227-.634.254-1.26.254-1.973 0-3.045-2.194-5.5-5.112-5.974-2.916.476-5.115 2.93-5.115 5.973 0 .713.02 1.335.254 1.972l-22.918 10.392 3.092-26.757c.626.198 1.26.36 1.956.36 3.415 0 6.187-2.76 6.187-6.153s-2.77-6.092-6.188-6.092c-2.063 0-3.92.912-5.05 2.45l-18.184-19.712 26.707-5.555c.48 2.912 2.993 5.077 6.063 5.077 3.415 0 6.187-2.76 6.187-6.15 0-2.62-1.596-4.792-3.914-5.675l10.922-18.93zm50.93 51.252l-.04.05c4.358 0 8.262 1.917 10.796 4.9l.085-.087c.02.086.055.144.083.257 1.818 2.213 2.905 4.983 2.905 8.002 0 .616-.057 1.2-.114 1.792 4.52 14.428 8.95 30.55 12.565 39.3-2.733-.348-17.238-3.936-20.142-4.242l-2.78-23.89c-1.344.453-2.793.718-4.292.718-7.343 0-13.26-5.904-13.26-13.2 0-4.79 2.542-8.975 6.377-11.288.022-.022.113-.02.113-.084.114-.113.298-.142.442-.256.37-.226.744-.412 1.135-.6 1.854-.87 3.912-1.372 6.125-1.372zm-101.823.05c2.214 0 4.27.5 6.125 1.375.394.17.767.38 1.135.598.142.114.303.144.442.257.022.022.113.02.113.084 3.834 2.314 6.377 6.498 6.377 11.29 0 7.295-5.916 13.2-13.258 13.2-1.5 0-2.95-.254-4.296-.716l-2.78 23.89c-2.903.304-17.41 3.893-20.14 4.24 3.615-8.75 8.046-24.87 12.565-39.3-.115-.59-.115-1.173-.115-1.79 0-3.02 1.09-5.79 2.905-8.004.02-.085.055-.143.084-.256l.086.086c2.533-2.982 6.438-4.9 10.796-4.9l-.04-.052z"}}]})(props);
};
