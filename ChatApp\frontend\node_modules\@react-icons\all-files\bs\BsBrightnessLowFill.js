// THIS FILE IS AUTO GENERATED
var GenIcon = require('../lib').GenIcon
module.exports.BsBrightnessLowFill = function BsBrightnessLowFill (props) {
  return GenIcon({"tag":"svg","attr":{"viewBox":"0 0 16 16","fill":"currentColor"},"child":[{"tag":"circle","attr":{"cx":"8","cy":"8","r":"4"}},{"tag":"circle","attr":{"cx":"8","cy":"2.5","r":".5"}},{"tag":"circle","attr":{"cx":"8","cy":"13.5","r":".5"}},{"tag":"circle","attr":{"cx":"13.5","cy":"8","r":".5","transform":"rotate(90 13.5 8)"}},{"tag":"circle","attr":{"cx":"2.5","cy":"8","r":".5","transform":"rotate(90 2.5 8)"}},{"tag":"circle","attr":{"cx":"11.889","cy":"4.111","r":".5","transform":"rotate(45 11.89 4.11)"}},{"tag":"circle","attr":{"cx":"4.111","cy":"11.889","r":".5","transform":"rotate(45 4.11 11.89)"}},{"tag":"circle","attr":{"cx":"11.889","cy":"11.889","r":".5","transform":"rotate(135 11.89 11.889)"}},{"tag":"circle","attr":{"cx":"4.111","cy":"4.111","r":".5","transform":"rotate(135 4.11 4.11)"}}]})(props);
};
