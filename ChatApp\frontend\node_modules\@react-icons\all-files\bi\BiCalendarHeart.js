// THIS FILE IS AUTO GENERATED
var GenIcon = require('../lib').GenIcon
module.exports.BiCalendarHeart = function BiCalendarHeart (props) {
  return GenIcon({"tag":"svg","attr":{"viewBox":"0 0 24 24"},"child":[{"tag":"path","attr":{"d":"M8.648,14.711L11.997,18l3.35-3.289c0.871-0.854,0.871-2.21,0-3.069c-0.875-0.855-2.255-0.855-3.126,0l-0.224,0.219 l-0.224-0.219c-0.87-0.855-2.25-0.855-3.125,0C7.777,12.501,7.777,13.856,8.648,14.711z"}},{"tag":"path","attr":{"d":"M19,4h-2V2h-2v2H9V2H7v2H5C3.897,4,3,4.897,3,6v2v12c0,1.103,0.897,2,2,2h14c1.103,0,2-0.897,2-2V8V6 C21,4.897,20.103,4,19,4z M19.002,20H5V8h14L19.002,20z"}}]})(props);
};
