// THIS FILE IS AUTO GENERATED
var GenIcon = require('../lib').GenIcon
module.exports.BiRadar = function BiRadar (props) {
  return GenIcon({"tag":"svg","attr":{"viewBox":"0 0 24 24"},"child":[{"tag":"path","attr":{"d":"M12,2C6.579,2,2,6.58,2,12s4.579,10,10,10s10-4.58,10-10S17.421,2,12,2z M12,20c-4.337,0-8-3.664-8-8 c0-3.998,3.115-7.417,7-7.927V6.09C8.167,6.569,6,9.033,6,12c0,3.309,2.691,6,6,6c1.595,0,3.1-0.626,4.237-1.763l-1.414-1.415 C14.063,15.582,13.061,16,12,16c-2.206,0-4-1.794-4-4c0-1.858,1.279-3.411,3-3.858v2.146c-0.59,0.353-1,0.993-1,1.712 c0,1.081,0.919,2,2,2s2-0.919,2-2c0-0.719-0.41-1.359-1-1.712V4.073c3.885,0.51,7,3.929,7,7.927C20,16.336,16.337,20,12,20z"}}]})(props);
};
