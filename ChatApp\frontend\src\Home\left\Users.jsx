import User from './User'
import GetAllUser from '../../Context/GetAllUser.jsx'

const Users = ({ onMobileSelect }) => {
  const [ allUser , loading] = GetAllUser();

  if (loading) return <p className="text-center py-4">Loading...</p>;

  return (
    <>
      <div className='flex-1 overflow-y-auto scroll-container h-full'>
        {allUser.length === 0 ? (
          <div className="flex flex-col items-center justify-center py-8 text-center h-full">
            <div className="w-16 h-16 bg-gray-200 dark:bg-gray-600 rounded-full flex items-center justify-center mb-4">
              <svg className="w-8 h-8 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z" />
              </svg>
            </div>
            <p className="text-gray-500 dark:text-gray-400 text-sm">No users found</p>
          </div>

        ) : (
          <div className="divide-y divide-gray-200 dark:divide-gray-700">
            {allUser.map((user) => (
              <User key={user._id} user={user} onMobileSelect={onMobileSelect} />
            ))}
          </div>
        )}
      </div>
    </>
  )
}


export default Users