// THIS FILE IS AUTO GENERATED
var GenIcon = require('../lib').GenIcon
module.exports.IoKeypadSharp = function IoKeypadSharp (props) {
  return GenIcon({"tag":"svg","attr":{"viewBox":"0 0 512 512"},"child":[{"tag":"rect","attr":{"width":"96","height":"96","x":"80","y":"16","rx":"8","ry":"8"}},{"tag":"rect","attr":{"width":"96","height":"96","x":"208","y":"16","rx":"8","ry":"8"}},{"tag":"rect","attr":{"width":"96","height":"96","x":"336","y":"16","rx":"8","ry":"8"}},{"tag":"rect","attr":{"width":"96","height":"96","x":"80","y":"144","rx":"8","ry":"8"}},{"tag":"rect","attr":{"width":"96","height":"96","x":"208","y":"144","rx":"8","ry":"8"}},{"tag":"rect","attr":{"width":"96","height":"96","x":"336","y":"144","rx":"8","ry":"8"}},{"tag":"rect","attr":{"width":"96","height":"96","x":"80","y":"272","rx":"8","ry":"8"}},{"tag":"rect","attr":{"width":"96","height":"96","x":"208","y":"272","rx":"8","ry":"8"}},{"tag":"rect","attr":{"width":"96","height":"96","x":"208","y":"400","rx":"8","ry":"8"}},{"tag":"rect","attr":{"width":"96","height":"96","x":"336","y":"272","rx":"8","ry":"8"}}]})(props);
};
