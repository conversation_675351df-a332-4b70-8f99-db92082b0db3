// THIS FILE IS AUTO GENERATED
var GenIcon = require('../lib').GenIcon
module.exports.GiPharoah = function GiPharoah (props) {
  return GenIcon({"tag":"svg","attr":{"viewBox":"0 0 512 512"},"child":[{"tag":"path","attr":{"d":"M246.515 24.295c-9.043.774-19.553 3.007-30.732 6.514l10.043 43.24c6.404-2.392 13.105-4.154 20.11-5.038l.58-44.717zm18.69.018l-.58 44.527c7.186.777 14.055 2.486 20.615 4.863l10.015-43.13c-10.946-3.37-21.215-5.51-30.05-6.26zm47.77 12.574l-10.204 43.94c3.492.978 6.903 2.132 10.194 3.456l33.313-30.94c-10.877-6.45-22.21-11.984-33.3-16.456zm-114.892.33c-11.06 4.526-22.338 10.102-33.152 16.582l33.195 30.827c3.268-1.37 6.667-2.544 10.146-3.543l-10.187-43.867zM362.41 63.865l-32.453 30.14c1.542 1.29 2.987 2.677 4.318 4.17l41.937-.325c-4.223-11.007-8.47-22.022-12.656-33.168-.38-.276-.765-.544-1.146-.817zm-213.547.514c-.138.1-.28.196-.416.296-4.22 11.237-8.502 22.342-12.76 33.437l41.826.367c1.21-1.374 2.513-2.662 3.9-3.867l-32.55-30.234zm97.006 30.628c-1.08-.014-2.177-.013-3.29.004-15.543.238-33.837 4.058-44.36 10.428-5.26 3.185-8.326 6.674-9.54 10.084-.823 2.308-1.11 4.925-.124 8.47 6.752-10.526 17.003-16.32 27.36-17.094 1.465-.11 2.925-.145 4.38-.115 10.185.21 20.108 3.645 29.89 7.563l-6.948 17.347c-10.284-4.118-19.614-6.63-25.932-6.158-6.318.472-10.116 2.216-14.46 11.094l-11.39-5.573a9.345 9.345 0 0 1-13.728 10.976c-3.358-2.066-5.31-1.69-5.404-1.715.015.274.078.86.363 2.395 2.545 13.71 3.765 24.454 9.113 35.607.06-.37.27-.846.264-1.152a9.345 9.345 0 0 1 18.404-2.467c3.085 12.266 8.35 27.187 14.824 37.968 3.237 5.39 6.782 9.69 9.983 12.2 3.2 2.513 5.602 3.277 8.24 3.052 2.436-.208 4.967.714 6.443 1.76 1.477 1.046 2.203 2.064 2.71 2.86 1.02 1.595 1.267 2.587 1.537 3.532.54 1.892.772 3.565 1.013 5.608.482 4.085.795 9.366 1.05 15.722.513 12.714.75 29.55 1.04 46.725.29 17.174.633 34.678 1.328 48.406.348 6.864.79 12.8 1.32 17.106.375 3.06 1.284 5.63 1.313 6.128.228.29.043.036.45.46.84.87 1.977 1.947 3.03 2.82.594.49.822.603 1.25.89.427-.287.655-.4 1.248-.89 1.054-.873 2.19-1.95 3.03-2.82.408-.424.223-.17.452-.46.03-.498.94-3.07 1.316-6.13.53-4.307.973-10.243 1.323-17.107.697-13.727 1.043-31.23 1.337-48.405.293-17.174.535-34.01 1.05-46.725.258-6.356.57-11.637 1.053-15.722.242-2.043.476-3.717 1.016-5.608.27-.945.52-1.937 1.537-3.53.51-.798 1.232-1.815 2.71-2.86 1.475-1.046 4.006-1.968 6.442-1.76 2.638.226 5.037-.538 8.235-3.05 3.197-2.51 6.737-6.81 9.97-12.2 6.467-10.78 11.725-25.7 14.81-37.97a9.345 9.345 0 0 1 18.405 2.468c-.006.308.204.786.264 1.158 5.36-11.16 6.6-21.908 9.143-35.613.285-1.535.348-2.12.364-2.395-.095.026-2.047-.35-5.405 1.715a9.345 9.345 0 0 1-13.767-10.857l-11.145 5.455c-4.345-8.877-8.142-10.62-14.46-11.093-6.32-.47-15.65 2.04-25.932 6.158l-6.95-17.347c9.783-3.918 19.706-7.352 29.89-7.563 1.456-.03 2.916.006 4.382.115 10.295.768 20.49 6.495 27.242 16.903 1.157-4.157.537-7.015-.838-9.73-1.948-3.845-6.715-7.964-13.877-11.237-14.323-6.546-36.892-8.966-51.803-7.29a9.345 9.345 0 0 1-2.09 0c-2.786-.313-5.85-.495-9.085-.538zm93.837 45.31c.007-.002.09.007.072-.002-.04-.02-.055-.062-.064-.11 0 .02-.008.086-.01.112zm-167.383 0c0-.026-.01-.092-.01-.11-.01.046-.025.09-.063.108-.016.01.066 0 .074.002zm-43.56-24.045c-2.29 6.08-4.55 12.18-6.77 18.305l33.045-.086c1.51-4.684 4.505-8.696 8.784-10.857 1.863-.942 3.79-1.538 5.744-1.85-.107-1.58-.118-3.145-.027-4.683l-40.78-.36.005-.47zm254.55.21l-40.895.32c.124 1.63.145 3.292.037 4.98 1.956.312 3.884.91 5.75 1.852 4.183 2.113 7.132 6 8.673 10.55l33.014.083c-2.16-5.952-4.354-11.876-6.58-17.784zM223.166 133.75c10.037-.22 18.92 8.16 19.4 15.402-10.74 5.74-28 2.03-34.354-.367l-9.318.733 8.588-6.946c4.85-6.233 10.426-8.707 15.684-8.822zm65.046 0c5.258.115 10.833 2.59 15.684 8.822l8.588 6.946-9.318-.733c-6.355 2.396-23.615 6.106-34.354.367.48-7.242 9.363-15.622 19.4-15.402zm68.31 19.12c-.965 5.556-1.963 11.17-3.464 16.935l48.988.1c-1.795-5.694-3.656-11.333-5.564-16.932l-39.96-.102zm-200.96.31l-40.154.103c-1.906 5.6-3.762 11.237-5.555 16.932l49.184-.1c-1.51-5.757-2.515-11.39-3.475-16.935zM256 172.074c4.22 3.656 9.21 3.53 14.093 3.875-3.58 4.924-7.777 9.088-14.093 10.593-6.317-1.505-10.514-5.67-14.094-10.594 4.884-.346 9.872-.22 14.094-3.876zm90.048 16.405c-1.518 2.962-3.255 5.957-5.314 8.995-2.754 4.062-9.806 6.16-13.95 4.88-1.48-.46-2.676-1.087-3.767-1.775-.21.56-.432 1.12-.65 1.682l-1.067 4.888 91.078-.607c-1.496-6.058-3.078-12.034-4.744-17.936l-61.586-.128zm-179.935.307l-61.834.13c-1.66 5.886-3.236 11.85-4.726 17.892l91.235.657-1.12-5.125c-.23-.59-.462-1.177-.684-1.766-1.093.69-2.288 1.318-3.772 1.778-4.143 1.282-11.195-.817-13.95-4.88-1.987-2.93-3.67-5.824-5.15-8.687zm82.248 5.838c2.965.038 6.15 1.136 7.64 2.793 2.918-4.328 12.308-2.212 13.28-.72 3.108 4.772 6.417 6.15 8.938 7.564-8.256 15.075-14.46 12.647-22.218 13.625-7.76-.978-13.963 1.45-22.22-13.625 2.522-1.415 5.863-2.792 8.97-7.563 1.135-1.492 3.306-2.104 5.61-2.075zm168.266 30.58l-99.412.662-3.818 17.498H420.05c-1.04-6.152-2.184-12.2-3.424-18.16zm-321.308.262c-1.24 5.975-2.386 12.04-3.424 18.21H198.7l-3.822-17.493-99.56-.717zm106.068 36.513v.385h-94.892c.656.71 1.315 1.406 1.976 2.086 6.65 6.843 13.19 11.907 22.233 13.71l6.12 1.22.014.067 66.065-.168c-.454-5.694-.95-11.45-1.516-17.3zm109.25.075c-.552 5.718-1.04 11.346-1.486 16.914l66.68.17v.116l5.498-1.096c9.042-1.803 15.57-6.864 22.213-13.705.758-.78 1.51-1.584 2.26-2.4h-95.164zm-82.91.1c-2.5.4-4.98.845-7.422 1.35 4.648 49.663 5.368 93.934 2.59 133.49 10.288 2.474 21.297 3.843 32.762 3.843 11.72 0 22.96-1.433 33.443-4.012-2.755-39.478-2.02-83.645 2.612-133.178-2.44-.514-4.92-.967-7.42-1.38-.402 11.81-.634 26.74-.898 42.175-.294 17.207-.638 34.822-1.36 49.037-.363 7.108-.813 13.35-1.44 18.438-.625 5.09-.38 8.172-3.493 13.2-1.352 2.186-2.078 2.746-3.37 4.085-1.292 1.34-2.838 2.813-4.57 4.246-1.733 1.432-3.547 2.823-6.008 4.01-1.23.595-2.636 1.186-4.69 1.436-.683.084-1.596-.03-2.462-.1-.867.07-1.78.184-2.463.1-2.054-.25-3.46-.84-4.69-1.435-2.46-1.187-4.275-2.578-6.008-4.01-1.733-1.434-3.28-2.907-4.57-4.247-1.293-1.34-2.02-1.9-3.372-4.084-3.115-5.032-2.87-8.115-3.494-13.204-.625-5.09-1.074-11.33-1.434-18.438-.72-14.215-1.06-31.83-1.35-49.037-.26-15.483-.492-30.46-.894-42.286zM99.28 281.544c-.72 1.317-1.373 2.64-1.937 3.967-50.147 118.058 18.382 202.193 158.66 202.193 140.28 0 208.8-84.135 158.653-202.193-.563-1.325-1.214-2.642-1.932-3.953-5.785 5.29-12.737 10.232-21.365 13.17-.64 3.412-1.235 6.815-1.816 10.213 9.45 14.837 14.872 31.597 14.872 49.397 0 32.043-17.544 60.723-44.6 80.857-27.056 20.135-63.782 32.237-104.16 32.237s-77.107-12.102-104.162-32.237c-27.056-20.134-44.6-48.814-44.6-80.857 0-18.156 5.636-35.23 15.44-50.283-.534-3.105-1.08-6.213-1.663-9.33-8.637-2.94-15.598-7.886-21.39-13.182zm208.54 16.11c-.362 5.946-.652 11.786-.89 17.556l61.794.16c.933-5.773 1.91-11.623 2.965-17.552l-63.87-.164zm-103.594.308l-63.83.164c1.053 5.93 2.026 11.78 2.957 17.553l61.75-.16c-.235-5.77-.518-11.61-.877-17.557zM306.353 333.9c-.115 6.07-.156 12.03-.13 17.897h56.405c1.092-5.836 2.106-11.747 3.086-17.746l-59.36-.15zm-100.686.307l-59.312.154c.98 6.002 1.997 11.91 3.09 17.747h56.346c.03-5.866-.01-11.83-.123-17.9zm-77.955 3.09c-1.398 5.542-2.13 11.237-2.13 17.043 0 16.922 6.143 32.915 17.23 46.867-7.565-21.032-11.59-42.33-15.1-63.91zm256.31 1.773c-3.36 20.423-7.27 40.59-14.27 60.52 10.295-13.566 15.974-28.978 15.974-45.25 0-5.188-.582-10.288-1.703-15.27zm-77.472 31.416c.186 6.065.45 12.015.79 17.856h46.43c1.87-5.867 3.478-11.822 4.92-17.856h-52.14zm-153.16.31c1.446 6.036 3.063 11.99 4.942 17.858h46.31c.345-5.84.616-11.792.807-17.857h-52.06zm155.332 36.235c.455 5.056.955 10.045 1.53 14.925 8.987 5.277 15.403 4.635 21.304 1.768 6.642-3.228 12.41-10.45 14.637-15.412.19-.425.37-.853.557-1.28h-38.028zm-143.338.312c.143.322.278.646.422.97 2.227 4.96 7.996 12.183 14.637 15.41 5.9 2.868 12.317 3.51 21.305-1.767.563-4.78 1.053-9.666 1.502-14.613h-37.866zm125.334 8.347c-11.13 2.5-22.896 3.837-35.062 3.837-11.92 0-23.457-1.284-34.39-3.69-.447 4.378-.925 8.717-1.464 12.974l-.513 4.06-3.333 2.374c-3.628 2.587-7.31 4.56-10.977 6.002 15.542 4.805 32.667 7.5 50.676 7.5 18.158 0 35.418-2.738 51.062-7.62-3.567-1.43-7.147-3.366-10.676-5.882l-3.332-2.375-.513-4.06c-.545-4.304-1.027-8.693-1.48-13.12z"}}]})(props);
};
