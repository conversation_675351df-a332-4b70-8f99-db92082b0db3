// THIS FILE IS AUTO GENERATED
var GenIcon = require('../lib').GenIcon
module.exports.GiFulguroPunch = function GiFulguroPunch (props) {
  return GenIcon({"tag":"svg","attr":{"viewBox":"0 0 512 512"},"child":[{"tag":"path","attr":{"d":"M62.47 19.156c13.14 31.823 32.193 62.18 56.874 88.063-11.85 10.762-22.447 22.84-31.594 36.03-21.292-29.564-45.02-57.614-66.78-80.813v118.438c12.978 5.168 26.796 9.27 41.31 12.125-5.44 15.432-9.145 31.695-10.843 48.53-10.186-4.116-20.337-7.584-30.468-10.31v71.218c10.492-2.71 21.038-5.896 31.56-9.657 2.62 18.31 7.565 35.868 14.595 52.345-16.343 2.983-32.122 6.41-46.156 9.844v92.155c26.926-13.143 49.837-34.07 70-60.22 9.276 12.652 19.935 24.22 31.75 34.5-26.53 22.142-47.847 47.28-63.19 73h92.595c7.958-12.727 14.725-26.335 20.5-40.655 14.284 6.6 29.427 11.637 45.22 14.906-2.747 8.596-4.954 17.188-6.657 25.75h71.875c-1.942-7.296-4.26-14.625-6.938-21.97 16.495-1.207 32.463-4.318 47.688-9.123 2.684 10.814 6.03 21.202 10.03 31.093h115.813c-23.84-19.925-47.823-38.786-71.875-57.125 12.516-8.426 24.06-18.15 34.47-29 25.58 24.163 52.782 43.8 84 64v-90.124c-17.742-8.918-33.945-15.385-50.22-20.344 9.954-18.584 17.19-38.836 21.19-60.218 9.756 2.242 19.454 3.836 29.03 4.78v-80.437c-9.193 1.4-18.433 3.515-27.688 6.25-2.908-19.878-8.58-38.86-16.656-56.53 15.598-2.51 30.427-6.222 44.344-11.376V72.845c-26.48 12.112-52.134 30.69-74.125 55.5-7.378-8.85-15.472-17.06-24.188-24.594 29.456-23.952 52.59-51.62 66.625-84.594H350.875C344.37 33.453 338.83 49.05 333.97 65.78c-13.245-4.94-27.122-8.56-41.47-10.75 5.04-11.997 8.417-23.91 10.156-35.874h-91.25c5.198 12.036 9.282 24.276 12.97 36.563-20.538 3.574-40.058 10.134-58.095 19.218.863-18.856 2.546-37.14 6-55.782H62.47zm221.436 53.532c15.717 1.902 30.857 5.723 45.156 11.187-5.604 22.29-10.235 46.36-14.437 71.97 24.754-12.34 47.75-25.702 68.25-40.532 9.987 8.286 19.13 17.59 27.25 27.718-8.474 11.214-16.246 23.475-23.094 36.782 15.655.24 30.835-.28 45.44-1.687 9.135 18.536 15.367 38.786 18.092 60.125-20.3 7.672-40.714 18.08-61.187 30.313 19.462 12.528 39.614 21.787 59.75 28.125-3.828 21.506-11.21 41.754-21.563 60.125-21.15-5.05-43.417-8.204-70.187-11.407 13.78 18.952 27.526 35.257 41.656 49.844-11.004 11.5-23.43 21.617-37 30.094-15.012-11.3-30.045-22.448-45.092-33.53-.895 18.467.216 36.25 3.03 53.155-16.226 5.274-33.403 8.442-51.218 9.186-6.473-14.724-14.24-29.49-23.063-44.25-8.664 13.46-15.733 27.18-21.375 41-15.862-3.034-31.045-8.007-45.25-14.687 6.427-19.07 11.392-39.227 15.25-60.19-20.634 9.633-39.58 21.047-56.687 33.69-13.398-11.228-25.254-24.26-35.156-38.72 7.19-10.558 14.04-21.775 20.593-33.53-12.062.98-24.544 2.525-36.97 4.436-7.907-17.455-13.265-36.334-15.624-56.125 11.61-4.926 23.126-10.618 34.468-17.217C93.12 261.5 81.323 255.126 69.56 249.53c1.28-18.735 5.23-36.73 11.5-53.593 13.34 1.555 27.166 2.064 41.407 1.375-6.995-12.546-14.884-25.17-23.314-37.656 9.352-14.623 20.64-27.89 33.5-39.437 10.033 9.138 20.835 17.568 32.438 25.124.2-17.244.225-33.383.53-48.938C185.06 85.3 206.64 77.54 229.595 73.812c4.884 17.445 9.602 34.82 16.094 51.688 16.128-18.296 28.83-35.744 38.218-52.813zM205.5 156.094L173.47 275.78l30.686 8.25 32.094-119.686-30.75-8.25zm48.78 13.094l-32.06 119.687 30.686 8.22L285 177.404l-30.72-8.218zm48.75 13.03l-32.06 119.688 30.686 8.22 32.094-119.688-30.72-8.22zm48.75 13.063l-32.06 119.69 30.717 8.25 32.094-119.69-30.75-8.25zM134.407 262l-11.03 41.188L240.5 334.563l5.375-20.032-28.5-7.624-9.03-2.437-9-2.408-39.75-10.656-9.032-2.406 2.406-9.03 3.25-12.126L134.405 262z"}}]})(props);
};
