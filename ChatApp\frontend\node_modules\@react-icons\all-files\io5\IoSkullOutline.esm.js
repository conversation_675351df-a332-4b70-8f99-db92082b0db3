// THIS FILE IS AUTO GENERATED
import { GenIcon } from '../lib';
export function IoSkullOutline (props) {
  return GenIcon({"tag":"svg","attr":{"viewBox":"0 0 512 512"},"child":[{"tag":"path","attr":{"fill":"none","strokeLinecap":"round","strokeMiterlimit":"10","strokeWidth":"32","d":"M448 225.64v99a64 64 0 01-40.23 59.42l-23.68 9.47A32 32 0 00364.6 417l-10 50.14A16 16 0 01338.88 480H173.12a16 16 0 01-15.69-12.86L147.4 417a32 32 0 00-19.49-23.44l-23.68-9.47A64 64 0 0164 324.67V224c0-105.92 85.77-191.81 191.65-192S448 119.85 448 225.64z"}},{"tag":"circle","attr":{"cx":"168","cy":"280","r":"40","fill":"none","strokeLinecap":"round","strokeMiterlimit":"10","strokeWidth":"32"}},{"tag":"circle","attr":{"cx":"344","cy":"280","r":"40","fill":"none","strokeLinecap":"round","strokeMiterlimit":"10","strokeWidth":"32"}},{"tag":"path","attr":{"fill":"none","strokeLinecap":"round","strokeLinejoin":"round","strokeWidth":"32","d":"M256 336l-16 48h32l-16-48zm0 112v32m-48-32v32m96-32v32"}}]})(props);
};
