// THIS FILE IS AUTO GENERATED
var GenIcon = require('../lib').GenIcon
module.exports.GiPropellerBeanie = function GiPropellerBeanie (props) {
  return GenIcon({"tag":"svg","attr":{"viewBox":"0 0 512 512"},"child":[{"tag":"path","attr":{"d":"M333.535 35.646c-23.904-.059-47.406 3.048-68.2 7.905a18.066 18.066 0 0 1 1.42 7.017c0 1.449-.182 2.827-.491 4.149 44.688 8.367 93.798 14.744 130.367 7.888 3.125-.585 4.278-1.71 4.945-2.8.667-1.091.994-2.57.342-4.842-1.305-4.543-7.388-11.923-22.3-14.918-15.011-3.015-30.6-4.36-46.083-4.399zM113.32 42.678c-20.836-.02-40.524 1.83-57.199 6.681-2.89.841-4.088 2.118-4.904 3.858-.816 1.74-1.076 4.233-.158 7.174 1.836 5.881 8.366 13.378 23.129 15.59 40.349 6.044 83.975-1.703 118.537-14.391-2.241-3.12-3.668-6.8-3.668-11.022 0-.59.032-1.17.086-1.74-21.796-3.015-44.584-5.473-66.829-6.031a372.548 372.548 0 0 0-8.994-.12zm114.586.736c-6.926 0-13.106 1.635-16.822 3.647-3.716 2.01-4.027 3.54-4.027 3.507 0-.033.31 1.495 4.027 3.506 3.716 2.011 9.896 3.647 16.822 3.647 6.927 0 13.108-1.636 16.824-3.647 3.717-2.011 4.026-3.539 4.026-3.506 0 .034-.31-1.496-4.026-3.507-3.716-2.012-9.897-3.647-16.824-3.647zm-9.351 31.617V122.8c2.157-.062 4.31-.097 6.449-.086 2.108-.086 4.194-.13 6.266-.147l.056-.271c.406.084.81.188 1.215.277 1.342-.002 2.686-.008 4.014.018V75.127c-2.792.38-5.674.594-8.649.594-3.224 0-6.345-.244-9.351-.69zm11.841 65.533l-.685.012c-50.239 59.006-48.933 142.418-21.82 222.791 34.727-7.601 70.359-12.709 102.683-14.974 18.393-1.29 35.538-1.772 50.957-1.202-.627-42.98-16.986-90.566-41.396-129.253-25.121-39.815-58.816-69.685-89.739-77.374zm-25.826 3.272c-11.678 2.616-23.135 10.158-34.12 21.934-13.789 14.779-26.309 35.889-36.282 59.384-19.687 46.382-29.235 102.435-24.336 135.903 16.432 5.315 36.595 9.462 57.326 12.533a640.07 640.07 0 0 1 23.115-6.13c-25.71-77.32-29.186-160.2 14.297-223.624zm79.668 5.195c19.03 15.395 36.418 35.998 51.121 59.301 26.274 41.64 43.908 92.361 44.186 140.106 9.116.972 17.402 2.442 24.701 4.496-1.456-42.26-13.892-94.37-40.058-135.998-19.228-30.59-45.217-55.514-79.95-67.905zm-128.586 6.266c-14.303 6.6-27.778 15.579-39.578 27.26-30.095 29.791-50.192 77.365-45.24 150.716.261 3.87 2.15 7.506 6.809 11.682 3.224 2.891 7.748 5.797 13.253 8.549-2.874-38.641 7.59-90.354 26.704-135.383 10.195-24.018 22.906-46.023 38.052-62.824zm188.08 209.885c-10.063.011-20.783.387-31.9 1.166-39.71 2.783-85.058 10.045-126.83 21 13.884 29.181 34.32 60.814 82.338 86.914l.14.076.135.08c.946.558 6.745 2.463 14.567 3.584 7.821 1.12 17.958 1.914 29.427 2.322 22.94.818 51.264.124 78.399-2.058 27.134-2.183 53.198-5.909 71.185-10.825 8.994-2.457 15.986-5.343 19.522-7.693 1.37-.91 1.727-1.416 2.053-1.803-14.925-30.08-34.237-54.162-65.21-80.252l-7.988-3.841c-11.36-5.463-35.646-8.705-65.838-8.67z"}}]})(props);
};
