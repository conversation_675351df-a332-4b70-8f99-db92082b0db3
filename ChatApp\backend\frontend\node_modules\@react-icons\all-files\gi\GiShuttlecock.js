// THIS FILE IS AUTO GENERATED
var GenIcon = require('../lib').GenIcon
module.exports.GiShuttlecock = function G<PERSON><PERSON>huttlecock (props) {
  return GenIcon({"tag":"svg","attr":{"viewBox":"0 0 512 512"},"child":[{"tag":"path","attr":{"d":"M256 25.577c-29.75 0-50.618 10.68-64.973 28.623-12.914 16.144-20.364 38.79-21.74 65.377h173.426c-1.376-26.586-8.826-49.233-21.74-65.377C306.618 36.257 285.75 25.577 256 25.577zm-87 112v14h174v-14H169zm12.443 32l-4.802 30H176v3.994l-12.357 77.167c1.428-.63 3.16-1.226 5.207-1.283.305-.01.616-.005.935.012.85.045 1.748.188 2.694.46 3.733 1.07 5.666 3.31 7.077 5.24.48.654.894 1.32 1.287 1.993l3.455-21.583h23.69l-.94 14.123c.77-.11 1.58-.17 2.448-.154.41.007.832.028 1.266.066 6.942.61 10.032 4.716 13.134 8.764.19.248.377.51.565.766l1.568-23.565h21.327v16.658c2.24-1.575 4.94-2.658 8.643-2.658 4.105 0 6.98 1.33 9.357 3.188v-17.188h22.065l1.726 21.443c2.796-3.567 5.923-6.866 12.088-7.408 2.057-.18 3.828.007 5.41.44l-1.166-14.475h23.25l3.41 20.04c.103-.15.194-.303.303-.452 1.41-1.928 3.344-4.17 7.078-5.24.947-.27 1.845-.414 2.695-.46.32-.016.63-.02.935-.01 2.137.06 3.942.705 5.405 1.364.392.176.757.372 1.125.566L336 200.966v-1.39h-.236l-5.104-30H312.4l5.104 30h-17.336l-2.414-30h-18.06l2.415 30h-16.753v-30h-18v30h-16.933l1.998-30h-18.04l-2 30H194.87l4.804-30h-18.23zm10.543 48h19.2l-2 30h-22.004l4.804-30zm37.24 0h18.13v30H227.23l1.997-30zm36.13 0h18.203l2.413 30h-20.616v-30zm36.26 0h18.95l5.104 30h-21.64l-2.413-30zm-92.542 81.246c-.26.187-.317.13-.615.403-2.248 2.058-5.392 5.725-8.773 10.486-6.76 9.522-14.636 23.43-21.718 39.035-14.166 31.21-24.75 69.83-20.933 93.586 1.633 10.164 4.142 16.383 9.713 22.98 5.046 5.977 13.334 12.386 25.902 20.348 7.703-3.16 13.956-6.07 19.063-8.903-6.09-7.457-9.938-16.05-12.442-25.98-7.73-30.66 1.108-71.263 13.133-105.434 2.81-7.982 5.82-15.557 8.914-22.56-1.638-4.26-3.286-8.186-4.902-11.6-2.498-5.278-4.953-9.437-6.807-11.856-.245-.322-.31-.274-.536-.504zm93.852 0c-.226.23-.29.182-.537.504-1.855 2.42-4.31 6.578-6.808 11.856-1.616 3.414-3.264 7.34-4.902 11.6 3.093 7.003 6.105 14.578 8.914 22.56 12.025 34.17 20.863 74.775 13.133 105.435-2.504 9.93-6.35 18.522-12.442 25.98 5.107 2.83 11.36 5.743 19.063 8.903 12.568-7.96 20.856-14.37 25.902-20.347 5.57-6.597 8.08-12.816 9.713-22.98 3.817-23.757-6.767-62.376-20.932-93.586-7.08-15.605-14.957-29.513-21.717-39.035-3.38-4.76-6.525-8.428-8.772-10.486-.297-.274-.353-.216-.614-.403zm-135.95 1.635c-1.903 1.823-4.114 4.144-6.685 7.29-7.01 8.585-15.662 21.378-23.95 35.925-16.576 29.093-31.543 65.874-32.223 89.785-.508 17.885 2.766 27.703 19.418 46.533 10.897-3.552 18.163-7.016 23.65-11.34-4.07-7.05-6.53-14.81-7.92-23.462-5.017-31.22 7.342-70.893 22.313-103.88 3.214-7.082 6.57-13.796 9.96-20.047-.554-3.766-1.154-7.28-1.798-10.41-.892-4.343-1.857-7.72-2.765-10.392zm178.05 0c-.91 2.672-1.874 6.05-2.766 10.39-.644 3.132-1.244 6.646-1.797 10.413 3.39 6.252 6.745 12.966 9.96 20.048 14.97 32.987 27.33 72.66 22.313 103.88-1.39 8.653-3.85 16.412-7.922 23.46 5.488 4.326 12.754 7.79 23.65 11.343 16.653-18.83 19.927-28.647 19.42-46.532-.68-23.91-15.648-60.692-32.224-89.785-8.288-14.547-16.94-27.34-23.95-35.924-2.572-3.148-4.783-5.47-6.685-7.292zm-96.97 9.328c-.153.258-.3.483-.454.746-5.9 10.077-12.528 24.62-18.217 40.785-11.378 32.33-18.54 71.73-12.658 95.06 2.516 9.983 5.562 15.958 11.69 22.042 5.55 5.51 14.366 11.172 27.583 18.003 13.217-6.83 22.034-12.493 27.584-18.004 6.127-6.085 9.173-12.06 11.69-22.042 5.882-23.332-1.28-62.73-12.66-95.06-5.688-16.166-12.315-30.71-18.216-40.786-.154-.263-.3-.488-.455-.746L256 465.108l-7.943-155.322z"}}]})(props);
};
