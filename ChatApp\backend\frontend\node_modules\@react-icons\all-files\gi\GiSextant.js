// THIS FILE IS AUTO GENERATED
var GenIcon = require('../lib').GenIcon
module.exports.GiSextant = function GiSextant (props) {
  return GenIcon({"tag":"svg","attr":{"viewBox":"0 0 512 512"},"child":[{"tag":"path","attr":{"d":"M259.348 69.037l17.33 4.867-6.016 21.416c4.62 5.93 7.39 13.372 7.39 21.47-.006 12.142-6.268 23.15-16.13 29.464h-45.147l1.434-4.848a35 35 0 0 1-10.16-24.615c0-17.235 12.463-31.55 28.866-34.452l-12.203 43.447 17.328 4.867 13.12-46.71 4.188-14.905zm26.048 76.078c14.414 11.826 33.446 30.675 48.047 40.774l-10.357 36.874 15.502 4.353c.257 1.342.64 2.682 1.195 4.014l68.957 90.68c7.848-6.045 12.38-.656 16.213 3.946a277.93 277.93 0 0 0 35.293-36.963l25.715 25.115c-57.415 70.63-144.954 115.828-242.91 115.828-14.104 0-27.986-.958-41.6-2.773 2.97-10.225 5.942-20.45 8.913-30.674a26.98 26.98 0 0 0 1.903-4.247 280.762 280.762 0 0 0 30.785 1.693c59.417 0 114.406-18.63 159.466-50.377l-31.246-41.09c-27.794 19.263-60.113 32.44-95.024 37.605a26.818 26.818 0 0 1-25.87 19.88 26.818 26.818 0 0 1-25.35-18.155c-3.626-.288-7.228-.662-10.805-1.12 1.67-5.84 3.352-11.68 5.036-17.52 2.008.23 4.024.433 6.05.604a26.817 26.817 0 0 1 3.17-6.046l-4.13-12.147c6.6-22.71 13.282-45.413 19.966-68.116h10.04l6.99 71.25a26.818 26.818 0 0 1 13.433 13.408c31.438-4.814 60.538-16.667 85.65-33.902l-80.82-106.278V152.27a53.75 53.75 0 0 0 5.79-7.155zm-132.066 3.14v78h-17.725v-78h17.725zm60.275 7v64H171.33v-64h42.275zm-96 0v64h-30v-64h30zm144 9v46h-30v-46h30zm94.25 1.19l17.327 4.867-17.305 61.616-17.33-4.867 8.312-29.593 1.507-5.367 7.487-26.655zm-286.25 5.81v32h-43v-32h43zm158.034 66l-37.312 127.388 8.14 15.27-18.33 63.05-62.273-20.6 20.455-62.324 17.303-7.13 34.225-115.656h37.79zm-64.802 7L129.43 290.52c7.644 5.007 15.65 9.503 23.953 13.482a9751.523 9751.523 0 0 0-5.123 17.428 225.38 225.38 0 0 1-29.32-16.38l-30.206 41.83a276.462 276.462 0 0 0 31.21 18.112c-4.116 11.318-7.75 22.83-11.378 34.344-30.56-14.602-58.404-34-82.527-57.244l22.86-27.674a277.37 277.37 0 0 0 18.405 16.53c2.902-3.237 8.592-5.312 12.445-2.37l60.887-84.324h22.2z"}}]})(props);
};
