// THIS FILE IS AUTO GENERATED
import { GenIcon } from '../lib';
export function GiNestBirds (props) {
  return GenIcon({"tag":"svg","attr":{"viewBox":"0 0 512 512"},"child":[{"tag":"path","attr":{"d":"M316.8 35.56l-47.6 44.09c11 2.51 21.2 8.69 29.5 16.59 6.4 6.16 11.9 13.46 15.6 21.46l32.1-35.43-47.3 14.29zm-58.2 60.86c-8 0-17 3.78-25 9.98-8.6 6.6-15.6 16-19 24.8-4.8 12.4-.8 38.3 6 60 5 16.3 9.9 27.6 12.5 33.5l31.9-4.6 11.6 1.4-5.4-35.1 3-3.2s7.8-8.3 15.1-19c7.4-10.6 12.8-24 11.9-28.9-1.4-8.7-7.1-18.5-15-26-7.8-7.5-17.6-12.35-26-12.84h-1.6zm127.6 23.38c-10.4.4-21.6 5-31.7 12.5-11.6 8.7-21.3 21-26.3 32.6v.1c-3.8 8.6-2 28.8 2.3 45.8 1.8 7.2 3.5 12.9 5.1 17.6l40.8 4.8-61.9 10.9-49.3-5.8-168.44 24 83.54 30 109.2-18.6-106 54.1-115.68-61.3-28.93 4.1L47.3 302l-29.27-11.9 37.91 44.2 4.73 17.7 43.83 20.9 63.1-16.5-56.8 41.9-42.17-16.5 10.86 40.6 24.01 7-41.99 17.2 97.39.4-52.8 21.9 107.1-7.6c73.4 25.8 90.3 22.4 173.3 30.1l-68.2-25.5 24.3-12.6-57.9-39.6c77.9 24.1 107.9 16.5 183.6 2.3l-42.2-6.2 51.8-27 .4-12.7 27.5-57.6-26.3 18.6 1-29.4-63.2 51.5-113-34.4 116.2 7.9 60.1-28.9.5-14.1 16.1-40.9-30.6 19.1-37.1-4.3-77.4 8.7s22.1-6.9 42.9-13.2l44-30.9-51.8 9.1-2.8-28.6 7.6-1.8s13.2-3.1 26.5-8.8c6.7-2.8 13.2-6.4 17.8-10 4.6-3.6 6.7-7 7-9.1 1.3-10.5-4.8-23.3-15.5-33.8-10.7-10.5-25.6-18.1-37.3-19.2-1.4-.2-2.8-.2-4.3-.2zM249 121.4c8.1 0 14.7 5.9 14.7 13.1 0 7.2-6.6 13.1-14.7 13.1s-14.7-5.9-14.7-13.1c0-7.2 6.6-13.1 14.7-13.1zm137.6 16.8c8.8 0 16 7.2 16 16s-7.2 16-16 16-16-7.2-16-16 7.2-16 16-16zm-259.2 1.5c-1.4 0-2.9 0-4.3.2-11.7 1.1-26.59 8.7-37.29 19.2-10.71 10.5-16.82 23.3-15.48 33.8.28 2.1 2.35 5.5 6.94 9.1 4.59 3.6 11.17 7.2 17.82 10 13.31 5.7 26.51 8.8 26.51 8.8l7.6 1.8-1.6 17.1 55.2-7.9c.1-.4.2-.7.3-1.1 4.3-17 6.1-37.2 2.3-45.8v-.1c-5-11.6-14.8-24-26.3-32.6-10.1-7.6-21.3-12.2-31.7-12.5zm332 16.4c1.9 6.1 2.6 12.6 1.7 19.1-.2 1.5-.5 2.9-.9 4.2l34.9-10.9zm-317.6.4c8.8 0 16 7.2 16 16s-7.2 16-16 16-16-7.2-16-16 7.2-16 16-16zm-79.07 2.1l-40.13 6.8 30.37 15.5c1.53-7.9 5.05-15.4 9.76-22.3zM207.8 365.4l73.2 13.8 78-9-79.6 30.8z"}}]})(props);
};
