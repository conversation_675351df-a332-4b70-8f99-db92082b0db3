// THIS FILE IS AUTO GENERATED
var GenIcon = require('../lib').GenIcon
module.exports.BiCalculator = function BiCalculator (props) {
  return GenIcon({"tag":"svg","attr":{"viewBox":"0 0 24 24"},"child":[{"tag":"path","attr":{"d":"M19,2H5C3.897,2,3,2.897,3,4v16c0,1.103,0.897,2,2,2h14c1.103,0,2-0.897,2-2V4C21,2.897,20.103,2,19,2z M5,20V4h14 l0.001,16H5z"}},{"tag":"path","attr":{"d":"M7 12H9V14H7zM7 16H9V18H7zM11 12H13V14H11zM7 6H17V10H7zM11 16H13V18H11zM15 12H17V18H15z"}}]})(props);
};
