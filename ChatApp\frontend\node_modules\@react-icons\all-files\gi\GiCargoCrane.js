// THIS FILE IS AUTO GENERATED
var GenIcon = require('../lib').GenIcon
module.exports.GiCargoCrane = function GiCargoCrane (props) {
  return GenIcon({"tag":"svg","attr":{"viewBox":"0 0 512 512"},"child":[{"tag":"path","attr":{"d":"M306.875 18.22c-.47-.002-.935.014-1.406.03-.158.005-.313.024-.47.03-1.583.056-3.176.204-4.78.47-3.61.597-7.01 1.7-10.158 3.22L49.595 120.062c-.918.42-1.808.877-2.688 1.343-.598.318-1.2.63-1.78.97-13.697 7.67-22.918 21.836-24.188 37.655-.058.635-.094 1.27-.125 1.907-.363 7.292.943 14.76 4.156 21.844 10.908 24.058 39.464 34.588 64.25 23.407l.093-.03L321.375 90.968l.156-.095.158-.063c19.685-8.925 28.3-31.436 19.656-50.5-6.28-13.85-19.92-22.07-34.47-22.093zm.28 18.718c8.924.308 16.542 6.77 18.064 15.968 1.737 10.51-5.208 20.23-15.72 21.97-10.512 1.738-20.2-5.21-21.938-15.72-1.738-10.51 5.208-20.23 15.72-21.968 1.313-.218 2.6-.295 3.874-.25zM354 86.656c-5.762 8.854-14.006 16.267-24.28 21-.108.05-.206.108-.314.156l-16.47 8.25 57.533 97.907c7.09-11.768 19.637-19.107 33.06-19.845 1.59-.087 3.18-.07 4.783.03L354 86.656zM67.906 135.03c.484-.005.96.015 1.438.032 13.393.478 24.876 10.217 27.156 24 2.606 15.75-7.873 30.426-23.625 33.032-15.752 2.606-30.426-7.906-33.03-23.656-2.607-15.75 7.903-30.395 23.655-33 1.477-.245 2.955-.387 4.406-.407zm60.188 73.595l-16.813 8.438 7.814 21.625-50.78-8.22-.064-.187c-7.272-.066-14.432-1.293-21.22-3.593l59.5 163.344-.405.783.75.187 16.906 46.406c4.612-4.79 9.704-9.125 15.22-12.875l-10.28-28.217 51.624 12.562h.03c3.962-.504 8-.78 12.095-.78 2.655 0 5.272.128 7.874.342l-72.25-199.812zm277.437 4.094c-.365 0-.73.012-1.092.03-8.72.434-16.66 5.983-19.844 14.78-4.244 11.733 1.706 24.414 13.437 28.657 11.733 4.244 24.445-1.705 28.69-13.437 4.243-11.734-1.707-24.412-13.44-28.656-2.565-.93-5.186-1.376-7.75-1.375zm39.158 9.81c2.728 8.395 2.806 17.714-.407 26.595-.456 1.264-.994 2.497-1.56 3.688l27.936 5-4.375 51.25-19.405 11.562 9.563 16.063 23.53-14.032 4.126-2.437.406-4.783 6.313-73.937.937-10.78-2.03.124-45.033-8.313zm-80.094 12.376l-44 26.78-.094.033v.03l-.063.03.157.158 4.47 9.5 31.498 67.187 2.032 4.344 4.72.874 26.905 5 3.436-18.375-22.22-4.126-21.467-45.813 26.842-16.186c-7.77-7.738-12.248-18.393-12.218-29.438zM75.656 250.594l35.156 5.687-18.593 39.845-16.564-45.53zm52.406 12.906l18.344 50.78-37.812-9.06 19.47-41.72zm-26.093 59.344l35.31 8.47-19.03 36.31-16.28-44.78zm52.968 15.03l18.093 49.97-39.28-9.53 21.188-40.44zm37.53 88.907c-39.29 0-71.475 29.325-76.062 67.345h22.5c4.46-25.5 26.83-45.03 53.563-45.03 26.73 0 49.074 19.53 53.53 45.03h22.5c-4.587-38.02-36.74-67.344-76.03-67.344zm0 41c-16.555 0-30.22 11.145-34.312 26.345h68.594c-4.092-15.2-17.725-26.344-34.28-26.344z"}}]})(props);
};
