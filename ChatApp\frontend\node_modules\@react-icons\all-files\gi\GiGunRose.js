// THIS FILE IS AUTO GENERATED
var GenIcon = require('../lib').GenIcon
module.exports.GiGunRose = function GiGunRose (props) {
  return GenIcon({"tag":"svg","attr":{"viewBox":"0 0 512 512"},"child":[{"tag":"path","attr":{"d":"M382.295 19.848l-51.34 54.99 27.06 85.992L419.644 176l16.406-49.58-45.104-23.934-8.447 23.213 15.62 9.598 2.673-11.535 14.617 7.539-5.763 27.047-49.16-27.498L384 78l43.107 24.766 17.795-57.682-62.607-25.236zM343.08 35.332c-2.256.45-43.49 8.084-53.945 9.863l-1.574 11.092-9.862 111.764 77.112 59.232 112.748-13.799 24.63-100.953-44.634-18.63-4.657 15.091 12.77 8.858-26.49 79.73-85.479-21.86-31.406-99.814 30.787-40.574zM234.521 59.408c-15.696 17.005-26.414 35.976-33.066 56.826 4.388.428 8.425.5 12.68.204 4.97-.346 10.51-1.257 17.322-2.725.01-.25.014-.444.023-.701.342-8.519.931-19.457 1.563-30.285.69-11.784.945-15.149 1.478-23.319zm31.301 61.22c-23.275 6.122-38.105 9.913-50.642 10.788-14.472 1.009-26.797-1.595-49.043-6.684-79.89 3.2-107.938 26.332-141.213 48.772-5.818 16.437-3.375 34.097 3.73 50.64 6.602 15.37 17.187 29.231 27.578 39.14 38.08-22.281 79.671-37.1 132.803-32.423l8.57.756-1.908 8.387c-5.176 22.752 1.912 43.235 15.256 59.676.173.213.36.417.535.629 1.956-6.117 6.192-10.892 11.207-14.15-6.2-11.15-5.858-35.113-2.652-49.89l-27.436-47.519 2.41-3.832c18.819-29.91 42.616-47.438 69.846-53.422l.96-10.867zm-2.345 26.587c-20.222 5.446-37.69 18.777-53.225 42.066l50.553 87.563c2.8-.216 5.62-.417 8.466-.623 4.176-.302 8.378-.609 12.524-.967l-39.01-67.57a7.506 7.506 0 0 1 2.746-10.254 7.508 7.508 0 0 1 10.256 2.748l42.264 73.203c5.233-.814 10.138-1.856 14.494-3.254l6.152-3.553-44.644-77.322a7.462 7.462 0 0 1-.955-4.56l-12.108-9.301 2.487-28.176zm-119.428 2.828c9.99-.24 19.844 4.832 25.158 14.035 7.73 13.388 3.084 30.726-10.303 38.455-13.387 7.73-30.725 3.083-38.455-10.304-7.729-13.387-3.083-30.726 10.305-38.456a27.858 27.858 0 0 1 13.295-3.73zm.453 14.941a13.244 13.244 0 0 0-6.24 1.791c-6.36 3.673-8.48 11.588-4.809 17.948 3.672 6.36 11.585 8.48 17.945 4.808 6.36-3.672 8.481-11.586 4.809-17.947-2.524-4.372-7.054-6.74-11.705-6.6zm156.736 41.325l30.532 52.88 15.615-8.949v-.002l-8.803-15.244-37.344-28.685zm-135.215 35.95c-6.768 44.82 6.932 72.251 15.245 104.313-17.695 43.045-35.085 81.537-71.348 107.38h-.006c-26.502 18.902-51.608 14.403-63.863.622-6.127-6.89-9.443-16.272-7.98-28.256 1.45-11.88 7.925-26.43 22.245-42.21 11.966-10.25 21.688-14.278 28.155-14.956 6.552-.687 9.856 1.215 12.338 4.328 4.963 6.227 5.047 20.812-7.48 31.327l12.013 14.316c19.1-16.03 22.676-41.487 10.082-57.291-6.297-7.9-17.037-12.509-28.9-11.266-11.866 1.245-24.787 7.552-38.885 19.76l-.41.356-.368.4c-16.57 18.11-25.296 36.22-27.34 52.973-2.045 16.753 2.877 32.043 12.567 42.94 19.38 21.79 56.223 25.322 88.68 2.175l.002-.002a160.698 160.698 0 0 0 16.992-13.967c29.887-7.727 58.964-10.74 92.3-37.435-21.192.977-40.373-1.703-53.542-15.131 13.248-24.99 23.122-52.169 34.386-78.918-9.727-9.81-17.748-20.652-23.406-32.656a94.12 94.12 0 0 1-8.234-28.065c-4.956-6.014-9.281-13.07-13.243-20.736zm188.87 20.983l-29.1 16.674 99.695 172.678 28.883-17.05-93.322-161.636.002-.002-6.158-10.664zm-50.715 25.293c-11.152 2.055-22.79 2.851-33.752 3.645-16.196 1.172-30.888 2.923-37.924 6.746-3.436 1.867-4.955 3.588-5.8 6.326-.847 2.738-.901 7.305 1.517 14.488 2.817 8.365 7.214 12.04 13.224 13.742 6.011 1.702 14.25.852 23.123-3.11 14.648-6.543 30.25-21.402 39.612-41.837zm14.008 8.23a108.398 108.398 0 0 1-8.811 15.329l28.938 50.123 13.244-7.647-33.371-57.804zm123.318 84.514l22.03 38.157 26.794-15.471c-13.261-12.692-28.152-21.575-48.824-22.686z"}}]})(props);
};
