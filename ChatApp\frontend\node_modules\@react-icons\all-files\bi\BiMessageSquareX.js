// THIS FILE IS AUTO GENERATED
var GenIcon = require('../lib').GenIcon
module.exports.BiMessageSquareX = function BiMessageSquareX (props) {
  return GenIcon({"tag":"svg","attr":{"viewBox":"0 0 24 24"},"child":[{"tag":"path","attr":{"d":"M16,2H8C4.691,2,2,4.691,2,8v13c0,0.553,0.447,1,1,1h13c3.309,0,6-2.691,6-6V8C22,4.691,19.309,2,16,2z M20,16 c0,2.206-1.794,4-4,4H4V8c0-2.206,1.794-4,4-4h8c2.206,0,4,1.794,4,4V16z"}},{"tag":"path","attr":{"d":"M15.292 7.295L12 10.587 8.708 7.295 7.294 8.709 10.586 12.001 7.294 15.293 8.708 16.707 12 13.415 15.292 16.707 16.706 15.293 13.414 12.001 16.706 8.709z"}}]})(props);
};
