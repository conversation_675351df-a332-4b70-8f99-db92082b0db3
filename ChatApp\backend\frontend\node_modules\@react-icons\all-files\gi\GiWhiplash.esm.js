// THIS FILE IS AUTO GENERATED
import { GenIcon } from '../lib';
export function GiWhiplash (props) {
  return GenIcon({"tag":"svg","attr":{"viewBox":"0 0 512 512"},"child":[{"tag":"path","attr":{"d":"M407.056 376.454C511.348 517.65 270.95 424.988 194.373 328.11c-59.935-75.823 212.347-41.197 101.407-177.47C172.653-.453-54.165-13.92 65.816 92.687 35.032 6.53 299.823 128.57 311.883 205.35c10.934 69.623-308.9 30.456-112.237 175.655 137.22 101.312 397.83 144.363 207.41-4.55z","fillRule":"evenodd"}}]})(props);
};
