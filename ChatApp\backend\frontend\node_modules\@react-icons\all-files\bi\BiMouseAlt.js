// THIS FILE IS AUTO GENERATED
var GenIcon = require('../lib').GenIcon
module.exports.BiMouseAlt = function BiMouseAlt (props) {
  return GenIcon({"tag":"svg","attr":{"viewBox":"0 0 24 24"},"child":[{"tag":"path","attr":{"d":"M13,2h-2C7.691,2,5,4.691,5,8v8c0,3.309,2.691,6,6,6h2c3.309,0,6-2.691,6-6v-4v-2V8C19,4.691,16.309,2,13,2z M11,4v6H7V8 C7,5.794,8.794,4,11,4z M17,16c0,2.206-1.794,4-4,4h-2c-2.206,0-4-1.794-4-4v-4h10V16z M13,10V4c2.206,0,4,1.794,4,4v2H13z"}}]})(props);
};
