// THIS FILE IS AUTO GENERATED
var GenIcon = require('../lib').GenIcon
module.exports.GiHammerSickle = function GiHammerSickle (props) {
  return GenIcon({"tag":"svg","attr":{"viewBox":"0 0 512 512"},"child":[{"tag":"path","attr":{"d":"M257.316 21.44C434.586 160.414 438.15 262.89 384.48 315.87l55.65 55.648c81.846-100.582 74.738-275.933-182.814-350.08zm-37.92 21.664c-16.713 4.847-34.67 7.066-50.61 6.75-13.684-.27-25.71-1.44-34.936-6.41L21.13 156.163l55.15 55.156L231.946 55.656l-12.55-12.552zm-41.29 91.85l-43.84 43.84L442 486.523c3.005 3.007 6.436 3.516 12.582 1.646 6.146-1.87 13.72-6.735 19.943-12.957 6.223-6.223 11.087-13.8 12.957-19.945 1.87-6.146 1.363-9.577-1.642-12.582L178.105 134.953zm-8.665 179.735l-40.75 37.124 50.814 50.346c59.746 49.805 126.245 53.237 182.033 29.36l-75.273-75.274c-43.74 2.716-88.647-10.463-116.823-41.557zm-58.936 45.873c-5.525 1.065-8.977 2.957-11.315 5.25-1.957 1.922-3.25 4.556-4.25 7.564l55.19 52.844c5.467-1.007 9.262-2.795 11.278-4.687 1.997-1.872 3.096-3.863 3.096-7.53l-54-53.44zm-24.72 30.314L30.16 441.562c-16.737 27 14.694 61.2 51.094 44.66l51.53-50.28-47-45.063v-.003z"}}]})(props);
};
