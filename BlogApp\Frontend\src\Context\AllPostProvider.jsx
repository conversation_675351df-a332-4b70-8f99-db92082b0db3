import { createContext } from "react";
import { useContext } from "react";
import { useState } from "react";
import React from 'react'

const AllPostContext = createContext()



 export const AllPostProvider = ({children}) => {
    const [allUserPost, setallUserPost] = useState([])
  return (
    <AllPostContext.Provider value={{allUserPost, setallUserPost}}>
        {children}
    </AllPostContext.Provider>
  )
}

// eslint-disable-next-line react-refresh/only-export-components
export const  useAllPost=()=>useContext(AllPostContext)

