// THIS FILE IS AUTO GENERATED
var GenIcon = require('../lib').GenIcon
module.exports.BiBox = function BiBox (props) {
  return GenIcon({"tag":"svg","attr":{"viewBox":"0 0 24 24"},"child":[{"tag":"path","attr":{"fill":"none","d":"M5 9v10h14.002L19 9H5zM16 13H8v-2h8V13zM20.002 7L20 5 4 5 4 7 20 7z"}},{"tag":"path","attr":{"d":"M20,3H4C2.897,3,2,3.897,2,5v2c0,0.736,0.405,1.375,1,1.722V19c0,1.103,0.897,2,2,2h14c1.103,0,2-0.897,2-2V8.722 C21.595,8.375,22,7.736,22,7V5C22,3.897,21.103,3,20,3z M4,5h16l0.002,2H20H4V5z M5,19V9h14l0.002,10H5z"}},{"tag":"path","attr":{"d":"M8 11H16V13H8z"}}]})(props);
};
