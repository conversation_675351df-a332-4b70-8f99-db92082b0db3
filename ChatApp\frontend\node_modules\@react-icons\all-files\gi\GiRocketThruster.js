// THIS FILE IS AUTO GENERATED
var GenIcon = require('../lib').GenIcon
module.exports.GiRocketThruster = function GiRocketThruster (props) {
  return GenIcon({"tag":"svg","attr":{"viewBox":"0 0 512 512"},"child":[{"tag":"path","attr":{"d":"M36.078 16L16 35.55v50.247L87.684 16H36.078zm121.83 0l-97.834 95.26c8.838-.48 17.72-.946 27.207-1.367L183.71 16H157.91zm107.768 0L217.72 62.693c11.73-7.19 24.684-14.537 37.987-21.455 17.973-9.346 36.334-17.656 52.807-23.023 2.466-.804 4.865-1.53 7.234-2.215h-50.072zm105.627 0l-4.29 3.432c56.944 23.005 97.62 63.682 120.628 120.627l8.16-10.2C475.674 75.84 440.51 38.356 388.93 16h-17.63zm55.894 0C455.86 34.214 478.884 58.318 496 88.027V63.582C484.442 45.14 469.57 29.234 451.488 16h-24.29zm52.176 0A192.31 192.31 0 0 1 496 33.447V16h-16.627zM341.316 30.266c-6.124-.108-15.98 1.4-27.226 5.064-14.994 4.886-32.696 12.84-50.078 21.88a519.558 519.558 0 0 0-18.948 10.384c27.085 5.188 54.39 17.866 79.737 35.3l69.516-49.654c-14.42-8.99-30.465-16.435-48.207-22.35-1.126-.374-2.752-.588-4.794-.624zm68.713 33.87l-69.99 49.995a317.653 317.653 0 0 1 28.007 24.895 317.684 317.684 0 0 1 24.902 28.002l49.988-69.986a181.614 181.614 0 0 0-32.91-32.905zm-193.41 18.08c-19.394.162-36.698 5.932-49.39 18.626a58.492 58.492 0 0 0-6.992 8.447c7.737.345 15.336.865 22.698 1.606 8.6-7.025 20.286-10.66 34.26-10.71 9.396-.034 19.82 1.55 31.025 4.802 29.88 8.675 64.076 29.196 94.372 59.492 30.296 30.297 50.82 64.496 59.494 94.374 8.674 29.877 5.474 54.205-8.582 68.26a40.523 40.523 0 0 1-7.076 5.63 234.492 234.492 0 0 1-5.32 22.106c9.518-3 18.04-7.923 25.124-15.006 20.31-20.31 22.893-52.42 13.14-86.01-9.36-32.248-30.12-66.993-60.245-98.2l-.09-.093-.057-.056a307.882 307.882 0 0 0-3.66-3.73h-.002a308.138 308.138 0 0 0-3.726-3.66l-.06-.06c-.034-.03-.067-.06-.1-.093-31.205-30.12-65.946-50.88-98.192-60.24-12.596-3.658-24.985-5.58-36.62-5.483zM453.83 112.76l-49.652 69.515c17.437 25.354 30.1 52.67 35.285 79.762a519.383 519.383 0 0 0 10.4-18.974c9.04-17.382 16.992-35.084 21.877-50.078 4.886-14.993 5.94-27.516 4.44-32.017-5.914-17.742-13.36-33.785-22.35-48.207zm-315.766 14.306c-24.424-.103-49.52 1.074-72.78 2.217 7.69 2.09 14.93 3.963 22.872 6.343 29.69 8.9 59.69 19.512 74.75 40.217l12.465 17.137-20.988-2.93c-45.902-6.41-74.37 5.044-101.47 15.693 33.77 7.288 58.506 15.248 87.683 36.73l13.96 10.278-16.44 5.504C78.56 278.195 47.45 315.047 16 357.834v22.143c9.58-5.07 19.283-10.596 29.057-15.934 27.153-14.832 55.85-28.346 85.502-19.572l7.84 2.32-1.56 8.028c-6.803 35.01-28.027 91.445-48.008 141.182h15.455c44.07-50.768 93.33-113.807 108.994-146.596l7.16-14.984 8.644 14.178c5.784 9.484 3.667 19.766.973 31.724-2.694 11.958-7.28 25.69-12.178 39.88-6.224 18.024-12.536 36.71-16.552 52.19 12.538-12.48 25.598-29.23 36.842-46.912 16.8-26.42 29.886-55.1 33.586-72.515l5.015-23.613 11.668 21.134c12.356 22.385 10.967 52.3 6.996 84.723-2.37 19.346-5.82 39.638-8.854 59.653 14.87-22.31 32.57-48.36 48.307-77.36 24.843-45.79 43.262-96.883 34.79-143.876-2.723-15.107-13.146-34.003-28.327-52.83-21.165 6.466-42.44 20.892-62.875 40.822 17.09-29.95 23.5-49.964 37.183-65.225-24.578 10.682-57.176 29.572-91.263 62.765 19.654-25.258 40.095-64.07 53.935-91.478-20.372 15.64-51.45 31.377-71.21 43.81 9.8-15.11 30.47-43.68 36.517-66.957-10.983-4.976-21.758-8.688-31.87-10.765l-.148-.032-.15-.035c-20.223-4.873-46.176-6.523-73.404-6.64zM31.73 138.86L16 154.177V179.3l35.928-34.984c-7.012-1.842-13.946-3.663-20.2-5.455zm82.047 25.04l-8.13 7.916a179.563 179.563 0 0 1 21.603-1.504c-4.143-2.217-8.685-4.355-13.473-6.412zM52.14 223.916L16 259.106v50.244l75.926-73.928C79.87 230.577 67.19 227.2 52.14 223.916zM496 287.72L346.97 432.83c-13.02 23.235-27.113 44.683-39.44 63.172h26.163L496 337.965V287.72zm-384.45 72.075c-3.92 0-7.91.426-11.972 1.203L16 442.378V467.5l97.834-95.258c1.202-4.276 2.285-8.36 3.193-12.162a51.858 51.858 0 0 0-5.476-.285zM496 368.81L365.373 496h25.8L496 393.934V368.81zm-295.277 45.924l-27.32 26.602c-14.17 18.31-29.498 36.993-44.624 54.664h14.282l44.74-43.56c3.907-12.385 8.606-25.225 12.92-37.706z"}}]})(props);
};
