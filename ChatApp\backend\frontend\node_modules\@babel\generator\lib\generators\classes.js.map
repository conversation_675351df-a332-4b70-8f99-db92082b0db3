{"version": 3, "names": ["_t", "require", "isExportDefaultDeclaration", "isExportNamedDeclaration", "ClassDeclaration", "node", "parent", "inExport", "_shouldPrintDecoratorsBeforeExport", "printJoin", "decorators", "declare", "word", "space", "abstract", "id", "print", "typeParameters", "superClass", "superTypeParameters", "implements", "printList", "body", "ClassBody", "token", "length", "newline", "separator", "classBodyEmptySemicolonsPrinter", "exit", "enterDelimited", "endsWith", "rightBrace", "printer", "tokenMap", "start", "end", "indexes", "getIndexes", "k", "occurrenceCount", "nextLocIndex", "advanceNextLocIndex", "i", "tok", "matchesOriginal", "_tokens", "undefined", "ClassProperty", "static", "format", "preserveFormat", "_node$key$loc", "endLine", "key", "loc", "line", "catchUp", "tsPrintClassMemberModifiers", "computed", "_variance", "optional", "definite", "typeAnnotation", "value", "semicolon", "ClassAccessorProperty", "_node$key$loc2", "ClassPrivateProperty", "ClassMethod", "_classMethodHead", "ClassPrivateMethod", "_node$key$loc3", "_methodHead", "StaticBlock", "printSequence"], "sources": ["../../src/generators/classes.ts"], "sourcesContent": ["import type Printer from \"../printer.ts\";\nimport {\n  isExportDefaultDeclaration,\n  isExportNamedDeclaration,\n} from \"@babel/types\";\nimport type * as t from \"@babel/types\";\n\n// We inline this package\n// eslint-disable-next-line import/no-extraneous-dependencies\nimport * as charCodes from \"charcodes\";\n\nexport function ClassDeclaration(\n  this: Printer,\n  node: t.ClassDeclaration,\n  parent: t.Node,\n) {\n  const inExport =\n    isExportDefaultDeclaration(parent) || isExportNamedDeclaration(parent);\n\n  if (\n    !inExport ||\n    !this._shouldPrintDecoratorsBeforeExport(\n      parent as t.ExportDeclaration & { declaration: t.ClassDeclaration },\n    )\n  ) {\n    this.printJoin(node.decorators);\n  }\n\n  if (node.declare) {\n    // TS\n    this.word(\"declare\");\n    this.space();\n  }\n\n  if (node.abstract) {\n    // TS\n    this.word(\"abstract\");\n    this.space();\n  }\n\n  this.word(\"class\");\n\n  if (node.id) {\n    this.space();\n    this.print(node.id);\n  }\n\n  this.print(node.typeParameters);\n\n  if (node.superClass) {\n    this.space();\n    this.word(\"extends\");\n    this.space();\n    this.print(node.superClass);\n    this.print(\n      process.env.BABEL_8_BREAKING\n        ? // @ts-ignore(Babel 7 vs Babel 8) Renamed\n          node.superTypeArguments\n        : // @ts-ignore(Babel 7 vs Babel 8) Renamed\n          node.superTypeParameters,\n    );\n  }\n\n  if (node.implements) {\n    this.space();\n    this.word(\"implements\");\n    this.space();\n    this.printList(node.implements);\n  }\n\n  this.space();\n  this.print(node.body);\n}\n\nexport { ClassDeclaration as ClassExpression };\n\nexport function ClassBody(this: Printer, node: t.ClassBody) {\n  this.token(\"{\");\n  if (node.body.length === 0) {\n    this.token(\"}\");\n  } else {\n    this.newline();\n\n    const separator = classBodyEmptySemicolonsPrinter(this, node);\n    separator?.(-1); // print leading semicolons in preserveFormat mode\n\n    const exit = this.enterDelimited();\n    this.printJoin(node.body, true, true, separator, true);\n    exit();\n\n    if (!this.endsWith(charCodes.lineFeed)) this.newline();\n\n    this.rightBrace(node);\n  }\n}\n\nfunction classBodyEmptySemicolonsPrinter(printer: Printer, node: t.ClassBody) {\n  if (!printer.tokenMap || node.start == null || node.end == null) {\n    return null;\n  }\n\n  // \"empty statements\" in class bodies are not represented in the AST.\n  // Print them by checking if there are any ; tokens between the current AST\n  // member and the next one.\n\n  const indexes = printer.tokenMap.getIndexes(node);\n  if (!indexes) return null;\n\n  let k = 1; // start from 1 to skip '{'\n\n  let occurrenceCount = 0;\n\n  let nextLocIndex = 0;\n  const advanceNextLocIndex = () => {\n    while (\n      nextLocIndex < node.body.length &&\n      node.body[nextLocIndex].start == null\n    ) {\n      nextLocIndex++;\n    }\n  };\n  advanceNextLocIndex();\n\n  return (i: number) => {\n    if (nextLocIndex <= i) {\n      nextLocIndex = i + 1;\n      advanceNextLocIndex();\n    }\n\n    const end =\n      nextLocIndex === node.body.length\n        ? node.end\n        : node.body[nextLocIndex].start;\n\n    let tok;\n    while (\n      k < indexes.length &&\n      printer.tokenMap.matchesOriginal(\n        (tok = printer._tokens[indexes[k]]),\n        \";\",\n      ) &&\n      tok.start < end\n    ) {\n      printer.token(\";\", undefined, occurrenceCount++);\n      k++;\n    }\n  };\n}\n\nexport function ClassProperty(this: Printer, node: t.ClassProperty) {\n  this.printJoin(node.decorators);\n\n  if (!node.static && !this.format.preserveFormat) {\n    // catch up to property key, avoid line break\n    // between member TS modifiers and the property key.\n    const endLine = node.key.loc?.end?.line;\n    if (endLine) this.catchUp(endLine);\n  }\n\n  this.tsPrintClassMemberModifiers(node);\n\n  if (node.computed) {\n    this.token(\"[\");\n    this.print(node.key);\n    this.token(\"]\");\n  } else {\n    this._variance(node);\n    this.print(node.key);\n  }\n\n  // TS\n  if (node.optional) {\n    this.token(\"?\");\n  }\n  if (node.definite) {\n    this.token(\"!\");\n  }\n\n  this.print(node.typeAnnotation);\n  if (node.value) {\n    this.space();\n    this.token(\"=\");\n    this.space();\n    this.print(node.value);\n  }\n  this.semicolon();\n}\n\nexport function ClassAccessorProperty(\n  this: Printer,\n  node: t.ClassAccessorProperty,\n) {\n  this.printJoin(node.decorators);\n\n  // catch up to property key, avoid line break\n  // between member modifiers and the property key.\n  const endLine = node.key.loc?.end?.line;\n  if (endLine) this.catchUp(endLine);\n\n  // TS does not support class accessor property yet\n  this.tsPrintClassMemberModifiers(node);\n\n  this.word(\"accessor\", true);\n  this.space();\n\n  if (node.computed) {\n    this.token(\"[\");\n    this.print(node.key);\n    this.token(\"]\");\n  } else {\n    // Todo: Flow does not support class accessor property yet.\n    this._variance(node);\n    this.print(node.key);\n  }\n\n  // TS\n  if (node.optional) {\n    this.token(\"?\");\n  }\n  if (node.definite) {\n    this.token(\"!\");\n  }\n\n  this.print(node.typeAnnotation);\n  if (node.value) {\n    this.space();\n    this.token(\"=\");\n    this.space();\n    this.print(node.value);\n  }\n  this.semicolon();\n}\n\nexport function ClassPrivateProperty(\n  this: Printer,\n  node: t.ClassPrivateProperty,\n) {\n  this.printJoin(node.decorators);\n  this.tsPrintClassMemberModifiers(node);\n  this.print(node.key);\n  // TS\n  if (node.optional) {\n    this.token(\"?\");\n  }\n  if (node.definite) {\n    this.token(\"!\");\n  }\n  this.print(node.typeAnnotation);\n  if (node.value) {\n    this.space();\n    this.token(\"=\");\n    this.space();\n    this.print(node.value);\n  }\n  this.semicolon();\n}\n\nexport function ClassMethod(this: Printer, node: t.ClassMethod) {\n  this._classMethodHead(node);\n  this.space();\n  this.print(node.body);\n}\n\nexport function ClassPrivateMethod(this: Printer, node: t.ClassPrivateMethod) {\n  this._classMethodHead(node);\n  this.space();\n  this.print(node.body);\n}\n\nexport function _classMethodHead(\n  this: Printer,\n  node: t.ClassMethod | t.ClassPrivateMethod | t.TSDeclareMethod,\n) {\n  this.printJoin(node.decorators);\n\n  if (!this.format.preserveFormat) {\n    // catch up to method key, avoid line break\n    // between member modifiers/method heads and the method key.\n    const endLine = node.key.loc?.end?.line;\n    if (endLine) this.catchUp(endLine);\n  }\n\n  this.tsPrintClassMemberModifiers(node);\n  this._methodHead(node);\n}\n\nexport function StaticBlock(this: Printer, node: t.StaticBlock) {\n  this.word(\"static\");\n  this.space();\n  this.token(\"{\");\n  if (node.body.length === 0) {\n    this.token(\"}\");\n  } else {\n    this.newline();\n    this.printSequence(node.body, true);\n    this.rightBrace(node);\n  }\n}\n"], "mappings": ";;;;;;;;;;;;;;AACA,IAAAA,EAAA,GAAAC,OAAA;AAGsB;EAFpBC,0BAA0B;EAC1BC;AAAwB,IAAAH,EAAA;AAQnB,SAASI,gBAAgBA,CAE9BC,IAAwB,EACxBC,MAAc,EACd;EACA,MAAMC,QAAQ,GACZL,0BAA0B,CAACI,MAAM,CAAC,IAAIH,wBAAwB,CAACG,MAAM,CAAC;EAExE,IACE,CAACC,QAAQ,IACT,CAAC,IAAI,CAACC,kCAAkC,CACtCF,MACF,CAAC,EACD;IACA,IAAI,CAACG,SAAS,CAACJ,IAAI,CAACK,UAAU,CAAC;EACjC;EAEA,IAAIL,IAAI,CAACM,OAAO,EAAE;IAEhB,IAAI,CAACC,IAAI,CAAC,SAAS,CAAC;IACpB,IAAI,CAACC,KAAK,CAAC,CAAC;EACd;EAEA,IAAIR,IAAI,CAACS,QAAQ,EAAE;IAEjB,IAAI,CAACF,IAAI,CAAC,UAAU,CAAC;IACrB,IAAI,CAACC,KAAK,CAAC,CAAC;EACd;EAEA,IAAI,CAACD,IAAI,CAAC,OAAO,CAAC;EAElB,IAAIP,IAAI,CAACU,EAAE,EAAE;IACX,IAAI,CAACF,KAAK,CAAC,CAAC;IACZ,IAAI,CAACG,KAAK,CAACX,IAAI,CAACU,EAAE,CAAC;EACrB;EAEA,IAAI,CAACC,KAAK,CAACX,IAAI,CAACY,cAAc,CAAC;EAE/B,IAAIZ,IAAI,CAACa,UAAU,EAAE;IACnB,IAAI,CAACL,KAAK,CAAC,CAAC;IACZ,IAAI,CAACD,IAAI,CAAC,SAAS,CAAC;IACpB,IAAI,CAACC,KAAK,CAAC,CAAC;IACZ,IAAI,CAACG,KAAK,CAACX,IAAI,CAACa,UAAU,CAAC;IAC3B,IAAI,CAACF,KAAK,CAKJX,IAAI,CAACc,mBACX,CAAC;EACH;EAEA,IAAId,IAAI,CAACe,UAAU,EAAE;IACnB,IAAI,CAACP,KAAK,CAAC,CAAC;IACZ,IAAI,CAACD,IAAI,CAAC,YAAY,CAAC;IACvB,IAAI,CAACC,KAAK,CAAC,CAAC;IACZ,IAAI,CAACQ,SAAS,CAAChB,IAAI,CAACe,UAAU,CAAC;EACjC;EAEA,IAAI,CAACP,KAAK,CAAC,CAAC;EACZ,IAAI,CAACG,KAAK,CAACX,IAAI,CAACiB,IAAI,CAAC;AACvB;AAIO,SAASC,SAASA,CAAgBlB,IAAiB,EAAE;EAC1D,IAAI,CAACmB,SAAK,IAAI,CAAC;EACf,IAAInB,IAAI,CAACiB,IAAI,CAACG,MAAM,KAAK,CAAC,EAAE;IAC1B,IAAI,CAACD,SAAK,IAAI,CAAC;EACjB,CAAC,MAAM;IACL,IAAI,CAACE,OAAO,CAAC,CAAC;IAEd,MAAMC,SAAS,GAAGC,+BAA+B,CAAC,IAAI,EAAEvB,IAAI,CAAC;IAC7DsB,SAAS,YAATA,SAAS,CAAG,CAAC,CAAC,CAAC;IAEf,MAAME,IAAI,GAAG,IAAI,CAACC,cAAc,CAAC,CAAC;IAClC,IAAI,CAACrB,SAAS,CAACJ,IAAI,CAACiB,IAAI,EAAE,IAAI,EAAE,IAAI,EAAEK,SAAS,EAAE,IAAI,CAAC;IACtDE,IAAI,CAAC,CAAC;IAEN,IAAI,CAAC,IAAI,CAACE,QAAQ,GAAmB,CAAC,EAAE,IAAI,CAACL,OAAO,CAAC,CAAC;IAEtD,IAAI,CAACM,UAAU,CAAC3B,IAAI,CAAC;EACvB;AACF;AAEA,SAASuB,+BAA+BA,CAACK,OAAgB,EAAE5B,IAAiB,EAAE;EAC5E,IAAI,CAAC4B,OAAO,CAACC,QAAQ,IAAI7B,IAAI,CAAC8B,KAAK,IAAI,IAAI,IAAI9B,IAAI,CAAC+B,GAAG,IAAI,IAAI,EAAE;IAC/D,OAAO,IAAI;EACb;EAMA,MAAMC,OAAO,GAAGJ,OAAO,CAACC,QAAQ,CAACI,UAAU,CAACjC,IAAI,CAAC;EACjD,IAAI,CAACgC,OAAO,EAAE,OAAO,IAAI;EAEzB,IAAIE,CAAC,GAAG,CAAC;EAET,IAAIC,eAAe,GAAG,CAAC;EAEvB,IAAIC,YAAY,GAAG,CAAC;EACpB,MAAMC,mBAAmB,GAAGA,CAAA,KAAM;IAChC,OACED,YAAY,GAAGpC,IAAI,CAACiB,IAAI,CAACG,MAAM,IAC/BpB,IAAI,CAACiB,IAAI,CAACmB,YAAY,CAAC,CAACN,KAAK,IAAI,IAAI,EACrC;MACAM,YAAY,EAAE;IAChB;EACF,CAAC;EACDC,mBAAmB,CAAC,CAAC;EAErB,OAAQC,CAAS,IAAK;IACpB,IAAIF,YAAY,IAAIE,CAAC,EAAE;MACrBF,YAAY,GAAGE,CAAC,GAAG,CAAC;MACpBD,mBAAmB,CAAC,CAAC;IACvB;IAEA,MAAMN,GAAG,GACPK,YAAY,KAAKpC,IAAI,CAACiB,IAAI,CAACG,MAAM,GAC7BpB,IAAI,CAAC+B,GAAG,GACR/B,IAAI,CAACiB,IAAI,CAACmB,YAAY,CAAC,CAACN,KAAK;IAEnC,IAAIS,GAAG;IACP,OACEL,CAAC,GAAGF,OAAO,CAACZ,MAAM,IAClBQ,OAAO,CAACC,QAAQ,CAACW,eAAe,CAC7BD,GAAG,GAAGX,OAAO,CAACa,OAAO,CAACT,OAAO,CAACE,CAAC,CAAC,CAAC,EAClC,GACF,CAAC,IACDK,GAAG,CAACT,KAAK,GAAGC,GAAG,EACf;MACAH,OAAO,CAACT,KAAK,CAAC,GAAG,EAAEuB,SAAS,EAAEP,eAAe,EAAE,CAAC;MAChDD,CAAC,EAAE;IACL;EACF,CAAC;AACH;AAEO,SAASS,aAAaA,CAAgB3C,IAAqB,EAAE;EAClE,IAAI,CAACI,SAAS,CAACJ,IAAI,CAACK,UAAU,CAAC;EAE/B,IAAI,CAACL,IAAI,CAAC4C,MAAM,IAAI,CAAC,IAAI,CAACC,MAAM,CAACC,cAAc,EAAE;IAAA,IAAAC,aAAA;IAG/C,MAAMC,OAAO,IAAAD,aAAA,GAAG/C,IAAI,CAACiD,GAAG,CAACC,GAAG,cAAAH,aAAA,GAAZA,aAAA,CAAchB,GAAG,qBAAjBgB,aAAA,CAAmBI,IAAI;IACvC,IAAIH,OAAO,EAAE,IAAI,CAACI,OAAO,CAACJ,OAAO,CAAC;EACpC;EAEA,IAAI,CAACK,2BAA2B,CAACrD,IAAI,CAAC;EAEtC,IAAIA,IAAI,CAACsD,QAAQ,EAAE;IACjB,IAAI,CAACnC,SAAK,GAAI,CAAC;IACf,IAAI,CAACR,KAAK,CAACX,IAAI,CAACiD,GAAG,CAAC;IACpB,IAAI,CAAC9B,SAAK,GAAI,CAAC;EACjB,CAAC,MAAM;IACL,IAAI,CAACoC,SAAS,CAACvD,IAAI,CAAC;IACpB,IAAI,CAACW,KAAK,CAACX,IAAI,CAACiD,GAAG,CAAC;EACtB;EAGA,IAAIjD,IAAI,CAACwD,QAAQ,EAAE;IACjB,IAAI,CAACrC,SAAK,GAAI,CAAC;EACjB;EACA,IAAInB,IAAI,CAACyD,QAAQ,EAAE;IACjB,IAAI,CAACtC,SAAK,GAAI,CAAC;EACjB;EAEA,IAAI,CAACR,KAAK,CAACX,IAAI,CAAC0D,cAAc,CAAC;EAC/B,IAAI1D,IAAI,CAAC2D,KAAK,EAAE;IACd,IAAI,CAACnD,KAAK,CAAC,CAAC;IACZ,IAAI,CAACW,SAAK,GAAI,CAAC;IACf,IAAI,CAACX,KAAK,CAAC,CAAC;IACZ,IAAI,CAACG,KAAK,CAACX,IAAI,CAAC2D,KAAK,CAAC;EACxB;EACA,IAAI,CAACC,SAAS,CAAC,CAAC;AAClB;AAEO,SAASC,qBAAqBA,CAEnC7D,IAA6B,EAC7B;EAAA,IAAA8D,cAAA;EACA,IAAI,CAAC1D,SAAS,CAACJ,IAAI,CAACK,UAAU,CAAC;EAI/B,MAAM2C,OAAO,IAAAc,cAAA,GAAG9D,IAAI,CAACiD,GAAG,CAACC,GAAG,cAAAY,cAAA,GAAZA,cAAA,CAAc/B,GAAG,qBAAjB+B,cAAA,CAAmBX,IAAI;EACvC,IAAIH,OAAO,EAAE,IAAI,CAACI,OAAO,CAACJ,OAAO,CAAC;EAGlC,IAAI,CAACK,2BAA2B,CAACrD,IAAI,CAAC;EAEtC,IAAI,CAACO,IAAI,CAAC,UAAU,EAAE,IAAI,CAAC;EAC3B,IAAI,CAACC,KAAK,CAAC,CAAC;EAEZ,IAAIR,IAAI,CAACsD,QAAQ,EAAE;IACjB,IAAI,CAACnC,SAAK,GAAI,CAAC;IACf,IAAI,CAACR,KAAK,CAACX,IAAI,CAACiD,GAAG,CAAC;IACpB,IAAI,CAAC9B,SAAK,GAAI,CAAC;EACjB,CAAC,MAAM;IAEL,IAAI,CAACoC,SAAS,CAACvD,IAAI,CAAC;IACpB,IAAI,CAACW,KAAK,CAACX,IAAI,CAACiD,GAAG,CAAC;EACtB;EAGA,IAAIjD,IAAI,CAACwD,QAAQ,EAAE;IACjB,IAAI,CAACrC,SAAK,GAAI,CAAC;EACjB;EACA,IAAInB,IAAI,CAACyD,QAAQ,EAAE;IACjB,IAAI,CAACtC,SAAK,GAAI,CAAC;EACjB;EAEA,IAAI,CAACR,KAAK,CAACX,IAAI,CAAC0D,cAAc,CAAC;EAC/B,IAAI1D,IAAI,CAAC2D,KAAK,EAAE;IACd,IAAI,CAACnD,KAAK,CAAC,CAAC;IACZ,IAAI,CAACW,SAAK,GAAI,CAAC;IACf,IAAI,CAACX,KAAK,CAAC,CAAC;IACZ,IAAI,CAACG,KAAK,CAACX,IAAI,CAAC2D,KAAK,CAAC;EACxB;EACA,IAAI,CAACC,SAAS,CAAC,CAAC;AAClB;AAEO,SAASG,oBAAoBA,CAElC/D,IAA4B,EAC5B;EACA,IAAI,CAACI,SAAS,CAACJ,IAAI,CAACK,UAAU,CAAC;EAC/B,IAAI,CAACgD,2BAA2B,CAACrD,IAAI,CAAC;EACtC,IAAI,CAACW,KAAK,CAACX,IAAI,CAACiD,GAAG,CAAC;EAEpB,IAAIjD,IAAI,CAACwD,QAAQ,EAAE;IACjB,IAAI,CAACrC,SAAK,GAAI,CAAC;EACjB;EACA,IAAInB,IAAI,CAACyD,QAAQ,EAAE;IACjB,IAAI,CAACtC,SAAK,GAAI,CAAC;EACjB;EACA,IAAI,CAACR,KAAK,CAACX,IAAI,CAAC0D,cAAc,CAAC;EAC/B,IAAI1D,IAAI,CAAC2D,KAAK,EAAE;IACd,IAAI,CAACnD,KAAK,CAAC,CAAC;IACZ,IAAI,CAACW,SAAK,GAAI,CAAC;IACf,IAAI,CAACX,KAAK,CAAC,CAAC;IACZ,IAAI,CAACG,KAAK,CAACX,IAAI,CAAC2D,KAAK,CAAC;EACxB;EACA,IAAI,CAACC,SAAS,CAAC,CAAC;AAClB;AAEO,SAASI,WAAWA,CAAgBhE,IAAmB,EAAE;EAC9D,IAAI,CAACiE,gBAAgB,CAACjE,IAAI,CAAC;EAC3B,IAAI,CAACQ,KAAK,CAAC,CAAC;EACZ,IAAI,CAACG,KAAK,CAACX,IAAI,CAACiB,IAAI,CAAC;AACvB;AAEO,SAASiD,kBAAkBA,CAAgBlE,IAA0B,EAAE;EAC5E,IAAI,CAACiE,gBAAgB,CAACjE,IAAI,CAAC;EAC3B,IAAI,CAACQ,KAAK,CAAC,CAAC;EACZ,IAAI,CAACG,KAAK,CAACX,IAAI,CAACiB,IAAI,CAAC;AACvB;AAEO,SAASgD,gBAAgBA,CAE9BjE,IAA8D,EAC9D;EACA,IAAI,CAACI,SAAS,CAACJ,IAAI,CAACK,UAAU,CAAC;EAE/B,IAAI,CAAC,IAAI,CAACwC,MAAM,CAACC,cAAc,EAAE;IAAA,IAAAqB,cAAA;IAG/B,MAAMnB,OAAO,IAAAmB,cAAA,GAAGnE,IAAI,CAACiD,GAAG,CAACC,GAAG,cAAAiB,cAAA,GAAZA,cAAA,CAAcpC,GAAG,qBAAjBoC,cAAA,CAAmBhB,IAAI;IACvC,IAAIH,OAAO,EAAE,IAAI,CAACI,OAAO,CAACJ,OAAO,CAAC;EACpC;EAEA,IAAI,CAACK,2BAA2B,CAACrD,IAAI,CAAC;EACtC,IAAI,CAACoE,WAAW,CAACpE,IAAI,CAAC;AACxB;AAEO,SAASqE,WAAWA,CAAgBrE,IAAmB,EAAE;EAC9D,IAAI,CAACO,IAAI,CAAC,QAAQ,CAAC;EACnB,IAAI,CAACC,KAAK,CAAC,CAAC;EACZ,IAAI,CAACW,SAAK,IAAI,CAAC;EACf,IAAInB,IAAI,CAACiB,IAAI,CAACG,MAAM,KAAK,CAAC,EAAE;IAC1B,IAAI,CAACD,SAAK,IAAI,CAAC;EACjB,CAAC,MAAM;IACL,IAAI,CAACE,OAAO,CAAC,CAAC;IACd,IAAI,CAACiD,aAAa,CAACtE,IAAI,CAACiB,IAAI,EAAE,IAAI,CAAC;IACnC,IAAI,CAACU,UAAU,CAAC3B,IAAI,CAAC;EACvB;AACF", "ignoreList": []}