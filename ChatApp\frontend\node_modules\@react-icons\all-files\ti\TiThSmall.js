// THIS FILE IS AUTO GENERATED
var GenIcon = require('../lib').GenIcon
module.exports.TiThSmall = function TiThSmall (props) {
  return GenIcon({"tag":"svg","attr":{"version":"1.2","baseProfile":"tiny","viewBox":"0 0 24 24"},"child":[{"tag":"circle","attr":{"cx":"5","cy":"19","r":"2.5"}},{"tag":"circle","attr":{"cx":"5","cy":"12","r":"2.5"}},{"tag":"circle","attr":{"cx":"5","cy":"5","r":"2.5"}},{"tag":"circle","attr":{"cx":"12","cy":"19","r":"2.5"}},{"tag":"circle","attr":{"cx":"12","cy":"12","r":"2.5"}},{"tag":"circle","attr":{"cx":"12","cy":"5","r":"2.5"}},{"tag":"circle","attr":{"cx":"19","cy":"19","r":"2.5"}},{"tag":"circle","attr":{"cx":"19","cy":"12","r":"2.5"}},{"tag":"circle","attr":{"cx":"19","cy":"5","r":"2.5"}}]})(props);
};
