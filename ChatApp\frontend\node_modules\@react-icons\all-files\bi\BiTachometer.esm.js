// THIS FILE IS AUTO GENERATED
import { GenIcon } from '../lib';
export function BiTachometer (props) {
  return GenIcon({"tag":"svg","attr":{"viewBox":"0 0 24 24"},"child":[{"tag":"path","attr":{"d":"M12,4C6.486,4,2,8.486,2,14c0,1.55,0.354,3.045,1.051,4.445C3.221,18.785,3.567,19,3.946,19h16.107 c0.379,0,0.726-0.215,0.896-0.555C21.646,17.045,22,15.55,22,14C22,8.486,17.514,4,12,4z M19.41,17H4.59 C4.198,16.042,4,15.035,4,14c0-4.411,3.589-8,8-8s8,3.589,8,8C20,15.035,19.802,16.042,19.41,17z"}},{"tag":"path","attr":{"d":"M10.939,12.939c-0.168,0.111-0.323,0.262-0.44,0.44c-0.465,0.708-0.268,1.657,0.44,2.121 c0.707,0.465,1.657,0.268,2.121-0.44l3.962-6.038c0.006-0.011,0.007-0.024,0-0.035c-0.01-0.015-0.03-0.019-0.045-0.01 L10.939,12.939z"}}]})(props);
};
