// THIS FILE IS AUTO GENERATED
var GenIcon = require('../lib').GenIcon
module.exports.SiGumtree = function SiGumtree (props) {
  return GenIcon({"tag":"svg","attr":{"role":"img","viewBox":"0 0 24 24"},"child":[{"tag":"title","attr":{},"child":[]},{"tag":"path","attr":{"d":"M18.829 6.52c-.189-.127-.315-.315-.315-.57C18.198 2.596 15.412 0 11.994 0s-6.203 2.594-6.52 5.949c0 .255-.126.442-.314.57-1.711 1.202-2.785 3.165-2.785 5.378 0 3.038 2.087 5.573 4.872 6.331.507.127.95.189 1.14.061.379-.188.76-1.832.57-2.213-.128-.189-.57-.379-1.014-.506-1.646-.443-2.848-1.961-2.848-3.732 0-1.015.379-1.9 1.014-2.6.252-.315.63-.567.948-.757.696-.38 1.14-1.142 1.14-1.964 0-.45.063-.826.189-1.201.506-1.532 1.961-2.659 3.67-2.659 1.711 0 3.166 1.127 3.672 2.659.126.375.189.751.189 1.201 0 .812.381 1.578 1.142 1.953.379.18.694.436.946.752.634.69 1.014 1.576 1.014 2.598 0 2.148-1.709 3.861-3.86 3.861-2.468 0-4.431 2.013-4.431 4.416v2.523c0 .496.064 1.006.19 1.142.315.316 1.962.316 2.278 0 .136-.136.195-.631.195-1.142v-2.583c0-.946.766-1.713 1.713-1.713 3.605 0 6.52-2.915 6.52-6.52 0-2.163-1.066-4.116-2.78-5.317l-.015.033z"}}]})(props);
};
