// THIS FILE IS AUTO GENERATED
var GenIcon = require('../lib').GenIcon
module.exports.GiSpottedBug = function GiSpottedBug (props) {
  return GenIcon({"tag":"svg","attr":{"viewBox":"0 0 512 512"},"child":[{"tag":"path","attr":{"d":"M437.507 294.138l-46.303 2.675a134.099 134.099 0 0 0-20.03-93.167l31.28-40.404-12.296-50.65-16.76 4.088 10.364 42.529-23.308 30.07A134.315 134.315 0 0 0 302.21 149.9l13.342-62.762-18.077-66.223-16.696 4.605 17.01 62.18-12.276 57.724a132.664 132.664 0 0 0-29.542-2.631 27.536 27.536 0 0 0-27.06-30.297 48.946 48.946 0 0 0-30.805-22.866 28.873 28.873 0 0 1 5.015 12.058 29.316 29.316 0 0 1-57.757 10.031 48.31 48.31 0 0 0-6.774 32.584 49.14 49.14 0 0 0 1.327 6.04 27.514 27.514 0 0 0 13.083 44.696 134.455 134.455 0 0 0-17.829 31.02l-45.106-7.356-51.232-57.434L26 172.755l55.244 61.997 48.374 7.895a133.096 133.096 0 0 0-.528 65.501q1.305 5.393 3.02 10.495l-28.927 3.128-48.612-38.182-10.656 13.569 54.145 42.517 40.835-4.422a133.344 133.344 0 0 0 46.38 52.753l-16.396 50.693-3.947 51.06 17.257 1.326 3.786-49.021 14.658-45.3a133.743 133.743 0 0 0 186.646-82.478l46.551-2.697 44.33 22.618 7.841-15.37zm-152.283-104.04c8.628-12.65 27.158-15.1 41.288-5.392 14.129 9.707 18.573 27.795 9.9 40.446-8.67 12.652-27.158 15.1-41.287 5.393-14.13-9.707-18.573-27.784-9.901-40.446zm-77.312-2.512c12.695-8.165 28.776-5.77 35.916 5.328 7.14 11.098 2.632 26.727-10.063 34.892-12.695 8.164-28.776 5.77-35.916-5.328-7.14-11.099-2.643-26.727 10.063-34.892zm-5.792 179.172c-3.98 4.93-12.414 4.735-18.832-.442-6.417-5.177-8.413-13.374-4.433-18.336 3.98-4.961 12.415-4.735 18.832.442 6.418 5.177 8.402 13.407 4.433 18.336zm-.928-42.194c-16.178 3.937-32.745-7.237-37.06-24.958-4.314-17.72 5.275-35.28 21.42-39.217 16.147-3.936 32.747 7.238 37.06 24.958 4.315 17.721-5.273 35.291-21.42 39.228zm42.82 54.306c-17.872-3.645-29.812-19.09-26.663-34.514 3.15-15.423 20.18-24.958 38.052-21.312 17.872 3.645 29.812 19.09 26.663 34.514-3.15 15.423-20.18 24.958-38.052 21.323zm54.608 16.179c-8.068 1.726-15.65-1.693-16.923-7.658-1.273-5.964 4.228-12.188 12.296-13.913 8.067-1.726 15.65 1.693 16.922 7.657 1.273 5.965-4.25 12.231-12.295 13.957zm59.817-69.18c-9.707 14.119-27.827 18.52-40.446 9.826-12.62-8.693-14.992-27.18-5.274-41.288 9.718-14.107 27.827-18.519 40.446-9.825 12.62 8.693 14.98 27.18 5.274 41.287zm6.062-55.805c-6.072.734-11.79-5.317-12.781-13.504-.992-8.186 3.128-15.434 9.2-16.178 6.072-.744 11.789 5.317 12.78 13.504.993 8.186-3.127 15.412-9.2 16.178z"}}]})(props);
};
