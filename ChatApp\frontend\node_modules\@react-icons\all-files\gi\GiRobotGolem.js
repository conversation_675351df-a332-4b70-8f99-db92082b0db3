// THIS FILE IS AUTO GENERATED
var GenIcon = require('../lib').GenIcon
module.exports.GiRobotGolem = function GiRobotGolem (props) {
  return GenIcon({"tag":"svg","attr":{"viewBox":"0 0 512 512"},"child":[{"tag":"path","attr":{"d":"M256.688 18.406c-29.86 0-54.44 21.716-58.875 50.282H315.53c-4.428-28.566-28.983-50.282-58.842-50.282zm-104.313 9.282L81.75 99.094c26.37 25.22 50.43 39.66 69.438 45.53 20.595 6.364 34.156 3.076 41.53-4.468 2.482-2.538 4.475-5.84 5.813-9.875-12.5-13.88-20.124-32.236-20.124-52.31 0-5.28.527-10.45 1.53-15.44-7.117-10.973-16.213-22.668-27.56-34.843zm208.594 0c-11.35 12.174-20.452 23.87-27.564 34.843 1.004 4.99 1.53 10.16 1.53 15.44.002 20.074-7.63 38.43-20.123 52.31 1.334 4.036 3.33 7.338 5.812 9.876 7.374 7.544 20.935 10.832 41.53 4.47 19.01-5.873 43.068-20.313 69.44-45.532l-70.626-71.406zM197.843 87.374c4.008 25.464 24.02 45.487 49.5 49.47v-49.47h-49.5zm68.187 0v49.47c25.476-3.983 45.466-24.006 49.47-49.47h-49.47zm-52.655 55.72c-1.93 3.73-4.352 7.127-7.28 10.124-7.01 7.17-16.34 11.444-27.157 12.843 17.245 30.84 47.478 45.278 77.718 45.187 30.135-.09 60.314-14.62 77.594-45.188-10.75-1.42-20.024-5.706-27-12.843-2.926-2.994-5.323-6.4-7.25-10.126-12.413 8.293-27.313 13.156-43.313 13.156-16 0-30.893-4.863-43.312-13.156zm-105.72.905c-11.884 8.09-22.142 17.595-30.03 28.47 5.18 1.992 10.066 5.204 14.47 9.374.287.273.557.562.843.844 7.992-10.844 19.192-20.188 33-28.188-5.933-2.94-12.04-6.43-18.282-10.5zm297.814.156c-6.274 4.077-12.418 7.563-18.376 10.5 13.946 8.04 25.26 17.42 33.312 28.28.26-.258.518-.527.78-.78 4.39-4.208 9.27-7.476 14.44-9.53-7.928-10.863-18.222-20.373-30.157-28.47zM65.405 188.844c-4.14.03-8.71 1.797-13.937 6.812-5.23 5.016-10.76 13.247-15.595 24.78-9.03 21.54-15.567 54.52-16.406 98.19h91.75c-.836-44.038-7.38-77.138-16.407-98.626-4.833-11.502-10.363-19.67-15.563-24.594-5.2-4.924-9.704-6.592-13.844-6.562zm382.656 0c-4.14.03-8.71 1.797-13.937 6.812-5.228 5.016-10.758 13.247-15.594 24.78-9.03 21.54-15.566 54.52-16.405 98.19h91.75c-.835-44.038-7.38-77.138-16.406-98.626-4.833-11.502-10.364-19.67-15.564-24.594-5.2-4.924-9.703-6.592-13.844-6.562zm-164.5 37.53c-8.798 2.334-17.828 3.536-26.875 3.564-9.09.027-18.16-1.13-27-3.438-5.288 5.608-8.437 12.862-8.437 20.656 0 17.25 15.35 31.844 35.438 31.844 20.087 0 35.437-14.593 35.437-31.844 0-7.854-3.2-15.155-8.563-20.78zm-76 41.94c-20.808 10.54-39.378 28.066-52.937 52.248 5.276 2.285 10.287 5.71 15 10.188 12.49-23.23 29.974-38.884 49.25-47.5-4.683-4.264-8.518-9.31-11.313-14.938zm98.157.248c-2.83 5.618-6.727 10.63-11.44 14.875 19.213 8.67 36.67 24.287 49.19 47.282.062-.06.123-.13.186-.19 4.588-4.308 9.586-7.692 14.844-9.967-13.558-23.972-32.056-41.42-52.78-52zm-166.595 67.375c-5.454-.038-11.282 2.203-17.688 8.22-6.405 6.016-13.017 15.817-18.812 29.5-7.377 17.416-13.346 41.16-16.72 70.937 8.495-4.2 17.876-6.245 27.19-6.22 14.79.043 29.66 5.315 40.968 16.032 9.487 8.993 16.182 21.848 18.093 37.563h23.25c-.856-52.36-8.71-91.89-19.656-117.783-11.6-27.438-25.718-38.173-36.625-38.25zm234.97 0c-5.455-.038-11.252 2.203-17.658 8.22-6.405 6.016-13.048 15.817-18.843 29.5-10.943 25.835-18.774 65.513-19.625 118.312h23.217c1.898-15.826 8.58-28.72 18.094-37.72 11.325-10.712 26.243-15.917 41.033-15.875 9.298.026 18.658 2.098 27.125 6.313-3.368-29.494-9.328-53.09-16.688-70.5-11.6-27.44-25.75-38.174-36.656-38.25zm-336.126 1.375c-1.03 3.895-1.02 8.08.186 12.22 3.82 13.102 19.167 21.597 34.532 17.812 15.24-3.754 23.346-17.03 19.75-30.03l-54.47-.002zm382.655 0c-1.028 3.895-1.02 8.08.188 12.22 3.818 13.102 19.166 21.597 34.53 17.812 15.24-3.754 23.347-17.03 19.75-30.03l-54.468-.002zM113.03 457.063c-10.365-.03-20.612 3.615-28.155 10.75-5.935 5.615-10.374 13.43-12.03 24.157h80.436c-1.664-10.603-6.128-18.377-12.06-24-7.56-7.167-17.823-10.878-28.19-10.908zm287.22 0c-10.366-.03-20.582 3.615-28.125 10.75-5.935 5.615-10.405 13.43-12.063 24.157H440.5c-1.665-10.603-6.13-18.377-12.063-24-7.56-7.167-17.82-10.878-28.187-10.908z"}}]})(props);
};
