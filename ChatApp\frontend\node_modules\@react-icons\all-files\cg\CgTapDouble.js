// THIS FILE IS AUTO GENERATED
var GenIcon = require('../lib').GenIcon
module.exports.CgTapDouble = function CgTapDouble (props) {
  return GenIcon({"tag":"svg","attr":{"viewBox":"0 0 24 24","fill":"none"},"child":[{"tag":"path","attr":{"fillRule":"evenodd","clipRule":"evenodd","d":"M14.9236 18V14C14.9236 12.3431 13.5804 11 11.9236 11C10.2667 11 8.92355 12.3431 8.92355 14V18C8.92355 19.6569 10.2667 21 11.9236 21C13.5804 21 14.9236 19.6569 14.9236 18ZM11.9236 9C9.16213 9 6.92355 11.2386 6.92355 14V18C6.92355 20.7614 9.16213 23 11.9236 23C14.685 23 16.9236 20.7614 16.9236 18V14C16.9236 11.2386 14.685 9 11.9236 9Z","fill":"currentColor"}},{"tag":"path","attr":{"d":"M10.9236 14C10.9236 13.4477 11.3713 13 11.9236 13C12.4758 13 12.9236 13.4477 12.9236 14V17H10.9236V14Z","fill":"currentColor"}},{"tag":"path","attr":{"d":"M11.9235 1C14.7023 1 17.2164 2.13339 19.0286 3.96311L17.6144 5.37735C16.1641 3.90955 14.15 3 11.9235 3C9.77436 3 7.82311 3.84749 6.38577 5.22651L4.97131 3.81205C6.7708 2.07122 9.22207 1 11.9235 1Z","fill":"currentColor"}},{"tag":"path","attr":{"fillRule":"evenodd","clipRule":"evenodd","d":"M11.9233 5C13.5809 5 15.1038 5.5761 16.3029 6.53898L14.8768 7.9651C14.0493 7.35833 13.0281 7 11.9233 7C10.848 7 9.85204 7.33943 9.03662 7.91699L7.60779 6.48816C8.79686 5.55586 10.2952 5 11.9233 5Z","fill":"currentColor"}}]})(props);
};
