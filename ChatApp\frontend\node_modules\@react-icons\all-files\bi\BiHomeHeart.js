// THIS FILE IS AUTO GENERATED
var GenIcon = require('../lib').GenIcon
module.exports.BiHomeHeart = function BiHomeHeart (props) {
  return GenIcon({"tag":"svg","attr":{"viewBox":"0 0 24 24"},"child":[{"tag":"path","attr":{"d":"M12.223,11.641L12,11.861l-0.224-0.22c-0.87-0.855-2.25-0.855-3.125,0c-0.871,0.86-0.871,2.215,0,3.07L12,18l3.349-3.289 c0.871-0.855,0.871-2.21,0-3.07C14.474,10.786,13.094,10.786,12.223,11.641z"}},{"tag":"path","attr":{"d":"M21.707,11.293l-9-9c-0.391-0.391-1.023-0.391-1.414,0l-9,9c-0.286,0.286-0.372,0.716-0.217,1.09 C2.231,12.756,2.596,13,3,13h1v2v5c0,1.103,0.897,2,2,2h12c1.103,0,2-0.897,2-2v-5v-2h1c0.404,0,0.77-0.244,0.924-0.617 C22.079,12.009,21.993,11.579,21.707,11.293z M18.001,20H6v-5v-3v-1.585l6-6l6,6V15l0,0L18.001,20z"}}]})(props);
};
