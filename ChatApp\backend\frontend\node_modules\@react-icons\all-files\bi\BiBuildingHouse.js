// THIS FILE IS AUTO GENERATED
var GenIcon = require('../lib').GenIcon
module.exports.BiBuildingHouse = function BiBuildingHouse (props) {
  return GenIcon({"tag":"svg","attr":{"viewBox":"0 0 24 24"},"child":[{"tag":"path","attr":{"d":"M19,2H9C7.897,2,7,2.897,7,4v5.586l-4.707,4.707c-0.286,0.286-0.372,0.716-0.217,1.09S2.596,16,3,16v1v4 c0,0.553,0.448,1,1,1h8h8c0.553,0,1-0.447,1-1V4C21,2.897,20.103,2,19,2z M11,20H5v-3v-2v-0.586l3-3l3,3V15v3V20z M19,20h-6v-2v-2 c0.404,0,0.77-0.243,0.924-0.617c0.155-0.374,0.069-0.804-0.217-1.09L9,9.586V4h10V20z"}},{"tag":"path","attr":{"d":"M11 6H13V8H11zM15 6H17V8H15zM15 10.031H17V12H15zM15 14H17V16H15zM7 15H9V17H7z"}}]})(props);
};
