// THIS FILE IS AUTO GENERATED
var GenIcon = require('../lib').GenIcon
module.exports.BiBookAdd = function BiBookAdd (props) {
  return GenIcon({"tag":"svg","attr":{"viewBox":"0 0 24 24"},"child":[{"tag":"path","attr":{"d":"M3,8v6v3v2c0,2.201,1.794,3,3,3h15v-2H6.012C5.55,19.988,5,19.806,5,19c0-0.101,0.009-0.191,0.024-0.273 C5.136,18.151,5.608,18.01,6.012,18H19h1h1v-1v-2V4c0-1.103-0.897-2-2-2H6C4.794,2,3,2.799,3,5V8z M6,4h13v11v1H5v-2V8V5 C5,4.194,5.55,4.012,6,4z"}},{"tag":"path","attr":{"d":"M11 14L13 14 13 11 16 11 16 9 13 9 13 6 11 6 11 9 8 9 8 11 11 11z"}}]})(props);
};
