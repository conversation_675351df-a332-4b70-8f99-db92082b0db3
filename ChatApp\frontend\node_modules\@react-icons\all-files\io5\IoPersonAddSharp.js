// THIS FILE IS AUTO GENERATED
var GenIcon = require('../lib').GenIcon
module.exports.IoPersonAddSharp = function IoPersonAddSharp (props) {
  return GenIcon({"tag":"svg","attr":{"viewBox":"0 0 512 512"},"child":[{"tag":"path","attr":{"d":"M106 304v-54h54v-36h-54v-54H70v54H16v36h54v54h36z"}},{"tag":"circle","attr":{"cx":"288","cy":"144","r":"112"}},{"tag":"path","attr":{"d":"M288 288c-69.42 0-208 42.88-208 128v64h416v-64c0-85.12-138.58-128-208-128z"}}]})(props);
};
