export declare const eachMonthOfIntervalWithOptions: import("./types.js").FPFn2<
  import("../eachMonthOfInterval.js").EachMonthOfIntervalResult<
    import("../fp.js").Interval<
      import("../fp.js").DateArg<Date>,
      import("../fp.js").DateArg<Date>
    >,
    | import("../eachMonthOfInterval.js").EachMonthOfIntervalOptions<Date>
    | undefined
  >,
  | import("../eachMonthOfInterval.js").EachMonthOfIntervalOptions<Date>
  | undefined,
  import("../fp.js").Interval<
    import("../fp.js").DateArg<Date>,
    import("../fp.js").DateArg<Date>
  >
>;
