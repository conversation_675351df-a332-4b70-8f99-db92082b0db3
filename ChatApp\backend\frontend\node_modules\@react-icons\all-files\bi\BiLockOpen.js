// THIS FILE IS AUTO GENERATED
var GenIcon = require('../lib').GenIcon
module.exports.BiLockOpen = function BiLockOpen (props) {
  return GenIcon({"tag":"svg","attr":{"viewBox":"0 0 24 24"},"child":[{"tag":"path","attr":{"d":"M12,4c1.654,0,3,1.346,3,3h2c0-2.757-2.243-5-5-5S7,4.243,7,7v2H6c-1.103,0-2,0.897-2,2v9c0,1.103,0.897,2,2,2h12 c1.103,0,2-0.897,2-2v-9c0-1.103-0.897-2-2-2H9V7C9,5.346,10.346,4,12,4z M18.002,20H13v-2.278c0.595-0.347,1-0.985,1-1.722 c0-1.103-0.897-2-2-2s-2,0.897-2,2c0,0.736,0.405,1.375,1,1.722V20H6v-9h12L18.002,20z"}}]})(props);
};
