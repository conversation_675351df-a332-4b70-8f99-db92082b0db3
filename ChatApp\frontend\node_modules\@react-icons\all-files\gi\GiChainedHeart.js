// THIS FILE IS AUTO GENERATED
var GenIcon = require('../lib').GenIcon
module.exports.GiChainedHeart = function GiChainedHeart (props) {
  return GenIcon({"tag":"svg","attr":{"viewBox":"0 0 512 512"},"child":[{"tag":"path","attr":{"d":"M257.08 11.29l-9.232 1.437-62.868 9.765 3.586 23.08C175.62 49.84 163.09 55.3 151.15 61.875l-14.51-18.03-7.28 5.86-49.56 39.89 14.163 17.594C84.847 117.31 76.6 128.367 69.39 140.3l-20.78-8.046-3.374 8.715-22.976 59.325 20.26 7.846c-2.96 13.812-4.544 27.692-4.805 41.47l-21.02 3.267 11.2 72.1 20.634-3.206c4.347 13.326 9.955 26.213 16.74 38.474l-16.165 13.012 45.75 56.84 15.858-12.764c10.425 9.35 21.846 17.776 34.183 25.104l-7.27 18.773 68.042 26.347 7.266-18.76c14.02 2.89 28.103 4.36 42.074 4.482l3.115 20.05 72.1-11.2-3.168-20.386c13.326-4.498 26.2-10.26 38.433-17.205l13.075 16.24 56.84-45.747-13.315-16.543c9.093-10.365 17.275-21.698 24.394-33.906l20.11 7.787 26.35-68.038-20.63-7.988c2.71-13.685 4.057-27.422 4.133-41.05l22.29-3.462-1.434-9.234-9.765-62.866-22.85 3.55c-4.413-12.947-10.017-25.466-16.746-37.378l18.103-14.57-5.86-7.28-39.89-49.56-18.28 14.715c-10.058-8.863-21.032-16.864-32.835-23.868l8.562-22.112-8.713-3.375L309.7 16.88 301.136 39c-13.31-2.74-26.68-4.21-39.95-4.45-.166-.004-.33-.002-.494-.005l-3.61-23.252zm-15.598 21.337l3.108 20.008-35.164 5.463-3.11-20.006 35.166-5.465zm78.897 8.43l33.184 12.85-7.312 18.882-33.184-12.853 7.31-18.88zM263.6 53.27c10.2.34 20.456 1.458 30.687 3.406l-5.4 13.94 68.042 26.35 5.402-13.952c8.848 5.43 17.16 11.487 24.91 18.08l-11.842 9.53 45.75 56.84 12.098-9.735c4.986 9.14 9.272 18.65 12.787 28.45l-15.443 2.398 11.2 72.1 15.98-2.483c-.226 10.394-1.278 20.852-3.174 31.29l-15.395-5.964-3.373 8.714-22.977 59.327 15.975 6.188c-5.567 9.24-11.81 17.91-18.633 25.97l-10.926-13.575-7.278 5.86-49.56 39.89 11.24 13.964c-9.47 5.195-19.338 9.627-29.514 13.24l-2.78-17.908-9.235 1.433-62.866 9.766 2.828 18.202c-10.74-.272-21.548-1.413-32.328-3.47l6.7-17.3-8.713-3.375-59.327-22.974-6.695 17.29c-9.38-5.763-18.16-12.224-26.3-19.286l14.302-11.512-5.86-7.28-39.888-49.56-14.02 11.285c-5.058-9.483-9.343-19.362-12.826-29.53l17.688-2.75-1.436-9.232-9.765-62.867-17.182 2.67c.385-10.546 1.607-21.15 3.715-31.723l15.826 6.128 26.35-68.04-15.258-5.91c5.624-8.986 11.9-17.41 18.726-25.24l10.118 12.57 56.84-45.75-9.778-12.147c9.155-4.854 18.672-8.996 28.465-12.387l2.366 15.234 72.1-11.202-2.324-14.96zM133.8 70.124l12.694 15.772-27.723 22.312-12.694-15.77L133.8 70.122zm283.653 30.648l22.313 27.722-15.774 12.696-22.312-27.72 15.773-12.697zm-84.244 42c-1.918-.015-3.84.045-5.76.187-23.033 1.7-45.933 15.003-61.29 44.006l-7.906 14.933-8.445-14.636c-20.227-35.054-56.5-47.824-87.093-41.81-30.592 6.012-55.328 29.383-53.947 72.85 1.278 40.24 29.05 67.447 63.59 97.54 30.045 26.176 64.672 53.784 85.763 93.478 22.093-39.507 57.856-65.95 88.133-91.453 17.386-14.644 33.017-28.982 43.86-44.736 10.846-15.755 17.13-32.66 16.456-54.243-1.262-40.46-26.58-66.478-56.316-73.916-5.576-1.394-11.296-2.148-17.047-2.2zM59.286 156.433l18.88 7.31-12.85 33.186-18.88-7.31 12.85-33.186zm412.65 44.636l5.463 35.164-20.01 3.11-5.462-35.164 20.01-3.11zm-413.9 64.3l5.465 35.165-20.006 3.108-5.465-35.163 20.007-3.11zm391.846 42.33l18.88 7.312-12.85 33.185-18.88-7.312 12.85-33.186zM91.156 363.4l22.313 27.723-15.775 12.695-22.312-27.722L91.156 363.4zm305.274 33.02l12.695 15.77-27.723 22.316-12.695-15.774 27.723-22.312zm-227.315 35.23l33.186 12.852-7.31 18.88-33.185-12.85 7.31-18.882zm136.662 14.873l3.11 20.01-35.164 5.463-3.11-20.008 35.164-5.465z"}}]})(props);
};
