// THIS FILE IS AUTO GENERATED
var GenIcon = require('../lib').GenIcon
module.exports.GiSaddle = function GiSaddle (props) {
  return GenIcon({"tag":"svg","attr":{"viewBox":"0 0 512 512"},"child":[{"tag":"path","attr":{"d":"M441.65 24.393a9.077 9.077 0 0 0-1.452.08c-4.125.547-9.426 3.87-11.367 11.46-5.247 20.526-12.76 40.182-17.077 50.79.086.108.178.21.264.318 6.13 7.756 11.292 17.36 15.593 27.788.75 1.816 1.46 3.66 2.157 5.518 5.272-.53 10.743-1.793 16.45-3.975 5.046-13.438 7.054-23.478 7.08-35.225.025-12.816-2.37-28.172-6.112-51.424-.447-2.777-1.324-3.7-2.34-4.394-.763-.52-1.835-.894-3.194-.933zM56.648 30.627c-4.033.01-7.61.662-9.5 1.457-.97.408-1.215.48-1.326.486-.508 14.077 3.406 30.818 10.277 44.497 6.895 13.727 16.732 23.946 25.94 27.115 21.892 7.53 54.657 9.57 90.7-15.656-18.006-6.55-32.917-9.786-46.672-14.436-17.293-5.846-32.997-15.124-46.416-35.773-1.404-2.16-11.79-7.233-21.25-7.653-.59-.026-1.176-.038-1.753-.037zM286.503 86.06c-4.53.004-9.036.025-13.514.065-24.478.218-48.16.99-71.243 2.25l-5.096 4.39a169.545 169.545 0 0 1-8.145 6.59c68.666 4.997 135.053 6.64 210.28.03-.297-.395-.593-.807-.89-1.182-4.478-5.666-8.872-8.65-11.84-9.428-34.768-1.863-67.832-2.738-99.552-2.714zm-123.61 29.007c-9.457 4.496-18.745 7.532-27.705 9.42 6.326 8.607 13.46 17.135 21.297 24.94 20.7 20.617 45.453 35.555 71.22 35.358L422.29 169.97c-.812-14.37-4.874-32.65-11.32-48.277a135.863 135.863 0 0 0-2.258-5.142c-88.857 8.45-165.386 5.506-245.848-1.128l.03-.355zm-84.03 7.015l-24.727.656c-.194.28-.545.824-1.055 2.14-1.08 2.79-2.008 7.784-2.23 13.94-.443 12.314 1.66 29.28 6.026 46.006 4.367 16.728 11.03 33.32 18.975 45.23 7.814 11.716 16.262 18.076 24.17 18.694l10.555-.96-3.46-32.505c-14.402-1.814-26.805-12.138-26.805-26.59 0-15.955 15.114-26.88 31.347-26.88 16.234 0 31.348 10.925 31.348 26.88 0 11.39-7.71 20.214-18.027 24.33l3.525 33.135 94.957-8.633c-20.206-3.435-38.645-21.4-48.62-50.564-11.286-6.858-21.688-15.45-31.056-24.778-11.12-11.074-20.865-23.257-29.04-35.244-13.365.387-25.55-1.62-35.882-4.856zm372.52 11.656a81.027 81.027 0 0 1-16.054 4.024c3.787 14.243 5.774 28.595 5.237 40.92l-.347 7.976-31.387 2.39a476.762 476.762 0 0 1 6.043 31.077l32.873-2.988c18.517-18.74 21.183-37.714 16.378-56.412-2.415-9.398-6.998-18.61-12.746-26.987zm-339.726 46.075c-8.45 0-13.347 5.084-13.347 8.88 0 3.793 4.896 8.875 13.347 8.875 8.452 0 13.348-5.082 13.348-8.876 0-3.795-4.896-8.88-13.348-8.88zm278.887 10.627l-95.22 7.25c26.347 58.248 31.033 115.17 23.38 170.486 22.613 2.476 44.69 3.89 60.373.932 8.888-1.678 15.438-4.64 19.492-8.598 4.054-3.958 6.427-8.995 6.376-18.035-.278-49.916-2.173-100.274-14.402-152.035zm-190.56 8.082c8.255 13.282 18.5 20.036 26.857 21.33 6.08.94 11.3-.463 16.108-4.647 3.426-2.98 6.64-7.613 8.975-14.21l-23.6 1.796-.297.003c-9.715.095-19.078-1.46-28.04-4.27zm76.203.625l-5.112.388c-2.987 12.56-8.576 22.517-16.31 29.248a37.803 37.803 0 0 1-11.782 6.97l46.49-4.228c-3.662-10.72-8.076-21.508-13.286-32.378zM130.66 264.04l-17.927 1.628c4.787 61.848 3.29 124.66-6.496 188.85l17.793 2.713c10.02-65.73 11.533-130.037 6.63-193.19zm271.593 116.155l-10.367 3.98c-15.544 5.968-36.597 5.06-52.77 3.743l-12.793-1.043 3.38 12.383 8.24 30.184c-3.815 8.488-6.973 15.055-8.16 22.76-1.275 8.288.454 16.908 4.99 27.24 1.728 3.937 4.332 4.995 6.076 5.748 1.742.752 3.262 1.103 4.89 1.404 3.253.6 6.896.86 11.095.967 8.397.216 18.84-.275 29.324-1.31 10.482-1.035 20.903-2.592 29.34-4.723 4.22-1.065 7.92-2.224 11.247-3.87 3.33-1.644 7.684-3.568 8.9-10.155 2.61-14.132-6.05-25.697-13.43-34.572-5.61-6.748-11.366-12.007-13.825-14.16l-6.14-38.575zM387.83 404.11l1.8 11.306-35.613 4.56-3.614-13.24c11.506.588 24.607.396 37.428-2.627zm10.566 29.925a99.39 99.39 0 0 1 9.982 10.4c5.098 6.133 8.577 13.233 9.316 17.446-1.512.652-3.593 1.435-6.6 2.194-6.995 1.766-16.85 3.29-26.704 4.264-9.855.973-19.81 1.416-27.092 1.23-3.22-.084-5.614-.338-7.33-.597-2.53-6.463-2.982-10.203-2.393-14.03.553-3.59 2.7-8.704 5.653-15.42l45.168-5.485z"}}]})(props);
};
