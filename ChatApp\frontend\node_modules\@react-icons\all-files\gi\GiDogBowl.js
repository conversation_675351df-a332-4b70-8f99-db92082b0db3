// THIS FILE IS AUTO GENERATED
var GenIcon = require('../lib').GenIcon
module.exports.GiDogBowl = function GiDogBowl (props) {
  return GenIcon({"tag":"svg","attr":{"viewBox":"0 0 512 512"},"child":[{"tag":"path","attr":{"d":"M466.514 72.842c-21.236-.326-48.863 15.524-76.766 35.336-55.475 15.033-197.205 13.795-254.367-.078-34.336-8.255-51.407-32.056-68.238-32.02-13.969.133-30.57 6.547-34.647 19.895-1.327 12.811 2.338 17.808 9.041 26.656-6.514 10.34-13.382 25.647-10.557 36.66 2.076 7.47 7.417 12.889 16.1 16.05 17.366 6.325 64.03-5.582 75.264-23.777l275.486-6.63c26.557 27.456 65.8 25.255 81.524 15.173 7.861-5.04 12.504-11.367 12.746-17.171.52-12.537-6.116-15.97-14.678-23.442 7.578-6.805 22.605-19.688 17.715-28.678-6.81-12.52-16.71-17.792-28.623-17.974zM256 274c-52.468 0-99.992 4.864-133.775 12.518-16.892 3.827-30.374 8.424-38.887 12.984-4.256 2.28-7.221 4.562-8.758 6.264-1.537 1.701-1.58 2.415-1.58 2.734 0 .32.043 1.033 1.58 2.734 1.537 1.702 4.502 3.984 8.758 6.264 8.513 4.56 21.995 9.157 38.887 12.984 4.822 1.093 9.953 2.121 15.31 3.094A120 16 0 0 1 256 320a120 16 0 0 1 118.516 13.568c5.338-.97 10.452-1.996 15.26-3.086 16.89-3.827 30.373-8.424 38.886-12.984 4.256-2.28 7.221-4.562 8.758-6.264 1.537-1.701 1.58-2.415 1.58-2.734 0-.32-.043-1.033-1.58-2.734-1.537-1.702-4.502-3.984-8.758-6.264-8.513-4.56-21.995-9.157-38.887-12.984C355.992 278.864 308.468 274 256 274zM65.021 326.912l-40.628 108.34-.038.09c-1.183 2.959-1.028 4.345.141 6.65 1.17 2.306 4.07 5.465 9.096 8.672 10.05 6.415 27.862 12.659 50.32 17.541C128.827 477.97 192.47 483 256 483c63.531 0 127.173-5.03 172.088-14.795 22.457-4.882 40.27-11.126 50.32-17.54 5.025-3.208 7.927-6.367 9.096-8.673 1.17-2.305 1.324-3.691.14-6.65l-.037-.09-40.627-108.34c-2.849 2.374-6.126 4.476-9.818 6.453-10.919 5.849-25.554 10.627-43.408 14.672C358.047 356.127 309.57 361 256 361c-53.57 0-102.047-4.873-137.754-12.963-17.854-4.045-32.49-8.823-43.408-14.672-3.691-1.977-6.968-4.08-9.817-6.453z"}}]})(props);
};
