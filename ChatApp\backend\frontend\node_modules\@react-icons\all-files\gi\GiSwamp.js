// THIS FILE IS AUTO GENERATED
var GenIcon = require('../lib').GenIcon
module.exports.GiSwamp = function GiSwamp (props) {
  return GenIcon({"tag":"svg","attr":{"viewBox":"0 0 512 512"},"child":[{"tag":"path","attr":{"d":"M199.188 19.97a9.5 9.5 0 0 0-8.407 4.843c-3.687 6.318-7.287 12.708-10.843 19.156-6.34-1.118-12.595-.258-16.406 3.53-24.12 23.98-59.702 101.133-45.31 111.688 1.968 1.444 4.086 2.64 6.31 3.656-16.64 42.836-30.184 86.292-40.124 128.562-1.928-65.01-14.337-127.164-62.22-162.937 44.087 58.265 48.88 155.865 41.877 236.405-11.69.81-23.34 1.66-34.97 2.53l1.407 18.94c10.527-.79 21.09-1.545 31.656-2.283-5.404 47.895-14.473 87.508-20.718 105.47l28.28-7.782 19.844 3.906c3.195-33.745 7.683-68.574 16.47-104.437 104.756-6.35 212.06-8.943 325.124-.814 9.21 20.087 7.668 38.25 2.563 64.156-.69-30.596-32.682-59.164-127.25-57.718-37.285.583-99.973 24.92-93.345 61.594 10.04 55.48 93.935 63.74 164.875 37.75l-32.78-43.72 76.467 37.75c7.045-10.18 11.56-21.598 12-32.843 14.556 1.83 29.126 3.61 43.625 5.875 20.6-36.8 25.25-154.36-88-314.47 39.61 88.105 71.88 190.382 63.157 224.22-2.253-.186-4.504-.385-6.75-.563-28.424-38.034-94.285-80.812-127.814-97.562C370.742 309.23 401.776 337.56 419 360.53c-38.743-2.512-76.81-3.813-114.313-4.155-66.03-.6-130.31 1.732-193.5 5.47 14.246-49.464 37.544-100.834 77.75-153.97-51.342 38.358-77.508 85.502-95.406 134.72 9.764-55.987 26.784-116.065 49.69-174.908 1.743.234 3.47.45 5.186.625 23.065 2.38 49.024-68.143 52.688-105.343.375-3.812-1.312-7.414-4.188-10.44 3.37-6.11 6.79-12.172 10.28-18.155a9.5 9.5 0 0 0-8-14.406z"}}]})(props);
};
