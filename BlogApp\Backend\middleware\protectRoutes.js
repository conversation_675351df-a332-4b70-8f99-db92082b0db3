import jwt from 'jsonwebtoken'

 export const protectRoutes= (req,res,next)=>{
 const token =   req.cookies.token
 if(!token){
    return res.status(401).json({message:"token not found or authorized"})
 }

   

   try {
    const decoded=  jwt.verify(token, process.env.JWT_SECRET)
    req.user=decoded
    console.log(decoded);
    next()

    
   } catch (error) {
    res.clearCookie("token")
    console.log(error);
      return res.status(401).json({message:"token not found or authorized"})
    
   }





}