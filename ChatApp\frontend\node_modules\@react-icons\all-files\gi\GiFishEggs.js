// THIS FILE IS AUTO GENERATED
var GenIcon = require('../lib').GenIcon
module.exports.GiFishEggs = function GiFishEggs (props) {
  return GenIcon({"tag":"svg","attr":{"viewBox":"0 0 512 512"},"child":[{"tag":"path","attr":{"d":"M335.2 34.44c-71.6.52-133.7 47.59-189.8 102.96-40-16.4-80.97-29.3-127.35-25.4 40.27 43.5 51.79 90.1 30.33 140.3 40.85-20.2 77.32-45.5 107.52-78.7 136.7 34 274.5 45 338-65.6-58-53.25-110.6-73.9-158.7-73.56zM376.7 64a16 16 0 0 1 16 16 16 16 0 0 1-16 16 16 16 0 0 1-16-16 16 16 0 0 1 16-16zm-88 166.3c-22.5 0-41 18.5-41 41 0 4.4.7 8.7 2 12.7-5.9-3.4-12.8-5.4-20.1-5.4-22.5 0-41 18.5-41 41 0 1.4.1 2.9.2 4.2-2.9-.6-6-1-9.1-1-16.5 0-30.9 10-37.4 24.2-6.8-5-15.2-8-24.2-8-22.52 0-41.02 18.5-41.02 41 0 10.2 3.78 19.5 9.99 26.7-1.82-.3-3.68-.4-5.57-.4-22.5 0-41 18.5-41 41s18.5 41 41 41c20.1 0 36.9-14.7 40.4-33.9 6.5 14 20.7 23.8 37.1 23.8 22.5 0 41-18.5 41-41 0-13.2-6.4-25.1-16.3-32.6 15.7-1.6 29-12.1 34.3-26.4.8 15.5 10.3 28.9 23.7 35.2-13.3 6.8-22.5 20.7-22.5 36.5 0 22.5 18.5 41 41 41 20.1 0 37-14.7 40.4-33.9 7.3 11.1 19.9 18.4 34.1 18.4 14.3 0 26.9-7.5 34.3-18.6 6.2 14.7 20.9 25.2 37.8 25.2 22.5 0 41-18.5 41-41 0-20.2-14.9-37.1-34.2-40.4 4.2-6.4 6.7-14.1 6.7-22.3 0-22.5-18.5-41-41-41-6.7 0-13 1.6-18.6 4.5.2-1.9.4-3.8.4-5.8 0-21.9-17.5-39.9-39.1-40.9 4.8-6.8 7.7-15 7.7-23.8 0-22.5-18.5-41-41-41zm0 18c12.8 0 23 10.2 23 23s-10.2 23-23 23-23-10.2-23-23c0-.5 0-1.1.1-1.6a16 16 0 0 0 6.9 1.6 16 16 0 0 0 16-16 16 16 0 0 0-1.6-6.9c.5-.1 1.1-.1 1.6-.1zm-59.1 48.3c12.8 0 23 10.2 23 23 0 8.3-4.3 15.5-10.7 19.5-1.1.5-2.2 1.1-3.3 1.7-2.7 1.2-5.8 1.8-9 1.8-12.8 0-23-10.2-23-23 0-.5 0-1.1.1-1.6a16 16 0 0 0 6.9 1.6 16 16 0 0 0 16-16 16 16 0 0 0-1.6-6.9c.5-.1 1.1-.1 1.6-.1zm39 10.3c5.4 3.1 11.6 5 18.2 5.3-4.8 6.8-7.7 15-7.7 23.8 0 1.6.1 3.2.3 4.8-3.8-2.2-7.9-3.8-12.3-4.7 2.2-5.1 3.5-10.6 3.5-16.5 0-4.4-.7-8.7-2-12.7zm51.5 6.1c12.8 0 23 10.2 23 23s-10.2 23-23 23-23-10.2-23-23c0-.5 0-1.1.1-1.6a16 16 0 0 0 6.9 1.6 16 16 0 0 0 16-16 16 16 0 0 0-1.6-6.9c.5-.1 1.1-.1 1.6-.1zm-140.4 27.8c12.8 0 23 10.2 23 23s-10.2 23-23 23c-9.7 0-17.9-5.8-21.3-14.2-.4-2-.9-3.9-1.5-5.7-.1-1-.2-2.1-.2-3.1 0-.5 0-1.1.1-1.6a16 16 0 0 0 6.9 1.6 16 16 0 0 0 16-16 16 16 0 0 0-1.6-6.9c.5-.1 1.1-.1 1.6-.1zm79.3 12.5c12.8 0 23 10.2 23 23s-10.2 23-23 23-23-10.2-23-23c0-.5 0-1.1.1-1.6a16 16 0 0 0 6.9 1.6 16 16 0 0 0 16-16 16 16 0 0 0-1.6-6.9c.5-.1 1.1-.1 1.6-.1zm120.3 2c12.8 0 23 10.2 23 23s-10.2 23-23 23-23-10.2-23-23c0-.5 0-1.1.1-1.6a16 16 0 0 0 6.9 1.6 16 16 0 0 0 16-16 16 16 0 0 0-1.6-6.9c.5-.1 1.1-.1 1.6-.1zM118.1 357c9.7 0 17.9 5.8 21.3 14.2.4 2 .9 3.9 1.5 5.7.1 1 .2 2.1.2 3.1 0 12.8-10.2 23-23 23s-23.02-10.2-23.02-23c0-.5 0-1.1.1-1.6A16 16 0 0 0 102 380a16 16 0 0 0 16.1-16 16 16 0 0 0-1.6-6.9c.5-.1 1.1-.1 1.6-.1zm102.4 2.6c.3 0 .7.1 1 .2l-.9 2.1c0-.8 0-1.6-.1-2.3zm79.2 11.9c6 3.5 13 5.5 20.4 5.5 6.7 0 13-1.6 18.6-4.5-.2 1.9-.4 3.8-.4 5.8 0 5.5 1.1 10.8 3.2 15.7-2.2-.4-4.5-.6-6.8-.6-20.1 0-37 14.7-40.4 33.9-4.1-6.2-9.9-11.3-16.7-14.5 13.2-6.8 22.4-20.7 22.4-36.5 0-1.6-.1-3.2-.3-4.8zm35 39.9c12.8 0 23 10.2 23 23s-10.2 23-23 23-23-10.2-23-23c0-.5 0-1.1.1-1.6a16 16 0 0 0 6.9 1.6 16 16 0 0 0 16-16 16 16 0 0 0-1.6-6.9c.5-.1 1.1-.1 1.6-.1zM159 414.2c12.8 0 23 10.2 23 23s-10.2 23-23 23-23-10.2-23-23c0-.5 0-1.1.1-1.6a16 16 0 0 0 6.9 1.6 16 16 0 0 0 16-16 16 16 0 0 0-1.6-6.9c.5-.1 1.1-.1 1.6-.1zm247.8 3.8c12.8 0 23 10.2 23 23s-10.2 23-23 23-23-10.2-23-23c0-.5 0-1.1.1-1.6a16 16 0 0 0 6.9 1.6 16 16 0 0 0 16-16 16 16 0 0 0-1.6-6.9c.5-.1 1.1-.1 1.6-.1zm-294.3 2.6c1.8.3 3.7.4 5.6.4 1.1 0 2.2-.1 3.4-.1-1.3 2.9-2.3 6-2.9 9.2-1.6-3.5-3.7-6.6-6.1-9.5zm-31 3.7c12.8 0 23 10.2 23 23s-10.2 23-23 23-23-10.2-23-23c0-.5 0-1.1.1-1.6a16 16 0 0 0 6.9 1.6 16 16 0 0 0 16-16 16 16 0 0 0-1.6-6.9c.5-.1 1.1-.1 1.6-.1zm178.7 2.6c12.8 0 23 10.2 23 23s-10.2 23-23 23-23-10.2-23-23c0-.5 0-1.1.1-1.6a16 16 0 0 0 6.9 1.6 16 16 0 0 0 16-16 16 16 0 0 0-1.6-6.9c.5-.1 1.1-.1 1.6-.1z"}}]})(props);
};
