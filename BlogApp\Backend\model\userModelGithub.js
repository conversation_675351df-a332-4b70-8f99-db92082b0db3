import mongoose from "mongoose";

const userSchema = new mongoose.Schema({
  githubId: { type: String, required: true, unique: true },
  name: String,
  email: String,
  // accessToken: String, // GitHub access token (backend use only)
  posts:[
          {
              type:mongoose.Schema.Types.ObjectId,
              ref:'post'
          }
      ],
});

export default mongoose.model("Usergithub", userSchema);
