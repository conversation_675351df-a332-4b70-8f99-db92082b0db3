// THIS FILE IS AUTO GENERATED
var GenIcon = require('../lib').GenIcon
module.exports.BiStreetView = function BiStreetView (props) {
  return GenIcon({"tag":"svg","attr":{"viewBox":"0 0 24 24"},"child":[{"tag":"circle","attr":{"cx":"12","cy":"4","r":"2"}},{"tag":"path","attr":{"d":"M12,18h2v-5h2V9c0-1.103-0.897-2-2-2h-4C8.897,7,8,7.897,8,9v4h2v5H12z"}},{"tag":"path","attr":{"d":"M18.446,11.386l-0.893,1.789C19.108,13.95,20,14.98,20,16c0,1.892-3.285,4-8,4s-8-2.108-8-4c0-1.02,0.892-2.05,2.446-2.825 l-0.893-1.789C3.295,12.512,2,14.193,2,16c0,3.364,4.393,6,10,6s10-2.636,10-6C22,14.193,20.705,12.512,18.446,11.386z"}}]})(props);
};
