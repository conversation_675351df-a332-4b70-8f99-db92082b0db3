// THIS FILE IS AUTO GENERATED
var GenIcon = require('../lib').GenIcon
module.exports.GiMandrillHead = function GiMandrillHead (props) {
  return GenIcon({"tag":"svg","attr":{"viewBox":"0 0 512 512"},"child":[{"tag":"path","attr":{"d":"M302.951 28.648C354.504 92.302 434.576 174.338 439 256c-10.827 48.475-41.014 84.049-72.533 115.389-12.215-9.303-24.189-17.186-36.016-23.618 3.27-19.799 5.635-41.41 5.635-58.99 0-11.2-4.564-24.588-13.6-36.726 7.288-8.878 13.928-19.548 19.313-30.315 7.022-14.04 12.058-27.674 12.058-39.558 0-2.597-.285-5.235-.818-7.87a1074.97 1074.97 0 0 0 17.807-5.773l-5.692-17.078a1271.888 1271.888 0 0 1-19.199 6.24 50.908 50.908 0 0 0-8.963-10.01c-16.316-10.968-30.492-13.979-47.63-9.773-19.383 4.844-26.372 7.266-33.362 7.266-6.99 0-13.98-2.422-33.361-7.266-17.14-4.206-31.315-1.195-47.631 9.773a50.908 50.908 0 0 0-8.963 10.01 1271.888 1271.888 0 0 1-19.2-6.24l-5.69 17.078a1074.97 1074.97 0 0 0 17.806 5.774c-.533 2.634-.818 5.272-.818 7.869 0 11.884 5.036 25.518 12.058 39.558 5.385 10.767 12.025 21.437 19.313 30.315-9.036 12.138-13.6 25.526-13.6 36.726 0 17.58 2.365 39.191 5.635 58.99-11.827 6.432-23.8 14.315-36.016 23.618C114.013 340.049 83.827 304.475 73 256c4.424-81.662 84.496-163.698 136.049-227.352C224 16 288 16 302.95 28.648zm.451 126.202c9.426.074 17.925 2.2 23.936 8.482-27.794 7.978-49.566 11.967-71.338 11.967-21.772 0-43.544-3.99-71.338-11.967 6.011-6.283 14.51-8.408 23.936-8.482 3.142-.025 6.387.178 9.677.53 19.169 4.792 28.447 7.186 37.725 7.186 9.278 0 18.556-2.394 37.725-7.185 3.29-.353 6.535-.556 9.677-.531zm32.229 24.697c.103 12.155-5.565 25.276-9.93 34.142-4.496 8.99-10.167 18.015-15.889 25.313-7.056-4.723-13.6-8.61-20.517-12.39.742.062 1.49.103 2.248.103 14.677 0 26.771-12.088 26.771-26.766 0-5.406-1.646-10.458-4.453-14.687 6.947-1.635 14.173-3.54 21.77-5.715zm-159.262 0c7.597 2.175 14.823 4.08 21.77 5.715-2.807 4.23-4.453 9.28-4.453 14.687 0 14.678 12.094 26.766 26.771 26.766.758 0 1.506-.04 2.248-.104-6.917 3.781-13.461 7.668-20.518 12.39-5.72-7.297-11.392-16.321-15.888-25.312-4.365-8.866-10.033-21.987-9.93-34.142zm115.174 11.635c4.952 0 8.771 3.82 8.771 8.767 0 4.947-3.819 8.766-8.771 8.766-4.952 0-8.772-3.819-8.772-8.766s3.82-8.767 8.772-8.767zm-71.086 0c4.952 0 8.772 3.82 8.772 8.767 0 4.947-3.82 8.766-8.772 8.766s-8.771-3.819-8.771-8.766 3.819-8.767 8.771-8.767zm45.414 1.242a26.375 26.375 0 0 0-1.1 7.525c0 8.125 3.71 15.45 9.508 20.377-6.023-1.786-12.12-2.728-18.279-2.728-6.16 0-12.256.942-18.28 2.728 5.799-4.927 9.509-12.252 9.509-20.377 0-2.609-.39-5.133-1.1-7.525a207.959 207.959 0 0 0 19.742 0zM256 235.598c2.895 0 5.819.292 8.771.869v83.842a69.806 69.806 0 0 0-17.542 0v-83.842a45.634 45.634 0 0 1 8.771-.87zm26.771 9.896a38.752 38.752 0 0 1 11.49 4.541l-5.6 78.422a54.392 54.392 0 0 0-5.89-3.113zm-53.542 0v79.85a54.392 54.392 0 0 0-5.89 3.113l-5.6-78.422a38.752 38.752 0 0 1 11.49-4.54zm81.832 21.973c4.693 8.087 7.025 16.686 7.025 21.314-.923 21.33-2.874 42.433-6.211 60.9l-6.242-6.24zm-110.122 0l5.428 75.974-6.242 6.24c-3.337-18.467-5.288-39.57-6.21-60.9 0-4.628 2.33-13.227 7.024-21.314zM256 337.756c7.572 0 15.029 1.76 21.166 4.943L256 361.215l-21.166-18.516c6.137-3.182 13.594-4.943 21.166-4.943zm35.133 16.64l8.388 8.387c-8.12 12.426-29.27 23.26-43.521 23.598-14.25-.339-35.401-11.172-43.521-23.598l8.388-8.387L256 385.13zm35.91 12.084c7.275 4.228 14.696 9.088 22.305 14.61-20.898 42.104-53.074 86.889-93.348 110.596-40.274-23.707-72.45-68.492-93.348-110.596 7.61-5.522 15.03-10.382 22.305-14.61 3.965 19.353 8.713 45.104 25.658 60.53 10.806 8.102 26.114 12.904 45.385 12.904 19.271 0 34.579-4.802 45.385-12.904 16.945-15.426 21.693-41.177 25.658-60.53zm-30.346 23.793c6.049 9.614.525 17.224-6.111 22.336-6.966 5.223-18.314 9.305-34.586 9.305s-27.62-4.082-34.586-9.305c-6.636-5.112-12.16-12.722-6.111-22.336 11.373 7.37 25.073 12.343 40.697 14.108 15.624-1.765 29.324-6.738 40.697-14.108z"}}]})(props);
};
