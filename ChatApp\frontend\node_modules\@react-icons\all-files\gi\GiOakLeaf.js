// THIS FILE IS AUTO GENERATED
var GenIcon = require('../lib').GenIcon
module.exports.GiOakLeaf = function GiOakLeaf (props) {
  return GenIcon({"tag":"svg","attr":{"viewBox":"0 0 512 512"},"child":[{"tag":"path","attr":{"d":"M92.239 26.432c-4.705.09-9.496.87-14.37 2.473-19.773 6.506-41.557 59.364-7.411 112.912 9.221 14.46-41 39.289-31.803 67.056 12.387 37.399 99.437 19.933 112.104 42.211 6.44 11.328-79.773 49.284-49.663 81.625 37.951 40.763 76.062 14.109 138.553 23.864 24.685 3.853-26.357 63.343 11.031 86.498 39.948 24.739 118.742 1.986 160.846-20.254a20577.214 20577.214 0 0 0-30.19-36.098c-33.45 10.371-71.807 15.824-106.036 13.664 36.092-6.615 65.118-14.246 94.8-27.025-21.566-25.637-43.299-51.22-65.357-76.479-36.846 7.379-103.783 18.406-166.793 13.88 8.83-1.316 110.772-14.937 154.935-27.38a3177.953 3177.953 0 0 0-24.357-27.318 6823.337 6823.337 0 0 0-27.935-35.486 6485.7 6485.7 0 0 0-15.413-19.34l-.115.658c-31.187 1.8-90.154 3.052-142.9-10.709 7.477.02 92.983 1.716 132.031-3.637-16.65-20.699-32.746-40.434-46.473-56.795-7.035-8.385-13.392-15.81-19.011-22.209l.05-.056c25.401 23.275 50.132 47.542 74.329 72.506 15.57-24.254 32.931-56.653 41.664-80.655 1.469 29.363-15.963 66.66-27.586 95.325 22.456 23.61 44.458 47.79 66.125 72.287 20.118-23.976 44.105-60.316 54.869-83.707-3.957 26.047-31.834 67.188-44.936 94.982 25.142 28.669 49.84 57.727 74.266 86.8 13.506-17.48 28.29-40.286 35.822-57.296 1.32 21.671-14.607 49.312-24.892 70.281l-.05-.014c9.624 11.49 19.211 22.974 28.766 34.428 3.016-2.12 5.604-4.173 7.582-6.095 31.459-30.573 36.26-79.699 17.842-116.51-12.519-25.021-70.096-8.654-77.265-23.846-9.068-19.214 51.563-76.204 28.146-104.902-16.456-20.168-75.04 1.983-85.264-16.182-16.343-29.04 28.13-74.832-21.763-99.244-26.468-12.95-46.397 5.349-88.338 44.103-21.236 19.623-62.13-63.165-113.828-64.312a48.694 48.694 0 0 0-2.012-.004zm345.39 402.365l-13.982 11.336 36.848 45.444 13.98-11.336-36.845-45.444z"}}]})(props);
};
