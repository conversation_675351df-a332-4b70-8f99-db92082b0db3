import React, { useEffect,  } from 'react'
import API from '../axios'
import { useAllPost } from '../Context/AllPostProvider'
import { useAuth } from '../Context/AuthProvider'


import { styled } from '@mui/material/styles';
import Card from '@mui/material/Card';
import CardHeader from '@mui/material/CardHeader';
import CardMedia from '@mui/material/CardMedia';
import CardContent from '@mui/material/CardContent';
import CardActions from '@mui/material/CardActions';
import Collapse from '@mui/material/Collapse';
import Avatar from '@mui/material/Avatar';
import IconButton from '@mui/material/IconButton';
import Typography from '@mui/material/Typography';
import { red } from '@mui/material/colors';
import FavoriteIcon from '@mui/icons-material/Favorite';
import ShareIcon from '@mui/icons-material/Share';
import ExpandMoreIcon from '@mui/icons-material/ExpandMore';
import MoreVertIcon from '@mui/icons-material/MoreVert';

import { Link, useNavigate } from 'react-router-dom';


const Home = () => {

  const naviagte=useNavigate()
 const {allUserPost, setallUserPost} = useAllPost()


  useEffect(() => {

    const allUserPosts= async()=>{
      const res=  await API.get('/api/user/allUserPosts')
      setallUserPost(res.data)
      // console.log(res.data)
    }

    allUserPosts()
    
  }, [allUserPost])
      
  


  return (
    <div>
      <div className='absolute'><Link to='/profile'>NEXT</Link></div>
     

    <div className='flex flex-wrap justify-center items-center mb-1 flex-col bg-gray-600'>
      {allUserPost.map((post, id) => (
        <Card  key={id} sx={{ maxWidth: 345 }} className='m-2  '>

      
        <React.Fragment key={id}>
          <CardHeader
            avatar={
              <div className='flex items-center justify-between gap-3'>
                <Avatar sx={{ bgcolor: red[500] }} aria-label="recipe">
                {/* {authUser.name} */}
              </Avatar>
              <h3 className='text-sm capitalize'>{post.user.name}</h3>
              </div>
            }
            action={
              <IconButton aria-label="settings">
                <MoreVertIcon />
              </IconButton>
            }
            // title={post.title}
            subheader={post.date }
          />
          <CardMedia
            component="img"
            height="194"
            image="https://images.unsplash.com/photo-1532614338840-ab30cf10ed36?auto=format&fit=crop&w=318"
            alt="Post image"
          />
          <CardContent>
            <h2>{post.title}</h2>
            <Typography variant="body2" color="text.secondary">
              {post.content}
            </Typography>
          </CardContent>

          <CardActions disableSpacing>
            <IconButton aria-label="add to favorites">
              <FavoriteIcon />
            </IconButton>
            <IconButton aria-label="share">
              <ShareIcon />
            </IconButton>

            
          </CardActions>
          
        </React.Fragment>
  
      
    </Card>
      )).reverse()}

      
    </div>
    </div>
  )
}

export default Home