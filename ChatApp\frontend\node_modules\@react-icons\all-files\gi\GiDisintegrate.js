// THIS FILE IS AUTO GENERATED
var GenIcon = require('../lib').GenIcon
module.exports.GiDisintegrate = function GiDisintegrate (props) {
  return GenIcon({"tag":"svg","attr":{"viewBox":"0 0 512 512"},"child":[{"tag":"path","attr":{"d":"M19.844 20.625v129.5l73.375 66.25c36.57 38.36 55.757 94.852 27.624 145.625l.72-.844-4.626 7.97 8.093 4.687 122.407 70.656 8.094 4.655 3.97-6.875c27.733-26.382 63.19-7.125 102.28 16.53l41.126 37.126h92.22V408.78l-44.063-43.967c-22.454-28.274-35.613-54.52-32.032-84.5 17.85-59.055-4.958-140.538-25.78-160.47-7.902-7.752-16.606-14.816-27.03-20.406-21.165-12.22-46.998-15.218-70.376-14.468-16.582.53-33.126 4.057-48.844 10.093-36.71 8.396-67.358-7.433-101.406-35.282l-39.22-39.155h-86.53zm280 83.313c2.78-.026 5.55.05 8.312.218-.036.097-.09.183-.125.28-6.752 18.694 38.538 37.97 49.126 14.97 55.007 34.127 69.07 117.013 36.063 174.188-.71 1.227-1.45 2.403-2.22 3.53l-6.313 6.845c7.46 4.334 12.742 11.783 12.157 21.31-.003.043.002.084 0 .126 5.824.896 11.176 5.245 10.78 11.656-.795 12.97-13.8 14.244-20.655 8.875-15.525 11.663-43.697 1.44-43.595-19.343-1.955.698-3.88 1.38-5.875 2.094l-27.125-27.594-13.344 13.094 21.564 21.937c-10.82 4.87-21.477 11.133-30.875 20.53l-.876.876-.625 1.064-6.658 11.5-14.812-8.563 10.313-17.874-16.188-9.344-10.313 17.875-13.656-7.875 10.313-17.875-16.19-9.343-10.31 17.875-13.94-8.064 10.314-17.875-16.188-9.342-10.312 17.875-15.25-8.782 6.656-11.5c5.53-12.61 4.07-28.693 2.938-39.31l30.25 7.81 4.687-18.092-38.03-9.813c-.616-3.4-1.223-6.765-1.782-10.063-2.202-12.97-3.66-24.87-2-36.156l5.218-16.687c.482-.96.98-1.922 1.532-2.876 9.726-16.845 23.427-31.258 39.375-42.438 1.944 19.517 29.105 28.628 44.188 17.063 7.884 12.587 33.59 13.47 34.97-8.97.8-13.03-14.17-20.428-25.376-16.875-.847-5.087-3.442-9.416-7.064-12.78 8.94-2.295 18.048-3.697 27.125-4.064 1.272-.05 2.545-.08 3.814-.093zm6.22 57.343c-6.418-.064-12.71 3.813-13.283 13.157-.918 14.96 26.277 19.934 27.5 0 .49-7.946-6.946-13.082-14.217-13.156zm-81.783 4.782c-9.155.277-18.194 4.64-25.124 14.938-19.17 28.49 33.978 72.874 60.688 38.28 7.888 4.022 19.703 1.605 20.5-11.374.534-8.688-8.413-14.002-16.25-13.03-5.094-15.572-22.663-29.33-39.813-28.814zm115.25 66.094c-9.155.276-18.194 4.607-25.124 14.906-19.576 29.093 36.255 74.772 62.344 36 14.376-21.366-11.905-51.67-37.22-50.906zm-56.5 8.875l-49.342 34.69 43.968 25.405 5.375-60.094zM164 324.97l15.25 8.78-11.156 19.313 16.187 9.343 11.157-19.312 13.938 8.062-11.156 19.313 16.186 9.342 11.156-19.312 13.657 7.875-11.157 19.313 16.187 9.343 11.156-19.31 14.813 8.56-21.564 37.314-106.22-61.313L164 324.97zm182.53 37.06c9.127-.25 17.758 10.78 12.19 19.19-3.474 5.245-8.023 6.81-12.22 6.155 2.446 6.643 2.232 14.06-2.156 20.688-21.842 32.983-63.58-2.503-47.188-27.25 10.818-16.336 26.53-15.88 37.625-8.25.216-1.442.723-2.856 1.626-4.22 2.904-4.384 6.554-6.213 10.125-6.312zm46.908.72c10.303.104 20.848 7.365 20.156 18.625-1.735 28.246-40.24 21.197-38.938 0 .813-13.24 9.69-18.717 18.78-18.625zm44.875 33.156c6.555.066 13.284 4.68 12.843 11.844-1.102 17.97-25.61 13.486-24.78 0 .516-8.42 6.153-11.902 11.937-11.844zm-59.407 15.875c6.555.067 13.285 4.682 12.844 11.845-1.103 17.97-25.642 13.486-24.813 0 .517-8.42 6.185-11.902 11.97-11.844z"}}]})(props);
};
