// THIS FILE IS AUTO GENERATED
var GenIcon = require('../lib').GenIcon
module.exports.GiMushroomCloud = function GiMushroomCloud (props) {
  return GenIcon({"tag":"svg","attr":{"viewBox":"0 0 512 512"},"child":[{"tag":"path","attr":{"d":"M279.775 17.137c-18.823-.262-38.412 6.822-51.54 20.02l-5.858 5.89-6.535-5.127c-35.796-28.083-78.147-22.538-98.727 10.39l-3.465 5.547-6.398-1.357c-27.027-5.737-53.6 4.882-69.373 23.047-15.776 18.165-21.16 42.634-5.937 68.43l3.68 6.234-5.12 5.122c-7.12 7.122-9.71 16.235-8.956 26.824.753 10.59 5.242 22.277 12.04 31.98 6.8 9.705 15.853 17.3 24.67 20.634 8.82 3.333 16.93 3.112 25.68-2.877l6.626-4.538 5.48 5.868c18.752 20.07 63.427 19.456 81.504-9.315l4.967-7.906 7.91 4.96c9.652 6.05 21.095 6.628 32.078 3.243 10.983-3.384 21.017-10.924 26.445-19.56l8.34-13.272 7.707 13.648c5.984 10.6 21.754 20.7 39.238 24.517 17.485 3.818 35.716 1.47 46.955-7.793l7.116-5.863 5.96 7.036c13.405 15.834 34.384 19.713 51.292 15.234 8.453-2.24 15.61-6.562 20.1-11.894 4.49-5.332 6.64-11.394 5.548-19.36l-1.412-10.292 10.386-.317c14.28-.435 23.67-5.757 30.195-13.635 6.527-7.877 9.99-18.775 9.966-30.103-.05-22.658-12.994-45.11-39.104-48.66l-9.843-1.34 1.936-9.743c1.49-7.49-1.057-16.514-7.54-25.764-6.485-9.25-16.708-18.272-28.37-24.904-11.662-6.634-24.726-10.866-36.596-11.388-11.87-.522-22.266 2.385-30.62 9.863l-7.743 6.934-6.072-8.437c-10.336-14.36-27.786-21.715-46.61-21.976zm-23.13 207.613c-7.79 7.932-17.737 13.957-28.64 17.316-12.57 3.874-26.675 4.027-39.61-1.3-13.607 16.66-33.646 25.044-53.58 25.658.803 2.694 2.022 5.36 3.65 7.885 4.48 6.947 11.786 12.436 20.377 14.188 8.59 1.752 18.766.2 30.504-8.578l9.133-6.828 4.9 10.295c1.805 3.793 10.955 9.652 22.417 10.94 11.462 1.286 23.836-1.518 30.982-8.206l7.2-6.737 6.34 7.55c7.687 9.153 18.164 11.31 28.223 9.768 10.06-1.54 18.25-8.15 19.394-10.936l4.225-10.297 9.41 5.943c9.343 5.9 17.365 6.48 24.47 4.494 7.103-1.987 13.382-6.983 17.39-13.273 3.672-5.763 5.297-12.387 4.51-18.327-7.83-2.86-15.302-7.21-21.903-13.22-16.45 9.763-37.038 10.807-55.794 6.712-16.836-3.676-32.71-11.465-43.6-23.045zm5.767 80.373c-7.74 4.837-16.592 7.306-25.443 8.002v41.938c-39.484 1.013-74.942 4.618-102.22 10.093-16.214 3.255-29.515 7.07-39.53 11.844-5.01 2.387-9.234 4.994-12.69 8.406-3.454 3.412-6.343 8.197-6.343 13.75 0 5.553 2.866 10.328 6.313 13.75 3.447 3.422 7.682 6.03 12.688 8.438 10.01 4.818 23.314 8.72 39.53 12.03 20.218 4.13 44.93 7.244 72.438 9-15.85 21.005-36.292 38.707-56.937 50.438H364.5c-20.393-12.03-39.75-29.664-54.72-50.312 28.51-1.726 54.114-4.872 74.94-9.125 16.215-3.312 29.52-7.213 39.53-12.03 5.006-2.41 9.24-5.016 12.688-8.44 3.446-3.42 6.28-8.196 6.28-13.75 0-5.55-2.857-10.337-6.312-13.75-3.455-3.41-7.68-6.018-12.687-8.405-10.017-4.773-23.32-8.59-39.533-11.844-27.645-5.55-63.688-9.17-103.812-10.125v-40.776c-6.473-1.61-12.817-4.55-18.463-9.13zm19.12 68.596c38.834.986 73.608 4.558 99.468 9.75 15.31 3.072 27.538 6.774 35.156 10.405 3.81 1.815 6.438 3.64 7.625 4.813.298.293.28.317.408.468-.13.156-.123.188-.438.5-1.196 1.187-3.814 3.04-7.625 4.875-7.622 3.67-19.85 7.406-35.156 10.533-22.08 4.51-50.67 7.833-82.72 9.343-8.816-16.007-14.824-33.213-16.72-50.687zm-45.157.06c-1.88 17.484-8.158 34.644-17.28 50.533-31.217-1.555-59.056-4.838-80.657-9.25-15.308-3.127-27.534-6.863-35.157-10.532-3.81-1.833-6.428-3.686-7.624-4.874-.32-.318-.31-.344-.437-.5.126-.15.134-.17.436-.47 1.188-1.17 3.785-2.996 7.594-4.81 7.62-3.632 19.846-7.334 35.156-10.407 25.525-5.125 59.738-8.648 97.97-9.69z"}}]})(props);
};
