// THIS FILE IS AUTO GENERATED
var GenIcon = require('../lib').GenIcon
module.exports.BiBarcodeReader = function BiBarcodeReader (props) {
  return GenIcon({"tag":"svg","attr":{"viewBox":"0 0 24 24"},"child":[{"tag":"path","attr":{"d":"M5 7H7V17H5zM14 7H15V17H14zM10 7H13V17H10zM8 7H9V17H8zM16 7H19V17H16z"}},{"tag":"path","attr":{"d":"M4 5h4V3H4C2.897 3 2 3.897 2 5v4h2V5zM4 21h4v-2H4v-4H2v4C2 20.103 2.897 21 4 21zM20 3h-4v2h4v4h2V5C22 3.897 21.103 3 20 3zM20 19h-4v2h4c1.103 0 2-.897 2-2v-4h-2V19z"}}]})(props);
};
