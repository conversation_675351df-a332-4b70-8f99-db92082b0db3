// THIS FILE IS AUTO GENERATED
var GenIcon = require('../lib').GenIcon
module.exports.DiMeteor = function DiMeteor (props) {
  return GenIcon({"tag":"svg","attr":{"version":"1.1","viewBox":"0 0 27 32"},"child":[{"tag":"path","attr":{"d":"M4.508 7.534c1.423 1.578 15.446 16.442 15.601 16.533 0.414 0.246 1.177-0.414 0.996-0.867-0.078-0.194-3.35-3.299-16.222-15.394-0.466-0.44-0.634-0.556-0.375-0.272zM15.244 15.348c5.007 5.42 6.041 6.481 6.326 6.481 0.453 0 0.828-0.414 0.737-0.802-0.052-0.181-2.678-2.704-6.442-6.184-3.506-3.234-6.429-5.912-6.494-5.938s2.574 2.872 5.873 6.442zM14.662 11.415c5.692 6.222 7.18 7.762 7.529 7.762 0.272 0 0.362-0.065 0.388-0.323 0.026-0.272-0.53-0.841-3.752-3.829-5.55-5.136-5.563-5.149-4.166-3.609zM11.674 18.763c5.847 6.339 5.938 6.429 6.429 6.429 0.401 0 0.518-0.065 0.608-0.284 0.052-0.155 0.078-0.388 0.052-0.505-0.052-0.194-12.121-11.449-12.755-11.888-0.155-0.103 2.393 2.704 5.666 6.248zM7.483 16.861c0.181 0.233 1.979 2.199 3.997 4.373 3.182 3.441 3.713 3.971 3.998 3.932 0.259-0.026 0.336-0.116 0.336-0.375s-0.543-0.828-2.781-2.898c-1.527-1.41-3.48-3.208-4.334-3.997s-1.397-1.255-1.216-1.035z"}}]})(props);
};
