// THIS FILE IS AUTO GENERATED
import { GenIcon } from '../lib';
export function GrQr (props) {
  return GenIcon({"tag":"svg","attr":{"viewBox":"0 0 24 24"},"child":[{"tag":"g","attr":{"fill":"none","fillRule":"evenodd"},"child":[{"tag":"path","attr":{"d":"M13,14 L14,14 L14,15 L13,15 L13,14 Z M14,15 L15,15 L15,16 L14,16 L14,15 Z M14,16 L15,16 L15,17 L14,17 L14,16 Z M16,16 L17,16 L17,17 L16,17 L16,16 Z M16,17 L17,17 L17,18 L16,18 L16,17 Z M13,16 L14,16 L14,17 L13,17 L13,16 Z M15,16 L16,16 L16,17 L15,17 L15,16 Z M15,17 L16,17 L16,18 L15,18 L15,17 Z M18,16 L19,16 L19,17 L18,17 L18,16 Z M18,15 L19,15 L19,16 L18,16 L18,15 Z M19,14 L20,14 L20,15 L19,15 L19,14 Z M17,16 L18,16 L18,17 L17,17 L17,16 Z M17,17 L18,17 L18,18 L17,18 L17,17 Z M16,18 L17,18 L17,19 L16,19 L16,18 Z M15,18 L16,18 L16,19 L15,19 L15,18 Z M17,18 L18,18 L18,19 L17,19 L17,18 Z M18,18 L19,18 L19,19 L18,19 L18,18 Z M16,19 L17,19 L17,20 L16,20 L16,19 Z M14,19 L15,19 L15,20 L14,20 L14,19 Z M15,19 L16,19 L16,20 L15,20 L15,19 Z M13,19 L14,19 L14,20 L13,20 L13,19 Z M13,20 L14,20 L14,21 L13,21 L13,20 Z M14,21 L15,21 L15,22 L14,22 L14,21 Z M15,21 L16,21 L16,22 L15,22 L15,21 Z M17,21 L18,21 L18,22 L17,22 L17,21 Z M18,21 L19,21 L19,22 L18,22 L18,21 Z M17,19 L18,19 L18,20 L17,20 L17,19 Z M18,19 L19,19 L19,20 L18,20 L18,19 Z M19,18 L20,18 L20,19 L19,19 L19,18 Z M19,17 L20,17 L20,18 L19,18 L19,17 Z M19,20 L20,20 L20,21 L19,21 L19,20 Z M19,19 L20,19 L20,20 L19,20 L19,19 Z M20,18 L21,18 L21,19 L20,19 L20,18 Z M20,17 L21,17 L21,18 L20,18 L20,17 Z M21,20 L22,20 L22,21 L21,21 L21,20 Z M21,18 L22,18 L22,19 L21,19 L21,18 Z M21,19 L22,19 L22,20 L21,20 L21,19 Z M19,16 L20,16 L20,17 L19,17 L19,16 Z M13,17 L14,17 L14,18 L13,18 L13,17 Z M12,17 L13,17 L13,18 L12,18 L12,17 Z M12,18 L13,18 L13,19 L12,19 L12,18 Z M14,18 L15,18 L15,19 L14,19 L14,18 Z M11,18 L12,18 L12,19 L11,19 L11,18 Z M13,18 L14,18 L14,19 L13,19 L13,18 Z M11,19 L12,19 L12,20 L11,20 L11,19 Z M11,20 L12,20 L12,21 L11,21 L11,20 Z M11,1 L12,1 L12,2 L11,2 L11,1 Z M12,2 L13,2 L13,3 L12,3 L12,2 Z M11,4 L12,4 L12,5 L11,5 L11,4 Z M12,5 L13,5 L13,6 L12,6 L12,5 Z M11,6 L12,6 L12,7 L11,7 L11,6 Z M12,6 L13,6 L13,7 L12,7 L12,6 Z M12,7 L13,7 L13,8 L12,8 L12,7 Z M12,8 L13,8 L13,9 L12,9 L12,8 Z M11,9 L12,9 L12,10 L11,10 L11,9 Z M12,9 L13,9 L13,10 L12,10 L12,9 Z M11,10 L12,10 L12,11 L11,11 L11,10 Z M1,11 L2,11 L2,12 L1,12 L1,11 Z M2,12 L3,12 L3,13 L2,13 L2,12 Z M4,11 L5,11 L5,12 L4,12 L4,11 Z M4,12 L5,12 L5,13 L4,13 L4,12 Z M5,11 L6,11 L6,12 L5,12 L5,11 Z M6,12 L7,12 L7,13 L6,13 L6,12 Z M7,11 L8,11 L8,12 L7,12 L7,11 Z M8,12 L9,12 L9,13 L8,13 L8,12 Z M8,11 L9,11 L9,12 L8,12 L8,11 Z M9,11 L10,11 L10,12 L9,12 L9,11 Z M10,11 L11,11 L11,12 L10,12 L10,11 Z M11,12 L12,12 L12,13 L11,13 L11,12 Z M13,12 L14,12 L14,13 L13,13 L13,12 Z M14,11 L15,11 L15,12 L14,12 L14,11 Z M15,11 L16,11 L16,12 L15,12 L15,11 Z M16,11 L17,11 L17,12 L16,12 L16,11 Z M15,13 L16,13 L16,14 L15,14 L15,13 Z M13,22 L14,22 L14,23 L13,23 L13,22 Z M12,22 L13,22 L13,23 L12,23 L12,22 Z M12,13 L13,13 L13,14 L12,14 L12,13 Z M11,13 L12,13 L12,14 L11,14 L11,13 Z M11,14 L12,14 L12,15 L11,15 L11,14 Z M11,15 L12,15 L12,16 L11,16 L11,15 Z M22,14 L23,14 L23,15 L22,15 L22,14 Z M21,15 L22,15 L22,16 L21,16 L21,15 Z M22,17 L23,17 L23,18 L22,18 L22,17 Z M17,13 L18,13 L18,14 L17,14 L17,13 Z M18,12 L19,12 L19,13 L18,13 L18,12 Z M22,12 L23,12 L23,13 L22,13 L22,12 Z M22,13 L23,13 L23,14 L22,14 L22,13 Z M21,13 L22,13 L22,14 L21,14 L21,13 Z M22,21 L23,21 L23,22 L22,22 L22,21 Z M21,22 L22,22 L22,23 L21,23 L21,22 Z M19,22 L20,22 L20,23 L19,23 L19,22 Z M22,22 L23,22 L23,23 L22,23 L22,22 Z"}},{"tag":"path","attr":{"stroke":"#000","strokeWidth":"2","d":"M15,2 L22,2 L22,9 L15,9 L15,2 Z M2,2 L9,2 L9,9 L2,9 L2,2 Z M2,15 L9,15 L9,22 L2,22 L2,15 Z M18,5 L19,5 L19,6 L18,6 L18,5 Z M5,5 L6,5 L6,6 L5,6 L5,5 Z M5,18 L6,18 L6,19 L5,19 L5,18 Z"}}]}]})(props);
};
