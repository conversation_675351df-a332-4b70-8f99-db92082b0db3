// THIS FILE IS AUTO GENERATED
var GenIcon = require('../lib').GenIcon
module.exports.GiHeadShot = function GiHeadShot (props) {
  return GenIcon({"tag":"svg","attr":{"viewBox":"0 0 512 512"},"child":[{"tag":"path","attr":{"d":"M256 16c-30.6 0-59.243 9.113-82.97 24.844l44.064 44.062c24.486-11.704 53.326-11.704 77.812 0l44.063-44.062C315.242 25.114 286.597 16 256 16zM130.844 83.03C115.114 106.758 106 135.402 106 166c0 30.6 9.113 59.245 24.844 82.97l44.062-44.064c-11.704-24.484-11.704-53.326 0-77.812L130.844 83.03zm250.312 0l-44.062 44.064c11.704 24.486 11.704 53.328 0 77.812l44.062 44.063C396.886 225.242 406 196.598 406 166c0-30.6-9.113-59.245-24.844-82.97zm-123.844 38a45 45 0 0 0-46.312 45 45 45 0 0 0 90 0 45 45 0 0 0-43.688-45zm-40.218 126.064l-44.063 44.062C196.758 306.886 225.402 316 256 316c30.6 0 59.243-9.113 82.97-24.844l-44.064-44.062c-24.486 11.704-53.326 11.704-77.812 0zM256 316H46c-15 0-30 15-30 30v60c0 30 45 30 60 90h360c15-60 60-60 60-90v-60c0-15-15-30-30-30H256z"}}]})(props);
};
