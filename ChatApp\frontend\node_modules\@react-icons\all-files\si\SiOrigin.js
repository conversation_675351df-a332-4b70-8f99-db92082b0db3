// THIS FILE IS AUTO GENERATED
var GenIcon = require('../lib').GenIcon
module.exports.SiOrigin = function SiOrigin (props) {
  return GenIcon({"tag":"svg","attr":{"role":"img","viewBox":"0 0 24 24"},"child":[{"tag":"title","attr":{},"child":[]},{"tag":"path","attr":{"d":"M12.588 3.11c1.189.071 2.352.384 3.417.919 1.031.514 1.95 1.225 2.706 2.094.751.865 1.322 1.853 1.715 2.963.391 1.109.548 2.278.464 3.502-.033.636-.135 1.252-.306 1.848-.167.588-.393 1.159-.674 1.703-.439.849-.929 1.652-1.47 2.412-.538.759-1.125 1.465-1.762 2.118-.638.653-1.313 1.254-2.032 1.802-.719.544-1.471 1.038-2.254 1.479l-.037.026c-.033.018-.071.026-.109.023-.063-.015-.118-.048-.159-.097-.041-.05-.063-.111-.062-.173 0-.029.004-.059.012-.085.008-.023.021-.044.037-.062.277-.393.506-.806.686-1.235.181-.434.303-.885.368-1.359 0-.032-.015-.064-.038-.085-.021-.025-.053-.038-.085-.038-.264.032-.528.053-.795.062-.266.009-.532-.003-.796-.037-1.189-.071-2.353-.385-3.418-.918-1.031-.515-1.949-1.226-2.705-2.095-.754-.87-1.336-1.875-1.715-2.963-.394-1.123-.552-2.314-.465-3.502.033-.636.135-1.252.306-1.848.171-.598.396-1.155.675-1.68.439-.864.931-1.676 1.469-2.436.539-.757 1.125-1.464 1.761-2.118.639-.652 1.314-1.252 2.033-1.8.72-.546 1.47-1.039 2.253-1.479l.038-.025c.033-.02.07-.027.109-.025.065.016.119.051.158.098.043.051.062.106.062.174.001.027-.003.057-.012.084-.007.023-.02.043-.036.061-.273.386-.505.801-.687 1.237-.181.433-.3.885-.366 1.358 0 .033.012.063.036.086.022.024.054.037.085.037.262-.033.527-.053.795-.061.272-.009.536.003.798.035zm-.807 12.367c.922.079 1.838-.231 2.521-.855.72-.639 1.109-1.438 1.176-2.4.078-.928-.232-1.846-.856-2.535-.601-.708-1.472-1.131-2.4-1.162-.927-.078-1.845.232-2.534.855-.709.602-1.132 1.473-1.164 2.4-.078.926.228 1.842.846 2.535.628.725 1.432 1.115 2.411 1.162z"}}]})(props);
};
