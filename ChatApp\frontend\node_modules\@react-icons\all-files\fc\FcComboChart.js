// THIS FILE IS AUTO GENERATED
var GenIcon = require('../lib').GenIcon
module.exports.FcComboChart = function FcComboChart (props) {
  return GenIcon({"tag":"svg","attr":{"version":"1","viewBox":"0 0 48 48","enableBackground":"new 0 0 48 48"},"child":[{"tag":"g","attr":{"fill":"#00BCD4"},"child":[{"tag":"rect","attr":{"x":"37","y":"18","width":"6","height":"24"}},{"tag":"rect","attr":{"x":"29","y":"26","width":"6","height":"16"}},{"tag":"rect","attr":{"x":"21","y":"22","width":"6","height":"20"}},{"tag":"rect","attr":{"x":"13","y":"32","width":"6","height":"10"}},{"tag":"rect","attr":{"x":"5","y":"28","width":"6","height":"14"}}]},{"tag":"g","attr":{"fill":"#3F51B5"},"child":[{"tag":"circle","attr":{"cx":"8","cy":"16","r":"3"}},{"tag":"circle","attr":{"cx":"16","cy":"18","r":"3"}},{"tag":"circle","attr":{"cx":"24","cy":"11","r":"3"}},{"tag":"circle","attr":{"cx":"32","cy":"13","r":"3"}},{"tag":"circle","attr":{"cx":"40","cy":"9","r":"3"}},{"tag":"polygon","attr":{"points":"39.1,7.2 31.8,10.9 23.5,8.8 15.5,15.8 8.5,14.1 7.5,17.9 16.5,20.2 24.5,13.2 32.2,15.1 40.9,10.8"}}]}]})(props);
};
