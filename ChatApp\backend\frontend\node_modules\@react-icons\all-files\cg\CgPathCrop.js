// THIS FILE IS AUTO GENERATED
var GenIcon = require('../lib').GenIcon
module.exports.CgPathCrop = function CgPathCrop (props) {
  return GenIcon({"tag":"svg","attr":{"viewBox":"0 0 24 24","fill":"none"},"child":[{"tag":"rect","attr":{"opacity":"0.5","x":"6","y":"6","width":"8","height":"8","stroke":"currentColor","strokeWidth":"2"}},{"tag":"path","attr":{"fillRule":"evenodd","clipRule":"evenodd","d":"M9 9H19V19H9V9ZM15 11H17V17H11V15H15V11Z","fill":"currentColor"}}]})(props);
};
