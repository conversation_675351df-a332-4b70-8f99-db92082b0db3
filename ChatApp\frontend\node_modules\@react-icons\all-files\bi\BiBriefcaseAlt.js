// THIS FILE IS AUTO GENERATED
var GenIcon = require('../lib').GenIcon
module.exports.BiBriefcaseAlt = function BiBriefcaseAlt (props) {
  return GenIcon({"tag":"svg","attr":{"viewBox":"0 0 24 24"},"child":[{"tag":"path","attr":{"d":"M20,6h-3V4c0-1.103-0.897-2-2-2H9C7.897,2,7,2.897,7,4v2H4C2.897,6,2,6.897,2,8v11c0,1.103,0.897,2,2,2h16 c1.103,0,2-0.897,2-2V8C22,6.897,21.103,6,20,6z M16,8v11H8V8H16z M15,4v2H9V4H15z M4,8h2v11H4V8z M18,19V8h2l0.001,11H18z"}}]})(props);
};
