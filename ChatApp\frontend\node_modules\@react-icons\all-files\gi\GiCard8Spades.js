// THIS FILE IS AUTO GENERATED
var GenIcon = require('../lib').GenIcon
module.exports.GiCard8Spades = function GiCard8Spades (props) {
  return GenIcon({"tag":"svg","attr":{"viewBox":"0 0 512 512"},"child":[{"tag":"path","attr":{"d":"M119.436 36c-16.126 0-29.2 17.237-29.2 38.5v363c0 21.263 13.074 38.5 29.2 38.5h275.298c16.126 0 29.198-17.237 29.198-38.5v-363c0-21.263-13.072-38.5-29.198-38.5H119.436zm26.652 8.047s46.338 33.838 47.271 63.068c.776 24.287-25.024 32.122-40.775 18.586l13.633 32.653h-40.115l13.613-32.635c-15.535 13.88-40.006 5.349-40.758-18.604-.88-28.01 47.13-63.068 47.13-63.068zm102.54 132.488c9.907 0 18.507 1.704 25.798 5.111 7.37 3.408 12.956 8.044 16.76 13.909 3.883 5.864 5.826 12.006 5.826 18.425 0 14.9-9.392 27.302-28.174 37.208 12.68 4.596 21.832 10.618 27.459 18.068 5.626 7.37 8.441 16.285 8.441 26.746 0 12.759-4.081 23.893-12.244 33.402-10.54 12.284-25.558 18.426-45.053 18.426-17.196 0-30.788-4.121-40.773-12.363-9.985-8.321-14.979-18.663-14.979-31.026 0-9.272 2.853-17.355 8.56-24.25 5.784-6.973 15.73-12.876 29.837-17.71-20.842-8.956-31.264-22.784-31.264-41.487 0-12.125 4.676-22.546 14.028-31.264 9.43-8.796 21.356-13.195 35.779-13.195zm-.831 8.203c-10.382 0-18.463 2.536-24.248 7.608-5.785 4.992-8.678 11.53-8.678 19.613 0 8.004 2.496 14.384 7.488 19.139 5.072 4.755 17.515 10.42 37.327 16.998 8.32-4.755 13.906-9.43 16.76-14.026 2.852-4.675 4.279-10.778 4.279-18.306 0-9.906-2.813-17.555-8.44-22.944-5.626-5.389-13.79-8.082-24.488-8.082zm-8.795 81.07c-11.253 4.755-19.06 10.104-23.418 16.047-4.359 5.944-6.54 13.235-6.54 21.874 0 11.253 3.29 20.01 9.868 26.271 6.657 6.181 16.207 9.271 28.649 9.271 13.155 0 23.377-3.09 30.668-9.271 7.29-6.26 10.937-14.504 10.937-24.727 0-9.272-3.17-16.284-9.51-21.039-6.26-4.754-19.812-10.897-40.654-18.425zm106.777 87.84h40.116l-13.633 32.653c15.75-13.536 41.551-5.701 40.775 18.586-.933 29.23-47.271 63.068-47.271 63.068s-48.01-35.057-47.131-63.066c.751-23.953 25.222-32.487 40.758-18.606l-13.614-32.635z"}}]})(props);
};
