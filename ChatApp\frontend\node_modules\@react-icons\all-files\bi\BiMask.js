// THIS FILE IS AUTO GENERATED
var GenIcon = require('../lib').GenIcon
module.exports.BiMask = function BiMask (props) {
  return GenIcon({"tag":"svg","attr":{"viewBox":"0 0 24 24"},"child":[{"tag":"path","attr":{"d":"M19,6H5C3.346,6,2,7.346,2,9v5c0,2.206,1.794,4,4,4h1.637c1.166,0,2.28-0.557,2.981-1.491 c0.66-0.879,2.104-0.88,2.764,0.001c0.701,0.934,1.815,1.49,2.981,1.49H18c2.206,0,4-1.794,4-4V9C22,7.346,20.654,6,19,6z M20,14 c0,1.103-0.897,2-2,2h-1.637c-0.54,0-1.057-0.259-1.382-0.69c-0.71-0.948-1.797-1.492-2.981-1.492s-2.271,0.544-2.981,1.491 C8.693,15.741,8.177,16,7.637,16H6c-1.103,0-2-0.897-2-2V9c0-0.551,0.448-1,1-1h14c0.552,0,1,0.449,1,1V14z"}},{"tag":"ellipse","attr":{"cx":"7.5","cy":"11.5","rx":"2.5","ry":"1.5"}},{"tag":"ellipse","attr":{"cx":"16.5","cy":"11.5","rx":"2.5","ry":"1.5"}}]})(props);
};
