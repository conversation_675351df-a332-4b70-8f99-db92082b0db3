import userModel from '../model/userModel.js';
import bcrypt from "bcryptjs";
import jwt from "jsonwebtoken";




// Signup Controller
export const signup = async (req, res) => {
    const { name, email, password , confirmPassword} = req.body;

    if(password!==confirmPassword){
        return res.status(400).json({message:"password do not match"})
    }

    try {
        // Check if user already exists
        const existingUser = await userModel.findOne({ email });
        if (existingUser) {
            return res.status(400).json({ message: "User already exists" });
        }

        // Hash password
        const hashedPassword = await bcrypt.hash(password, 10);

        // Create new user
        const user = new userModel({
            name,
            email,
            password: hashedPassword,
        });

        await user.save();

        // Generate JWT token
        const token = jwt.sign(
            { id: user._id, email: user.email },
            process.env.JWT_SECRET,
            { expiresIn: "1h" }
        );
        res.cookie("token", token,{
            httpOnly:true,
            secure:false, // Set to false for development (HTTP)
            sameSite:"lax" // Changed from strict to lax for development
        })

      return  res.status(201).json({
            message: "User registered successfully",
            user: { id: user._id, name: user.name, email: user.email,posts:user.posts },
            token,
        });
    } catch (error) {
        console.error(error);
        res.status(500).json({ message: "Internal server error" });
    }
};




export const login = async (req, res) => {
    const { email, password } = req.body;
    try {
        // Find user by email
        const user = await userModel.findOne({ email });
        console.log(user)
        if (!user) {
            return res.status(400).json({ message: "Invalid email or password" });
        }
          
        // Compare password
        const isMatch = await bcrypt.compare(password, user.password);
        if (!isMatch) {
            return res.status(400).json({ message: "Invalid email or password" });
        }

        // Generate JWT token
        const token = jwt.sign(
            { id: user._id, email: user.email },
           process.env.JWT_SECRET,
            { expiresIn: "1d" }
        );
         res.cookie("token", token,{
            httpOnly:true,
            secure:false, // Set to false for development (HTTP)
            sameSite:"lax" // Changed from strict to lax for development
        })

        res.status(200).json({
            message: "Login successful",
            user: { id: user._id, name: user.name, email: user.email },
            token,
        });
    } catch (error) {
        console.error(error);
        res.status(500).json({ message: "Internal server error" });
    }
};


//! logout 

 export const logout= async (req,res)=>{

        //    console.log(await userModel.findOne({email:req.user.email}))
    try {
        //    
        res.clearCookie("token")
        res.status(200).json({message:"user logout successfully"})
    } catch (error) {
        console.log(error);
        res.status(500).json({message:"internal server error"})
    }

}
