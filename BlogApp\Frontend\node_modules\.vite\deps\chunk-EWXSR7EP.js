import {
  useForkRef
} from "./chunk-E4MPIUBI.js";
import {
  useEnhancedEffect_default,
  useId
} from "./chunk-OTOGCSRI.js";
import {
  require_react
} from "./chunk-BQYK6RGN.js";
import {
  __toESM
} from "./chunk-G3PMV62Z.js";

// node_modules/@mui/material/esm/utils/useId.js
var useId_default = useId;

// node_modules/@mui/utils/esm/useControlled/useControlled.js
var React = __toESM(require_react(), 1);

// node_modules/@mui/utils/esm/useEventCallback/useEventCallback.js
var React2 = __toESM(require_react(), 1);
function useEventCallback(fn) {
  const ref = React2.useRef(fn);
  useEnhancedEffect_default(() => {
    ref.current = fn;
  });
  return React2.useRef((...args) => (
    // @ts-expect-error hide `this`
    (0, ref.current)(...args)
  )).current;
}
var useEventCallback_default = useEventCallback;

// node_modules/@mui/material/esm/utils/useEventCallback.js
var useEventCallback_default2 = useEventCallback_default;

// node_modules/@mui/material/esm/utils/useForkRef.js
var useForkRef_default = useForkRef;

export {
  useId_default,
  useEventCallback_default2 as useEventCallback_default,
  useForkRef_default
};
//# sourceMappingURL=chunk-EWXSR7EP.js.map
