// THIS FILE IS AUTO GENERATED
var GenIcon = require('../lib').GenIcon
module.exports.BiBookmarks = function BiBookmarks (props) {
  return GenIcon({"tag":"svg","attr":{"viewBox":"0 0 24 24"},"child":[{"tag":"path","attr":{"d":"M14,5H6C4.897,5,4,5.897,4,7v2.822v2.53V23l6-3.601L16,23V12.353v-1.53V7C16,5.897,15.103,5,14,5z M14,19.467l-4-2.399 l-4,2.399v-7.114v-2.53V7h8v3.822v1.53V19.467z"}},{"tag":"path","attr":{"d":"M18,1h-1h-2.002H10C8.897,1,8,1.897,8,3h2h4.586H16c1.103,0,2,0.897,2,2v1.414v0.408v1.53v6.047v1.044l2,2.489v-9.58v-1.53 V6V4V3C20,1.897,19.103,1,18,1z"}}]})(props);
};
