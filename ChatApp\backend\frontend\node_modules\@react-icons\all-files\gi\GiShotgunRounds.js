// THIS FILE IS AUTO GENERATED
var GenIcon = require('../lib').GenIcon
module.exports.GiShotgunRounds = function GiShotgunRounds (props) {
  return GenIcon({"tag":"svg","attr":{"viewBox":"0 0 512 512"},"child":[{"tag":"path","attr":{"d":"M235.223 42.74c-8.67.003-17.32.527-25.252 1.49l22.995 7.374 26.06-7.204-14.904 8.266 29.183.938-31.168 1.953 7.627 4.894c12.12-1.384 25.08-4.335 39.133-9.216-2.915-1.435-6.917-2.873-11.635-4.064-11.368-2.87-26.75-4.434-42.037-4.43zm-25.452 1.512c-5.98.732-11.56 1.705-16.362 2.932-4.785 1.222-8.804 2.705-11.63 4.156 3.057 1.185 6.148 2.28 9.277 3.285l32.324-1.734zm-18.176 10.543c7.56 2.397 15.358 4.233 23.506 5.353l8.462-4.365zm40.732 2.166l-13.195 3.68c8.334.888 17.042 1.022 26.23.247zm61.553 11.56c-46.084 15.603-83.883 12.33-116.5.343v265.383c35.69 13.848 74.13 13.286 116.5-1.504.03-87.962.005-180.644 0-264.22zm26.42 162.384c-.07 0-.143.003-.214.002.998 1.19 1.976 2.432 2.94 3.723 7.82 10.48 14.66 24.324 19.845 38.66 2.266 6.26 4.196 12.576 5.693 18.76l3.472-17.605-.037.202c.346-1.993.785-3.923 1.283-5.81-2.916-10.882-7.918-20.998-13.995-27.82-5.77-6.48-11.836-9.965-18.984-10.113zm97.268 5.592c-24.834.066-43.015 13.465-47.84 41.23l-.017.102-3.814 21.336c15.8-9.586 32.307-15.674 50.56-15.127 20.85.625 43.688 7 61.058 18.172l-3.38-25.292-.01-.11c-5.358-29.38-29.49-40.086-56.556-40.312zm-105.69 13.7v83.113l19.768-6.546-.017-.086c.516-.107.265.336.983-1.34.72-1.676 1.386-5.31 1.27-10.024-.233-9.425-3.236-22.905-7.938-35.902-3.877-10.72-8.928-21.185-14.068-29.215zM159.38 266.26c-26.83 5.996-54.302 12.135-74.978 16.775-3.493.784-5.36 1.21-8.54 1.924 2.44 1.747 4.765 3.663 6.945 5.737 12.8 12.178 21.682 29.216 26.61 47.237 4.93 18.02 5.93 37.097 1.563 53.85-.805 3.094-1.827 6.122-3.058 9.044l51.46-17.035zM44.138 292.244c-.22.004-.448.026-.67.033l-.766.174c-2.258.52-4.354 1.064-3.49.818l-.187.052-.187.045c-3.245.778-5.52 2.43-7.758 5.71-2.238 3.282-4.103 8.264-5.115 14.433-2.024 12.338-.608 29.12 3.922 45.05 4.53 15.926 12.184 31.026 21.205 40.588 8.128 8.617 16.29 12.72 25.928 11.456 8.42-4.317 13.658-12.273 16.546-23.356 3.324-12.754 2.71-29.15-1.505-44.564-4.218-15.415-12.014-29.77-21.658-38.946-7.835-7.454-16.33-11.65-26.265-11.492zm371.504 9.742c-21.177-.128-41.18 6.894-55.216 22.91l-3.385 17.155c15.41-16.114 37.094-26.187 61.087-26.187 26.287 0 49.818 12.072 65.348 30.957l-2.55-19.078C465 312.387 441.15 302.724 417.92 302.027c-.76-.022-1.52-.036-2.277-.04zM38.997 315.023L57.7 343.928l13.663-21.848-6.69 29.15 27.198 15.944-24.782-4.742 10.422 27.355-17.703-26.1-12.787 20.225 5.455-27.603-25.204-14.65 24.07 5.488zm379.13 16.614c-38.096 0-68.81 30.715-68.81 68.812 0 38.096 30.714 68.81 68.81 68.81 38.098 0 68.813-30.714 68.813-68.81 0-38.098-30.716-68.813-68.813-68.813zM293.88 351.71c-40.93 13.13-80.13 14.003-116.5 1.66v40.038c35.69 14.074 74.128 13.705 116.5-.928zm124.247 24.265c13.517 0 24.475 10.957 24.475 24.474 0 13.516-10.958 24.474-24.475 24.474-13.517 0-24.475-10.958-24.475-24.475 0-13.518 10.958-24.475 24.475-24.475zm-121.23 34.488c-42.767 14.164-83.67 15-121.467 1.433l-3.157 13.618c38.888 19.18 84.998 18.925 128.79-.21z"}}]})(props);
};
