// THIS FILE IS AUTO GENERATED
import { GenIcon } from '../lib';
export function BiRuler (props) {
  return GenIcon({"tag":"svg","attr":{"viewBox":"0 0 24 24"},"child":[{"tag":"path","attr":{"d":"M20.875,7H3.125C1.953,7,1,7.897,1,9v6c0,1.103,0.953,2,2.125,2h17.75C22.047,17,23,16.103,23,15V9 C23,7.897,22.047,7,20.875,7z M20.875,15H3.125c-0.057,0-0.096-0.016-0.113-0.016c-0.007,0-0.011,0.002-0.012,0.008L2.988,9.046 C2.995,9.036,3.04,9,3.125,9H5v3h2V9h2v4h2V9h2v3h2V9h2v4h2V9h1.875C20.954,9.001,20.997,9.028,21,9.008l0.012,5.946 C21.005,14.964,20.96,15,20.875,15z"}}]})(props);
};
