// THIS FILE IS AUTO GENERATED
var GenIcon = require('../lib').GenIcon
module.exports.GiMimicChest = function GiMimicChest (props) {
  return GenIcon({"tag":"svg","attr":{"viewBox":"0 0 512 512"},"child":[{"tag":"path","attr":{"d":"M165.3 45.79c4.5 9.27 23.4 21.66 44.6 30.54 11.9 5.01 23.8 9 34.6 12.32-16.7-25.45-35.8-49.69-57.3-65.16-12.3-8.9-24.6 15.79-21.9 22.3zM444.7 25.6c-19.6 17.71-41.1 39.17-58.1 64.42 10.8-3.19 22.8-7.03 34.8-11.89 21.3-8.62 40.3-20.76 44.9-29.98 4.8-10.51-12.7-30.59-21.6-22.55zM263.5 84.68c6.6 11 11.5 21.12 17.5 32.92l-19.5-5.3c-4.9-1.3-11.5-3.2-19.1-5.5-.8 4.5-2.6 8.6-5.2 12.1 47.7-1.5 100.3-1.6 157.3 2.3-3.1-3.7-5.3-8.1-6.4-12.9-7.5 2.1-14 3.9-18.8 5.1l-19.6 5c5.6-10.7 11.7-22.65 17.6-32-36.4-2.41-68.7-2.78-103.8-1.72zm-73.7 4.06c-25.7 2-52.2 4.62-79.5 7.82C153 111 157.1 111.6 193.6 120.6c-4.4-5.1-7-11.7-7-18.8 0-4.65 1.2-9.11 3.2-13.06zm17.4 5.92c-1.6 1.85-2.6 4.39-2.6 7.14 0 6.1 4.6 10.6 10.1 10.6 6.9-1.7 10-6 10.1-11.3-5.8-1.95-11.7-4.07-17.6-6.44zm235.8 1.5c.5 1.99.7 4.04.7 6.24 0 8.8-4 16.8-10.3 22.1 15.6 1.6 31.6 3.5 47.9 5.8L459 99.65c-5.2-1.2-10.5-2.41-16-3.49zm-19 .28c-6.3 2.41-12.5 4.56-18.5 6.46.3 5.9 4.8 10.1 10.1 10.1 5.5 0 10.1-4.5 10.1-10.6 0-2.3-.6-4.25-1.7-5.96zM30.62 235.6c26.49-42 57.67-79.7 87.78-117.8C37.98 85.89-4.308 156.6 30.62 235.6zM136.7 124c-34.8 43.6-70.14 86-98.33 133.4 2.71 7.2 5.54 14.5 8.35 21.9 34.39-49.8 67.78-100.9 113.68-146.8-8.2-3-16.2-5.9-23.7-8.5zm150.1 11.9c7.1 18.9 14.6 37.7 28.5 54.9 6.1-18.8 13.1-37.4 24.8-54.4-19.2-.3-36-1-53.3-.5zm-7.1 0c-17.4.2-34.2.6-50.4 1.2 4 30 8.5 60 26.8 87.3 3-29.5 10-58.9 23.6-88.5zm71.5.9c11 16.2 15.7 34.2 20.2 52.3 15-16.7 20.8-33.3 24.2-50-15.1-1.1-29.9-1.8-44.4-2.3zm-128.2.5c-18.7.7-43.6 1.7-43.6 1.7-2.3 2.1-4.5 4.3-6.7 6.4 3.6 17.5 11.1 34.4 21.2 50.9 6.9-19.6 16.1-39.3 29.1-59zm181.2 2.4c10.2 21.2 12.1 64.5 14.2 84.4 19-15.2 25.4-57.2 29.8-80.2-14.9-1.7-29.6-3.1-44-4.2zm50.4 5c8.6 15.6 16.6 31.4 13.3 52.6 17.3-14.7 22.7-30.8 25.8-47.2-13.2-2.1-26.2-3.9-39.1-5.4zm-298.9 18.7c-8.1 8.8-15.8 17.9-23.2 27.2 11.4 15 30.8 17.9 48.4 19.4-14-14.3-21.7-29.9-25.2-46.6zm-28.5 33.9c-9.1 11.6-17.7 23.3-26.1 35.2 9.3 7.7 20.8 11.9 33.6 13.8-4.8-15.8-7.8-32-7.5-49zm283 46.1c-3 20.2-8.3 57.5-16.4 82.1 15.2-1 30.4-2.3 45.6-3.8-6.1-23.6-17.6-59.9-29.2-78.3zm-156.3.6c-13.2 19.5-30.2 57-33.8 80.6 17.4 1.4 34.7 2.4 52.1 3-8.9-24.7-13.4-62.9-18.3-83.6zM108 260.5c-17.86 10.2-25.35 23.9-33.41 37.4 11.97 3.5 24.21 6.9 36.71 10.1-4.5-15.9-6.3-31.7-3.3-47.5zm37.1 0c-12 16.2-25 32.3-29.7 48.5 14.5 4.1 24.6 5.7 38.2 7.8-8.9-18.8-7.9-37.5-8.5-56.3zm318.4 3.2c-3.3 19.5-7.6 38.7-16.1 57.2 12.5-1.3 25.1-2.7 37.6-4.3l1.8-.3c-3-19.4-13.1-36.1-23.3-52.6zm-265.7 3.9c-16.8 17-25.5 33.8-34 50.7 16 2.2 32 4.1 47.9 5.5-9.6-17.1-13.3-36.2-13.9-56.2zm161.3 3.3c-5.9 19.2-14 38.3-23.6 57.1 16.7-.4 33.4-1 50.1-2-7.4-18.7-16-37-26.5-55.1zm-55 .8c-9.7 18.7-17.2 37.4-23.7 56.1 15 .4 30 .5 45 .4-8.5-18.6-16-37.3-21.3-56.5zM51.96 309.8c-3.76 16.6-6.09 34.7-6.86 52.6 14.38 31.4 29.54 64 44.46 90l7.81 3.2 36.83-43.1c.6-26.5 3.2-53 8.1-78.9-32.1-6.9-61.94-14.9-90.34-23.8zm426.14 25.7c-36.3 4.4-72.5 7.6-108.8 9.3v109.3c-35.6 8-74.8 4.3-105.8-.1V345.2c-34.4-1.5-68.8-4.5-103.2-9.4-4.6 23.9-7.2 48.4-8 73.1l56.2 75.7c75.3 7.2 148.2 6.5 220 1.4l52-70.9c.8-26.7.6-53.3-2.4-79.6zm-126.8 10c-23.3.7-46.5.9-69.8.3v92.5c23.3 3.3 45.9 5.5 69.8 1.3zm-34.2 15.7c7.7 0 13.9 6.9 13.9 15.4 0 6.3-3.4 11.9-8.6 14.3L331 423h-27.7l8.6-32.1c-5.2-2.4-8.6-8-8.6-14.3 0-8.5 6.2-15.4 13.8-15.4zM46.78 407.9c.22 10.1-.82 19 2.15 28l14.29 5.8c-5.68-11-11.12-22.4-16.44-33.8zm105.42 31c.3 13.2 1.1 26.3 2.3 39.3 9.9 1.4 19.8 2.7 29.7 3.9zm-18 1.1l-19.4 22.7 21 8.5c-.8-10.3-1.4-20.7-1.6-31.2zm345 7.4l-27 36.8c8.5-.7 16.9-1.5 25.3-2.3.5-11.5 1.1-23 1.7-34.5z"}}]})(props);
};
