// THIS FILE IS AUTO GENERATED
var GenIcon = require('../lib').GenIcon
module.exports.GiGlassShot = function GiGlassShot (props) {
  return GenIcon({"tag":"svg","attr":{"viewBox":"0 0 512 512"},"child":[{"tag":"path","attr":{"d":"M256.53 27.375c-48.39 0-92.187 5.807-124.655 15.53-16.234 4.863-29.648 10.643-39.688 17.75-10.04 7.11-17.5 16.403-17.5 27.75 0 1.172.097 2.312.25 3.44h-.187l1 10.25 32.875 339.592h.03c.536 9.302 7.122 16.108 15.064 21.22 8.123 5.228 18.688 9.385 31.436 12.937 25.497 7.103 59.68 11.406 97.438 11.406 37.76 0 71.92-4.304 97.437-11.406 12.76-3.55 23.357-7.718 31.5-12.938 7.934-5.085 14.494-11.798 15.158-21.03h.03l39.25-339.595.47-4.093c1.244-3.065 1.937-6.327 1.937-9.78 0-11.35-7.46-20.642-17.5-27.75-10.04-7.11-23.453-12.89-39.688-17.75-32.468-9.725-76.265-15.532-124.656-15.532zm0 18.688c46.88 0 89.348 5.775 119.314 14.75 14.983 4.487 26.83 9.84 34.25 15.093 7.42 5.254 9.594 9.582 9.594 12.5 0 2.918-2.175 7.216-9.594 12.47-7.42 5.252-19.267 10.605-34.25 15.093-29.966 8.973-72.434 14.75-119.313 14.75-46.878 0-89.314-5.777-119.28-14.75-14.983-4.49-26.83-9.842-34.25-15.095-7.42-5.253-9.625-9.55-9.625-12.47 0-2.917 2.206-7.245 9.625-12.5 7.42-5.252 19.267-10.605 34.25-15.093 29.966-8.974 72.402-14.75 119.28-14.75zM96.126 118.75c9.51 5.925 21.563 10.876 35.75 15.125 32.468 9.724 76.265 15.53 124.656 15.53 34.11 0 65.91-2.89 93.095-8l-4 76.25c-25.53-4.724-57.128-7.5-91.344-7.5-68.924 0-127.24 11.32-146.686 26.94L96.124 118.75zm319.03 1.094l-13.624 117.72c-4.28-3.6-10.63-6.96-18.686-10.033l9.187-97.186c8.758-3.123 16.52-6.593 23.126-10.5zm-16.405 141.72l-9.53 82.498c-3.96 9.04-10.745 16.984-19.658 23.813L378.72 271c8.322-2.87 15.116-6.047 20.03-9.438zm-288.78.092c21.506 14.704 78 25.22 144.31 25.22 32.79 0 63.15-2.597 88.095-6.97l-5.5 105.188c-73.98 27.105-197.158 14.24-219.094-42.625l-7.81-80.814z"}}]})(props);
};
