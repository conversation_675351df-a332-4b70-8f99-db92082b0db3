// THIS FILE IS AUTO GENERATED
import { GenIcon } from '../lib';
export function BiPaintRoll (props) {
  return GenIcon({"tag":"svg","attr":{"viewBox":"0 0 24 24"},"child":[{"tag":"path","attr":{"d":"M18,2H7C5.897,2,5,2.897,5,4v3c0,1.103,0.897,2,2,2h11c1.103,0,2-0.897,2-2V4C20,2.897,19.103,2,18,2z M7,7V4h11l0.002,3H7 z"}},{"tag":"path","attr":{"d":"M13,15v-2c0-1.103-0.897-2-2-2H4V5C2.897,5,2,5.897,2,7v4c0,1.103,0.897,2,2,2h7v2c-0.553,0-1,0.447-1,1v5 c0,0.553,0.447,1,1,1h2c0.553,0,1-0.447,1-1v-5C14,15.447,13.553,15,13,15z"}}]})(props);
};
