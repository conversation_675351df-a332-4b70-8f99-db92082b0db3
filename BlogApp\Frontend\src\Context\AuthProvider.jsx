import { createContext, useState,useContext } from "react";


const AuthContext = createContext()




 export const AuthProvider = ({children}) => {


    const userInfo = localStorage.getItem('userData')

    const [authUser, setAuthUser] = useState(userInfo?JSON.parse(userInfo):undefined)
    console.log(authUser)

  return (
    <div>

        <AuthContext.Provider  value={{authUser , setAuthUser}}>
            {children}
        </AuthContext.Provider>



    </div>
  )
}

// eslint-disable-next-line react-refresh/only-export-components
export const useAuth = () => useContext(AuthContext)


