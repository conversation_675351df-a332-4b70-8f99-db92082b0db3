// THIS FILE IS AUTO GENERATED
import { GenIcon } from '../lib';
export function BsImage (props) {
  return GenIcon({"tag":"svg","attr":{"viewBox":"0 0 16 16","fill":"currentColor"},"child":[{"tag":"path","attr":{"fillRule":"evenodd","d":"M14.002 2h-12a1 1 0 00-1 1v10a1 1 0 001 1h12a1 1 0 001-1V3a1 1 0 00-1-1zm-12-1a2 2 0 00-2 2v10a2 2 0 002 2h12a2 2 0 002-2V3a2 2 0 00-2-2h-12z","clipRule":"evenodd"}},{"tag":"path","attr":{"d":"M10.648 7.646a.5.5 0 01.577-.093L15.002 9.5V14h-14v-2l2.646-2.354a.5.5 0 01.63-.062l2.66 1.773 3.71-3.71z"}},{"tag":"path","attr":{"fillRule":"evenodd","d":"M4.502 7a1.5 1.5 0 100-3 1.5 1.5 0 000 3z","clipRule":"evenodd"}}]})(props);
};
