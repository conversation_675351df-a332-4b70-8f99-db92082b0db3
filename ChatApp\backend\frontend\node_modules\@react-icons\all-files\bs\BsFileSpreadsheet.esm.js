// THIS FILE IS AUTO GENERATED
import { GenIcon } from '../lib';
export function BsFileSpreadsheet (props) {
  return GenIcon({"tag":"svg","attr":{"viewBox":"0 0 16 16","fill":"currentColor"},"child":[{"tag":"path","attr":{"fillRule":"evenodd","d":"M4 1h8a2 2 0 012 2v10a2 2 0 01-2 2H4a2 2 0 01-2-2V3a2 2 0 012-2zm0 1a1 1 0 00-1 1v10a1 1 0 001 1h8a1 1 0 001-1V3a1 1 0 00-1-1H4z","clipRule":"evenodd"}},{"tag":"path","attr":{"fillRule":"evenodd","d":"M13 6H3V5h10v1zm0 3H3V8h10v1zm0 3H3v-1h10v1z","clipRule":"evenodd"}},{"tag":"path","attr":{"fillRule":"evenodd","d":"M5 14V6h1v8H5zm4 0V6h1v8H9z","clipRule":"evenodd"}}]})(props);
};
