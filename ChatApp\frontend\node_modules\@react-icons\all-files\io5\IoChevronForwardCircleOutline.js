// THIS FILE IS AUTO GENERATED
var GenIcon = require('../lib').GenIcon
module.exports.IoChevronForwardCircleOutline = function IoChevronForwardCircleOutline (props) {
  return GenIcon({"tag":"svg","attr":{"viewBox":"0 0 512 512"},"child":[{"tag":"path","attr":{"fill":"none","strokeMiterlimit":"10","strokeWidth":"32","d":"M64 256c0 106 86 192 192 192s192-86 192-192S362 64 256 64 64 150 64 256z"}},{"tag":"path","attr":{"fill":"none","strokeLinecap":"round","strokeLinejoin":"round","strokeWidth":"32","d":"M216 352l96-96-96-96"}}]})(props);
};
