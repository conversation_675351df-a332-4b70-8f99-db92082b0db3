// THIS FILE IS AUTO GENERATED
var GenIcon = require('../lib').GenIcon
module.exports.GiAbstract056 = function GiAbstract056 (props) {
  return GenIcon({"tag":"svg","attr":{"viewBox":"0 0 512 512"},"child":[{"tag":"path","attr":{"d":"m255.8529,21c-14.7556,0-28.4595,15.2239-40.0971,41.4188-5.6029-11.9545-17.6715-20.2688-31.725-20.2688-19.3574,0-34.9558,15.7453-34.9558,35.1031 0,13.1928 7.1759,24.7006 17.9183,30.6969-52.9037,19.8213-94.6232,56.6835-114.7091,102.225 30.0988-38.0558 81.2521-66.7726 142.4687-78.4312-5.6414,27.1185-10.0162,57.9376-12.6312,91.5031-22.5671,18.2108-41.0564,44.4077-53.0217,75.6406-29.8318.3206-54.3348,31.9662-58.1625,72.85-17.5394-4.9357-34.2381-10.665-49.9374-16.8906 29.1908,25.2737 67.8158,45.5721 112.3592,58.8969 0,42.6364 54.9195,77.2562 122.6408,77.2562s122.4933-34.6199 122.4933-77.2562c44.54-13.2876 83.2976-33.5046 112.5067-58.75-15.6914,6.2115-32.4037,11.9801-49.9375,16.8906-3.7821-40.9577-28.2949-72.6758-58.1625-72.9969-12.0033-31.3287-30.3662-57.6044-53.0221-75.7875-2.6254-33.5202-7.1266-64.2713-12.7779-91.3563 61.2166,11.6586 112.5166,40.3754 142.6154,78.4312-20.1029-45.5805-61.7387-82.57-114.7096-102.3719 10.6906-6.023 18.0659-17.4283 18.0659-30.55 0-19.3578-15.7455-35.1031-35.1034-35.1031-14.053,0-26.2683,8.3143-31.8716,20.2688-11.6847-26.2123-25.4806-41.4188-40.2438-41.4188zm0,42.1532c1.1116,0 2.2809.0966 3.3779.294 2.1968.3942 4.3306,1.1954 6.4625,2.35 3.1974,1.7321 6.3769,4.3024 9.4,7.6375 1.0302,1.1338 1.9317,2.3628 2.9375,3.6719 3.5302,4.582 6.9245,10.2871 10.1346,16.8906-10.5045-1.2966-21.2257-2.0563-32.1654-2.0563-11.0286,0-21.8748.7392-32.4596,2.0563 .5816-1.1005 1.2121-2.1687 1.7625-3.2313 .05-.036-.043-.1117 0-.1463 .893-1.7222 1.8725-3.2728 2.7904-4.8468 .05-.036-.043-.1153 0-.1463 .9198-1.5703 1.8485-2.9885 2.7908-4.4062 .051-.0288-.043-.1189 0-.1463 .9429-1.415 1.8241-2.7078 2.7909-3.9656 1.0039-1.309 2.0563-2.5382 3.0841-3.6719 2.9935-3.302 6.0889-5.9064 9.2534-7.6376 .2962-.1614.5823-.147.8808-.294 1.6859-.8353 3.4169-1.5458 5.1409-1.9093 1.253-.2573 2.5469-.4403 3.8187-.4403zm.1456,62.8625c15.4592,0 30.4743,1.0705 45.0904,3.0844 .8483,2.9054 1.7042,5.7259 2.4971,8.8125 3.4657,13.5124 6.4784,28.4776 8.9591,44.65 1.9844,12.9379 3.5917,26.5352 4.8466,40.8312 .5766,6.5486 1.0562,13.1984 1.4692,19.975 .043.6709.1125,1.382.1456,2.0563 .7156,12.3837 1.0585,25.2973 1.1751,38.3344 .043,2.6074.1455,5.1577.1455,7.7844 0,2.8084.043,5.4411 0,8.225h-14.5403c.043-1.9075.1455-3.8038.1455-5.7281 0-86.3931-22.4876-156.4219-50.0846-156.4219s-49.7904,70.0288-49.7904,156.4219v5.7281h-14.3942c-.043-2.7839 0-5.4166 0-8.225v-7.4906c.1131-13.1433.4561-26.1472 1.175-38.6281 .043-.6738.1081-1.3843.1456-2.0563 .4043-6.7344.9066-13.3281 1.4687-19.8281 .2912-3.3639.694-6.5455 1.0284-9.8406 .3762-3.7073.7435-7.5477 1.175-11.1625 1.6262-13.6553 3.563-26.6309 5.875-38.775 1.1449-6.0235 2.373-11.8828 3.6716-17.4782v-.1463c1.3837-5.9508 2.8553-11.6192 4.4063-17.0375 14.6682-2.0291 29.8647-3.0844 45.3846-3.0844zm-75.7876,128.9562c-.632,14.3553-1.026,29.0632-1.0283,44.0625h-28.7875c7.8255-16.995 17.9638-31.8865 29.8158-44.0625zm151.428,0c11.8867,12.1602 22.1163,27.0218 29.9625,44.0625h-28.9346c0-14.9993-.3899-29.7072-1.0279-44.0625zm-179.0404,65.2125h23.0591 160.6812 23.353c26.0093,0 46.8651,26.495 48.0283,59.925-11.9389,2.6036-24.3244,4.7795-36.8659,6.7562-17.3082-29.3751-62.149-50.2313-114.8557-50.2313-52.6081,0-97.4452,20.797-114.8568,50.0844-12.5372-1.9717-24.7769-4.1346-36.7182-6.7562 1.2271-33.3576 22.2056-59.7781 48.175-59.7781zm103.4,52.7281c59.1837,0 107.0716,18.2465 107.0716,40.8312s-47.8879,40.8312-107.0716,40.8312c-59.1853,0-107.2193-18.2465-107.2193-40.8312s48.034-40.8312 107.2193-40.8312zm-6.4625,14.2469c-32.2204,1.3637-57.4284,12.7403-57.4284,26.5844 0,14.767 28.6065,26.7312 63.8909,26.7312s63.8903-11.9643 63.8903-26.7312-28.6075-26.5844-63.8903-26.5844c-2.2058,0-4.3151-.0907-6.4625,0z"}}]})(props);
};
