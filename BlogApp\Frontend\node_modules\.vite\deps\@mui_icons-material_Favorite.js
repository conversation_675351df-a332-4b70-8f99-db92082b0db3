"use client";
import "./chunk-C6WWHQR7.js";
import "./chunk-EWXSR7EP.js";
import {
  createSvgIcon
} from "./chunk-ZSQNKXJG.js";
import "./chunk-E4MPIUBI.js";
import "./chunk-UG6SC5TT.js";
import "./chunk-ERAZGTZ6.js";
import "./chunk-YWU2A45M.js";
import "./chunk-OTOGCSRI.js";
import "./chunk-64FVIM6J.js";
import {
  require_jsx_runtime
} from "./chunk-OBYCLIUT.js";
import "./chunk-BQYK6RGN.js";
import {
  __toESM
} from "./chunk-G3PMV62Z.js";

// node_modules/@mui/icons-material/esm/Favorite.js
var import_jsx_runtime = __toESM(require_jsx_runtime());
var Favorite_default = createSvgIcon((0, import_jsx_runtime.jsx)("path", {
  d: "m12 21.35-1.45-1.32C5.4 15.36 2 12.28 2 8.5 2 5.42 4.42 3 7.5 3c1.74 0 3.41.81 4.5 2.09C13.09 3.81 14.76 3 16.5 3 19.58 3 22 5.42 22 8.5c0 3.78-3.4 6.86-8.55 11.54z"
}), "Favorite");
export {
  Favorite_default as default
};
//# sourceMappingURL=@mui_icons-material_Favorite.js.map
