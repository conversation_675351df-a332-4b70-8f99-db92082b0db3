// THIS FILE IS AUTO GENERATED
var GenIcon = require('../lib').GenIcon
module.exports.GiAubergine = function GiAubergine (props) {
  return GenIcon({"tag":"svg","attr":{"viewBox":"0 0 512 512"},"child":[{"tag":"path","attr":{"d":"M81.156 19.22c-9.98 17.95-11.653 41.482-8.47 63.624 1.683 11.693 4.66 23.045 9.19 33-10.98-2.333-22.86-6.74-35.25-12.063l19.624 36.47c-14.538-7.018-29.415-5.078-43.594-.313 31.984 20.98 18.398 38.788-3.093 59.157 10.004-2.09 20.2-6.646 29.343-9.72-5.812 19.467-6.532 42.407.22 67.814 53.11 199.91 223.06 260.347 345.312 221.375 80.27-25.585 108.82-99.86 95.343-161.938-6.737-31.04-23.74-59.45-49.874-77.625-26.132-18.175-61.312-25.596-102.094-15.47h-.03c-35.654 8.892-64.878 12.25-86 6.532-21.123-5.718-35.554-19.17-45-49.843-3.635-11.8-8.864-21.88-15.282-30.157-.96-18.358 6.62-32.632 21.125-43.594-20.593-4.363-40.967-6.693-58.688 16.31 5.405-13.443-.73-37.854-12.843-44.905-7.468 25.998-20.424 37.084-36.875 39.344-5.926-8.934-10.95-22.564-13.032-37.032-2.755-19.153-.53-39.567 6.312-51.876L81.156 19.22zm74.125 125.624l3.22 1.312c12.98 6.565 24.084 18.935 30.438 39.563 10.698 34.737 31.14 55.11 57.968 62.374 14.837 4.017 31.185 4.424 49.063 2.437-105.218 87.705 83.948 233.89 175.124 125.657-8.69 36.317-34.728 69.418-82.344 84.594-112.234 35.78-270.73-17.073-321.563-208.405-6.915-26.024-5.132-47.923 1.844-65.28 5.14 1.723 8.945 6.424 10.845 16.06 17.377-13.67 29.664-28.077 31.406-43.968 17.52 0 35.045 2.896 52.564 14.22 1.267-10.08-.185-19.735-8.563-28.563z"}}]})(props);
};
