// THIS FILE IS AUTO GENERATED
var GenIcon = require('../lib').GenIcon
module.exports.GiBouncingSpring = function GiBouncingSpring (props) {
  return GenIcon({"tag":"svg","attr":{"viewBox":"0 0 512 512"},"child":[{"tag":"path","attr":{"d":"M256 23.07c-58.1 0-110.7 6.28-149.4 16.71-19.39 5.22-35.31 11.4-47.09 18.9-5.67 3.61-10.62 7.73-14.25 12.39C38.9 97.64 39.09 142.1 39 152c0 12.2 8.74 21.9 20.51 29.4 7.93 5 17.73 9.5 29.2 13.5-2.7 6.9-3.89 14.5-3.89 22.1 0 11.4 2.67 23 9.22 32-6.55 9-9.22 20.6-9.22 32 0 11.4 2.67 23 9.22 32-6.55 9-9.22 20.6-9.22 32 0 11.4 2.67 23 9.22 32-6.55 9-9.22 20.6-9.22 32 0 14.6 4.31 29.7 15.68 39 45.6 37.4 127.2 47.9 198.6 36.6 35.8-5.7 69.1-17 94.1-34.6 8.7-6.2 16.4-13.2 22.7-21 .8 3.8 1.3 7.8 1.3 12h18c0-11.5-2.4-22.2-6.8-32 4.4-9.8 6.8-20.5 6.8-32s-2.4-22.2-6.8-32c4.4-9.8 6.8-20.5 6.8-32s-2.4-22.2-6.8-32c4.4-9.8 6.8-20.5 6.8-32s-2.4-22.2-6.8-32c3.7-8.3 6-17.1 6.6-26.6 6.6-2.8 12.4-5.8 17.5-9 11.8-7.5 20.5-17.2 20.5-29.4-.2-11.2-2.2-64.7-6.2-80.93-3.7-4.66-8.6-8.78-14.3-12.39-11.8-7.5-27.7-13.68-47.1-18.9-38.7-10.43-91.3-16.71-149.4-16.71zm-9 18.23c29.3.16 59.8 1.15 71.2 2.72-23.9 21.22-54.7 65.38-55.3 90.28-49.3 1.1-88.2-2.6-115.6-7.2 7.8-10.5 19.7-22.9 33.9-35.48 19.2-16.97 42.5-34.6 65.8-50.32zm-31.7.2c-16.4 11.88-32.2 24.34-46 36.65-17.8 15.72-32.4 30.85-41.4 45.25-18.6-4.2-28.18-8.1-28.09-9.1 18.39-29.69 50.29-54.89 85.49-71.06 5.1-.89 16.2-1.45 30-1.74zM57 115.7c.82.6 1.65 1.2 2.51 1.7 11.78 7.5 27.7 13.7 47.09 18.9C145.3 146.7 197.9 153 256 153c58.1 0 110.7-6.3 149.4-16.7 19.4-5.2 35.3-11.4 47.1-18.9.9-.5 1.7-1.1 2.5-1.7.7 12.2.4 29.2 0 36.3 0 3.2-2.9 8.3-12.2 14.2-9.3 5.9-23.8 11.8-42.1 16.7-36.5 9.8-87.9 16.1-144.7 16.1-56.8 0-108.2-6.3-144.7-16.1-18.3-4.9-32.85-10.8-42.12-16.7C52.68 156.7 57 134.4 57 115.7zm49.3 84.5c.1 0 .2.1.3.1 14.1 3.3 32 10.6 39.4 16.7-14.1 5.2-27.1 11.7-38.3 19.6-2.9-5.1-4.9-12.1-4.9-19.6 0-6.2 1.4-12.1 3.5-16.8zm290.6 2.3c4.5 4.5 8.4 9.4 11.5 14.5-4.6 7.6-10.8 14.5-18.5 20.9-12.8-8.5-27.5-15.4-43.5-20.9 23-5.8 33.8-10.7 50.5-14.5zm-84.8 23.7c23.3 5.1 44.6 12.7 61.8 22.8-17.2 10.1-38.5 17.7-61.8 22.8-4.3-.9-8.6-1.7-13-2.4-39.4-6.2-81.9-5.8-119.7 2.1-22.2-5-42.2-12.5-57.7-22.5 15.5-10 35.5-17.5 57.7-22.5 37.8 7.9 80.3 8.3 119.7 2.1 4.4-.7 8.7-1.5 13-2.4zM415.9 237c.8 3.8 1.3 7.8 1.3 12s-.5 8.2-1.3 12c-3.4-4.2-7.3-8.2-11.5-12 4.2-3.8 8.1-7.8 11.5-12zm-26 23.1c7.7 6.4 13.9 13.3 18.5 20.9-4.6 7.6-10.8 14.5-18.5 20.9-12.8-8.5-27.5-15.4-43.5-20.9 16-5.5 30.7-12.4 43.5-20.9zm-282.2 1.3c11.2 7.9 24.2 14.4 38.3 19.6-14.1 5.2-27.1 11.7-38.3 19.6-2.9-5.1-4.9-12.1-4.9-19.6 0-7.5 2-14.5 4.9-19.6zm204.4 28.8c23.3 5.1 44.6 12.7 61.8 22.8-17.2 10.1-38.5 17.7-61.8 22.8-4.3-.9-8.6-1.7-13-2.4-39.4-6.2-81.9-5.8-119.7 2.1-22.2-5-42.2-12.5-57.7-22.5 15.5-10 35.5-17.5 57.7-22.5 37.8 7.9 80.3 8.3 119.7 2.1 4.4-.7 8.7-1.5 13-2.4zM415.9 301c.8 3.8 1.3 7.8 1.3 12s-.5 8.2-1.3 12c-3.4-4.2-7.3-8.2-11.5-12 4.2-3.8 8.1-7.8 11.5-12zm-26 23.1c7.7 6.4 13.9 13.3 18.5 20.9-4.6 7.6-10.8 14.5-18.5 20.9-12.8-8.5-27.5-15.4-43.5-20.9 16-5.5 30.7-12.4 43.5-20.9zm-282.2 1.3c11.2 7.9 24.2 14.4 38.3 19.6-14.1 5.2-27.1 11.7-38.3 19.6-2.9-5.1-4.9-12.1-4.9-19.6 0-7.5 2-14.5 4.9-19.6zm204.4 28.8c23.3 5.1 44.6 12.7 61.8 22.8-21 12.3-48.2 21.1-77.6 25.8-62.2 9.9-133.7.5-174.6-25.8 15.5-10 35.5-17.5 57.7-22.5 37.8 7.9 80.3 8.3 119.7 2.1 4.4-.7 8.7-1.5 13-2.4zM415.9 365c.8 3.8 1.3 7.8 1.3 12s-.5 8.2-1.3 12c-3.4-4.2-7.3-8.2-11.5-12 4.2-3.8 8.1-7.8 11.5-12zm-26 23.1c7.7 6.4 13.9 13.3 18.5 20.9-5.9 9.8-14.5 18.5-25.6 26.2-21.8 15.4-52.7 26.2-86.5 31.6-67.5 10.7-145.9-1.2-184.4-32.8-5.1-4.2-9.1-14.3-9.1-25 0-7.5 2-14.5 4.9-19.6 46.7 33 123.6 41.9 191.4 31.2 34.3-5.5 66.1-16.1 90.8-32.5z"}}]})(props);
};
