(()=>{var A;function U(G){return U=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(H){return typeof H}:function(H){return H&&typeof Symbol=="function"&&H.constructor===Symbol&&H!==Symbol.prototype?"symbol":typeof H},U(G)}function x(G,H){var J=Object.keys(G);if(Object.getOwnPropertySymbols){var X=Object.getOwnPropertySymbols(G);H&&(X=X.filter(function(Z){return Object.getOwnPropertyDescriptor(G,Z).enumerable})),J.push.apply(J,X)}return J}function Q(G){for(var H=1;H<arguments.length;H++){var J=arguments[H]!=null?arguments[H]:{};H%2?x(Object(J),!0).forEach(function(X){E(G,X,J[X])}):Object.getOwnPropertyDescriptors?Object.defineProperties(G,Object.getOwnPropertyDescriptors(J)):x(Object(J)).forEach(function(X){Object.defineProperty(G,X,Object.getOwnPropertyDescriptor(J,X))})}return G}function E(G,H,J){if(H=N(H),H in G)Object.defineProperty(G,H,{value:J,enumerable:!0,configurable:!0,writable:!0});else G[H]=J;return G}function N(G){var H=z(G,"string");return U(H)=="symbol"?H:String(H)}function z(G,H){if(U(G)!="object"||!G)return G;var J=G[Symbol.toPrimitive];if(J!==void 0){var X=J.call(G,H||"default");if(U(X)!="object")return X;throw new TypeError("@@toPrimitive must return a primitive value.")}return(H==="string"?String:Number)(G)}var W=Object.defineProperty,XG=function G(H,J){for(var X in J)W(H,X,{get:J[X],enumerable:!0,configurable:!0,set:function Z(Y){return J[X]=function(){return Y}}})},D={lessThanXSeconds:{one:{standalone:"manje od 1 sekunde",withPrepositionAgo:"manje od 1 sekunde",withPrepositionIn:"manje od 1 sekundu"},dual:"manje od {{count}} sekunde",other:"manje od {{count}} sekundi"},xSeconds:{one:{standalone:"1 sekunda",withPrepositionAgo:"1 sekunde",withPrepositionIn:"1 sekundu"},dual:"{{count}} sekunde",other:"{{count}} sekundi"},halfAMinute:"pola minute",lessThanXMinutes:{one:{standalone:"manje od 1 minute",withPrepositionAgo:"manje od 1 minute",withPrepositionIn:"manje od 1 minutu"},dual:"manje od {{count}} minute",other:"manje od {{count}} minuta"},xMinutes:{one:{standalone:"1 minuta",withPrepositionAgo:"1 minute",withPrepositionIn:"1 minutu"},dual:"{{count}} minute",other:"{{count}} minuta"},aboutXHours:{one:{standalone:"oko 1 sat",withPrepositionAgo:"oko 1 sat",withPrepositionIn:"oko 1 sat"},dual:"oko {{count}} sata",other:"oko {{count}} sati"},xHours:{one:{standalone:"1 sat",withPrepositionAgo:"1 sat",withPrepositionIn:"1 sat"},dual:"{{count}} sata",other:"{{count}} sati"},xDays:{one:{standalone:"1 dan",withPrepositionAgo:"1 dan",withPrepositionIn:"1 dan"},dual:"{{count}} dana",other:"{{count}} dana"},aboutXWeeks:{one:{standalone:"oko 1 tjedan",withPrepositionAgo:"oko 1 tjedan",withPrepositionIn:"oko 1 tjedan"},dual:"oko {{count}} tjedna",other:"oko {{count}} tjedana"},xWeeks:{one:{standalone:"1 tjedan",withPrepositionAgo:"1 tjedan",withPrepositionIn:"1 tjedan"},dual:"{{count}} tjedna",other:"{{count}} tjedana"},aboutXMonths:{one:{standalone:"oko 1 mjesec",withPrepositionAgo:"oko 1 mjesec",withPrepositionIn:"oko 1 mjesec"},dual:"oko {{count}} mjeseca",other:"oko {{count}} mjeseci"},xMonths:{one:{standalone:"1 mjesec",withPrepositionAgo:"1 mjesec",withPrepositionIn:"1 mjesec"},dual:"{{count}} mjeseca",other:"{{count}} mjeseci"},aboutXYears:{one:{standalone:"oko 1 godinu",withPrepositionAgo:"oko 1 godinu",withPrepositionIn:"oko 1 godinu"},dual:"oko {{count}} godine",other:"oko {{count}} godina"},xYears:{one:{standalone:"1 godina",withPrepositionAgo:"1 godine",withPrepositionIn:"1 godinu"},dual:"{{count}} godine",other:"{{count}} godina"},overXYears:{one:{standalone:"preko 1 godinu",withPrepositionAgo:"preko 1 godinu",withPrepositionIn:"preko 1 godinu"},dual:"preko {{count}} godine",other:"preko {{count}} godina"},almostXYears:{one:{standalone:"gotovo 1 godinu",withPrepositionAgo:"gotovo 1 godinu",withPrepositionIn:"gotovo 1 godinu"},dual:"gotovo {{count}} godine",other:"gotovo {{count}} godina"}},S=function G(H,J,X){var Z,Y=D[H];if(typeof Y==="string")Z=Y;else if(J===1)if(X!==null&&X!==void 0&&X.addSuffix)if(X.comparison&&X.comparison>0)Z=Y.one.withPrepositionIn;else Z=Y.one.withPrepositionAgo;else Z=Y.one.standalone;else if(J%10>1&&J%10<5&&String(J).substr(-2,1)!=="1")Z=Y.dual.replace("{{count}}",String(J));else Z=Y.other.replace("{{count}}",String(J));if(X!==null&&X!==void 0&&X.addSuffix)if(X.comparison&&X.comparison>0)return"za "+Z;else return"prije "+Z;return Z};function $(G){return function(){var H=arguments.length>0&&arguments[0]!==void 0?arguments[0]:{},J=H.width?String(H.width):G.defaultWidth,X=G.formats[J]||G.formats[G.defaultWidth];return X}}var M={full:"EEEE, d. MMMM y.",long:"d. MMMM y.",medium:"d. MMM y.",short:"dd. MM. y."},R={full:"HH:mm:ss (zzzz)",long:"HH:mm:ss z",medium:"HH:mm:ss",short:"HH:mm"},L={full:"{{date}} 'u' {{time}}",long:"{{date}} 'u' {{time}}",medium:"{{date}} {{time}}",short:"{{date}} {{time}}"},V={date:$({formats:M,defaultWidth:"full"}),time:$({formats:R,defaultWidth:"full"}),dateTime:$({formats:L,defaultWidth:"full"})},j={lastWeek:function G(H){switch(H.getDay()){case 0:return"'pro\u0161lu nedjelju u' p";case 3:return"'pro\u0161lu srijedu u' p";case 6:return"'pro\u0161lu subotu u' p";default:return"'pro\u0161li' EEEE 'u' p"}},yesterday:"'ju\u010Der u' p",today:"'danas u' p",tomorrow:"'sutra u' p",nextWeek:function G(H){switch(H.getDay()){case 0:return"'idu\u0107u nedjelju u' p";case 3:return"'idu\u0107u srijedu u' p";case 6:return"'idu\u0107u subotu u' p";default:return"'pro\u0161li' EEEE 'u' p"}},other:"P"},w=function G(H,J,X,Z){var Y=j[H];if(typeof Y==="function")return Y(J);return Y};function I(G){return function(H,J){var X=J!==null&&J!==void 0&&J.context?String(J.context):"standalone",Z;if(X==="formatting"&&G.formattingValues){var Y=G.defaultFormattingWidth||G.defaultWidth,B=J!==null&&J!==void 0&&J.width?String(J.width):Y;Z=G.formattingValues[B]||G.formattingValues[Y]}else{var C=G.defaultWidth,q=J!==null&&J!==void 0&&J.width?String(J.width):G.defaultWidth;Z=G.values[q]||G.values[C]}var T=G.argumentCallback?G.argumentCallback(H):H;return Z[T]}}var _={narrow:["pr.n.e.","AD"],abbreviated:["pr. Kr.","po. Kr."],wide:["Prije Krista","Poslije Krista"]},F={narrow:["1.","2.","3.","4."],abbreviated:["1. kv.","2. kv.","3. kv.","4. kv."],wide:["1. kvartal","2. kvartal","3. kvartal","4. kvartal"]},v={narrow:["1.","2.","3.","4.","5.","6.","7.","8.","9.","10.","11.","12."],abbreviated:["sij","velj","o\u017Eu","tra","svi","lip","srp","kol","ruj","lis","stu","pro"],wide:["sije\u010Danj","velja\u010Da","o\u017Eujak","travanj","svibanj","lipanj","srpanj","kolovoz","rujan","listopad","studeni","prosinac"]},P={narrow:["1.","2.","3.","4.","5.","6.","7.","8.","9.","10.","11.","12."],abbreviated:["sij","velj","o\u017Eu","tra","svi","lip","srp","kol","ruj","lis","stu","pro"],wide:["sije\u010Dnja","velja\u010De","o\u017Eujka","travnja","svibnja","lipnja","srpnja","kolovoza","rujna","listopada","studenog","prosinca"]},f={narrow:["N","P","U","S","\u010C","P","S"],short:["ned","pon","uto","sri","\u010Det","pet","sub"],abbreviated:["ned","pon","uto","sri","\u010Det","pet","sub"],wide:["nedjelja","ponedjeljak","utorak","srijeda","\u010Detvrtak","petak","subota"]},k={narrow:{am:"AM",pm:"PM",midnight:"pono\u0107",noon:"podne",morning:"ujutro",afternoon:"popodne",evening:"nave\u010Der",night:"no\u0107u"},abbreviated:{am:"AM",pm:"PM",midnight:"pono\u0107",noon:"podne",morning:"ujutro",afternoon:"popodne",evening:"nave\u010Der",night:"no\u0107u"},wide:{am:"AM",pm:"PM",midnight:"pono\u0107",noon:"podne",morning:"ujutro",afternoon:"poslije podne",evening:"nave\u010Der",night:"no\u0107u"}},b={narrow:{am:"AM",pm:"PM",midnight:"pono\u0107",noon:"podne",morning:"ujutro",afternoon:"popodne",evening:"nave\u010Der",night:"no\u0107u"},abbreviated:{am:"AM",pm:"PM",midnight:"pono\u0107",noon:"podne",morning:"ujutro",afternoon:"popodne",evening:"nave\u010Der",night:"no\u0107u"},wide:{am:"AM",pm:"PM",midnight:"pono\u0107",noon:"podne",morning:"ujutro",afternoon:"poslije podne",evening:"nave\u010Der",night:"no\u0107u"}},h=function G(H,J){var X=Number(H);return X+"."},m={ordinalNumber:h,era:I({values:_,defaultWidth:"wide"}),quarter:I({values:F,defaultWidth:"wide",argumentCallback:function G(H){return H-1}}),month:I({values:v,defaultWidth:"wide",formattingValues:P,defaultFormattingWidth:"wide"}),day:I({values:f,defaultWidth:"wide"}),dayPeriod:I({values:b,defaultWidth:"wide",formattingValues:k,defaultFormattingWidth:"wide"})};function O(G){return function(H){var J=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{},X=J.width,Z=X&&G.matchPatterns[X]||G.matchPatterns[G.defaultMatchWidth],Y=H.match(Z);if(!Y)return null;var B=Y[0],C=X&&G.parsePatterns[X]||G.parsePatterns[G.defaultParseWidth],q=Array.isArray(C)?c(C,function(K){return K.test(B)}):y(C,function(K){return K.test(B)}),T;T=G.valueCallback?G.valueCallback(q):q,T=J.valueCallback?J.valueCallback(T):T;var JG=H.slice(B.length);return{value:T,rest:JG}}}function y(G,H){for(var J in G)if(Object.prototype.hasOwnProperty.call(G,J)&&H(G[J]))return J;return}function c(G,H){for(var J=0;J<G.length;J++)if(H(G[J]))return J;return}function g(G){return function(H){var J=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{},X=H.match(G.matchPattern);if(!X)return null;var Z=X[0],Y=H.match(G.parsePattern);if(!Y)return null;var B=G.valueCallback?G.valueCallback(Y[0]):Y[0];B=J.valueCallback?J.valueCallback(B):B;var C=H.slice(Z.length);return{value:B,rest:C}}}var d=/^(\d+)\./i,p=/\d+/i,u={narrow:/^(pr\.n\.e\.|AD)/i,abbreviated:/^(pr\.\s?Kr\.|po\.\s?Kr\.)/i,wide:/^(Prije Krista|prije nove ere|Poslije Krista|nova era)/i},l={any:[/^pr/i,/^(po|nova)/i]},i={narrow:/^[1234]/i,abbreviated:/^[1234]\.\s?kv\.?/i,wide:/^[1234]\. kvartal/i},n={any:[/1/i,/2/i,/3/i,/4/i]},s={narrow:/^(10|11|12|[123456789])\./i,abbreviated:/^(sij|velj|(ožu|ozu)|tra|svi|lip|srp|kol|ruj|lis|stu|pro)/i,wide:/^((siječanj|siječnja|sijecanj|sijecnja)|(veljača|veljače|veljaca|veljace)|(ožujak|ožujka|ozujak|ozujka)|(travanj|travnja)|(svibanj|svibnja)|(lipanj|lipnja)|(srpanj|srpnja)|(kolovoz|kolovoza)|(rujan|rujna)|(listopad|listopada)|(studeni|studenog)|(prosinac|prosinca))/i},r={narrow:[/1/i,/2/i,/3/i,/4/i,/5/i,/6/i,/7/i,/8/i,/9/i,/10/i,/11/i,/12/i],abbreviated:[/^sij/i,/^velj/i,/^(ožu|ozu)/i,/^tra/i,/^svi/i,/^lip/i,/^srp/i,/^kol/i,/^ruj/i,/^lis/i,/^stu/i,/^pro/i],wide:[/^sij/i,/^velj/i,/^(ožu|ozu)/i,/^tra/i,/^svi/i,/^lip/i,/^srp/i,/^kol/i,/^ruj/i,/^lis/i,/^stu/i,/^pro/i]},o={narrow:/^[npusčc]/i,short:/^(ned|pon|uto|sri|(čet|cet)|pet|sub)/i,abbreviated:/^(ned|pon|uto|sri|(čet|cet)|pet|sub)/i,wide:/^(nedjelja|ponedjeljak|utorak|srijeda|(četvrtak|cetvrtak)|petak|subota)/i},a={narrow:[/^s/i,/^m/i,/^t/i,/^w/i,/^t/i,/^f/i,/^s/i],any:[/^su/i,/^m/i,/^tu/i,/^w/i,/^th/i,/^f/i,/^sa/i]},e={any:/^(am|pm|ponoc|ponoć|(po)?podne|navecer|navečer|noću|poslije podne|ujutro)/i},t={any:{am:/^a/i,pm:/^p/i,midnight:/^pono/i,noon:/^pod/i,morning:/jutro/i,afternoon:/(poslije\s|po)+podne/i,evening:/(navece|naveče)/i,night:/(nocu|noću)/i}},GG={ordinalNumber:g({matchPattern:d,parsePattern:p,valueCallback:function G(H){return parseInt(H,10)}}),era:O({matchPatterns:u,defaultMatchWidth:"wide",parsePatterns:l,defaultParseWidth:"any"}),quarter:O({matchPatterns:i,defaultMatchWidth:"wide",parsePatterns:n,defaultParseWidth:"any",valueCallback:function G(H){return H+1}}),month:O({matchPatterns:s,defaultMatchWidth:"wide",parsePatterns:r,defaultParseWidth:"wide"}),day:O({matchPatterns:o,defaultMatchWidth:"wide",parsePatterns:a,defaultParseWidth:"any"}),dayPeriod:O({matchPatterns:e,defaultMatchWidth:"any",parsePatterns:t,defaultParseWidth:"any"})},HG={code:"hr",formatDistance:S,formatLong:V,formatRelative:w,localize:m,match:GG,options:{weekStartsOn:1,firstWeekContainsDate:1}};window.dateFns=Q(Q({},window.dateFns),{},{locale:Q(Q({},(A=window.dateFns)===null||A===void 0?void 0:A.locale),{},{hr:HG})})})();

//# debugId=2CC5AFDA00ED4EB864756E2164756E21
