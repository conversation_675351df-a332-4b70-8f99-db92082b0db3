// THIS FILE IS AUTO GENERATED
var GenIcon = require('../lib').GenIcon
module.exports.GiBerriesBowl = function GiBerriesBowl (props) {
  return GenIcon({"tag":"svg","attr":{"viewBox":"0 0 512 512"},"child":[{"tag":"path","attr":{"d":"M285.4 61.74c-12-.1-22.7 6.5-26.9 17.14-2.4 5.9-2.3 12.28-.4 18.17 2.6 2.49 4.9 5.25 6.9 8.05 4.2-1.2 8.6-1.9 13.2-1.9 8.8 0 17.1 2.5 24.1 6.8 3.5-7 8.9-12.96 15.6-17.4.3-11.66-7.3-23.3-20.2-28.43-4.1-1.62-8.2-2.4-12.3-2.43zm-65.7 37.75c-11.8 0-22 5.91-25.9 15.31-5.4 13.1 2.5 30.1 19.6 37.2 17.2 7.1 34.8.7 40.2-12.4 5.5-13-2.4-30-19.6-37.1-4.8-2.1-9.7-2.99-14.3-3.01zM346.8 102c-15.6 0-27.6 9.6-30.2 21.4 4.6 6.6 7.4 14.6 7.9 23.1 5.5 4.9 13.3 8.1 22.3 8.1 17.7 0 30.8-12.3 30.8-26.3S364.5 102 346.8 102zm-68.6 19.2c-2 0-4 .2-5.9.6 1.9 8.1 1.4 16.6-2 24.7-3.3 7.9-8.7 14.1-15.4 18.5 5.1 7.3 13.6 12 23.3 12 15.8 0 28.4-12.5 28.4-27.9 0-15.4-12.6-27.9-28.4-27.9zm-138.3 15.1c-5.1-.1-10.2 1.2-14.7 3.8-14.4 8.5-19.1 28.8-9 46.2 10.1 17.3 30.2 23.2 44.6 14.8 14.5-8.4 19.1-28.8 9-46.1-6.9-12-18.6-18.5-29.9-18.7zm263.4 22.4c-18.8-.3-34.5 11-37.6 26.8-3.5 18 10.4 36.9 32.8 41.2 22.4 4.4 42.4-7.9 45.9-25.9s-10.4-36.9-32.8-41.2c-2.8-.5-5.6-.8-8.3-.9zm-211 1.3c-.4.1-.7.1-1 .2 5.2 19.8-.8 40.9-16.5 53.1 6.3 6.2 15.2 10.1 25.2 10.1 19.4 0 34.5-14.6 34.5-32.1 0-6.8-2.3-13.2-6.2-18.4-7.2 0-14.7-1.3-21.8-4.3-5.3-2.2-10.1-5.1-14.2-8.6zm129.2 6.1c-.1.1-.2.1-.3.2-2.6 6.3-6.6 12-11.6 16.5-.3 1.3-.4 2.6-.4 4 0 13.3 11.4 24.4 26.3 24.4 5.4 0 10.3-1.5 14.4-4-2.8-7.9-3.6-16.4-1.9-25.1.7-3.4 1.7-6.6 3-9.7-1.4.1-2.8.2-4.2.2-9.2 0-17.9-2.4-25.3-6.5zm-69.2 21c.1 1.4.2 2.8.2 4.2 0 12.9-5.1 24.6-13.5 33.4 9 1.4 17.2 5 24.1 10.1 5.6-13.4 16.9-25 31.3-32.1-1.2-2.9-2.1-6-2.6-9.1-.2-.2-.4-.3-.6-.5-4.2 1.2-8.5 1.9-13 1.9-9.5 0-18.5-2.9-25.9-7.9zM98.41 202.5c-10.6 0-21.75 6.7-27.74 18.9-5.44 11.1-4.72 22.8.36 31.2 11.47 2.6 22.99 5 34.57 7.1 5.3-3.2 10-8.1 13.2-14.7 5.3-10.9 4.7-22.5-.3-30.8-4.1-2.8-8-6.1-11.4-10-2.7-1.1-5.7-1.6-8.69-1.7zm206.59 15c-16.1 6.9-26.5 20.3-27.6 34 3.1 5.9 4.9 12.4 5.2 19.4.7 1.1 1.6 2.1 2.4 3.1 26.1-.7 51.8-2.7 77.1-5.9 6.4-8.2 9.1-18 7.2-27.3-1.5-6.9-5.4-13-11-17.7-6.7 3.9-14.5 6.1-22.8 6.1-11.7 0-22.5-4.4-30.5-11.7zm-146.9 3.9c-5.9 1.5-12.1 1.8-18.1.9 1.4 10-.3 20.8-5.1 30.7-1.9 3.9-4.2 7.4-6.9 10.6 14.9 2.4 29.8 4.3 44.7 5.9.7-.2 1.3-.5 2-.7 1.1-.4 2.3-.7 3.4-1 1-9.5 5-18.2 11-25.3-.2-.8-.4-1.6-.7-2.4-12.2-2.6-22.9-9.3-30.3-18.7zm292.4 6c-13 14.2-34.1 21.1-55.4 17-1.7-.3-3.3-.7-4.9-1.2-.3 1.9-.5 3.8-.5 5.8 0 5.2 1.2 10.2 3.2 14.6 22.2-3.8 44-8.5 65.2-14.2v-.4c0-8.2-2.9-15.8-7.6-21.6zM230.2 242c-16.4 0-29.6 10.1-33.4 23.2 4.9.1 9.9.7 14.9 1.9l.8.2.8.3c3.5 1.7 6.8 3.6 9.9 5.8 13.8.7 27.7 1 41.5.9V273c0-16.8-15-31-34.5-31zM31.29 260.9c9.75 21.4 26.04 48.9 45.97 74.9 25.44 33.4 57.04 64.5 86.84 79.4l5 2.5v5.6c0 3 1.2 5.6 5.1 8.9 3.9 3.4 10.5 6.8 18.7 9.6 16.6 5.5 40 8.5 63.2 8.5 23.2 0 46.6-3 63.2-8.5 8.2-2.8 14.8-6.2 18.7-9.6 3.9-3.3 5.1-5.9 5.1-8.9v-5.6l5-2.5c29.8-14.9 61.4-46 86.9-79.4 19.7-25.8 35.9-53 45.7-74.3-74.9 22.5-157.3 32.6-241.2 30.4 4.3 8.4 5.8 18.4 2.7 28-10.4 31.5-42.7 52.9-76.8 67.2l-5.9 2.5-4.3-4.7c-11.3-12.5-15.3-30.4-15.7-48.1-.4-17.6 2.9-35.2 9.6-48.2v-.1c.6-1.2 1.3-2.4 2.1-3.5-40.3-5.2-80.53-13.2-119.91-24.1zm163.41 22.3c-.4.1-.7.1-1.1.1-4.6.3-9 1.2-13 2.6-7.9 2.7-13.7 7.5-15.5 11-4.7 8.9-7.9 24.5-7.6 39.5.3 12 3 23.3 7.4 31.1 28.9-13.2 53.2-32.1 60.1-53.2 1.8-5.3.8-10.6-2.6-16.1-2.3-3.7-5.7-7.2-9.8-10.1-10.3 19.1-19.3 37.6-20.7 47.8l-17.8-2.4c2.1-15.2 10.9-32.5 20.6-50.3z"}}]})(props);
};
