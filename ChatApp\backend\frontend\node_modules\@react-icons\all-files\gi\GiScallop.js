// THIS FILE IS AUTO GENERATED
var GenIcon = require('../lib').GenIcon
module.exports.GiScallop = function GiScallop (props) {
  return GenIcon({"tag":"svg","attr":{"viewBox":"0 0 512 512"},"child":[{"tag":"path","attr":{"d":"M259.844 73.406l1.625 214.47-18.69.155-1.655-214.342C206.358 75.24 172.012 82.588 141 95.78c36.116 61.6 59.493 126.474 75.813 196.5l-18.22 4.25C182.46 227.29 159.504 163.924 124 103.78c-37.016 19.19-67.986 47.49-87.156 84.97 57.884 24.66 105.126 67.86 140.937 118.688l-15.28 10.75c-34.284-48.66-79.092-89.328-133.28-112.344-8.57 22.082-13.345 46.943-13.345 74.594 95.028 17.855 145.516 75.937 151.406 92 3.752 10.228-27.905 21.074-27.905 38.156 0 12.34 25.52 20.537 59.668 24.67-3.846-4.94-7.694-10.374-11.59-16.31l15.625-10.255c9.802 14.937 18.996 25.865 27.354 32.73 8.358 6.864 15.493 9.632 22.423 9.68 13.862.094 31.592-12.316 53.723-42.776l15.12 10.984c-4.31 5.93-8.553 11.385-12.76 16.35 36.362-4.006 64.125-12.375 64.125-25.074 0-17.92-35.487-28.412-33.72-39.97 2.31-15.09 55.528-74.91 156.626-90.187 0-28.807-5.284-54.622-14.72-77.437-57.322 22.41-104.478 64.46-140.22 115.188l-15.28-10.75c37.145-52.72 86.607-97.216 147.47-121.344-20.457-37.87-53.207-66.233-92.064-85.03-36.426 60.947-59.84 125.186-76.22 195.467l-18.186-4.25c16.523-70.893 40.278-136.5 77.156-198.78-32.42-12.835-68.166-19.55-104.062-20.094z","fillRule":"evenodd"}}]})(props);
};
