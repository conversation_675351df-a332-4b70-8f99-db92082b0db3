// THIS FILE IS AUTO GENERATED
var GenIcon = require('../lib').GenIcon
module.exports.BiBath = function BiBath (props) {
  return GenIcon({"tag":"svg","attr":{"viewBox":"0 0 24 24"},"child":[{"tag":"path","attr":{"d":"M21,10H7V7c0-1.103,0.897-2,2-2s2,0.897,2,2h2c0-2.206-1.794-4-4-4S5,4.794,5,7v3H3c-0.553,0-1,0.447-1,1v2 c0,2.606,1.674,4.823,4,5.65V22h2v-3h8v3h2v-3.35c2.326-0.827,4-3.044,4-5.65v-2C22,10.447,21.553,10,21,10z M20,13 c0,2.206-1.794,4-4,4H8c-2.206,0-4-1.794-4-4v-1h16V13z"}}]})(props);
};
