// THIS FILE IS AUTO GENERATED
var GenIcon = require('../lib').GenIcon
module.exports.<PERSON><PERSON><PERSON><PERSON><PERSON> = function <PERSON><PERSON><PERSON><PERSON><PERSON> (props) {
  return GenIcon({"tag":"svg","attr":{"viewBox":"0 0 512 512"},"child":[{"tag":"path","attr":{"d":"M274.416 16.05c-13.347 2.888-35.426 19.905-27.508 51.26 9.262 36.67-75.52 36.213-41.63-24.058C183.646 58.92 167.17 55.08 161.8 23.148c-22.233 27.904-22.512 73.433-9.892 101.918 16.77 37.852-29.304 44.6-49.738 8.97 22.28 116.072-46.865 107.22-30.28 71.462 17.254-37.196 27.745-77.21-42.12-94.23 38.313 20.83 32.962 105.25.93 70.77-19.66 36.393 28.903 46.923 30.32 73.558 1.145 21.548-18.805 21.214-33.916 7.468 1.39 33.63 26.576 36.938 39.054 54.836 27.306 39.166-14.327 68.647-39.308 46.163 4.337 38.776 26.144 64.053 54.576 84.212C92.46 304.957 131.467 205.87 178.576 150.22c25.333-29.928 53.268-47.503 80.98-51.273 3.463-.47 6.913-.712 10.34-.726 21.053-.087 41.165 8.386 57.596 24.544 36.358-6.065 45.915-44.68 13.487-70.46 1.828 64.344-67.158 21.956-66.564-36.255zm104.12 3.993c-6.333.11-13.55 2.39-21.448 7.477 35.57 10.134 37.05 44.868 14.775 81.2 49.277-26.507 40.868-89.265 6.672-88.677zm-272.333 3.12C69.545 22.65 54.15 79.77 92.807 103.84c-11.116-31.483 5.797-61.904 27.802-77.71-5.083-1.97-9.905-2.9-14.407-2.964zm163.87 93.696c-2.633.034-5.3.237-8 .605-21.6 2.94-45.96 17.336-69.233 44.828-44.866 53.002-84.617 154.055-93.772 303.228 8.6-2.924 16.847-2.806 23.96-.305 5.363 1.885 10.034 4.62 14.37 7.623 5.066-72.095 13.248-143.837 30.592-214.016l.473.117-6.81 147.94.25.01c-2.782 26.187-4.914 52.54-6.655 79.008 7.317 4.79 14.645 8.118 23.81 8.262 9.88.156 22.915-3.568 40.856-15.93-16.913-92.954-21.596-183.95 3.97-268.86-.17-3.325.088-6.7.848-10.056 2.023-8.927 7.973-17.458 17.41-22.906l.003-.002 52.53-30.29 17.848-11.81c-12.794-12.023-27.02-17.653-42.45-17.447zm68.37 27.91c-3.02.173-4.934.53-6.95 1.69h-.007l-80.004 46.136c-5.395 3.116-7.58 6.683-8.523 10.847-.945 4.166-.275 9.192 1.894 13.848 4.337 9.313 13.166 15.533 22.32 13.08l6.924-1.852 57.933 100.294.036.063c8.55 15.338 10.57 28.975 8.63 40.664-1.936 11.688-7.168 20.96-11.337 29.63-4.17 8.67-7.43 16.607-7.702 26.346-.27 9.734 2.376 21.82 12.088 38.724l.01.02 17.71 30.353H493.27V326.61c-12.267-6.23-24.566-5.693-39.182-7.292-16.313-1.783-35.21-8.355-49.412-31.873l-.04-.066-49.236-84.71-53.31 30.78-9.346-16.186 53.264-30.752-.1-.172 7.2-4.895c3.907-2.655 5.554-5.617 6.26-9.334.704-3.715.116-8.287-1.74-12.64-3.708-8.707-11.835-15.106-19.18-14.7h-.005z"}}]})(props);
};
