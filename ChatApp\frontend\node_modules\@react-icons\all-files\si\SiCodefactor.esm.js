// THIS FILE IS AUTO GENERATED
import { GenIcon } from '../lib';
export function SiCodefactor (props) {
  return GenIcon({"tag":"svg","attr":{"role":"img","viewBox":"0 0 24 24"},"child":[{"tag":"title","attr":{},"child":[]},{"tag":"path","attr":{"d":"M5.37 2.65A2.64 2.64 0 0 1 2.75 5.3 2.64 2.64 0 0 1 .12 2.65 2.64 2.64 0 0 1 2.75 0a2.64 2.64 0 0 1 2.62 2.65zm0 9.35a2.64 2.64 0 0 1-2.62 2.65A2.64 2.64 0 0 1 .12 12a2.64 2.64 0 0 1 2.63-2.65A2.64 2.64 0 0 1 5.37 12zm0 9.35A2.64 2.64 0 0 1 2.75 24a2.64 2.64 0 0 1-2.63-2.65 2.64 2.64 0 0 1 2.63-2.65 2.64 2.64 0 0 1 2.62 2.65zM11.31 0A2.64 2.64 0 0 0 8.7 2.65 2.64 2.64 0 0 0 11.3 5.3h9.94a2.64 2.64 0 0 0 2.63-2.65A2.64 2.64 0 0 0 21.25 0h-9.94zM8.7 12a2.64 2.64 0 0 1 2.62-2.65H17A2.64 2.64 0 0 1 19.6 12 2.64 2.64 0 0 1 17 14.65H11.3A2.64 2.64 0 0 1 8.7 12z"}}]})(props);
};
