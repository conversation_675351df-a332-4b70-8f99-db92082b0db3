/*! 🌼 daisyUI 5.0.42 - MIT License */ @layer utilities{.drawer{grid-auto-columns:max-content auto;width:100%;display:grid;position:relative}.drawer-content{grid-row-start:1;grid-column-start:2;min-width:0}.drawer-side{pointer-events:none;visibility:hidden;inset-inline-start:0;z-index:1;overscroll-behavior:contain;opacity:0;width:100%;transition:opacity .2s ease-out .1s allow-discrete,visibility .3s ease-out .1s allow-discrete;grid-template-rows:repeat(1,minmax(0,1fr));grid-template-columns:repeat(1,minmax(0,1fr));grid-row-start:1;grid-column-start:1;place-items:flex-start start;height:100dvh;display:grid;position:fixed;top:0;overflow:hidden;&>.drawer-overlay{cursor:pointer;background-color:oklch(0% 0 0/.4);place-self:stretch stretch;position:sticky;top:0}&>*{grid-row-start:1;grid-column-start:1}&>:not(.drawer-overlay){will-change:transform;transition:translate .3s ease-out;translate:-100%;[dir=rtl] &{translate:100%}}}.drawer-toggle{appearance:none;opacity:0;width:0;height:0;position:fixed;&:checked{&~.drawer-side{pointer-events:auto;visibility:visible;opacity:1;overflow-y:auto;&>:not(.drawer-overlay){translate:0%}}}&:focus-visible~.drawer-content label.drawer-button{outline-offset:2px;outline:2px solid}}.drawer-end{grid-auto-columns:auto max-content;&>.drawer-toggle{&~.drawer-content{grid-column-start:1}&~.drawer-side{grid-column-start:2;justify-items:end}&~.drawer-side>:not(.drawer-overlay){translate:100%;[dir=rtl] &{translate:-100%}}&:checked~.drawer-side>:not(.drawer-overlay){translate:0%}}}.drawer-open{&>.drawer-side{overflow-y:auto}&>.drawer-toggle{display:none;&~.drawer-side{pointer-events:auto;visibility:visible;overscroll-behavior:auto;opacity:1;width:auto;display:block;position:sticky;&>.drawer-overlay{cursor:default;background-color:#0000}&>:not(.drawer-overlay){translate:0%;[dir=rtl] &{translate:0%}}}&:checked~.drawer-side{pointer-events:auto;visibility:visible}}}@media (width>=640px){.sm\:drawer{grid-auto-columns:max-content auto;width:100%;display:grid;position:relative}.sm\:drawer-content{grid-row-start:1;grid-column-start:2;min-width:0}.sm\:drawer-side{pointer-events:none;visibility:hidden;inset-inline-start:0;z-index:1;overscroll-behavior:contain;opacity:0;width:100%;transition:opacity .2s ease-out .1s allow-discrete,visibility .3s ease-out .1s allow-discrete;grid-template-rows:repeat(1,minmax(0,1fr));grid-template-columns:repeat(1,minmax(0,1fr));grid-row-start:1;grid-column-start:1;place-items:flex-start start;height:100dvh;display:grid;position:fixed;top:0;overflow:hidden;&>.drawer-overlay{cursor:pointer;background-color:oklch(0% 0 0/.4);place-self:stretch stretch;position:sticky;top:0}&>*{grid-row-start:1;grid-column-start:1}&>:not(.drawer-overlay){will-change:transform;transition:translate .3s ease-out;translate:-100%;[dir=rtl] &{translate:100%}}}.sm\:drawer-toggle{appearance:none;opacity:0;width:0;height:0;position:fixed;&:checked{&~.drawer-side{pointer-events:auto;visibility:visible;opacity:1;overflow-y:auto;&>:not(.drawer-overlay){translate:0%}}}&:focus-visible~.drawer-content label.drawer-button{outline-offset:2px;outline:2px solid}}.sm\:drawer-end{grid-auto-columns:auto max-content;&>.drawer-toggle{&~.drawer-content{grid-column-start:1}&~.drawer-side{grid-column-start:2;justify-items:end}&~.drawer-side>:not(.drawer-overlay){translate:100%;[dir=rtl] &{translate:-100%}}&:checked~.drawer-side>:not(.drawer-overlay){translate:0%}}}.sm\:drawer-open{&>.drawer-side{overflow-y:auto}&>.drawer-toggle{display:none;&~.drawer-side{pointer-events:auto;visibility:visible;overscroll-behavior:auto;opacity:1;width:auto;display:block;position:sticky;&>.drawer-overlay{cursor:default;background-color:#0000}&>:not(.drawer-overlay){translate:0%;[dir=rtl] &{translate:0%}}}&:checked~.drawer-side{pointer-events:auto;visibility:visible}}}}@media (width>=768px){.md\:drawer{grid-auto-columns:max-content auto;width:100%;display:grid;position:relative}.md\:drawer-content{grid-row-start:1;grid-column-start:2;min-width:0}.md\:drawer-side{pointer-events:none;visibility:hidden;inset-inline-start:0;z-index:1;overscroll-behavior:contain;opacity:0;width:100%;transition:opacity .2s ease-out .1s allow-discrete,visibility .3s ease-out .1s allow-discrete;grid-template-rows:repeat(1,minmax(0,1fr));grid-template-columns:repeat(1,minmax(0,1fr));grid-row-start:1;grid-column-start:1;place-items:flex-start start;height:100dvh;display:grid;position:fixed;top:0;overflow:hidden;&>.drawer-overlay{cursor:pointer;background-color:oklch(0% 0 0/.4);place-self:stretch stretch;position:sticky;top:0}&>*{grid-row-start:1;grid-column-start:1}&>:not(.drawer-overlay){will-change:transform;transition:translate .3s ease-out;translate:-100%;[dir=rtl] &{translate:100%}}}.md\:drawer-toggle{appearance:none;opacity:0;width:0;height:0;position:fixed;&:checked{&~.drawer-side{pointer-events:auto;visibility:visible;opacity:1;overflow-y:auto;&>:not(.drawer-overlay){translate:0%}}}&:focus-visible~.drawer-content label.drawer-button{outline-offset:2px;outline:2px solid}}.md\:drawer-end{grid-auto-columns:auto max-content;&>.drawer-toggle{&~.drawer-content{grid-column-start:1}&~.drawer-side{grid-column-start:2;justify-items:end}&~.drawer-side>:not(.drawer-overlay){translate:100%;[dir=rtl] &{translate:-100%}}&:checked~.drawer-side>:not(.drawer-overlay){translate:0%}}}.md\:drawer-open{&>.drawer-side{overflow-y:auto}&>.drawer-toggle{display:none;&~.drawer-side{pointer-events:auto;visibility:visible;overscroll-behavior:auto;opacity:1;width:auto;display:block;position:sticky;&>.drawer-overlay{cursor:default;background-color:#0000}&>:not(.drawer-overlay){translate:0%;[dir=rtl] &{translate:0%}}}&:checked~.drawer-side{pointer-events:auto;visibility:visible}}}}@media (width>=1024px){.lg\:drawer{grid-auto-columns:max-content auto;width:100%;display:grid;position:relative}.lg\:drawer-content{grid-row-start:1;grid-column-start:2;min-width:0}.lg\:drawer-side{pointer-events:none;visibility:hidden;inset-inline-start:0;z-index:1;overscroll-behavior:contain;opacity:0;width:100%;transition:opacity .2s ease-out .1s allow-discrete,visibility .3s ease-out .1s allow-discrete;grid-template-rows:repeat(1,minmax(0,1fr));grid-template-columns:repeat(1,minmax(0,1fr));grid-row-start:1;grid-column-start:1;place-items:flex-start start;height:100dvh;display:grid;position:fixed;top:0;overflow:hidden;&>.drawer-overlay{cursor:pointer;background-color:oklch(0% 0 0/.4);place-self:stretch stretch;position:sticky;top:0}&>*{grid-row-start:1;grid-column-start:1}&>:not(.drawer-overlay){will-change:transform;transition:translate .3s ease-out;translate:-100%;[dir=rtl] &{translate:100%}}}.lg\:drawer-toggle{appearance:none;opacity:0;width:0;height:0;position:fixed;&:checked{&~.drawer-side{pointer-events:auto;visibility:visible;opacity:1;overflow-y:auto;&>:not(.drawer-overlay){translate:0%}}}&:focus-visible~.drawer-content label.drawer-button{outline-offset:2px;outline:2px solid}}.lg\:drawer-end{grid-auto-columns:auto max-content;&>.drawer-toggle{&~.drawer-content{grid-column-start:1}&~.drawer-side{grid-column-start:2;justify-items:end}&~.drawer-side>:not(.drawer-overlay){translate:100%;[dir=rtl] &{translate:-100%}}&:checked~.drawer-side>:not(.drawer-overlay){translate:0%}}}.lg\:drawer-open{&>.drawer-side{overflow-y:auto}&>.drawer-toggle{display:none;&~.drawer-side{pointer-events:auto;visibility:visible;overscroll-behavior:auto;opacity:1;width:auto;display:block;position:sticky;&>.drawer-overlay{cursor:default;background-color:#0000}&>:not(.drawer-overlay){translate:0%;[dir=rtl] &{translate:0%}}}&:checked~.drawer-side{pointer-events:auto;visibility:visible}}}}@media (width>=1280px){.xl\:drawer{grid-auto-columns:max-content auto;width:100%;display:grid;position:relative}.xl\:drawer-content{grid-row-start:1;grid-column-start:2;min-width:0}.xl\:drawer-side{pointer-events:none;visibility:hidden;inset-inline-start:0;z-index:1;overscroll-behavior:contain;opacity:0;width:100%;transition:opacity .2s ease-out .1s allow-discrete,visibility .3s ease-out .1s allow-discrete;grid-template-rows:repeat(1,minmax(0,1fr));grid-template-columns:repeat(1,minmax(0,1fr));grid-row-start:1;grid-column-start:1;place-items:flex-start start;height:100dvh;display:grid;position:fixed;top:0;overflow:hidden;&>.drawer-overlay{cursor:pointer;background-color:oklch(0% 0 0/.4);place-self:stretch stretch;position:sticky;top:0}&>*{grid-row-start:1;grid-column-start:1}&>:not(.drawer-overlay){will-change:transform;transition:translate .3s ease-out;translate:-100%;[dir=rtl] &{translate:100%}}}.xl\:drawer-toggle{appearance:none;opacity:0;width:0;height:0;position:fixed;&:checked{&~.drawer-side{pointer-events:auto;visibility:visible;opacity:1;overflow-y:auto;&>:not(.drawer-overlay){translate:0%}}}&:focus-visible~.drawer-content label.drawer-button{outline-offset:2px;outline:2px solid}}.xl\:drawer-end{grid-auto-columns:auto max-content;&>.drawer-toggle{&~.drawer-content{grid-column-start:1}&~.drawer-side{grid-column-start:2;justify-items:end}&~.drawer-side>:not(.drawer-overlay){translate:100%;[dir=rtl] &{translate:-100%}}&:checked~.drawer-side>:not(.drawer-overlay){translate:0%}}}.xl\:drawer-open{&>.drawer-side{overflow-y:auto}&>.drawer-toggle{display:none;&~.drawer-side{pointer-events:auto;visibility:visible;overscroll-behavior:auto;opacity:1;width:auto;display:block;position:sticky;&>.drawer-overlay{cursor:default;background-color:#0000}&>:not(.drawer-overlay){translate:0%;[dir=rtl] &{translate:0%}}}&:checked~.drawer-side{pointer-events:auto;visibility:visible}}}}@media (width>=1536px){.\32 xl\:drawer{grid-auto-columns:max-content auto;width:100%;display:grid;position:relative}.\32 xl\:drawer-content{grid-row-start:1;grid-column-start:2;min-width:0}.\32 xl\:drawer-side{pointer-events:none;visibility:hidden;inset-inline-start:0;z-index:1;overscroll-behavior:contain;opacity:0;width:100%;transition:opacity .2s ease-out .1s allow-discrete,visibility .3s ease-out .1s allow-discrete;grid-template-rows:repeat(1,minmax(0,1fr));grid-template-columns:repeat(1,minmax(0,1fr));grid-row-start:1;grid-column-start:1;place-items:flex-start start;height:100dvh;display:grid;position:fixed;top:0;overflow:hidden;&>.drawer-overlay{cursor:pointer;background-color:oklch(0% 0 0/.4);place-self:stretch stretch;position:sticky;top:0}&>*{grid-row-start:1;grid-column-start:1}&>:not(.drawer-overlay){will-change:transform;transition:translate .3s ease-out;translate:-100%;[dir=rtl] &{translate:100%}}}.\32 xl\:drawer-toggle{appearance:none;opacity:0;width:0;height:0;position:fixed;&:checked{&~.drawer-side{pointer-events:auto;visibility:visible;opacity:1;overflow-y:auto;&>:not(.drawer-overlay){translate:0%}}}&:focus-visible~.drawer-content label.drawer-button{outline-offset:2px;outline:2px solid}}.\32 xl\:drawer-end{grid-auto-columns:auto max-content;&>.drawer-toggle{&~.drawer-content{grid-column-start:1}&~.drawer-side{grid-column-start:2;justify-items:end}&~.drawer-side>:not(.drawer-overlay){translate:100%;[dir=rtl] &{translate:-100%}}&:checked~.drawer-side>:not(.drawer-overlay){translate:0%}}}.\32 xl\:drawer-open{&>.drawer-side{overflow-y:auto}&>.drawer-toggle{display:none;&~.drawer-side{pointer-events:auto;visibility:visible;overscroll-behavior:auto;opacity:1;width:auto;display:block;position:sticky;&>.drawer-overlay{cursor:default;background-color:#0000}&>:not(.drawer-overlay){translate:0%;[dir=rtl] &{translate:0%}}}&:checked~.drawer-side{pointer-events:auto;visibility:visible}}}}}