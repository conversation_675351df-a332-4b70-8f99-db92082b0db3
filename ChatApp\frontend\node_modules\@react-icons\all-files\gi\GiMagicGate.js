// THIS FILE IS AUTO GENERATED
var GenIcon = require('../lib').GenIcon
module.exports.GiMagicGate = function GiMagicGate (props) {
  return GenIcon({"tag":"svg","attr":{"viewBox":"0 0 512 512"},"child":[{"tag":"path","attr":{"d":"M43.72 13.844L18.155 64.97c27.97 13.658 58.89 24.446 91.53 32.436l-89.53 393.438H123.5l41.25-373.438 1.03-9.437 9.44 1.186c52.986 6.706 108.616 6.364 161.56-.594l9.44-1.218 1.06 9.47 41.25 374.03h103.314L402.156 96.75C433.81 88.828 463.8 78.25 491 64.97l-25.53-51.126c-112.422 54.9-309.332 54.9-421.75 0zm286 114.344c-14.14 1.644-28.428 2.836-42.783 3.562-21.804 10.4-32.54 26.267-35.312 54.406 27.25-30.575 55.034-36.127 81.188-29.906l-3.094-28.063zm-147.44.53l-10.03 90.97c8.553-37.432 27.04-73.21 54.875-87.625-15.038-.6-30.02-1.7-44.844-3.344zm64.97 13.313c-34.87 17.008-72.704 109.172-38.156 162.907-23.665 3.18-37.152-6.345-43.406-26l-4.313 39.125c9.257 32.188 30.822 44 56.844 44-12.7 21.47-47.05 23.77-62.94 11.188l-1.655 15.188c10.71 28.704 37.626 43.795 74.344 42.093-18.365 23.685-30.833 24.123-63.064 14 39.07 48.74 103.567 18.582 119.125-31.53 13.896 22.31-2.982 63.977-44.374 67.75 82.914 13.18 105.38-62.936 57.125-124.406 21.172 2.414 47.277 11.1 62.314 38.312l-10.813-98.22c-25.328-31.6-75.52-19.695-66.56-46.373 4.55-13.55 34.584-16.545 42.03 11.093 12.44-41.053-37.255-49.697-52.563-29.78 16.442-29.484 50.19-27.943 69.125-7.157l-5.03-45.5c-33.345-13.155-72.762 8.446-89.188 52.655-12.81-21.176-21.274-66.546 1.156-89.344zm92.03 255.22c9.187 42.963-7.44 81.635-44.81 93l75.217.25L363 429.875c-7.54-14.486-14.178-24.364-23.72-32.625zm-192.467 52.78l-4.5 40.845h67.843c-27.745-8.302-48.728-18.543-63.344-40.844z"}}]})(props);
};
