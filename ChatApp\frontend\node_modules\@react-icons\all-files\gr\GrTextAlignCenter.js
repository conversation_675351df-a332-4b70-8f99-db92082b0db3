// THIS FILE IS AUTO GENERATED
var GenIcon = require('../lib').GenIcon
module.exports.GrTextAlignCenter = function GrTextAlignCenter (props) {
  return GenIcon({"tag":"svg","attr":{"viewBox":"0 0 24 24"},"child":[{"tag":"rect","attr":{"x":"0.46","y":"3.06","width":"23.08","height":"2.18"}},{"tag":"rect","attr":{"x":"4.1","y":"8.29","width":"15.81","height":"2.18"}},{"tag":"rect","attr":{"x":"0.46","y":"13.53","width":"23.08","height":"2.18"}},{"tag":"rect","attr":{"x":"4.1","y":"18.76","width":"15.81","height":"2.18"}}]})(props);
};
