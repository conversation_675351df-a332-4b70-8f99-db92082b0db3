// THIS FILE IS AUTO GENERATED
var GenIcon = require('../lib').GenIcon
module.exports.BiLineChart = function BiLineChart (props) {
  return GenIcon({"tag":"svg","attr":{"viewBox":"0 0 24 24"},"child":[{"tag":"path","attr":{"d":"M3,3v17c0,0.553,0.447,1,1,1h17v-2H5V3H3z"}},{"tag":"path","attr":{"d":"M15.293,14.707c0.391,0.391,1.023,0.391,1.414,0l5-5l-1.414-1.414L16,12.586l-2.293-2.293 c-0.391-0.391-1.023-0.391-1.414,0l-5,5l1.414,1.414L13,12.414L15.293,14.707z"}}]})(props);
};
