// import React, { useEffect } from 'react'
import { createContext, useState ,useContext} from "react";




const PostContext=createContext()




  export const PostProvider = ({children}) => {

    const [userPost, setUserPost] = useState([])
// console.log(userPost)
   

    
  return (
    <div>
 <PostContext.Provider   value={{userPost, setUserPost}}>
    {children}
 </PostContext.Provider>

    </div>
  )
}

// eslint-disable-next-line react-refresh/only-export-components
export const usePost= () => useContext(PostContext)

