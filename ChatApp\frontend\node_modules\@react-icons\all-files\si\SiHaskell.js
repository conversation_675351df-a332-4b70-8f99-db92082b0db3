// THIS FILE IS AUTO GENERATED
var GenIcon = require('../lib').GenIcon
module.exports.SiHaskell = function SiHaskell (props) {
  return GenIcon({"tag":"svg","attr":{"viewBox":"0 0 24 24","role":"img"},"child":[{"tag":"title","attr":{},"child":[]},{"tag":"path","attr":{"d":"M.006 20.47L5.649 12 .006 3.531h4.235l5.674 8.47-5.674 8.468zm5.643 0L11.29 12 5.65 3.531h4.266l11.253 16.938h-4.224l-3.535-5.34-3.495 5.34zM19.3 15.525l-1.877-2.827h6.571v2.826zm-2.826-4.235l-1.877-2.827h9.387v2.827z"}}]})(props);
};
