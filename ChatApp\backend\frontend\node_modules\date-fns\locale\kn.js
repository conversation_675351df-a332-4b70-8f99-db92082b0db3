import { formatDistance } from "./kn/_lib/formatDistance.js";
import { formatLong } from "./kn/_lib/formatLong.js";
import { formatRelative } from "./kn/_lib/formatRelative.js";
import { localize } from "./kn/_lib/localize.js";
import { match } from "./kn/_lib/match.js";

/**
 * @category Locales
 * @summary Kannada locale (India).
 * @language Kannada
 * @iso-639-2 kan
 * <AUTHOR> [@developergouli](https://github.com/developergouli)
 */
export const kn = {
  code: "kn",
  formatDistance: formatDistance,
  formatLong: formatLong,
  formatRelative: formatRelative,
  localize: localize,
  match: match,
  options: {
    weekStartsOn: 1 /* Monday */,
    firstWeekContainsDate: 1,
  },
};

// Fallback for modularized imports:
export default kn;
