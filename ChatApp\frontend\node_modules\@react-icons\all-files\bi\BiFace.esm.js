// THIS FILE IS AUTO GENERATED
import { GenIcon } from '../lib';
export function BiFace (props) {
  return GenIcon({"tag":"svg","attr":{"viewBox":"0 0 24 24"},"child":[{"tag":"path","attr":{"d":"M12,2C6.486,2,2,6.486,2,12s4.486,10,10,10s10-4.486,10-10S17.514,2,12,2z M12,4c3.213,0,5.982,1.908,7.254,4.648 c-0.302-0.139-0.581-0.301-0.895-0.498c-0.409-0.258-0.873-0.551-1.46-0.772C16.23,7.123,15.499,7,14.665,7 S13.1,7.123,12.431,7.377C11.844,7.6,11.38,7.893,10.959,8.158c-0.378,0.237-0.703,0.443-1.103,0.594C9.41,8.921,8.926,9,8.33,9 C7.735,9,7.251,8.921,6.806,8.752c-0.4-0.151-0.728-0.358-1.106-0.598C5.539,8.053,5.36,7.946,5.18,7.841C6.587,5.542,9.113,4,12,4 z M12,20c-4.411,0-8-3.589-8-8c0-0.81,0.123-1.59,0.348-2.327c0.094,0.058,0.185,0.11,0.283,0.173 c0.411,0.26,0.876,0.554,1.466,0.776C6.766,10.877,7.496,11,8.33,11c0.833,0,1.564-0.123,2.235-0.377 c0.587-0.223,1.051-0.516,1.472-0.781c0.378-0.237,0.703-0.443,1.103-0.595C13.585,9.079,14.069,9,14.665,9s1.08,0.079,1.525,0.248 c0.399,0.15,0.725,0.356,1.114,0.602c0.409,0.258,0.873,0.551,1.46,0.773c0.363,0.138,0.748,0.229,1.153,0.291 C19.966,11.271,20,11.631,20,12C20,16.411,16.411,20,12,20z"}},{"tag":"circle","attr":{"cx":"8.5","cy":"13.5","r":"1.5"}},{"tag":"circle","attr":{"cx":"15.5","cy":"13.5","r":"1.5"}}]})(props);
};
