// THIS FILE IS AUTO GENERATED
var GenIcon = require('../lib').GenIcon
module.exports.BiPointer = function BiPointer (props) {
  return GenIcon({"tag":"svg","attr":{"viewBox":"0 0 24 24"},"child":[{"tag":"path","attr":{"d":"M20.978,13.21c0.084-0.39-0.072-0.792-0.396-1.024l-14-10c-0.321-0.23-0.75-0.249-1.09-0.047 C5.15,2.34,4.961,2.724,5.007,3.117l2,17c0.047,0.398,0.327,0.73,0.713,0.843C7.813,20.987,7.906,21,8,21 c0.295,0,0.581-0.131,0.774-0.367l3.612-4.416l3.377,5.46l1.701-1.052l-3.357-5.428l6.089-1.218 C20.587,13.902,20.895,13.6,20.978,13.21z M12.304,13.52c-0.227,0.045-0.431,0.168-0.578,0.347l-3.008,3.677L7.257,5.127 l10.283,7.345L12.304,13.52z"}}]})(props);
};
