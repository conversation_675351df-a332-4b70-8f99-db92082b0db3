// THIS FILE IS AUTO GENERATED
var GenIcon = require('../lib').GenIcon
module.exports.GiGooeyEyedSun = function GiGooeyEyedSun (props) {
  return GenIcon({"tag":"svg","attr":{"viewBox":"0 0 512 512"},"child":[{"tag":"path","attr":{"d":"M273.75 22.156c-17.335.085-32.11 13.28-33.813 30.907-1.01 10.464 2.8 20.284 9.625 27.25l10.313 25.874-3.72 38.72-6.53 10.155c-18.054.746-34.962 5.93-49.656 14.5l-10.282-4.78-35.5-45.595-3.5-18.593c1.68-7.84.025-16.34-5.282-23.156-5.653-7.263-14.174-10.978-22.72-10.813-5.845.113-11.717 2.038-16.686 5.906-12.23 9.523-14.428 27.177-4.906 39.407 5.307 6.818 13.143 10.522 21.156 10.813l17.22 7.97 35.405 45.5L177 187.5c-9.233 9.476-16.74 20.644-22 33l-11.03 2.406L88.25 208l-13.938-12.625c-3.39-7.27-9.836-13.05-18.187-15.28-2.808-.752-5.616-1.05-8.375-.94-11.955.484-22.747 8.647-26 20.814-4.003 14.973 4.9 30.37 19.875 34.374 8.453 2.26 17.028.392 23.625-4.344l18.156-3.938 55.72 14.875 8.25 7.5c-.614 4.652-.907 9.4-.907 14.22 0 14.202 2.753 27.74 7.75 40.156l-6.032 8.937-38.313 21.094-35.78 1c-4.75-1.87-9.86-2.84-15-2.813-6.47.037-13 1.632-19.064 4.97-19.405 10.682-26.493 35.094-15.81 54.5 10.68 19.406 35.092 26.463 54.5 15.78 10.816-5.953 17.784-16.17 20-27.405l19.81-29.438 38.626-21.28 11.28-.313c10.212 13.403 23.482 24.33 38.783 31.75l1.75 11.47-18.376 54.655-13.406 13.03c-7.494 2.914-13.678 9-16.438 17.22-4.935 14.692 2.963 30.595 17.656 35.53 14.694 4.935 30.627-2.994 35.563-17.688 2.788-8.303 1.446-16.993-2.876-23.875l-2.78-18.28 18.342-54.688 8.53-8.283c6.162 1.09 12.502 1.657 18.97 1.657 6.773 0 13.39-.647 19.813-1.844l8.717 8.47 25.594 76.217-1.75 11.563c-3.5 5.666-4.566 12.788-2.28 19.594 4.078 12.14 17.234 18.67 29.375 14.594 12.14-4.078 18.672-17.234 14.594-29.375-2.278-6.78-7.383-11.81-13.564-14.22l-8.812-8.593-25.47-75.72 1.845-12c12.603-6.298 23.81-15 33-25.5l12.467 1.376 45.282 30.03 14.25 25.502c1.122 9.742 6.376 18.942 15.186 24.78 15.845 10.502 37.187 6.158 47.688-9.687 10.5-15.844 6.188-37.216-9.656-47.717-5.942-3.938-12.684-5.778-19.313-5.72-3.266.03-6.506.534-9.625 1.47l-26.22-2.907-47.31-31.344-6.22-11.125c6.47-13.832 10.094-29.25 10.094-45.53 0-1.99-.05-3.973-.156-5.938l10.28-8.19 56.626-11.31 18.03 5.124c6.26 5.044 14.63 7.387 23.126 5.687 15.2-3.04 25.073-17.83 22.03-33.03-2.66-13.3-14.32-22.506-27.374-22.563-1.865-.008-3.756.152-5.656.532-8.525 1.705-15.384 7.12-19.22 14.218l-14.655 11.687-56.5 11.313-12.813-3.657c-3.345-9.405-7.913-18.224-13.593-26.218l4.124-12.406 41.22-40.25 17.748-5.437c8.06.885 16.434-1.738 22.688-7.845 11.09-10.83 11.297-28.597.47-39.688-5.754-5.89-13.467-8.71-21.095-8.437-6.73.24-13.395 2.893-18.594 7.97-6.237 6.09-9.017 14.377-8.342 22.436l-5.875 17.625-41.282 40.314-12 3.656c-12.1-9.6-26.326-16.648-41.842-20.344l-4.438-11.187 3.78-39.19 14.783-22.968c8.045-5.524 13.674-14.426 14.687-24.906 1.817-18.8-11.95-35.526-30.75-37.343-1.175-.114-2.344-.162-3.5-.157zM253.53 212.22c.92-.017 1.833-.017 2.75 0 29.392.494 57.826 16.55 81.25 44.78l4.97 6-4.97 5.97c-25.895 30.954-54.887 47.158-84.06 46.81-29.175-.346-56.753-17.266-79.814-47.092l-4.47-5.813 4.564-5.72c23.272-29.172 51.33-44.46 79.78-44.936zm13.095 19.75c13.107 4.84 22.47 17.46 22.47 32.25 0 13.52-7.83 25.205-19.19 30.81 15.592-4.102 31.748-14.45 47.876-32.06-16.222-17.543-33.73-27.82-51.155-31zm-24.156.124c-16.672 3.24-33.372 13.162-49.157 31 15.107 17.754 30.93 28.328 46.843 32.25-11.698-5.48-19.78-17.354-19.78-31.125 0-14.66 9.166-27.193 22.093-32.126z"}}]})(props);
};
