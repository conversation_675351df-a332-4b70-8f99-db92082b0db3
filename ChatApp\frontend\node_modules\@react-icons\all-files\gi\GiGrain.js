// THIS FILE IS AUTO GENERATED
var GenIcon = require('../lib').GenIcon
module.exports.GiGrain = function GiGrain (props) {
  return GenIcon({"tag":"svg","attr":{"viewBox":"0 0 512 512"},"child":[{"tag":"path","attr":{"d":"M161.021 23.56c-33.581.017-61.171 3.348-88.61 8.921-17.004-6.939-33.71-11.495-41.27 1.18-7.755 13-.298 25.252 12.243 37.315C16.961 176.419 23.36 323.974 37.281 420.24c-10.227 10.317-16.86 20.99-10.172 31.194 8.874 13.538 24.589 11.055 42.342 3.994 47.522 8.267 100.352 10.029 154.365 7.802 12.925-47.395 36.719-95.082 59.352-130.25 16.112-22.799 31.442-44.715 50.035-61.974-11.969-76.414-29.46-148.738-56.082-207.172 7.74-9.542 11.027-19.274 4.947-29.467-6.655-11.157-20.402-8.94-35.195-3.523-29.143-5.066-58.17-7.3-85.852-7.285zm-16.355 70.546c16.406 18.518 26.656 36.925 30.427 52.338l-13.625 29.983-25.326-23.194c-2.09-15.382.74-35.81 8.524-59.127zm59.798 40.83c2.124 16.568 2.256 31.597.723 44.41l-27.605 37.801-5.936-29.343 18.168-39.98c4.441-4.312 9.32-8.62 14.65-12.887zm-84.045 24.209l37.924 33.368 5.786 28.601-39.69-23.646c-6.777-11.435-20.586-35.652-20.119-43.252 15.217 4.34 13.568 3.98 16.1 4.929zm104.73 31.74c1.362 22.872-1.842 40.924-7.91 53.684l-29.628 22.168-6.277-31.039 24.875-34.06c5.742-3.67 12.06-7.269 18.94-10.752zm-112.19 22.964c4.943.367 9.692.878 14.228 1.521l40.682 24.238 6.097 30.143-31.023-7.457c-11.134-9.151-21.831-25.208-29.984-48.445zm124.043 40.892c-6.873 38.457-22.52 60.066-38.758 64.557l-7.567-37.406 29.946-22.409a207.182 207.182 0 0 1 16.379-4.742zm101.445 36.297c3.464-1.324 7.972-2.059 12.65-2.062 10.422 0 18.87 3.557 18.871 7.945.002 4.389-8.447 7.947-18.87 7.947-9.797-.003-17.96-3.161-18.792-7.271-11.324 12.604-23.028 28.276-34.054 45.209-4.24 6.509-8.363 13.222-12.348 20.015 9.174.608 15.935 3.936 15.94 7.846-.001 4.388-8.451 7.946-18.874 7.945a43.256 43.256 0 0 1-5.765-.388c-15.36 28.273-27.56 56.712-33.207 77.927 15.825 3.992 32.039 7.123 48.447 9.479l-.002.02c0 4.388 8.45 7.945 18.871 7.945 3.762-.002 7.436-.478 10.55-1.366a3.435 3.435 0 0 0-.12.87c0 4.388 8.45 7.945 18.87 7.945 10.423 0 18.871-3.557 18.872-7.945-.007-.714-.242-1.423-.7-2.11a592.75 592.75 0 0 0 39.723-.498 3.538 3.538 0 0 0-.263 1.31c0 4.389 8.45 7.946 18.87 7.946 10.422 0 18.871-3.557 18.872-7.945-.006-1.388-.876-2.751-2.522-3.953a584.77 584.77 0 0 0 22.723-2.836c3.317 2.536 9.65 4.112 16.527 4.113 10.425 0 18.875-3.558 18.873-7.947-.006-1.389-.876-2.752-2.523-3.955.051-11.342-3.426-27.164-10.037-44.473-6.863-17.968-16.742-37.59-27.897-56.426a42.718 42.718 0 0 1-6.625.516c-10.423 0-18.872-3.559-18.87-7.947.01-3.928 6.836-7.263 16.06-7.848-13.387-20.802-27.843-39.76-40.547-53.287-6.722-7.158-12.984-12.79-17.996-16.28-5.012-3.49-8.798-4.221-16.454-1.677-7.656 2.544-19.344 12.102-28.253 21.236zm-197.436-14.84l36.01 8.655 7.469 36.923c-16.307.307-37.748-13.351-58.192-43.464 5.083-.92 9.987-1.614 14.713-2.114zm60.457 55.746c2.917 17.949 6.504 35.498 11.006 52.602l-13.437 3.816c-4.671-17.697-8.37-35.774-11.36-54.173zm131.37 72.213c10.423 0 18.872 3.559 18.87 7.948 0 4.388-8.449 7.945-18.87 7.945a44.295 44.295 0 0 1-3.36-.133c1.22 1.068 1.857 2.238 1.863 3.424 0 4.388-8.45 7.945-18.873 7.945-10.421 0-18.87-3.557-18.87-7.945s8.449-7.945 18.87-7.945c1.127.002 2.252.046 3.36.132-1.22-1.068-1.857-2.238-1.864-3.423-.002-4.39 8.45-7.948 18.874-7.948zm93.41 15.451c10.423 0 18.872 3.559 18.87 7.948 0 4.388-8.449 7.945-18.87 7.945-10.423 0-18.872-3.557-18.873-7.945-.002-4.39 8.448-7.948 18.873-7.948zm-208.795 54.483c-10.424 0-18.873 3.558-18.871 7.947 0 4.388 8.45 7.945 18.87 7.945a43.931 43.931 0 0 0 4.227-.21c1.488 3.843 9.37 6.666 18.618 6.667 10.424 0 18.874-3.558 18.873-7.947-.001-4.388-8.451-7.945-18.873-7.945-1.423.003-2.84.074-4.225.21-1.488-3.843-9.37-6.666-18.62-6.667z"}}]})(props);
};
