// THIS FILE IS AUTO GENERATED
var GenIcon = require('../lib').GenIcon
module.exports.GiTumulus = function GiTumulus (props) {
  return GenIcon({"tag":"svg","attr":{"viewBox":"0 0 512 512"},"child":[{"tag":"path","attr":{"d":"M205.361 49.538c-15.867 19.005-28.39 48.131-39.488 73.392-11.93-16.218-24.672-32.051-41.172-46.097 15.568 25.842 17.773 51.192 28.035 77.093-17.84-14.584-39.392-25.488-64.162-33.79 21.037 14.94 33.77 31.53 45.715 50.156-30.948 16.73-46.247 47.694-66.486 75.994-11.118-13.549-20.631-28.163-27.68-47.664 1.485 28.14 6.877 53.49 11.469 68.48 4.591 14.991 3.819 18.709 2.02 19.356-9.649-14.243-16.356-29.496-29.715-43.804 3.282 26.95 15.324 41.297 20.603 60.372-4.905 9.376-8.4 1.866-19.958-7.236 1.912 11.572 12.614 27.236 12.52 40.367-1.373 28.952 5.954 39.675 30.225 43.965 36.178 6.97 59.993 14.477 64.572 20.375 2.29 3.753 5.038 13.403 5.954 22.517 2.29 24.663 22.897 30.56 118.15 32.168 43.963.536 90.213 3.217 102.578 5.362 21.524 3.753 22.897 3.219 34.346-12.866 10.075-13.94 16.486-17.696 37.093-21.449 27.935-4.29 45.797-13.403 45.797-23.59-5.324-10.812-14.225-16.439-24.005-21.385-2.207.596-4.184 1.311-6.18 2.043-23.005 8.894-41.12 26.06-62.32 34.356-16.53 5.821 1.742 16.57-24.731 13.402-13.43-4.255-36.605 11.139-49.965 15.856l-5.875-17.014c.44-.152 4.064-1.802 9.285-4.254.526-12.01.254-22.902-.644-32.848-9.08-13.521-24.01-27.444-35.904-21.257-6.412 3.753-4.806 13.68-3.432 17.968 2.407 12.522-15.653.641-16.043 11.526 0 3.217-4.123 10.187-9.16 15.549-10.533 10.186-21.437 3.594-14.11-26.43 8.701-36.458 43.684-47.3 62.918-26.926 6.331 6.486 10.356 3.88 10.659-.498a181.726 181.726 0 0 0-6.647-20.795l-11.512 4.492c-6.892-19.519-13.4-41.262-11.16-57.851l.7-4.633 4.197-2.086c43.814-21.76 91.052-39.01 137.716-50.316-7.333-21.903-18.344-46.195-30.396-52.358-16.88-8.631-46.033-19.451-64.965-21.338 11.543-20.579 17.61-41.534 33.533-61.445-14.553 9.802-26.694 21.414-38.23 33.479 3.08-18.148 5.064-36.012 13.096-54.909-12.73 14.909-22.118 31.09-30.813 47.537-6.73-15.507-14.259-30.787-24.615-45.257 5.894 18 6.73 34.686 8.017 51.566-31.938.027-70.784-9.862-96.904 1.86 2.712-26.93 5.275-57.917 17.074-81.135zm9.942 95.45c10.242-.01 20.155 4.589 15.472 17.526-14.861 41.06-45.066 8.319-49.01 38.086-1.253 20.96 13.541 27.807-5.497 31.635-17.26 3.463-44.749-3.794-47.955 13.539-5.63 23.76 14.015 35.159 41.681 34.965 22.946 3.035 31.982 41.861 20.48 62.926-1.373 1.608-4.12-3.754-5.953-11.26-8.847-24.871-26.902-37.822-43.044-19.301-8.701 10.187-13.957 9.25-13.957-.4 0-11.26-16.268-42.493-23.137-42.493-21.248 13.468-20.583 60.367-43.637 63.825-9.973 1.495-2.338-11.435 4.175-23.863 6.512-12.429 11.6-22.905 11.86-30.758 16.788-44.943 85.81-133.833 138.522-134.426zm152.855 44.802c-17.639 4.004-108.586 74.902-112.195 70.771-8.665-8.682 44.148-80.222 112.195-70.771zm104.894 28.79c-14.439 19.03-11.194 54.971-15.863 77.178-1.456-.693-3.12-1.301-4.521-1.777.902 19.875 2.43 40.19-.586 63.238 10.85-11.677 39.307-25.027 29.506-42.98 1.913-20.555-1.662-37.304 6.515-60.018-8.958 11.807-11.895 20.01-18.058 28.612-1.837-17.86-3.446-46.327 3.007-64.252zm-38.37 27.072c-44.992 10.578-97.79 29.46-140.381 50.198-.496 9.027 1.518 17.849 4.533 27.068l142.978-55.567c-.93-7.648-1.739-17.164-7.13-21.699zm-.094 45.048l-28.35 10.935c.58 26.518 2.381 53.496.827 81.037 9.155-2.912 17.712-5.96 23.974-10.746 6.933-29.604 4.554-54.35 3.549-81.226zm-96.746 36.422c-7.218 2.605-14.333 5.488-21.48 8.277 8.236 21.45 14.305 48.237 13.745 83.297 7.214-2.2 13.549-4.9 18.766-8.524-2.796-29.236-3.857-58.891-11.031-83.05z"}}]})(props);
};
