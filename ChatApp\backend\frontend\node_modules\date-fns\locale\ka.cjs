"use strict";
exports.ka = void 0;
var _index = require("./ka/_lib/formatDistance.cjs");
var _index2 = require("./ka/_lib/formatLong.cjs");
var _index3 = require("./ka/_lib/formatRelative.cjs");
var _index4 = require("./ka/_lib/localize.cjs");
var _index5 = require("./ka/_lib/match.cjs");

/**
 * @category Locales
 * @summary Georgian locale.
 * @language Georgian
 * @iso-639-2 geo
 * <AUTHOR> [@Landish](https://github.com/Landish)
 * <AUTHOR> [@shvelo](https://github.com/shvelo)
 */
const ka = (exports.ka = {
  code: "ka",
  formatDistance: _index.formatDistance,
  formatLong: _index2.formatLong,
  formatRelative: _index3.formatRelative,
  localize: _index4.localize,
  match: _index5.match,
  options: {
    weekStartsOn: 1 /* Monday */,
    firstWeekContainsDate: 1,
  },
});
