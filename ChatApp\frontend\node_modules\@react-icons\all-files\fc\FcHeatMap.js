// THIS FILE IS AUTO GENERATED
var GenIcon = require('../lib').GenIcon
module.exports.FcHeatMap = function FcHeatMap (props) {
  return GenIcon({"tag":"svg","attr":{"version":"1","viewBox":"0 0 48 48","enableBackground":"new 0 0 48 48"},"child":[{"tag":"polygon","attr":{"fill":"#CFD8DC","points":"9,39 9,6 7,6 7,41 42,41 42,39"}},{"tag":"g","attr":{"fill":"#00BCD4"},"child":[{"tag":"circle","attr":{"cx":"14","cy":"11","r":"2"}},{"tag":"circle","attr":{"cx":"32","cy":"11","r":"2"}},{"tag":"circle","attr":{"cx":"39","cy":"11","r":"2"}},{"tag":"circle","attr":{"cx":"23","cy":"11","r":"4"}},{"tag":"circle","attr":{"cx":"14","cy":"33","r":"2"}},{"tag":"circle","attr":{"cx":"30","cy":"33","r":"2"}},{"tag":"circle","attr":{"cx":"22","cy":"33","r":"3"}},{"tag":"circle","attr":{"cx":"38","cy":"33","r":"4"}},{"tag":"circle","attr":{"cx":"14","cy":"22","r":"2"}},{"tag":"circle","attr":{"cx":"39","cy":"22","r":"2"}},{"tag":"circle","attr":{"cx":"32","cy":"22","r":"3"}}]}]})(props);
};
