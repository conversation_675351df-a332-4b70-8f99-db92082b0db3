// THIS FILE IS AUTO GENERATED
var GenIcon = require('../lib').GenIcon
module.exports.BiLeftDownArrowCircle = function BiLeftDownArrowCircle (props) {
  return GenIcon({"tag":"svg","attr":{"viewBox":"0 0 24 24"},"child":[{"tag":"path","attr":{"d":"M12.006,2.007c-2.675,0-5.187,1.038-7.071,2.923c-3.898,3.898-3.898,10.242,0,14.142c1.885,1.885,4.396,2.923,7.071,2.923 s5.187-1.038,7.071-2.923c3.898-3.899,3.898-10.243,0-14.142C17.192,3.045,14.681,2.007,12.006,2.007z M17.663,17.657 c-1.507,1.507-3.516,2.337-5.657,2.337s-4.15-0.83-5.657-2.337c-3.118-3.119-3.118-8.194,0-11.313 c1.507-1.507,3.517-2.337,5.657-2.337s4.15,0.83,5.657,2.337C20.781,9.463,20.781,14.538,17.663,17.657z"}},{"tag":"path","attr":{"d":"M14.346 8.247L11.131 11.462 9.006 9.337 9.006 15 14.669 15 12.545 12.876 15.76 9.661z"}}]})(props);
};
