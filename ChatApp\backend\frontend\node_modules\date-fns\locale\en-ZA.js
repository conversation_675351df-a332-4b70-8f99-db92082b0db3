import { formatDistance } from "./en-US/_lib/formatDistance.js";
import { formatRelative } from "./en-US/_lib/formatRelative.js";
import { localize } from "./en-US/_lib/localize.js";
import { match } from "./en-US/_lib/match.js";

import { formatLong } from "./en-ZA/_lib/formatLong.js";

/**
 * @category Locales
 * @summary English locale (South Africa).
 * @language English
 * @iso-639-2 eng
 * <AUTHOR> [@shaykav](https://github.com/shaykav)
 */
export const enZA = {
  code: "en-ZA",
  formatDistance: formatDistance,
  formatLong: formatLong,
  formatRelative: formatRelative,
  localize: localize,
  match: match,
  options: {
    weekStartsOn: 0, // Sunday is the first day of the week.
    firstWeekContainsDate: 1, // The week that contains Jan 1st is the first week of the year.
  },
};

// Fallback for modularized imports:
export default enZA;
