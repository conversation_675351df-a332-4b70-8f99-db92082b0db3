import { isSameWeek } from "../../../isSameWeek.js";

const weekdays = [
  "svētdienā",
  "pirmdienā",
  "otrdien<PERSON>",
  "trešdien<PERSON>",
  "ceturtdienā",
  "piektdienā",
  "sestdienā",
];

const formatRelativeLocale = {
  lastWeek: (date, baseDate, options) => {
    if (isSameWeek(date, baseDate, options)) {
      return "eeee 'plkst.' p";
    }

    const weekday = weekdays[date.getDay()];
    return "'Pagā<PERSON><PERSON><PERSON> " + weekday + " plkst.' p";
  },
  yesterday: "'<PERSON><PERSON><PERSON> plkst.' p",
  today: "'Š<PERSON>ien plkst.' p",
  tomorrow: "'Rīt plkst.' p",
  nextWeek: (date, baseDate, options) => {
    if (isSameWeek(date, baseDate, options)) {
      return "eeee 'plkst.' p";
    }

    const weekday = weekdays[date.getDay()];
    return "'Nāka<PERSON>jā " + weekday + " plkst.' p";
  },
  other: "P",
};

export const formatRelative = (token, date, baseDate, options) => {
  const format = formatRelativeLocale[token];

  if (typeof format === "function") {
    return format(date, baseDate, options);
  }

  return format;
};
