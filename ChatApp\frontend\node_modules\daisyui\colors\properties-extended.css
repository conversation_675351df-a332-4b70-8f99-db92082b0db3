/*! 🌼 daisyUI 5.0.42 - MIT License */ .from-base-100{--tw-gradient-from:var(--color-base-100);--tw-gradient-stops:var(--tw-gradient-via-stops,var(--tw-gradient-position),var(--tw-gradient-from)var(--tw-gradient-from-position),var(--tw-gradient-to)var(--tw-gradient-to-position))}.via-base-100{--tw-gradient-via:var(--color-base-100);--tw-gradient-via-stops:var(--tw-gradient-position),var(--tw-gradient-from)var(--tw-gradient-from-position),var(--tw-gradient-via)var(--tw-gradient-via-position),var(--tw-gradient-to)var(--tw-gradient-to-position);--tw-gradient-stops:var(--tw-gradient-via-stops)}.to-base-100{--tw-gradient-to:var(--color-base-100);--tw-gradient-stops:var(--tw-gradient-via-stops,var(--tw-gradient-position),var(--tw-gradient-from)var(--tw-gradient-from-position),var(--tw-gradient-to)var(--tw-gradient-to-position))}.ring-base-100{--tw-ring-color:var(--color-base-100)}.fill-base-100{fill:var(--color-base-100)}.stroke-base-100{stroke:var(--color-base-100)}.shadow-base-100{--tw-shadow-color:color-mix(in oklab,var(--color-base-100)var(--tw-shadow-alpha),transparent)}.outline-base-100{outline-color:var(--color-base-100)}.from-base-200{--tw-gradient-from:var(--color-base-200);--tw-gradient-stops:var(--tw-gradient-via-stops,var(--tw-gradient-position),var(--tw-gradient-from)var(--tw-gradient-from-position),var(--tw-gradient-to)var(--tw-gradient-to-position))}.via-base-200{--tw-gradient-via:var(--color-base-200);--tw-gradient-via-stops:var(--tw-gradient-position),var(--tw-gradient-from)var(--tw-gradient-from-position),var(--tw-gradient-via)var(--tw-gradient-via-position),var(--tw-gradient-to)var(--tw-gradient-to-position);--tw-gradient-stops:var(--tw-gradient-via-stops)}.to-base-200{--tw-gradient-to:var(--color-base-200);--tw-gradient-stops:var(--tw-gradient-via-stops,var(--tw-gradient-position),var(--tw-gradient-from)var(--tw-gradient-from-position),var(--tw-gradient-to)var(--tw-gradient-to-position))}.ring-base-200{--tw-ring-color:var(--color-base-200)}.fill-base-200{fill:var(--color-base-200)}.stroke-base-200{stroke:var(--color-base-200)}.shadow-base-200{--tw-shadow-color:color-mix(in oklab,var(--color-base-200)var(--tw-shadow-alpha),transparent)}.outline-base-200{outline-color:var(--color-base-200)}.from-base-300{--tw-gradient-from:var(--color-base-300);--tw-gradient-stops:var(--tw-gradient-via-stops,var(--tw-gradient-position),var(--tw-gradient-from)var(--tw-gradient-from-position),var(--tw-gradient-to)var(--tw-gradient-to-position))}.via-base-300{--tw-gradient-via:var(--color-base-300);--tw-gradient-via-stops:var(--tw-gradient-position),var(--tw-gradient-from)var(--tw-gradient-from-position),var(--tw-gradient-via)var(--tw-gradient-via-position),var(--tw-gradient-to)var(--tw-gradient-to-position);--tw-gradient-stops:var(--tw-gradient-via-stops)}.to-base-300{--tw-gradient-to:var(--color-base-300);--tw-gradient-stops:var(--tw-gradient-via-stops,var(--tw-gradient-position),var(--tw-gradient-from)var(--tw-gradient-from-position),var(--tw-gradient-to)var(--tw-gradient-to-position))}.ring-base-300{--tw-ring-color:var(--color-base-300)}.fill-base-300{fill:var(--color-base-300)}.stroke-base-300{stroke:var(--color-base-300)}.shadow-base-300{--tw-shadow-color:color-mix(in oklab,var(--color-base-300)var(--tw-shadow-alpha),transparent)}.outline-base-300{outline-color:var(--color-base-300)}.from-base-content{--tw-gradient-from:var(--color-base-content);--tw-gradient-stops:var(--tw-gradient-via-stops,var(--tw-gradient-position),var(--tw-gradient-from)var(--tw-gradient-from-position),var(--tw-gradient-to)var(--tw-gradient-to-position))}.via-base-content{--tw-gradient-via:var(--color-base-content);--tw-gradient-via-stops:var(--tw-gradient-position),var(--tw-gradient-from)var(--tw-gradient-from-position),var(--tw-gradient-via)var(--tw-gradient-via-position),var(--tw-gradient-to)var(--tw-gradient-to-position);--tw-gradient-stops:var(--tw-gradient-via-stops)}.to-base-content{--tw-gradient-to:var(--color-base-content);--tw-gradient-stops:var(--tw-gradient-via-stops,var(--tw-gradient-position),var(--tw-gradient-from)var(--tw-gradient-from-position),var(--tw-gradient-to)var(--tw-gradient-to-position))}.ring-base-content{--tw-ring-color:var(--color-base-content)}.fill-base-content{fill:var(--color-base-content)}.stroke-base-content{stroke:var(--color-base-content)}.shadow-base-content{--tw-shadow-color:color-mix(in oklab,var(--color-base-content)var(--tw-shadow-alpha),transparent)}.outline-base-content{outline-color:var(--color-base-content)}.from-primary{--tw-gradient-from:var(--color-primary);--tw-gradient-stops:var(--tw-gradient-via-stops,var(--tw-gradient-position),var(--tw-gradient-from)var(--tw-gradient-from-position),var(--tw-gradient-to)var(--tw-gradient-to-position))}.via-primary{--tw-gradient-via:var(--color-primary);--tw-gradient-via-stops:var(--tw-gradient-position),var(--tw-gradient-from)var(--tw-gradient-from-position),var(--tw-gradient-via)var(--tw-gradient-via-position),var(--tw-gradient-to)var(--tw-gradient-to-position);--tw-gradient-stops:var(--tw-gradient-via-stops)}.to-primary{--tw-gradient-to:var(--color-primary);--tw-gradient-stops:var(--tw-gradient-via-stops,var(--tw-gradient-position),var(--tw-gradient-from)var(--tw-gradient-from-position),var(--tw-gradient-to)var(--tw-gradient-to-position))}.ring-primary{--tw-ring-color:var(--color-primary)}.fill-primary{fill:var(--color-primary)}.stroke-primary{stroke:var(--color-primary)}.shadow-primary{--tw-shadow-color:color-mix(in oklab,var(--color-primary)var(--tw-shadow-alpha),transparent)}.outline-primary{outline-color:var(--color-primary)}.from-primary-content{--tw-gradient-from:var(--color-primary-content);--tw-gradient-stops:var(--tw-gradient-via-stops,var(--tw-gradient-position),var(--tw-gradient-from)var(--tw-gradient-from-position),var(--tw-gradient-to)var(--tw-gradient-to-position))}.via-primary-content{--tw-gradient-via:var(--color-primary-content);--tw-gradient-via-stops:var(--tw-gradient-position),var(--tw-gradient-from)var(--tw-gradient-from-position),var(--tw-gradient-via)var(--tw-gradient-via-position),var(--tw-gradient-to)var(--tw-gradient-to-position);--tw-gradient-stops:var(--tw-gradient-via-stops)}.to-primary-content{--tw-gradient-to:var(--color-primary-content);--tw-gradient-stops:var(--tw-gradient-via-stops,var(--tw-gradient-position),var(--tw-gradient-from)var(--tw-gradient-from-position),var(--tw-gradient-to)var(--tw-gradient-to-position))}.ring-primary-content{--tw-ring-color:var(--color-primary-content)}.fill-primary-content{fill:var(--color-primary-content)}.stroke-primary-content{stroke:var(--color-primary-content)}.shadow-primary-content{--tw-shadow-color:color-mix(in oklab,var(--color-primary-content)var(--tw-shadow-alpha),transparent)}.outline-primary-content{outline-color:var(--color-primary-content)}.from-secondary{--tw-gradient-from:var(--color-secondary);--tw-gradient-stops:var(--tw-gradient-via-stops,var(--tw-gradient-position),var(--tw-gradient-from)var(--tw-gradient-from-position),var(--tw-gradient-to)var(--tw-gradient-to-position))}.via-secondary{--tw-gradient-via:var(--color-secondary);--tw-gradient-via-stops:var(--tw-gradient-position),var(--tw-gradient-from)var(--tw-gradient-from-position),var(--tw-gradient-via)var(--tw-gradient-via-position),var(--tw-gradient-to)var(--tw-gradient-to-position);--tw-gradient-stops:var(--tw-gradient-via-stops)}.to-secondary{--tw-gradient-to:var(--color-secondary);--tw-gradient-stops:var(--tw-gradient-via-stops,var(--tw-gradient-position),var(--tw-gradient-from)var(--tw-gradient-from-position),var(--tw-gradient-to)var(--tw-gradient-to-position))}.ring-secondary{--tw-ring-color:var(--color-secondary)}.fill-secondary{fill:var(--color-secondary)}.stroke-secondary{stroke:var(--color-secondary)}.shadow-secondary{--tw-shadow-color:color-mix(in oklab,var(--color-secondary)var(--tw-shadow-alpha),transparent)}.outline-secondary{outline-color:var(--color-secondary)}.from-secondary-content{--tw-gradient-from:var(--color-secondary-content);--tw-gradient-stops:var(--tw-gradient-via-stops,var(--tw-gradient-position),var(--tw-gradient-from)var(--tw-gradient-from-position),var(--tw-gradient-to)var(--tw-gradient-to-position))}.via-secondary-content{--tw-gradient-via:var(--color-secondary-content);--tw-gradient-via-stops:var(--tw-gradient-position),var(--tw-gradient-from)var(--tw-gradient-from-position),var(--tw-gradient-via)var(--tw-gradient-via-position),var(--tw-gradient-to)var(--tw-gradient-to-position);--tw-gradient-stops:var(--tw-gradient-via-stops)}.to-secondary-content{--tw-gradient-to:var(--color-secondary-content);--tw-gradient-stops:var(--tw-gradient-via-stops,var(--tw-gradient-position),var(--tw-gradient-from)var(--tw-gradient-from-position),var(--tw-gradient-to)var(--tw-gradient-to-position))}.ring-secondary-content{--tw-ring-color:var(--color-secondary-content)}.fill-secondary-content{fill:var(--color-secondary-content)}.stroke-secondary-content{stroke:var(--color-secondary-content)}.shadow-secondary-content{--tw-shadow-color:color-mix(in oklab,var(--color-secondary-content)var(--tw-shadow-alpha),transparent)}.outline-secondary-content{outline-color:var(--color-secondary-content)}.from-accent{--tw-gradient-from:var(--color-accent);--tw-gradient-stops:var(--tw-gradient-via-stops,var(--tw-gradient-position),var(--tw-gradient-from)var(--tw-gradient-from-position),var(--tw-gradient-to)var(--tw-gradient-to-position))}.via-accent{--tw-gradient-via:var(--color-accent);--tw-gradient-via-stops:var(--tw-gradient-position),var(--tw-gradient-from)var(--tw-gradient-from-position),var(--tw-gradient-via)var(--tw-gradient-via-position),var(--tw-gradient-to)var(--tw-gradient-to-position);--tw-gradient-stops:var(--tw-gradient-via-stops)}.to-accent{--tw-gradient-to:var(--color-accent);--tw-gradient-stops:var(--tw-gradient-via-stops,var(--tw-gradient-position),var(--tw-gradient-from)var(--tw-gradient-from-position),var(--tw-gradient-to)var(--tw-gradient-to-position))}.ring-accent{--tw-ring-color:var(--color-accent)}.fill-accent{fill:var(--color-accent)}.stroke-accent{stroke:var(--color-accent)}.shadow-accent{--tw-shadow-color:color-mix(in oklab,var(--color-accent)var(--tw-shadow-alpha),transparent)}.outline-accent{outline-color:var(--color-accent)}.from-accent-content{--tw-gradient-from:var(--color-accent-content);--tw-gradient-stops:var(--tw-gradient-via-stops,var(--tw-gradient-position),var(--tw-gradient-from)var(--tw-gradient-from-position),var(--tw-gradient-to)var(--tw-gradient-to-position))}.via-accent-content{--tw-gradient-via:var(--color-accent-content);--tw-gradient-via-stops:var(--tw-gradient-position),var(--tw-gradient-from)var(--tw-gradient-from-position),var(--tw-gradient-via)var(--tw-gradient-via-position),var(--tw-gradient-to)var(--tw-gradient-to-position);--tw-gradient-stops:var(--tw-gradient-via-stops)}.to-accent-content{--tw-gradient-to:var(--color-accent-content);--tw-gradient-stops:var(--tw-gradient-via-stops,var(--tw-gradient-position),var(--tw-gradient-from)var(--tw-gradient-from-position),var(--tw-gradient-to)var(--tw-gradient-to-position))}.ring-accent-content{--tw-ring-color:var(--color-accent-content)}.fill-accent-content{fill:var(--color-accent-content)}.stroke-accent-content{stroke:var(--color-accent-content)}.shadow-accent-content{--tw-shadow-color:color-mix(in oklab,var(--color-accent-content)var(--tw-shadow-alpha),transparent)}.outline-accent-content{outline-color:var(--color-accent-content)}.from-neutral{--tw-gradient-from:var(--color-neutral);--tw-gradient-stops:var(--tw-gradient-via-stops,var(--tw-gradient-position),var(--tw-gradient-from)var(--tw-gradient-from-position),var(--tw-gradient-to)var(--tw-gradient-to-position))}.via-neutral{--tw-gradient-via:var(--color-neutral);--tw-gradient-via-stops:var(--tw-gradient-position),var(--tw-gradient-from)var(--tw-gradient-from-position),var(--tw-gradient-via)var(--tw-gradient-via-position),var(--tw-gradient-to)var(--tw-gradient-to-position);--tw-gradient-stops:var(--tw-gradient-via-stops)}.to-neutral{--tw-gradient-to:var(--color-neutral);--tw-gradient-stops:var(--tw-gradient-via-stops,var(--tw-gradient-position),var(--tw-gradient-from)var(--tw-gradient-from-position),var(--tw-gradient-to)var(--tw-gradient-to-position))}.ring-neutral{--tw-ring-color:var(--color-neutral)}.fill-neutral{fill:var(--color-neutral)}.stroke-neutral{stroke:var(--color-neutral)}.shadow-neutral{--tw-shadow-color:color-mix(in oklab,var(--color-neutral)var(--tw-shadow-alpha),transparent)}.outline-neutral{outline-color:var(--color-neutral)}.from-neutral-content{--tw-gradient-from:var(--color-neutral-content);--tw-gradient-stops:var(--tw-gradient-via-stops,var(--tw-gradient-position),var(--tw-gradient-from)var(--tw-gradient-from-position),var(--tw-gradient-to)var(--tw-gradient-to-position))}.via-neutral-content{--tw-gradient-via:var(--color-neutral-content);--tw-gradient-via-stops:var(--tw-gradient-position),var(--tw-gradient-from)var(--tw-gradient-from-position),var(--tw-gradient-via)var(--tw-gradient-via-position),var(--tw-gradient-to)var(--tw-gradient-to-position);--tw-gradient-stops:var(--tw-gradient-via-stops)}.to-neutral-content{--tw-gradient-to:var(--color-neutral-content);--tw-gradient-stops:var(--tw-gradient-via-stops,var(--tw-gradient-position),var(--tw-gradient-from)var(--tw-gradient-from-position),var(--tw-gradient-to)var(--tw-gradient-to-position))}.ring-neutral-content{--tw-ring-color:var(--color-neutral-content)}.fill-neutral-content{fill:var(--color-neutral-content)}.stroke-neutral-content{stroke:var(--color-neutral-content)}.shadow-neutral-content{--tw-shadow-color:color-mix(in oklab,var(--color-neutral-content)var(--tw-shadow-alpha),transparent)}.outline-neutral-content{outline-color:var(--color-neutral-content)}.from-info{--tw-gradient-from:var(--color-info);--tw-gradient-stops:var(--tw-gradient-via-stops,var(--tw-gradient-position),var(--tw-gradient-from)var(--tw-gradient-from-position),var(--tw-gradient-to)var(--tw-gradient-to-position))}.via-info{--tw-gradient-via:var(--color-info);--tw-gradient-via-stops:var(--tw-gradient-position),var(--tw-gradient-from)var(--tw-gradient-from-position),var(--tw-gradient-via)var(--tw-gradient-via-position),var(--tw-gradient-to)var(--tw-gradient-to-position);--tw-gradient-stops:var(--tw-gradient-via-stops)}.to-info{--tw-gradient-to:var(--color-info);--tw-gradient-stops:var(--tw-gradient-via-stops,var(--tw-gradient-position),var(--tw-gradient-from)var(--tw-gradient-from-position),var(--tw-gradient-to)var(--tw-gradient-to-position))}.ring-info{--tw-ring-color:var(--color-info)}.fill-info{fill:var(--color-info)}.stroke-info{stroke:var(--color-info)}.shadow-info{--tw-shadow-color:color-mix(in oklab,var(--color-info)var(--tw-shadow-alpha),transparent)}.outline-info{outline-color:var(--color-info)}.from-info-content{--tw-gradient-from:var(--color-info-content);--tw-gradient-stops:var(--tw-gradient-via-stops,var(--tw-gradient-position),var(--tw-gradient-from)var(--tw-gradient-from-position),var(--tw-gradient-to)var(--tw-gradient-to-position))}.via-info-content{--tw-gradient-via:var(--color-info-content);--tw-gradient-via-stops:var(--tw-gradient-position),var(--tw-gradient-from)var(--tw-gradient-from-position),var(--tw-gradient-via)var(--tw-gradient-via-position),var(--tw-gradient-to)var(--tw-gradient-to-position);--tw-gradient-stops:var(--tw-gradient-via-stops)}.to-info-content{--tw-gradient-to:var(--color-info-content);--tw-gradient-stops:var(--tw-gradient-via-stops,var(--tw-gradient-position),var(--tw-gradient-from)var(--tw-gradient-from-position),var(--tw-gradient-to)var(--tw-gradient-to-position))}.ring-info-content{--tw-ring-color:var(--color-info-content)}.fill-info-content{fill:var(--color-info-content)}.stroke-info-content{stroke:var(--color-info-content)}.shadow-info-content{--tw-shadow-color:color-mix(in oklab,var(--color-info-content)var(--tw-shadow-alpha),transparent)}.outline-info-content{outline-color:var(--color-info-content)}.from-success{--tw-gradient-from:var(--color-success);--tw-gradient-stops:var(--tw-gradient-via-stops,var(--tw-gradient-position),var(--tw-gradient-from)var(--tw-gradient-from-position),var(--tw-gradient-to)var(--tw-gradient-to-position))}.via-success{--tw-gradient-via:var(--color-success);--tw-gradient-via-stops:var(--tw-gradient-position),var(--tw-gradient-from)var(--tw-gradient-from-position),var(--tw-gradient-via)var(--tw-gradient-via-position),var(--tw-gradient-to)var(--tw-gradient-to-position);--tw-gradient-stops:var(--tw-gradient-via-stops)}.to-success{--tw-gradient-to:var(--color-success);--tw-gradient-stops:var(--tw-gradient-via-stops,var(--tw-gradient-position),var(--tw-gradient-from)var(--tw-gradient-from-position),var(--tw-gradient-to)var(--tw-gradient-to-position))}.ring-success{--tw-ring-color:var(--color-success)}.fill-success{fill:var(--color-success)}.stroke-success{stroke:var(--color-success)}.shadow-success{--tw-shadow-color:color-mix(in oklab,var(--color-success)var(--tw-shadow-alpha),transparent)}.outline-success{outline-color:var(--color-success)}.from-success-content{--tw-gradient-from:var(--color-success-content);--tw-gradient-stops:var(--tw-gradient-via-stops,var(--tw-gradient-position),var(--tw-gradient-from)var(--tw-gradient-from-position),var(--tw-gradient-to)var(--tw-gradient-to-position))}.via-success-content{--tw-gradient-via:var(--color-success-content);--tw-gradient-via-stops:var(--tw-gradient-position),var(--tw-gradient-from)var(--tw-gradient-from-position),var(--tw-gradient-via)var(--tw-gradient-via-position),var(--tw-gradient-to)var(--tw-gradient-to-position);--tw-gradient-stops:var(--tw-gradient-via-stops)}.to-success-content{--tw-gradient-to:var(--color-success-content);--tw-gradient-stops:var(--tw-gradient-via-stops,var(--tw-gradient-position),var(--tw-gradient-from)var(--tw-gradient-from-position),var(--tw-gradient-to)var(--tw-gradient-to-position))}.ring-success-content{--tw-ring-color:var(--color-success-content)}.fill-success-content{fill:var(--color-success-content)}.stroke-success-content{stroke:var(--color-success-content)}.shadow-success-content{--tw-shadow-color:color-mix(in oklab,var(--color-success-content)var(--tw-shadow-alpha),transparent)}.outline-success-content{outline-color:var(--color-success-content)}.from-warning{--tw-gradient-from:var(--color-warning);--tw-gradient-stops:var(--tw-gradient-via-stops,var(--tw-gradient-position),var(--tw-gradient-from)var(--tw-gradient-from-position),var(--tw-gradient-to)var(--tw-gradient-to-position))}.via-warning{--tw-gradient-via:var(--color-warning);--tw-gradient-via-stops:var(--tw-gradient-position),var(--tw-gradient-from)var(--tw-gradient-from-position),var(--tw-gradient-via)var(--tw-gradient-via-position),var(--tw-gradient-to)var(--tw-gradient-to-position);--tw-gradient-stops:var(--tw-gradient-via-stops)}.to-warning{--tw-gradient-to:var(--color-warning);--tw-gradient-stops:var(--tw-gradient-via-stops,var(--tw-gradient-position),var(--tw-gradient-from)var(--tw-gradient-from-position),var(--tw-gradient-to)var(--tw-gradient-to-position))}.ring-warning{--tw-ring-color:var(--color-warning)}.fill-warning{fill:var(--color-warning)}.stroke-warning{stroke:var(--color-warning)}.shadow-warning{--tw-shadow-color:color-mix(in oklab,var(--color-warning)var(--tw-shadow-alpha),transparent)}.outline-warning{outline-color:var(--color-warning)}.from-warning-content{--tw-gradient-from:var(--color-warning-content);--tw-gradient-stops:var(--tw-gradient-via-stops,var(--tw-gradient-position),var(--tw-gradient-from)var(--tw-gradient-from-position),var(--tw-gradient-to)var(--tw-gradient-to-position))}.via-warning-content{--tw-gradient-via:var(--color-warning-content);--tw-gradient-via-stops:var(--tw-gradient-position),var(--tw-gradient-from)var(--tw-gradient-from-position),var(--tw-gradient-via)var(--tw-gradient-via-position),var(--tw-gradient-to)var(--tw-gradient-to-position);--tw-gradient-stops:var(--tw-gradient-via-stops)}.to-warning-content{--tw-gradient-to:var(--color-warning-content);--tw-gradient-stops:var(--tw-gradient-via-stops,var(--tw-gradient-position),var(--tw-gradient-from)var(--tw-gradient-from-position),var(--tw-gradient-to)var(--tw-gradient-to-position))}.ring-warning-content{--tw-ring-color:var(--color-warning-content)}.fill-warning-content{fill:var(--color-warning-content)}.stroke-warning-content{stroke:var(--color-warning-content)}.shadow-warning-content{--tw-shadow-color:color-mix(in oklab,var(--color-warning-content)var(--tw-shadow-alpha),transparent)}.outline-warning-content{outline-color:var(--color-warning-content)}.from-error{--tw-gradient-from:var(--color-error);--tw-gradient-stops:var(--tw-gradient-via-stops,var(--tw-gradient-position),var(--tw-gradient-from)var(--tw-gradient-from-position),var(--tw-gradient-to)var(--tw-gradient-to-position))}.via-error{--tw-gradient-via:var(--color-error);--tw-gradient-via-stops:var(--tw-gradient-position),var(--tw-gradient-from)var(--tw-gradient-from-position),var(--tw-gradient-via)var(--tw-gradient-via-position),var(--tw-gradient-to)var(--tw-gradient-to-position);--tw-gradient-stops:var(--tw-gradient-via-stops)}.to-error{--tw-gradient-to:var(--color-error);--tw-gradient-stops:var(--tw-gradient-via-stops,var(--tw-gradient-position),var(--tw-gradient-from)var(--tw-gradient-from-position),var(--tw-gradient-to)var(--tw-gradient-to-position))}.ring-error{--tw-ring-color:var(--color-error)}.fill-error{fill:var(--color-error)}.stroke-error{stroke:var(--color-error)}.shadow-error{--tw-shadow-color:color-mix(in oklab,var(--color-error)var(--tw-shadow-alpha),transparent)}.outline-error{outline-color:var(--color-error)}.from-error-content{--tw-gradient-from:var(--color-error-content);--tw-gradient-stops:var(--tw-gradient-via-stops,var(--tw-gradient-position),var(--tw-gradient-from)var(--tw-gradient-from-position),var(--tw-gradient-to)var(--tw-gradient-to-position))}.via-error-content{--tw-gradient-via:var(--color-error-content);--tw-gradient-via-stops:var(--tw-gradient-position),var(--tw-gradient-from)var(--tw-gradient-from-position),var(--tw-gradient-via)var(--tw-gradient-via-position),var(--tw-gradient-to)var(--tw-gradient-to-position);--tw-gradient-stops:var(--tw-gradient-via-stops)}.to-error-content{--tw-gradient-to:var(--color-error-content);--tw-gradient-stops:var(--tw-gradient-via-stops,var(--tw-gradient-position),var(--tw-gradient-from)var(--tw-gradient-from-position),var(--tw-gradient-to)var(--tw-gradient-to-position))}.ring-error-content{--tw-ring-color:var(--color-error-content)}.fill-error-content{fill:var(--color-error-content)}.stroke-error-content{stroke:var(--color-error-content)}.shadow-error-content{--tw-shadow-color:color-mix(in oklab,var(--color-error-content)var(--tw-shadow-alpha),transparent)}.outline-error-content{outline-color:var(--color-error-content)}