// THIS FILE IS AUTO GENERATED
var GenIcon = require('../lib').GenIcon
module.exports.GiQuicksand = function GiQuicksand (props) {
  return GenIcon({"tag":"svg","attr":{"viewBox":"0 0 512 512"},"child":[{"tag":"path","attr":{"d":"M256.97 41.688c-16.077 0-30.897 8.46-42.22 23.343-11.323 14.886-18.656 36.052-18.656 59.626 0 23.575 7.332 44.74 18.656 59.625 11.324 14.886 26.143 23.376 42.22 23.376 16.074 0 30.925-8.49 42.25-23.375 11.322-14.884 18.655-36.05 18.655-59.624 0-23.574-7.333-44.74-18.656-59.625-11.325-14.883-26.175-23.343-42.25-23.343zm-34.533 78.937h18.688V166h-18.688v-45.375zm50.72 0h18.687V166h-18.688v-45.375zm-208.47 7L21.657 248.5c-.01.035-.023.06-.032.094-2.42 9.275-.887 15.685 2.375 20.656 3.274 4.99 8.77 8.62 15.063 10.188 6.293 1.566 13.143.978 18.687-1.813 5.544-2.79 10.055-7.5 12.438-15.938l.124-.406.157-.436 26.28-67.75 3.063-7.875 8.156 2.25 54.874 15.186 5.656 1.563 1.063 5.75 13.906 75c-34.572 4.345-65.652 11.722-91.282 21.436-21.318 8.08-38.918 17.745-51.688 29.188-12.77 11.443-20.97 25.266-20.97 40.5 0 15.233 8.2 29.09 20.97 40.53 12.77 11.443 30.37 21.078 51.688 29.157 42.634 16.16 100.356 25.876 164.125 25.876 63.768 0 121.52-9.717 164.156-25.875 21.316-8.078 38.916-17.713 51.686-29.155 12.77-11.442 20.97-25.298 20.97-40.53 0-15.235-8.2-29.058-20.97-40.5-12.77-11.444-30.37-21.11-51.687-29.19-25.57-9.69-56.584-17.055-91.064-21.405l13.906-75.03 1.063-5.75 5.656-1.564 54.876-15.187 8.156-2.25 3.063 7.874 26.28 67.75.158.437.125.408c2.382 8.437 6.862 13.146 12.406 15.937 5.543 2.79 12.425 3.38 18.72 1.813 6.292-1.567 11.787-5.198 15.06-10.188 3.263-4.97 4.797-11.38 2.376-20.656l-.03-.094-43.033-120.875-115.75 29.47c-3.794 14.508-10.07 27.624-18.343 38.5-14.097 18.53-34.41 30.75-57.125 30.75-22.715 0-42.998-12.22-57.095-30.75-8.213-10.797-14.447-23.805-18.25-38.19l-116.938-29.78zm122.188 175.78l3.406 18.345c-7.998 1.91-15.457 4.144-22.25 6.72-11.607 4.4-21.315 9.67-28.655 16.25-7.34 6.576-12.563 15.068-12.563 24.686 0 9.62 5.223 18.11 12.563 24.688 7.34 6.577 17.048 11.85 28.656 16.25 23.218 8.798 54.145 13.937 88.282 13.937 34.138 0 65.065-5.138 88.282-13.936 11.608-4.4 21.347-9.673 28.687-16.25s12.533-15.07 12.533-24.688c0-9.618-5.192-18.11-12.532-24.687-7.34-6.58-17.078-11.85-28.686-16.25-6.72-2.55-14.097-4.76-22-6.658L326 303.438c33.72 4.197 63.78 11.317 87.844 20.438 19.844 7.522 35.563 16.44 45.812 25.625 10.25 9.185 14.78 18.024 14.78 26.594.002 8.57-4.53 17.41-14.78 26.594-10.25 9.183-25.968 18.104-45.812 25.625-39.69 15.04-95.677 24.656-157.53 24.656-61.856 0-117.813-9.616-157.502-24.658-19.844-7.52-35.594-16.44-45.843-25.625-10.25-9.183-14.75-18.023-14.75-26.593s4.5-17.41 14.75-26.594c10.248-9.185 25.998-18.103 45.843-25.625 24.12-9.143 54.25-16.277 88.062-20.47zm6.844 36.783l2.25 12.218v11.78l.31.002c-.097.413-.155.83-.155 1.25 0 9.76 26.975 17.656 60.25 17.656s60.25-7.897 60.25-17.656c0-.422-.09-.836-.188-1.25h.313V353.25l2.406-13c6.863 1.676 13.193 3.59 18.813 5.72 10.134 3.84 17.99 8.367 22.81 12.686 4.82 4.32 6.345 7.795 6.345 10.75 0 2.956-1.524 6.463-6.344 10.78-4.82 4.32-12.676 8.848-22.81 12.69-20.272 7.68-49.434 12.718-81.658 12.718-32.223 0-61.385-5.037-81.656-12.72-10.135-3.84-17.992-8.368-22.812-12.687-4.82-4.318-6.344-7.825-6.344-10.78 0-2.956 1.524-6.432 6.344-10.75 4.82-4.32 12.677-8.847 22.812-12.688 5.688-2.157 12.103-4.093 19.063-5.783z"}}]})(props);
};
