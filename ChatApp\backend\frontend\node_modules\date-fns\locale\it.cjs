"use strict";
exports.it = void 0;
var _index = require("./it/_lib/formatDistance.cjs");
var _index2 = require("./it/_lib/formatLong.cjs");
var _index3 = require("./it/_lib/formatRelative.cjs");
var _index4 = require("./it/_lib/localize.cjs");
var _index5 = require("./it/_lib/match.cjs");

/**
 * @category Locales
 * @summary Italian locale.
 * @language Italian
 * @iso-639-2 ita
 * <AUTHOR> [@albertorestifo](https://github.com/albertorestifo)
 * <AUTHOR> [@giofilo](https://github.com/giofilo)
 * <AUTHOR> [@vin-car](https://github.com/vin-car)
 */
const it = (exports.it = {
  code: "it",
  formatDistance: _index.formatDistance,
  formatLong: _index2.formatLong,
  formatRelative: _index3.formatRelative,
  localize: _index4.localize,
  match: _index5.match,
  options: {
    weekStartsOn: 1 /* Monday */,
    firstWeekContainsDate: 4,
  },
});
