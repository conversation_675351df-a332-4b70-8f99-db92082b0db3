// THIS FILE IS AUTO GENERATED
var GenIcon = require('../lib').GenIcon
module.exports.FcGenealogy = function FcGenealogy (props) {
  return GenIcon({"tag":"svg","attr":{"version":"1","viewBox":"0 0 48 48","enableBackground":"new 0 0 48 48"},"child":[{"tag":"polygon","attr":{"fill":"#CFD8DC","points":"40,9 40,7 31,7 31,12 24,12 15,12 15,23 8,23 8,25 15,25 15,36 24,36 31,36 31,41 40,41 40,39 33,39 33,31 40,31 40,29 31,29 31,34 24,34 17,34 17,14 24,14 31,14 31,19 40,19 40,17 33,17 33,9"}},{"tag":"rect","attr":{"x":"4","y":"20","fill":"#00BCD4","width":"8","height":"8"}},{"tag":"g","attr":{"fill":"#3F51B5"},"child":[{"tag":"rect","attr":{"x":"36","y":"14","width":"8","height":"8"}},{"tag":"rect","attr":{"x":"36","y":"4","width":"8","height":"8"}},{"tag":"rect","attr":{"x":"20","y":"9","width":"8","height":"8"}},{"tag":"rect","attr":{"x":"20","y":"31","width":"8","height":"8"}},{"tag":"rect","attr":{"x":"36","y":"36","width":"8","height":"8"}},{"tag":"rect","attr":{"x":"36","y":"26","width":"8","height":"8"}}]}]})(props);
};
