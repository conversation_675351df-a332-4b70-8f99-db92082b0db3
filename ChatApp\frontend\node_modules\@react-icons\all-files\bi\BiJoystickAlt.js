// THIS FILE IS AUTO GENERATED
var GenIcon = require('../lib').GenIcon
module.exports.BiJoystickAlt = function BiJoystickAlt (props) {
  return GenIcon({"tag":"svg","attr":{"viewBox":"0 0 24 24"},"child":[{"tag":"path","attr":{"fill":"none","d":"M15,7H9c-2.753,0-4.997,2.247-5,5.009C4.003,14.76,6.247,17,9,17h6c2.754,0,4.996-2.24,5-4.994 C19.996,9.247,17.754,7,15,7z M12,13h-2v2H8v-2H6v-2h2V9h2v2h2V13z M15,14c-0.553,0-1-0.447-1-1s0.447-1,1-1s1,0.447,1,1 S15.553,14,15,14z M17,12c-0.553,0-1-0.447-1-1s0.447-1,1-1s1,0.447,1,1S17.553,12,17,12z"}},{"tag":"circle","attr":{"cx":"15","cy":"13","r":"1"}},{"tag":"circle","attr":{"cx":"17","cy":"11","r":"1"}},{"tag":"path","attr":{"d":"M10 9L8 9 8 11 6 11 6 13 8 13 8 15 10 15 10 13 12 13 12 11 10 11z"}},{"tag":"path","attr":{"d":"M15,5H9c-3.848,0-6.984,3.132-7,6.988C2,11.992,2,11.996,2,12v0.016c0,0.003,0,0.007,0,0.01C2.014,15.873,5.151,19,9,19h6 c3.854,0,6.995-3.136,7-6.994c0,0,0-0.001,0-0.001c0-0.001,0-0.003,0-0.005c0-0.003,0-0.007,0-0.01C21.985,8.134,18.85,5,15,5z M15,17H9c-2.753,0-4.997-2.24-5-4.991C4.003,9.247,6.247,7,9,7h6c2.754,0,4.996,2.247,5,5.006C19.996,14.76,17.754,17,15,17z"}}]})(props);
};
