// THIS FILE IS AUTO GENERATED
var GenIcon = require('../lib').GenIcon
module.exports.GiBowlingStrike = function GiBowlingStrike (props) {
  return GenIcon({"tag":"svg","attr":{"viewBox":"0 0 512 512"},"child":[{"tag":"path","attr":{"d":"M57.685 30.997c-2.48-.013-4.73.228-6.613.692-12.055 2.97-33.197 20.964-27.258 45.074 4.695 19.063 16.214 41.833 24.03 63.607l56.564-13.933c-3.192-22.913-3.569-48.428-8.264-67.49-5.01-20.343-25.07-27.876-38.459-27.95zm278.34 18.493c-7.87-.035-16.233 2.54-22.68 9.853-10.873 12.334-21.038 31.133-32.287 46.908l36.596 32.264c14.24-13.138 31.617-25.58 42.49-37.914 13.752-15.6 5.028-37.151-2.771-44.027-4.144-3.653-12.429-7.046-21.348-7.084zm43.994 67.617c-13.351-.237-32.583 6.265-38.64 25.51-5.896 18.726-7.89 44.167-12.528 66.832l55.567 17.492c9.18-21.235 22.12-43.229 28.015-61.955C419.89 141.3 399.932 122 388.09 118.273c-2.22-.7-4.989-1.112-8.07-1.166zm-108.088.457c-.684.82-1.374 1.631-2.068 2.416-1.15 1.298-2.65 2.625-4.428 3.984L301.773 156c1.126-1.933 2.255-3.59 3.398-4.892.692-.788 1.41-1.574 2.14-2.356zm-18.691 15.008c-21.913 12.69-58.91 28.307-76.387 49.615-4.496 5.48-8.968 12.61-13.258 20.513 11.695 21.574 24.632 46.159 26.426 68.65 10.533 1.003 18.645 3.475 28.121 7.222 14.283-8.783 28.051-18.484 36.707-27.625 18.949-20.012 29.803-58.673 39.647-82.004zm-144.434 9.66L54.125 155.7c.338 1.23.665 2.456.962 3.672.492 2.011.772 4.39.895 7.059l56.164-13.834c-1.13-2.42-1.987-4.657-2.486-6.666a130.537 130.537 0 0 1-.854-3.7zm12.48 24.99l-63.765 15.707c-2.47 30.135-12.341 77.058-3.34 108.709 3.816 13.419 12.04 29.809 21.42 45.855 17.03-38.834 55.644-66.136 100.57-66.634-.383-3.138-.855-6.128-1.437-8.914-6.729-32.212-37.264-69.183-53.447-94.723zm204.298 55.84c-.35 1.227-.71 2.445-1.088 3.638-.626 1.974-1.623 4.15-2.905 6.495l55.174 17.369c.292-2.656.722-5.013 1.34-6.989.374-1.194.777-2.397 1.193-3.603zm-11.92 22.281c-13.445 18.507-35.494 43.023-49.229 67.598 15.082 18.972 24.108 42.964 24.108 69.023 0 32.424-13.968 61.649-36.198 81.983.084.803.22 2.144.22 2.144l23.685 7.457 23.685 7.455s58.375-66.515 72.807-107.24c10.99-31.017 4.118-78.47 3.564-108.701zm-136.243 43.5c-51.536 0-93.12 41.585-93.12 93.121 0 51.536 41.584 93.121 93.12 93.121 51.537 0 93.122-41.585 93.122-93.12 0-51.537-41.585-93.122-93.122-93.122zm281.088 5.772c-3.844-.034-7.858.731-11.91 2.585-14.646 6.703-31.236 19.472-47.59 29.104l19.889 43.457c17.98-6.08 38.487-10.288 53.133-16.99 18.523-8.477 19.309-31.238 15.07-40.5-3.311-7.236-14.864-17.538-28.592-17.656zm-279.732 7.6c2.665-.019 5.305.374 7.959 1.62 2.653 1.246 5.551 3.673 6.824 7.364 1.899 5.506-.7 10.59-3.4 13.449s-6 4.683-9.791 5.85c-3.79 1.166-7.544 1.502-11.344.728-3.8-.775-8.679-3.087-10.574-8.594-1.896-5.506.705-10.585 3.404-13.443 2.699-2.858 5.996-4.683 9.785-5.85 2.317-.713 4.69-1.108 7.137-1.125zm-44.328 20.015c.337-.006.678-.004 1.025.008h.002c3.132.106 6.956 1.314 9.596 4.459 3.744 4.459 3.14 10.133 1.646 13.77-1.494 3.636-3.919 6.521-7.037 8.97-3.118 2.45-6.5 4.11-10.324 4.752-3.824.641-9.205.236-12.953-4.22v-.003c-3.748-4.458-3.146-10.136-1.652-13.773 1.494-3.638 3.92-6.523 7.039-8.973 3.627-2.848 7.6-4.9 12.658-4.99zm259.234 19.97c-.385 5.798-1.017 11.525-1.984 17.083l8.603 18.802c1.778-1.281 3.45-2.315 4.992-3.025.934-.43 1.888-.845 2.852-1.254zm-219.44 7.647c2.665-.018 5.305.375 7.96 1.621 2.653 1.246 5.55 3.672 6.824 7.363 1.899 5.507-.7 10.59-3.4 13.45-2.7 2.859-6 4.682-9.792 5.85-3.79 1.166-7.543 1.502-11.343.728-3.8-.775-8.679-3.087-10.574-8.594-1.896-5.506.704-10.585 3.404-13.443 2.699-2.858 5.996-4.683 9.785-5.85 2.316-.714 4.69-1.109 7.137-1.125zm212.926 27.072c-2.746 7.713-6.742 15.926-11.455 24.274 5.555-5.649 10.78-10.995 15.506-15.42z"}}]})(props);
};
