// THIS FILE IS AUTO GENERATED
var GenIcon = require('../lib').GenIcon
module.exports.BiRun = function BiRun (props) {
  return GenIcon({"tag":"svg","attr":{"viewBox":"0 0 24 24"},"child":[{"tag":"circle","attr":{"cx":"17","cy":"4","r":"2"}},{"tag":"path","attr":{"d":"M15.777,10.969c0.376,0.563,1.008,0.89,1.666,0.89c0.16,0,0.322-0.02,0.482-0.06l3.316-0.829L20.758,9.03l-3.316,0.829 l-1.379-2.067c-0.291-0.439-0.756-0.751-1.272-0.854l-3.846-0.77c-0.888-0.181-1.778,0.265-2.181,1.067l-1.658,3.316l1.789,0.895 l1.658-3.317l1.967,0.394L7.434,17H3v2h4.434c0.698,0,1.355-0.372,1.715-0.971l1.918-3.196l5.169,1.034l1.816,5.449l1.896-0.633 l-1.815-5.448c-0.226-0.679-0.802-1.188-1.506-1.33l-3.039-0.607l1.772-2.954L15.777,10.969z"}}]})(props);
};
