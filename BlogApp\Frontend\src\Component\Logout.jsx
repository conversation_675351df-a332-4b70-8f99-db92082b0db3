import React from 'react'
import API from '../axios'

import { useNavigate } from 'react-router-dom'


const Logout = () => {
   
const navigate = useNavigate()
     const Logout= async ()=>{
        try {
            const res = await API.post('/api/user/logout')

            localStorage.removeItem("userData")
            console.log(res.data)
            navigate('/login')
        } catch (error) {
            console.log(error)
          alert("successfully logout")
            
        }
     }
              




  return (

    <div>

<button onClick={Logout}>Logout</button>

    </div>
    
  )
}

export default Logout