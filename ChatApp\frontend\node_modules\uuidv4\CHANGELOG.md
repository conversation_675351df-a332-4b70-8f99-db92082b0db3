## [6.2.13](https://github.com/thenativeweb/uuidv4/compare/6.2.12...6.2.13) (2022-03-25)


### Bug Fixes

* bump minimist from 1.2.5 to 1.2.6 ([#387](https://github.com/thenativeweb/uuidv4/issues/387)) ([f3460a3](https://github.com/thenativeweb/uuidv4/commit/f3460a33224c7389bb4bf8d946e6c27cddc7a5f9))

## [6.2.12](https://github.com/thenativeweb/uuidv4/compare/6.2.11...6.2.12) (2021-08-16)


### Bug Fixes

* bump path-parse from 1.0.6 to 1.0.7 ([#364](https://github.com/thenativeweb/uuidv4/issues/364)) ([78014ec](https://github.com/thenativeweb/uuidv4/commit/78014ec86e829a74e597d73185ef68a3fa53c2bb))

## [6.2.11](https://github.com/thenativeweb/uuidv4/compare/6.2.10...6.2.11) (2021-07-06)


### Bug Fixes

* bump @types/uuid from 8.3.0 to 8.3.1 ([#357](https://github.com/thenativeweb/uuidv4/issues/357)) ([028b971](https://github.com/thenativeweb/uuidv4/commit/028b971db72e4c549bde23b9785cd58cd4fb9366))

## [6.2.10](https://github.com/thenativeweb/uuidv4/compare/6.2.9...6.2.10) (2021-06-08)


### Bug Fixes

* bump trim-newlines from 3.0.0 to 3.0.1 ([#349](https://github.com/thenativeweb/uuidv4/issues/349)) ([3dc4a7f](https://github.com/thenativeweb/uuidv4/commit/3dc4a7f15eda76ec1986d109b8a15ecf3f620aff))

## [6.2.9](https://github.com/thenativeweb/uuidv4/compare/6.2.8...6.2.9) (2021-06-08)


### Bug Fixes

* bump glob-parent from 5.1.1 to 5.1.2 ([#350](https://github.com/thenativeweb/uuidv4/issues/350)) ([c43aae4](https://github.com/thenativeweb/uuidv4/commit/c43aae41db9e02b9fecbb8f8977a473efae26483))

## [6.2.8](https://github.com/thenativeweb/uuidv4/compare/6.2.7...6.2.8) (2021-05-10)


### Bug Fixes

* bump hosted-git-info from 2.8.8 to 2.8.9 ([#336](https://github.com/thenativeweb/uuidv4/issues/336)) ([98ca567](https://github.com/thenativeweb/uuidv4/commit/98ca567369eda02e9914b01d024d073eada10208))

## [6.2.7](https://github.com/thenativeweb/uuidv4/compare/6.2.6...6.2.7) (2021-03-25)


### Bug Fixes

* Migrate from master to main. ([#321](https://github.com/thenativeweb/uuidv4/issues/321)) ([fa5d1ed](https://github.com/thenativeweb/uuidv4/commit/fa5d1ed9626f3d91219e383a676676c510b46965))

## [6.2.6](https://github.com/thenativeweb/uuidv4/compare/6.2.5...6.2.6) (2020-12-09)


### Bug Fixes

* bump uuid from 8.3.1 to 8.3.2 ([#272](https://github.com/thenativeweb/uuidv4/issues/272)) ([a312602](https://github.com/thenativeweb/uuidv4/commit/a3126029f3dd2810f79fa2e078fc5a1ce6a0f2ae))

## [6.2.5](https://github.com/thenativeweb/uuidv4/compare/6.2.4...6.2.5) (2020-11-03)


### Bug Fixes

* Fix headline for robot section in readme. ([#233](https://github.com/thenativeweb/uuidv4/issues/233)) ([0b0b31c](https://github.com/thenativeweb/uuidv4/commit/0b0b31ccb957faf1f2966c89b712bc9bd91ebfd5))

## [6.2.4](https://github.com/thenativeweb/uuidv4/compare/6.2.3...6.2.4) (2020-10-05)


### Bug Fixes

* bump uuid from 8.3.0 to 8.3.1 ([#220](https://github.com/thenativeweb/uuidv4/issues/220)) ([f4943fc](https://github.com/thenativeweb/uuidv4/commit/f4943fce397762efe249bbfef36aacf511b103ee))

## [6.2.3](https://github.com/thenativeweb/uuidv4/compare/6.2.2...6.2.3) (2020-08-18)


### Bug Fixes

* bump @types/uuid from 8.0.1 to 8.3.0 ([#192](https://github.com/thenativeweb/uuidv4/issues/192)) ([2fe07a8](https://github.com/thenativeweb/uuidv4/commit/2fe07a804b13ab2dfecb6310bb745c84ebfc3266))

## [6.2.2](https://github.com/thenativeweb/uuidv4/compare/6.2.1...6.2.2) (2020-08-05)


### Bug Fixes

* bump @types/uuid from 8.0.0 to 8.0.1 ([#189](https://github.com/thenativeweb/uuidv4/issues/189)) ([117ad00](https://github.com/thenativeweb/uuidv4/commit/117ad00757cc9ab52b305b5ad029afa4f471ed88))

## [6.2.1](https://github.com/thenativeweb/uuidv4/compare/6.2.0...6.2.1) (2020-07-31)


### Bug Fixes

* bump uuid from 8.2.0 to 8.3.0 ([#186](https://github.com/thenativeweb/uuidv4/issues/186)) ([3666eea](https://github.com/thenativeweb/uuidv4/commit/3666eea7298b5df89bdef936900895adb6bd2e4f))

# [6.2.0](https://github.com/thenativeweb/uuidv4/compare/6.1.1...6.2.0) (2020-07-16)


### Features

* Make namespace for UUIDv5 configurable. ([3bb6fe1](https://github.com/thenativeweb/uuidv4/commit/3bb6fe10308804f904687582a5c61f778b675ae4))

## [6.1.1](https://github.com/thenativeweb/uuidv4/compare/6.1.0...6.1.1) (2020-06-24)


### Bug Fixes

* bump uuid from 8.1.0 to 8.2.0 ([#171](https://github.com/thenativeweb/uuidv4/issues/171)) ([e088395](https://github.com/thenativeweb/uuidv4/commit/e08839564cfbb4fae11f0c5813f41abd3519bf3d))

# [6.1.0](https://github.com/thenativeweb/uuidv4/compare/6.0.9...6.1.0) (2020-05-27)


### Features

* Allow UUID verification using JSON schemas. ([#167](https://github.com/thenativeweb/uuidv4/issues/167)) ([8af8a27](https://github.com/thenativeweb/uuidv4/commit/8af8a27a01a9a301484bcddf402d87840a19cbbb))

## [6.0.9](https://github.com/thenativeweb/uuidv4/compare/6.0.8...6.0.9) (2020-05-21)


### Bug Fixes

* bump uuid from 8.0.0 to 8.1.0 ([#163](https://github.com/thenativeweb/uuidv4/issues/163)) ([50ec04e](https://github.com/thenativeweb/uuidv4/commit/50ec04e86a33f123f3b4fac5038bec07c2075bee))

## [6.0.8](https://github.com/thenativeweb/uuidv4/compare/6.0.7...6.0.8) (2020-04-30)


### Bug Fixes

* bump uuid from 7.0.3 to 8.0.0 ([#155](https://github.com/thenativeweb/uuidv4/issues/155)) ([778b642](https://github.com/thenativeweb/uuidv4/commit/778b642e614a80c44853e23710c0797deea851ea))

## [6.0.7](https://github.com/thenativeweb/uuidv4/compare/6.0.6...6.0.7) (2020-03-31)


### Bug Fixes

* bump uuid from 7.0.2 to 7.0.3 ([#150](https://github.com/thenativeweb/uuidv4/issues/150)) ([e5a1bd3](https://github.com/thenativeweb/uuidv4/commit/e5a1bd3842afba47e2d7303ea22306a2dbd6d887))

## [6.0.6](https://github.com/thenativeweb/uuidv4/compare/6.0.5...6.0.6) (2020-03-04)


### Bug Fixes

* bump uuid from 7.0.1 to 7.0.2 ([#132](https://github.com/thenativeweb/uuidv4/issues/132)) ([dc8cfb6](https://github.com/thenativeweb/uuidv4/commit/dc8cfb618c4edb75cdf60270f90fcdcfa513962c))

## [6.0.5](https://github.com/thenativeweb/uuidv4/compare/6.0.4...6.0.5) (2020-02-25)


### Bug Fixes

* bump uuid from 7.0.0 to 7.0.1 ([#123](https://github.com/thenativeweb/uuidv4/issues/123)) ([f636525](https://github.com/thenativeweb/uuidv4/commit/f636525eb27f4978c9e6dc8ec434af4d8c1b5743))

## [6.0.4](https://github.com/thenativeweb/uuidv4/compare/6.0.3...6.0.4) (2020-02-25)


### Bug Fixes

* Update dependencies. ([#122](https://github.com/thenativeweb/uuidv4/issues/122)) ([8dfd604](https://github.com/thenativeweb/uuidv4/commit/8dfd604a0ea9472295664bfa8d651d7cd1a85806))

## [6.0.3](https://github.com/thenativeweb/uuidv4/compare/6.0.2...6.0.3) (2020-02-24)


### Bug Fixes

* bump uuid from 3.4.0 to 7.0.0 ([#119](https://github.com/thenativeweb/uuidv4/issues/119)) ([607ebd3](https://github.com/thenativeweb/uuidv4/commit/607ebd32259cd0519de87f0e3f63edc8865548cd))

## [6.0.2](https://github.com/thenativeweb/uuidv4/compare/6.0.1...6.0.2) (2020-01-23)


### Bug Fixes

* Fix regexs for v4 and v5 UUIDs. Closes [#87](https://github.com/thenativeweb/uuidv4/issues/87) ([#89](https://github.com/thenativeweb/uuidv4/issues/89)) ([75f273f](https://github.com/thenativeweb/uuidv4/commit/75f273fd4bb95b86f46e40b3754a987e6c7f4501))

## [6.0.1](https://github.com/thenativeweb/uuidv4/compare/6.0.0...6.0.1) (2020-01-16)


### Bug Fixes

* bump uuid from 3.3.3 to 3.4.0 ([#78](https://github.com/thenativeweb/uuidv4/issues/78)) ([a9f8831](https://github.com/thenativeweb/uuidv4/commit/a9f8831bf78e6ecfda9797a095162f91147e7701))
