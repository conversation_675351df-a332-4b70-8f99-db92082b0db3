// THIS FILE IS AUTO GENERATED
var GenIcon = require('../lib').GenIcon
module.exports.GiPathDistance = function GiPathDistance (props) {
  return GenIcon({"tag":"svg","attr":{"viewBox":"0 0 512 512"},"child":[{"tag":"path","attr":{"d":"M92.58 21.05c-32 0-64 24-64 72l64 127.95L156.6 93.05c0-48-32-72-64.02-72zM374.9 60.06l-.3.21H374.3l-3 .24h-.2l-3 .32H367.8l-3 .41h-.2l-3.1.5h-.2l-3.1.59h-.2l-2.3.49 3.8 17.6 2.1-.46 3-.57 2.8-.45 2.8-.39 2.7-.29 2.7-.22h.2zm-282.32.99a32 32 0 0 1 32.02 32A32 32 0 0 1 92.58 125a32 32 0 0 1-32-31.95 32 32 0 0 1 32-32zm302.22.16l-3 ********** 2.2.47 2.1.56 2.1.65 2 .73 2 .83 1.8.9 1 .55 8.7-15.79-1.2-.66-.2-.11-.2-.1-2.3-1.11-.2-.1-.2-.1-2.4-1-.2-.1-.2-.1-2.4-.88-.2-.1-.2-.1-2.5-.77-.2-.1h-.2l-2.5-.66-.2-.1h-.2l-2.6-.55h-.4zm-57.5 6.73l-3 1.25h-.2l-3.3 1.21h-.2l-3.3 1.28h-.2l-3.4 1.34h-.1l-3.4 1.41h-.1l-.2.1 7.1 16.55.2-.1 3.2-1.32 3.3-1.32h.1l3.2-1.21 3.1-1.11 3.1-1.05zM431 81.36l-13.7 11.71 1 1.1 1.4 1.88 1.4 2L423 101l1.7 3 1.2 2.3 16.1-8.05-1.3-2.46-.1-.21-.1-.2-1.9-3.37-.1-.19-.1-.19-2.2-3.4-.1-.18-.1-.17-1.7-2.35-.1-.17-.1-.18-1.8-2.21-.1-.18-.1-.17zm-127.8.91l-.9.56h-.1l-7.1 3.59h-.1l-.1.1-7.1 3.79h-.1l-1 .53 8.8 15.76.8-.5h.1l6.9-3.8 6.8-3.45h.1l.8-.4zm-32.3 17.83l-5 2.9v.1h-.1l-7.2 4.5h-.1l-3.2 2.1 9.8 15.1 3-1.9.1-.1 7.1-4.4 4.8-2.9zM448.2 118l-9.3 1.4-8.5 1 .1 1.4.1 1.3.1 1.3v2.4l-.1 1.3-.1 1.3-.1 1.2-.2 1.2-.2 1.2-.5 2.1 17.5 4.1.6-2.3v-.2l.1-.3.2-1.5.1-.1v-.2l.2-1.5v-.1l.1-.2.1-1.5.1-.2v-.1l.1-1.5V129.2l.1-1.6V123.7l-.1-1.6V121.8l-.2-1.5V120l-.2-1.6v-.2zm-208.1 1.7l-3.3 2.2v.1l-7.1 4.9h-.1v.1l-4.4 3.1 10.4 14.7 4.3-3.1 7-4.9h.1l3.2-2.2zm-29.6 21.1l-2 1.4v.1l-6.9 5.1-5.6 4.3 10.9 14.3 5.6-4.1v-.1l6.7-5 .1-.1 1.9-1.4zm213 8l-1 1.6-1.8 2.6-1.9 2.5-2.1 2.6-2.2 2.5-1 1 13.1 12.4 1-1.2.1-.1.1-.1 2.5-2.8.1-.1.1-.1 2.3-2.8.1-.1.1-.2 2.2-2.8.1-.2h.1l2-3h.1l.1-.2 1.1-1.8zm-241.8 14.1l-.1.1h-.1l-6.4 5.1v.1l-6.3 5.1-1.2 1 11.4 13.9 1.2-1 6.2-5v-.1l6.3-5h.1l.1-.1zm219.4 10.2l-.3.2-3.1 2.5-3.3 2.6-3.4 2.4-.1.1-3.6 2.5 10.3 14.8 3.7-2.6.1-.1h.1l3.6-2.7.1-.1 3.5-2.6.1-.1 3.4-2.6.1-.1.3-.3zm-247.4 12.8l-2.8 2.3-.1.1-10.9 9.3 11.7 13.7 10.9-9.4 2.7-2.2zm218.9 7l-.8.5h-.1l-4.1 2.4v.1h-.1l-4.2 2.4v.1l-4.4 2.4-1.5.8 8.6 15.9 1.6-.9.1-.1 4.4-2.5h.1l4.4-2.5h.1l4.3-2.6h.1l.9-.6zm-246.4 16.7l-6.1 5.5h-.1l-7.2 6.6 12.1 13.3 7.2-6.5 6.1-5.4zm215.5.3l-1.4.7-9.7 4.9h-.1l-4.7 2.3 7.7 16.2 4.9-2.4h.1l9.9-4.9h.1l.1-.1 1.3-.7zm-32 15.4l-10.1 4.5v.1l-6.3 2.7 7.3 16.5 6.3-2.8h.1l10.1-4.6zM99.58 234l-1.5 1.3h-.1v.1l-8.59 8.1v.1l-3.02 2.9 12.61 13 2.92-2.9 8.6-8.1 1.4-1.2zm177.32 5.8l-10.1 4.3-6.4 2.7 7 16.7 6.4-2.7 10.2-4.4zm-33.1 14l-10.9 4.7-5.7 2.3 7 16.7 5.7-2.4 10.9-4.6zm184.8 7.2c-32 0-64 24-64 72l64 128 64-128c0-48-32-72-64-72zm-218 6.8l-.3.2h-.1l-11.2 4.8h-.1l-4.9 2.1 7.1 16.6 4.9-2.1 11.2-4.8h.1l.3-.2zM177.4 282l-.8.3v.2l-11 4.8v.1l-4.7 2.1 7.4 16.4 4.6-2.1.1-.2 10.8-4.6.1-.2.7-.3zm-33 15l-.2.1h-.1l-10.3 5h-.1L128 305l8.1 16.1 5.5-2.8 10.2-4.8.1-.2h.1zm284.2 4a32 32 0 0 1 32 32 32 32 0 0 1-32 32 32 32 0 0 1-32-32 32 32 0 0 1 32-32zm-316.8 12.3l-2.9 1.5v.2l-4.7 2.5v.1h-.1l-4.62 2.5v.1l-3.68 2.1 9 15.6 3.6-2.1h.1l4.4-2.5 4.5-2.5h.1l2.8-1.5zM80.09 332l-2.43 1.5-.1.1h-.1l-3.95 2.7h-.1l-.1.1-3.84 2.7h-.2l-3.71 2.8h-.1l-.1.1-.89.6L75.35 357l.83-.7h.1l3.52-2.6 3.98-2.6 3.78-2.5.1-.1 2.26-1.4zM50.2 354.6l-1.81 1.7-.1.1-.1.1-2.9 2.8-.1.1-.1.1-2.76 2.8-.1.2-.1.1-2.61 2.8-.1.1-.1.2-2.33 2.7 13.73 11.7 2.25-2.7 2.38-2.6 2.58-2.7 2.75-2.7 1.71-1.5zm223.9.9l-2.1.1h-.2l-2.1.1H269.4l-1.4.2 1.6 17.9 1.3-.2 1.9-.1 1.7-.1h1.8l1.8.1 1.8.1 1.7.2 1.7.2 1.9.3 3.1-17.7-2.2-.4h-.4l-2-.3H283.4l-2.1-.2h-.2l-2.1-.1H278.7l-2-.1h-2.5zm-25.4 4l-.1.1h-.2l-4.3 1.3-.2.1h-.1l-4.4 1.5-.1.1h-.2l-4.4 1.7-.1.1h-.1l-3.7 1.5 6.8 16.7 3.7-1.5 4.1-1.6 4-1.4 4.3-1.3-1.8-5.7-3-11.6zm59.6 5.5l-10.2 14.8 1.5 1.1 1.9 1.4 1.9 1.6 1.8 1.7 1.9 1.9 1.8 1.8.8 1 13.6-11.8-.9-1-.1-.2-.1-.1-2-2.2-.1-.1-.2-.1-2-2.1-.2-.1-.1-.1-2.1-2-.2-.1-.1-.2-2.2-1.8-.2-.2-.1-.1-2.3-1.7-.1-.2-.2-.1zm-94.5 9l-2.3 1.1-.1.2-4.5 2.5h-.1l-4.5 2.6h-.1v.1l-4.5 2.6 9.1 15.5 4.5-2.6 4.3-2.5h.1l4.2-2.3v-.2l2.3-1.1zM26.03 385l-.11.1-.1.2-1.44 3-.1.2-.1.3-1.26 3-.1.3-.1.2-1.07 3.2-.1.1-.1.4-.89 3.1-.1.3-.1.3-.69 3.1v.4l-.25 1.5 17.74 3.1.23-1.4.58-2.6.74-2.7.9-2.6 1.07-2.6 1.35-2.8-8.05-3.8zm156.07 7.8l-7.2 4.7-.1.1-7.8 5.2 10 15 7.8-5.2v-.1l7.1-4.7zm152.7 1.2l-15.1 9.8 1.4 2.2h.1l3.4 5.5 3.4 5.6.9 1.6 15.6-9-1-1.6v-.1l-3.5-5.9-.1-.1-3.5-5.7-.1-.1zm-182.7 19l-12.5 8.3-2.4 1.7 10 15 2.5-1.7v-.1h.1l12.4-8.4zm-114.8 9.5l-2.81.6-14.62 2.7v.4l.35 1.7v.2l.42 1.7V430.1l.46 1.7V432.1l.53 1.7v.2l.59 1.7V436l.64 1.7.1.1v.1l.7 1.8.1.1.1.1.75 1.7.1.2.1.1 1.46 3.1 16.22-7.8-1.38-2.9-.64-1.5-.63-1.5-.52-1.4-.53-1.6-.44-1.4-.39-1.4-.36-1.5-.3-1.4zm316.1 2.8l-15.6 9 .3.5v.1h.1l3.4 5.9.1.1 3.5 5.7v.1l.1.1 2.1 3.3 15.1-9.7-2-3.2-3.4-5.5v-.1l-3.4-5.7v-.1zm-231 7.5l-3.4 2.1h-.1l-3.9 2.4v.1h-.1l-3.9 2.3-3.7 2.1 8.9 15.7 3.8-2.2.1-.1h.1l4-2.4.1-.1 4.1-2.5h.1l3.4-2.2zm-71.27 16.3l-12.28 13.2.81.7.15.1.16.2 1.24 1.1.16.1.17.1 1.26 1 .17.1.18.2 1.29.9.18.1.18.1 1.31.8.19.2.2.1 1.33.7.2.1.19.1 1.36.7.2.1.21.1 1.38.6.2.1.21.1 1.4.5.21.1h.21l1.42.5.21.1h.21l1.44.4.21.1h.2l1.47.3 3.92-17.5-1.28-.3-1.02-.3-1.01-.3-1.01-.4-.98-.4-.95-.5-.93-.5-.96-.6-.93-.7-.9-.7-.95-.8zm41.01.3l-3.05 1.3-3.35 1.3-3.22 1.1-3.09.9-2.92.6 4.15 17.6 3.12-.8H84l.21-.1 3.51-1 .19-.1h.18l3.58-1.2.17-.1H92l3.64-1.4.15-.1.14-.1 3.15-1.3zm280.26 4.8l-13.9 11.3.4.5v.1l.1.1 2 2.3.1.1.1.1 2.1 2.3.1.1.1.1 2.1 2.1.1.1.1.1 2.2 2.1.1.1.2.1 2.2 1.9.1.1.2.2 1.9 1.5 11.1-14.2-1.8-1.4-1.9-1.6-1.9-1.8-1.8-1.8-1.8-2-1.9-2.2zm24.1 17.5l-4.5 17.5 2 .5h.3l.2.1 2.9.5.3.1h.3l2.9.4h.4l1.5.1H403.1l1.5.1h2.1l1.6-.1H408.6l1.6-.1h.2l1.6-.2H412.3l.9-.1-2.2-17.9-.8.1-1.3.2h-1.3l-1.2.1h-1.3l-1.2-.1h-1.1l-2.4-.3-2.3-.5z"}}]})(props);
};
