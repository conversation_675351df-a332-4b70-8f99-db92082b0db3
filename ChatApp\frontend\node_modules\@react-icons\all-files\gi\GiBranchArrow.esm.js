// THIS FILE IS AUTO GENERATED
import { GenIcon } from '../lib';
export function GiBranchArrow (props) {
  return GenIcon({"tag":"svg","attr":{"viewBox":"0 0 512 512"},"child":[{"tag":"path","attr":{"d":"M95.053 18.096l.002.004v-.004h-.002zm.002.004L88.43 75.234l64.943 69.444c22.73-80.96-33.616-62.34-58.318-126.578zM76.307 89.633l-59.713 6.924c73.505 19.88 58.272 84.583 122.982 60.728l-63.27-67.652zm193.775 25.39c-59.893 27.56-58.306 75.928 3.877 110.715 39.215-50.06-20.79-53.14-3.878-110.715zm-103.77 42.375l-13.14 13.29c30.208 29.86 63.767 58.348 98.982 83.347l54.108 81.645 64.763 51.605c2.657-5.314 5.914-9.756 9.95-13.38.516-.465 1.045-.9 1.578-1.33l-42.22-33.643.692-.233c-4.833-14.35-4.43-28.26.516-42.946l-17.71-5.965c-3.46 10.272-5.133 20.8-4.78 31.353l-53.19-80.256-1.43-1.01c-34.845-24.603-68.176-52.88-98.12-82.48zM244.52 301.4c-16.637-.16-33.458 13.866-46.784 44.596 49.157-22.96 48.114 35.165 92.475-4.058-12.595-26.026-29.05-40.376-45.69-40.538zm200.507 43.03l-15.737 27.808 30.877 87.125-89.932-31.867-27.494 13.393 152.524 54.606-50.24-151.066zm-32.426 36.668c-8.966.967-14.955 2.948-19.14 6.71-4.377 3.93-8.102 11.308-10.782 24.27l46.775 16.574-16.853-47.554z"}}]})(props);
};
