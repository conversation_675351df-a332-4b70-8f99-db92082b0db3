import { StrictMode } from 'react'
import { createRoot } from 'react-dom/client'
import "@radix-ui/themes/styles.css";
import { Theme } from "@radix-ui/themes";
import './index.css'
import App from './App.jsx'
import { AuthProvider } from './Context/AuthProvider.jsx'
import { BrowserRouter } from 'react-router-dom'

import { PostProvider} from './Context/PostProvider.jsx'

createRoot(document.getElementById('root')).render(

  <BrowserRouter>
  <AuthProvider>
    <PostProvider>
    <Theme>
    <App />

    </Theme>
    </PostProvider>
    </AuthProvider>

    </BrowserRouter>
 
)
