// THIS FILE IS AUTO GENERATED
var GenIcon = require('../lib').GenIcon
module.exports.GiBowlingPropulsion = function GiBowlingPropulsion (props) {
  return GenIcon({"tag":"svg","attr":{"viewBox":"0 0 512 512"},"child":[{"tag":"path","attr":{"d":"M155.68.252l-.012.016h.03l-.018-.016zM86.924 18.955C74.384 30.29 66.502 46.685 66.502 64.92c0 34.21 27.732 61.945 61.943 61.945 26.94 0 49.85-17.2 58.38-41.215.754 5.132 1.15 10.38 1.15 15.725 0 59.227-47.884 107.11-107.112 107.11-22.086 0-42.535-6.67-59.572-18.108v21.78c17.75 9.562 38.054 15.015 59.573 15.015 69.34 0 125.8-56.458 125.8-125.797 0-31.478-11.624-60.325-30.802-82.42H86.925zm115.562 0c17.02 23.856 27.02 52.967 27.02 84.484 0 80.68-65.484 146-146.43 146-22.106 0-43.014-4.89-61.785-13.626v20.366c19.1 7.697 39.97 11.95 61.786 11.95 1.133 0 2.26-.02 3.39-.044l71.083 98.89-12.052-88.488 120.53 215.72h21.408L163.922 273.142l172.012 221.064H496.56V339.145L307.17 197.165l130.154 43.056-169.207-113.745 68.66 6.615-89.08-42.438c-2.012-26.043-10.118-50.43-22.884-71.697h-22.324zM41.27 108.52c-7.723 0-14.743 2.99-19.98 7.87v42.903c5.237 4.88 12.257 7.87 19.98 7.87 16.193 0 29.32-13.13 29.32-29.323 0-16.193-13.127-29.32-29.32-29.32z"}}]})(props);
};
