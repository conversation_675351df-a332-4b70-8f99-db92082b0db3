// THIS FILE IS AUTO GENERATED
var GenIcon = require('../lib').GenIcon
module.exports.DiGulp = function DiGulp (props) {
  return GenIcon({"tag":"svg","attr":{"version":"1.1","viewBox":"0 0 32 32"},"child":[{"tag":"path","attr":{"d":"M22.211 0.517c-0.031 0.025-0.906 0.931-1.938 2.019l-1.875 1.975-0.75 2.925-2.094 0.037c-3.894 0.081-6.194 0.394-6.619 0.9-0.081 0.094-0.069 0.125 0.075 0.25 1.019 0.875 9.062 1.131 12.781 0.412 1.319-0.256 1.656-0.575 0.938-0.881-0.544-0.237-2.538-0.563-3.431-0.563-0.244 0-0.3-0.019-0.3-0.112 0-0.056 0.137-0.631 0.3-1.269l0.294-1.162 1.75-1.85c0.963-1.019 1.756-1.9 1.769-1.95 0.063-0.281-0.681-0.881-0.9-0.731zM8.998 9.18c0 0.188 0.756 7.3 0.794 7.412 0.012 0.050 0.075-0.044 0.137-0.225 0.556-1.556 1.662-2.875 2.6-3.094 0.456-0.106 0.8 0.006 1.169 0.375 0.406 0.406 0.481 0.844 0.169 1.012-0.156 0.081-0.306-0.031-0.506-0.394-0.431-0.769-1.231-0.363-2.094 1.075-0.912 1.512-1.15 3.188-0.456 3.188 0.256 0 0.875-0.488 1.306-1.025 0.294-0.375 0.406-0.581 0.569-1.063 0.106-0.325 0.231-0.625 0.281-0.662s0.162-0.063 0.262-0.050c0.162 0.019 0.175 0.044 0.194 0.313 0.012 0.206-0.037 0.456-0.175 0.844-0.181 0.512-0.387 1.169-0.775 2.456-0.087 0.294-0.2 0.594-0.256 0.669-0.137 0.194-0.331 0.181-0.419-0.019-0.063-0.131-0.044-0.269 0.131-0.856 0.113-0.387 0.225-0.756 0.244-0.825 0.019-0.081-0.125 0.025-0.394 0.275-0.625 0.581-1.238 0.825-1.6 0.631-0.106-0.056-0.119-0.044-0.119 0.106 0 0.475 0.45 4.738 0.506 4.806 0.188 0.225 1.519 0.606 2.869 0.819 1.031 0.162 3.969 0.162 5 0 0.887-0.144 1.856-0.369 2.325-0.544 0.662-0.244 0.581 0.025 0.956-3.219 0.181-1.581 0.325-2.887 0.313-2.894-0.012-0.012-0.213 0.137-0.444 0.331-0.5 0.425-0.831 0.588-1.2 0.594-0.331 0-0.5-0.081-0.575-0.287-0.088-0.219 0.031-0.475 0.506-1.063 0.531-0.656 0.619-0.781 0.619-0.913 0-0.281-0.644 0.012-1.012 0.463-0.119 0.15-0.294 0.394-0.387 0.544-0.212 0.331-0.762 1.587-1.037 2.369-0.213 0.606-0.412 0.887-0.619 0.887-0.119 0-0.319-0.15-0.319-0.238 0-0.163 0.2-0.669 0.569-1.475 0.238-0.512 0.431-0.944 0.431-0.956s-0.119 0.069-0.269 0.188c-0.356 0.294-0.75 0.481-0.994 0.481-0.238 0-0.488-0.15-0.488-0.287 0-0.113 0.012-0.119-0.381 0.1-0.594 0.325-0.931 0.206-0.931-0.338v-0.25l-0.256 0.269c-0.469 0.481-1.106 0.65-1.456 0.375-0.225-0.175-0.219-0.588 0.019-1.3 0.431-1.275 0.762-1.775 1.056-1.587 0.181 0.113 0.15 0.294-0.137 0.887-0.494 1.019-0.525 1.5-0.087 1.375 0.419-0.119 0.919-0.775 1.269-1.65 0.094-0.244 0.212-0.488 0.269-0.55 0.137-0.156 0.344-0.113 0.413 0.087 0.044 0.131 0 0.306-0.244 0.906-0.2 0.488-0.306 0.838-0.306 1.025-0.006 0.269 0 0.281 0.175 0.269 0.225-0.019 0.587-0.256 0.637-0.425 0.194-0.631 1.637-4.156 1.75-4.281 0.125-0.125 0.175-0.144 0.319-0.088 0.106 0.037 0.181 0.113 0.194 0.194 0.012 0.075-0.106 0.425-0.262 0.781-0.613 1.419-1.412 3.469-1.412 3.625 0 0.1 0.031 0.175 0.063 0.175 0.15 0 0.563-0.262 1.012-0.644 0.4-0.35 0.494-0.463 0.581-0.744 0.238-0.725 0.412-0.931 0.669-0.794 0.1 0.056 0.119 0.113 0.094 0.281-0.019 0.119-0.019 0.213 0 0.213s0.162-0.113 0.313-0.256c0.475-0.431 0.881-0.538 1.256-0.306 0.175 0.106 0.2 0.156 0.2 0.394 0 0.325-0.25 0.769-0.775 1.387-0.194 0.225-0.35 0.438-0.35 0.475 0 0.031 0.063 0.056 0.144 0.056 0.137 0 1.188-0.806 1.419-1.088 0.069-0.087 0.213-1.238 0.544-4.281 0.244-2.287 0.444-4.169 0.431-4.175-0.006-0.012-0.169 0.037-0.363 0.106-0.488 0.169-1.544 0.35-2.813 0.481-1.375 0.137-6.031 0.156-7.394 0.031-1.269-0.119-2.494-0.313-3.013-0.481l-0.456-0.144v0.137zM10.873 24.686c0 0.012 0.2 0.363 0.438 0.775l0.444 0.756 0.181 2.512 0.188 2.519 0.188 0.162c0.469 0.4 1.594 0.625 3.344 0.663 1.988 0.044 3.537-0.206 4.044-0.663l0.2-0.175 0.144-2.537 0.144-2.531 0.406-0.713c0.225-0.387 0.406-0.725 0.406-0.75 0-0.019-0.188 0.025-0.425 0.106-0.844 0.287-2.488 0.544-3.95 0.625-1.625 0.088-4.144-0.219-5.419-0.662-0.181-0.063-0.331-0.1-0.331-0.088z"}}]})(props);
};
