// THIS FILE IS AUTO GENERATED
var GenIcon = require('../lib').GenIcon
module.exports.GiGuardedTower = function GiGuardedTower (props) {
  return GenIcon({"tag":"svg","attr":{"viewBox":"0 0 512 512"},"child":[{"tag":"path","attr":{"d":"M71.604 21.99v111.12l61.156 75.814v188.654H93.408l-28.7 92.836h151.1l-6.103-92.613h.043l-1.17-119.35-18.687.185.848 86.545h-22.92c-13.43-71.44 4.462-150.097 60.967-158.86-9.133-11.458-15.006-27.38-15.006-45.02 0-34.844 22.177-62.962 49.413-62.962 1.703 0 3.466-.262 5.123 0h.002c24.873 3.23 44.29 30.293 44.29 62.96 0 18.09-6.196 34.244-15.74 45.755 51.727 9.73 76.564 84.763 61.333 158.127h-24.616l.85-86.545-18.688-.184-1.115 113.64-7.323 98.324h146.54l-28.702-92.836H385.79V208.924l61.155-75.813V21.99H382.52v52.414h-39.213V21.99h-65.123v52.414H239.67V21.99h-65.125v52.414h-38.512V21.99h-64.43zm180.652 326.62v142.245h18.69V348.61h-18.69z"}}]})(props);
};
