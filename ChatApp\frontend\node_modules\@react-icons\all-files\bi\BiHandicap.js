// THIS FILE IS AUTO GENERATED
var GenIcon = require('../lib').GenIcon
module.exports.BiHandicap = function BiHandicap (props) {
  return GenIcon({"tag":"svg","attr":{"viewBox":"0 0 24 24"},"child":[{"tag":"circle","attr":{"cx":"9","cy":"4","r":"2"}},{"tag":"path","attr":{"d":"M16.98,14.804C16.887,14.337,16.477,14,16,14h-4.133l-0.429-3H16V9h-4.847L10.99,7.858C10.92,7.366,10.498,7,10,7H9 C8.71,7,8.435,7.126,8.244,7.345C8.055,7.563,7.969,7.854,8.01,8.142l0.877,6.142C9.026,15.262,9.878,16,10.867,16h4.313 l0.839,4.196C16.113,20.663,16.523,21,17,21h3v-2h-2.181L16.98,14.804z"}},{"tag":"path","attr":{"d":"M12.51,17.5C11.771,18.976,10.26,20,8.5,20C6.019,20,4,17.981,4,15.5c0-1.886,1.169-3.498,2.817-4.167L6.528,9.308 C3.905,10.145,2,12.604,2,15.5C2,19.084,4.916,22,8.5,22c2.348,0,4.402-1.256,5.545-3.126L13.771,17.5H12.51z"}}]})(props);
};
