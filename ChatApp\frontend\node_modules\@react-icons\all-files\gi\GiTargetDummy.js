// THIS FILE IS AUTO GENERATED
var GenIcon = require('../lib').GenIcon
module.exports.GiTargetDummy = function GiTargetDummy (props) {
  return GenIcon({"tag":"svg","attr":{"viewBox":"0 0 512 512"},"child":[{"tag":"path","attr":{"d":"M304.482 19.73c-19.18.347-39.96 9.122-56.498 25.66-22.422 22.423-30.554 52.63-22.783 76.09-65.956-30.317-59.724-32.208-123.327-73.015-10.072-6.462-19.445-9.412-27.816-9.805-13.953-.655-25.126 5.793-32.135 14.926-11.214 14.613-12.37 36.164 10.95 54.418 31.027 24.286 58.825 41.022 79.015 59.45 20.19 18.43 32.658 40.606 28.355 70.046v.002c-3.16 21.608-13.68 37.998-26.46 51.47-12.78 13.47-27.77 24.476-41.46 36.676-27.38 24.4-50.32 51.77-45.052 114.266 3.327 39.483 34.167 55.107 59.666 52.365 12.75-1.372 23.497-7.357 29.394-17.49 5.897-10.13 7.69-25.42-1.037-47.733h-.002c-7.592-19.416 3.128-40.832 18.368-57.09 15.24-16.26 36.807-28.817 58.852-24.998 6.175 1.07 11.446 4.722 15.056 9.157 3.61 4.435 5.982 9.604 7.955 15.234 3.948 11.262 6.26 24.69 9.117 38.58 5.71 27.777 13.67 55.973 33.068 68.474 37.964 24.468 75.254 17.395 91.902.44 8.325-8.475 11.916-18.84 9.127-31.14-2.788-12.297-12.694-27.158-34.267-41.845-23.664-16.11-32.657-48.282-33.846-80.118-1.188-31.836 5.29-64.08 20.13-84.033 6.878-9.25 17.534-13.128 29.645-17.417 12.11-4.29 26.21-8.48 40.753-14.694 29.086-12.426 59.652-32.204 79.14-76.788 17.078-39.068 3.357-64.292-15.298-73.474-9.327-4.592-20.128-5.156-30.435-.65-10.308 4.505-20.442 14.216-27.45 31.78-13.14 32.932-39.195 51.68-70.414 56.41-8.096 1.225-16.527 1.586-25.223 1.163 7.505-4.07 14.71-9.39 21.25-15.93 29.4-29.4 34.27-72.202 10.87-95.6-10.236-10.237-24.187-15.063-39.105-14.79zm-51.925 131.79c.84-.02 1.683-.017 2.525.007 4.49.13 8.978.873 13.393 2.305 17.656 5.73 29.82 21.068 35.47 39.566 5.65 18.5 5.292 40.598-1.843 62.635-7.136 22.038-19.795 40.148-35.213 51.815-15.42 11.666-34.268 16.947-51.925 11.218-17.657-5.73-29.82-21.067-35.47-39.566-5.65-18.5-5.29-40.6 1.845-62.637 7.135-22.038 19.795-40.148 35.213-51.814 10.84-8.204 23.378-13.25 36.004-13.53zm.21 18.65c-8.075.183-16.6 3.475-24.937 9.783-11.857 8.972-22.66 23.984-28.71 42.666-6.048 18.68-6.098 37.188-1.75 51.42 4.346 14.234 12.583 23.75 23.362 27.25 10.78 3.497 23.022.627 34.88-8.345 11.857-8.972 22.66-23.986 28.708-42.668 6.05-18.68 6.097-37.187 1.75-51.42-4.346-14.232-12.583-23.75-23.363-27.248-2.695-.875-5.48-1.352-8.33-1.434-.534-.016-1.07-.018-1.61-.006zm-7.25 27.643c.412-.01.826-.01 1.24.003 2.203.065 4.426.448 6.62 1.204 8.777 3.022 14.27 10.818 16.682 19.2 2.41 8.383 2.197 17.916-.7 27.415-2.9 9.498-8.015 17.453-14.762 22.87-6.748 5.42-15.967 8.365-24.744 5.343-8.778-3.023-14.27-10.82-16.682-19.202-2.412-8.382-2.2-17.917.7-27.416 2.897-9.498 8.01-17.453 14.76-22.87 4.743-3.81 10.71-6.398 16.886-6.548zm.46 18.63c-.477-.017-.995.038-1.563.192-1.137.307-2.475 1.006-4.082 2.297-3.215 2.58-6.668 7.466-8.586 13.752-1.917 6.285-1.854 12.49-.615 16.795 1.24 4.303 3.2 6.144 4.808 6.698 1.608.554 3.743.337 6.958-2.244 3.216-2.582 6.67-7.467 8.588-13.752 1.918-6.285 1.854-12.49.616-16.793-1.24-4.306-3.2-6.147-4.81-6.7-.4-.14-.837-.23-1.313-.247z","fillRule":"evenodd"}}]})(props);
};
