// THIS FILE IS AUTO GENERATED
var GenIcon = require('../lib').GenIcon
module.exports.BiCrosshair = function BiCrosshair (props) {
  return GenIcon({"tag":"svg","attr":{"viewBox":"0 0 24 24"},"child":[{"tag":"path","attr":{"d":"M12,2C6.486,2,2,6.486,2,12s4.486,10,10,10s10-4.486,10-10S17.514,2,12,2z M13,19.931V17h-2v2.931 C7.389,19.478,4.522,16.611,4.069,13H7v-2H4.069C4.522,7.389,7.389,4.522,11,4.069V7h2V4.069c3.611,0.453,6.478,3.319,6.931,6.931 H17v2h2.931C19.478,16.611,16.611,19.478,13,19.931z"}}]})(props);
};
