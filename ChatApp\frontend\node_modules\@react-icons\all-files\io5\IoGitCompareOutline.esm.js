// THIS FILE IS AUTO GENERATED
import { GenIcon } from '../lib';
export function IoGitCompareOutline (props) {
  return GenIcon({"tag":"svg","attr":{"viewBox":"0 0 512 512"},"child":[{"tag":"path","attr":{"fill":"none","strokeLinecap":"round","strokeLinejoin":"round","strokeWidth":"32","d":"M304 160l-64-64 64-64m-97 320l64 64-64 64"}},{"tag":"circle","attr":{"cx":"112","cy":"96","r":"48","fill":"none","strokeLinecap":"round","strokeLinejoin":"round","strokeWidth":"32"}},{"tag":"circle","attr":{"cx":"400","cy":"416","r":"48","fill":"none","strokeLinecap":"round","strokeLinejoin":"round","strokeWidth":"32"}},{"tag":"path","attr":{"fill":"none","strokeLinecap":"round","strokeLinejoin":"round","strokeWidth":"32","d":"M256 96h84a60 60 0 0160 60v212m-145 48h-84a60 60 0 01-60-60V144"}}]})(props);
};
