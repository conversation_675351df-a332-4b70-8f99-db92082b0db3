// THIS FILE IS AUTO GENERATED
var GenIcon = require('../lib').GenIcon
module.exports.GiTripleLock = function GiTripleLock (props) {
  return GenIcon({"tag":"svg","attr":{"viewBox":"0 0 512 512"},"child":[{"tag":"path","attr":{"d":"M205.5 22.46c-61.276.074-108.74 30.975-141.547 69.665-17.357 21.62-31.42 48.005-40.875 79.04l126.236 33.823-2.418 9.024c-4.61 17.21-1.716 30.275 4.954 40.35 6.67 10.074 17.62 17.196 29.87 20.27 12.247 3.072 25.55 2.006 36.512-3.54 7.643-3.866 14.295-9.773 19.098-18.477-9.31-13.04-13.174-27.55-12.412-41.334.956-17.288 8.75-33.3 20.426-45.356 11.676-12.056 27.436-20.32 44.767-21.383 14.838-.908 30.607 3.823 44.77 15.114l8.034-8.035c-15.862-20.453-34.494-37.005-54.187-49.4-26.933-16.854-52.138-19.52-73.422-9.89-1.975.894-3.926 1.904-5.85 3.025.987.835 1.953 1.705 2.883 2.635 18.255 18.255 18.256 48.136 0 66.393-14.328 14.327-35.815 17.405-53.225 9.242l-71.375-19.127 2.418-9.02C106.016 86.293 152.1 57.25 203.084 56.84c3.4-.028 6.82.072 10.256.3 50.24 3.315 104.02 31.904 142.892 81.16l27.995-27.993c-12.52-16.068-26.082-29.883-40.356-41.56-49.615-39.21-98.123-46.334-138.37-46.286zM427.69 93.27l-92.41 92.41-6.608-6.606c-12.6-12.6-25.358-16.62-37.418-15.883-12.06.738-23.703 6.66-32.488 15.732-8.786 9.07-14.515 21.123-15.194 33.39-.547 9.894 1.928 20.006 9.12 29.768 16.166-1.687 30.865 2.235 42.552 9.873 14.495 9.473 24.466 24.23 29.068 40.37 4.603 16.14 3.88 33.92-3.865 49.46-6.63 13.304-18.614 24.595-35.474 31.213l1.806 6.744c24.85-3.646 47.784-11.384 67.814-21.953 28.048-14.897 42.954-35.387 45.256-58.63.213-2.156.314-4.35.304-6.58-1.216.438-2.453.84-3.722 1.18-24.937 6.683-50.816-8.256-57.498-33.195-5.245-19.573 2.834-39.723 18.61-50.718l52.25-52.246 6.604 6.605c46.213 46.213 45.53 105.066 14.91 150.848-27.682 41.387-78.487 73.422-139.658 82.87l10.248 38.246c90.136-13.23 151.69-66.77 175.885-132.922 24.066-65.8 11.283-144.29-50.094-209.976zm-248.55 9.603c-7.21 0-14.424 2.78-19.98 8.337-11.114 11.114-11.112 28.847 0 39.96 11.114 11.113 28.85 11.11 39.96 0 11.115-11.115 11.113-28.848 0-39.96-5.556-5.557-12.768-8.336-19.98-8.336zM63.927 201.45c-36.107 86.415-20.79 168.475 25.242 223.51 44.95 53.74 119.32 81.915 206.893 61.604l-23.177-86.5c-.354.027-.704.065-1.06.09l-.39-5.504-9.197-34.32 9.024-2.418c17.21-4.612 27.077-13.65 32.467-24.465 5.39-10.813 6.082-23.858 2.62-36.002-3.464-12.144-11.038-23.132-21.324-29.853-8.428-5.508-18.61-8.425-30.923-6.914-6.674 12.36-16.37 21.49-27.432 27.086-15.45 7.816-33.217 9.072-49.496 4.988-16.28-4.084-31.315-13.6-40.9-28.078-8.207-12.395-11.992-28.417-9.293-46.328l-6.623-1.774c-10.547 24.858-15.907 50.268-16.828 74.426-1.124 31.74 9.168 54.897 28.146 68.512 1.76 1.263 3.61 2.45 5.545 3.554.23-1.272.5-2.544.84-3.814 5.664-21.138 25.12-35.095 46.106-34.787.677.01 1.355.034 2.035.074 3.11.182 6.24.68 9.36 1.516 19.57 5.244 32.98 22.314 34.616 41.474L249.3 438.9l-9.025 2.418c-63.128 16.915-113.755-13.102-138.093-62.51-22.604-45.89-24.443-107.98-.03-167.115L63.926 201.45zm319.95 18.685c-.41.007-.82.025-1.23.05-1.882.118-3.782.424-5.68.932-15.18 4.068-24.047 19.428-19.98 34.608 4.068 15.18 19.428 24.047 34.608 19.98 15.18-4.068 24.048-19.428 19.98-34.607-3.448-12.87-15.01-21.2-27.7-20.963zM183.812 343.123c-12.69-.237-24.25 8.096-27.698 20.963-4.068 15.182 4.8 30.54 19.98 34.607 15.18 4.068 30.54-4.8 34.606-19.98 4.07-15.182-4.8-30.538-19.98-34.606-1.897-.508-3.796-.816-5.677-.933-.41-.026-.82-.043-1.23-.05z","fillRule":"evenodd"}}]})(props);
};
