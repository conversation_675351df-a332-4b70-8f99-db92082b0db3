// THIS FILE IS AUTO GENERATED
var GenIcon = require('../lib').GenIcon
module.exports.GiParrotHead = function GiParrotHead (props) {
  return GenIcon({"tag":"svg","attr":{"viewBox":"0 0 512 512"},"child":[{"tag":"path","attr":{"d":"M246 18.844c-2.157.02-4.292.068-6.406.156-73.28 3.064-120.385 46.286-139.406 94.094-11.706 29.42-12.328 60.463-1.813 84.78 10.515 24.32 31.522 42.552 66.906 47.845 18.85 2.818 37.402 5.89 55.314 9.5 59.88-25.15 71.217-72.16 86.625-115.19 7.735-21.602 16.277-42.437 33.874-56.655 12.758-10.31 29.792-16.212 52.47-16.563-28.51-20.475-62.022-35.544-99.095-43.093-17.22-3.507-33.372-5.024-48.47-4.876zm147.47 66.53c-19.04.243-31.413 5.12-40.626 12.564-12.818 10.356-20.528 27.42-28.03 48.374-13.216 36.904-26.567 85.154-75.752 115.282 42.035 10.47 78.926 24.997 105.188 47.5 21.364 18.306 35.342 42.586 37.406 72.75 1.54 22.492-3.3 47.973-15.25 77.187C477.462 385.23 506.27 291.847 488.844 209.75c-9.833-46.322-34.647-89.155-70.344-122.5-9.447-1.382-17.738-1.968-25.03-1.875zm-308.376 15.44c-20.48 6.425-42.002 17.384-64.28 33.655l-.002 359.155h230.875c11.166-23.298 19.82-46.743 26.188-69.97-34.545-1.148-67.374-12.59-94.563-31.06-48.912-33.232-80.487-89.767-72.25-149.22-13.442-10.208-23.455-23.32-29.843-38.094-12.89-29.806-11.65-65.73 1.624-99.093.717-1.802 1.46-3.587 2.25-5.374zm144.656 27.124c23.412 0 42.406 18.968 42.406 42.374 0 23.407-18.994 42.375-42.406 42.375s-42.375-18.968-42.375-42.375c0-23.406 18.963-42.375 42.375-42.375zM128.78 254.125c-3.5 48.926 23.382 94.734 65.032 123.03 44.693 30.365 105.29 39.94 162.657 9.658-6.057-.988-11.844-2.296-17.376-3.907-13.214-17.616-20.057-42.566-18.688-63.187.245-3.687.764-7.21 1.5-10.533-5.35-3.108-11.05-6.05-17.094-8.812-1.66 5.77-2.648 11.858-3.062 18.094-1.14 17.168 2.01 35.7 9.188 52.592-35.888-20.445-55.414-55.432-58.22-88.875-27.608-7.465-58.322-13.202-90.187-17.968-12.5-1.87-23.76-5.332-33.75-10.095z","fillRule":"evenodd"}}]})(props);
};
