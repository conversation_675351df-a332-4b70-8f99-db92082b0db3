(()=>{var $;function U(B){return U=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(C){return typeof C}:function(C){return C&&typeof Symbol=="function"&&C.constructor===Symbol&&C!==Symbol.prototype?"symbol":typeof C},U(B)}function A(B,C){var G=Object.keys(B);if(Object.getOwnPropertySymbols){var H=Object.getOwnPropertySymbols(B);C&&(H=H.filter(function(J){return Object.getOwnPropertyDescriptor(B,J).enumerable})),G.push.apply(G,H)}return G}function Q(B){for(var C=1;C<arguments.length;C++){var G=arguments[C]!=null?arguments[C]:{};C%2?A(Object(G),!0).forEach(function(H){z(B,H,G[H])}):Object.getOwnPropertyDescriptors?Object.defineProperties(B,Object.getOwnPropertyDescriptors(G)):A(Object(G)).forEach(function(H){Object.defineProperty(B,H,Object.getOwnPropertyDescriptor(G,H))})}return B}function z(B,C,G){if(C=E(C),C in B)Object.defineProperty(B,C,{value:G,enumerable:!0,configurable:!0,writable:!0});else B[C]=G;return B}function E(B){var C=W(B,"string");return U(C)=="symbol"?C:String(C)}function W(B,C){if(U(B)!="object"||!B)return B;var G=B[Symbol.toPrimitive];if(G!==void 0){var H=G.call(B,C||"default");if(U(H)!="object")return H;throw new TypeError("@@toPrimitive must return a primitive value.")}return(C==="string"?String:Number)(B)}var D=Object.defineProperty,GB=function B(C,G){for(var H in G)D(C,H,{get:G[H],enumerable:!0,configurable:!0,set:function J(X){return G[H]=function(){return X}}})},N={lessThanXSeconds:{standalone:{one:"\u0C38\u0C46\u0C15\u0C28\u0C41 \u0C15\u0C28\u0C4D\u0C28\u0C3E \u0C24\u0C15\u0C4D\u0C15\u0C41\u0C35",other:"{{count}} \u0C38\u0C46\u0C15\u0C28\u0C4D\u0C32 \u0C15\u0C28\u0C4D\u0C28\u0C3E \u0C24\u0C15\u0C4D\u0C15\u0C41\u0C35"},withPreposition:{one:"\u0C38\u0C46\u0C15\u0C28\u0C41",other:"{{count}} \u0C38\u0C46\u0C15\u0C28\u0C4D\u0C32"}},xSeconds:{standalone:{one:"\u0C12\u0C15 \u0C38\u0C46\u0C15\u0C28\u0C41",other:"{{count}} \u0C38\u0C46\u0C15\u0C28\u0C4D\u0C32"},withPreposition:{one:"\u0C12\u0C15 \u0C38\u0C46\u0C15\u0C28\u0C41",other:"{{count}} \u0C38\u0C46\u0C15\u0C28\u0C4D\u0C32"}},halfAMinute:{standalone:"\u0C05\u0C30 \u0C28\u0C3F\u0C2E\u0C3F\u0C37\u0C02",withPreposition:"\u0C05\u0C30 \u0C28\u0C3F\u0C2E\u0C3F\u0C37\u0C02"},lessThanXMinutes:{standalone:{one:"\u0C12\u0C15 \u0C28\u0C3F\u0C2E\u0C3F\u0C37\u0C02 \u0C15\u0C28\u0C4D\u0C28\u0C3E \u0C24\u0C15\u0C4D\u0C15\u0C41\u0C35",other:"{{count}} \u0C28\u0C3F\u0C2E\u0C3F\u0C37\u0C3E\u0C32 \u0C15\u0C28\u0C4D\u0C28\u0C3E \u0C24\u0C15\u0C4D\u0C15\u0C41\u0C35"},withPreposition:{one:"\u0C12\u0C15 \u0C28\u0C3F\u0C2E\u0C3F\u0C37\u0C02",other:"{{count}} \u0C28\u0C3F\u0C2E\u0C3F\u0C37\u0C3E\u0C32"}},xMinutes:{standalone:{one:"\u0C12\u0C15 \u0C28\u0C3F\u0C2E\u0C3F\u0C37\u0C02",other:"{{count}} \u0C28\u0C3F\u0C2E\u0C3F\u0C37\u0C3E\u0C32\u0C41"},withPreposition:{one:"\u0C12\u0C15 \u0C28\u0C3F\u0C2E\u0C3F\u0C37\u0C02",other:"{{count}} \u0C28\u0C3F\u0C2E\u0C3F\u0C37\u0C3E\u0C32"}},aboutXHours:{standalone:{one:"\u0C38\u0C41\u0C2E\u0C3E\u0C30\u0C41 \u0C12\u0C15 \u0C17\u0C02\u0C1F",other:"\u0C38\u0C41\u0C2E\u0C3E\u0C30\u0C41 {{count}} \u0C17\u0C02\u0C1F\u0C32\u0C41"},withPreposition:{one:"\u0C38\u0C41\u0C2E\u0C3E\u0C30\u0C41 \u0C12\u0C15 \u0C17\u0C02\u0C1F",other:"\u0C38\u0C41\u0C2E\u0C3E\u0C30\u0C41 {{count}} \u0C17\u0C02\u0C1F\u0C32"}},xHours:{standalone:{one:"\u0C12\u0C15 \u0C17\u0C02\u0C1F",other:"{{count}} \u0C17\u0C02\u0C1F\u0C32\u0C41"},withPreposition:{one:"\u0C12\u0C15 \u0C17\u0C02\u0C1F",other:"{{count}} \u0C17\u0C02\u0C1F\u0C32"}},xDays:{standalone:{one:"\u0C12\u0C15 \u0C30\u0C4B\u0C1C\u0C41",other:"{{count}} \u0C30\u0C4B\u0C1C\u0C41\u0C32\u0C41"},withPreposition:{one:"\u0C12\u0C15 \u0C30\u0C4B\u0C1C\u0C41",other:"{{count}} \u0C30\u0C4B\u0C1C\u0C41\u0C32"}},aboutXWeeks:{standalone:{one:"\u0C38\u0C41\u0C2E\u0C3E\u0C30\u0C41 \u0C12\u0C15 \u0C35\u0C3E\u0C30\u0C02",other:"\u0C38\u0C41\u0C2E\u0C3E\u0C30\u0C41 {{count}} \u0C35\u0C3E\u0C30\u0C3E\u0C32\u0C41"},withPreposition:{one:"\u0C38\u0C41\u0C2E\u0C3E\u0C30\u0C41 \u0C12\u0C15 \u0C35\u0C3E\u0C30\u0C02",other:"\u0C38\u0C41\u0C2E\u0C3E\u0C30\u0C41 {{count}} \u0C35\u0C3E\u0C30\u0C3E\u0C32\u0C32"}},xWeeks:{standalone:{one:"\u0C12\u0C15 \u0C35\u0C3E\u0C30\u0C02",other:"{{count}} \u0C35\u0C3E\u0C30\u0C3E\u0C32\u0C41"},withPreposition:{one:"\u0C12\u0C15 \u0C35\u0C3E\u0C30\u0C02",other:"{{count}} \u0C35\u0C3E\u0C30\u0C3E\u0C32\u0C32"}},aboutXMonths:{standalone:{one:"\u0C38\u0C41\u0C2E\u0C3E\u0C30\u0C41 \u0C12\u0C15 \u0C28\u0C46\u0C32",other:"\u0C38\u0C41\u0C2E\u0C3E\u0C30\u0C41 {{count}} \u0C28\u0C46\u0C32\u0C32\u0C41"},withPreposition:{one:"\u0C38\u0C41\u0C2E\u0C3E\u0C30\u0C41 \u0C12\u0C15 \u0C28\u0C46\u0C32",other:"\u0C38\u0C41\u0C2E\u0C3E\u0C30\u0C41 {{count}} \u0C28\u0C46\u0C32\u0C32"}},xMonths:{standalone:{one:"\u0C12\u0C15 \u0C28\u0C46\u0C32",other:"{{count}} \u0C28\u0C46\u0C32\u0C32\u0C41"},withPreposition:{one:"\u0C12\u0C15 \u0C28\u0C46\u0C32",other:"{{count}} \u0C28\u0C46\u0C32\u0C32"}},aboutXYears:{standalone:{one:"\u0C38\u0C41\u0C2E\u0C3E\u0C30\u0C41 \u0C12\u0C15 \u0C38\u0C02\u0C35\u0C24\u0C4D\u0C38\u0C30\u0C02",other:"\u0C38\u0C41\u0C2E\u0C3E\u0C30\u0C41 {{count}} \u0C38\u0C02\u0C35\u0C24\u0C4D\u0C38\u0C30\u0C3E\u0C32\u0C41"},withPreposition:{one:"\u0C38\u0C41\u0C2E\u0C3E\u0C30\u0C41 \u0C12\u0C15 \u0C38\u0C02\u0C35\u0C24\u0C4D\u0C38\u0C30\u0C02",other:"\u0C38\u0C41\u0C2E\u0C3E\u0C30\u0C41 {{count}} \u0C38\u0C02\u0C35\u0C24\u0C4D\u0C38\u0C30\u0C3E\u0C32"}},xYears:{standalone:{one:"\u0C12\u0C15 \u0C38\u0C02\u0C35\u0C24\u0C4D\u0C38\u0C30\u0C02",other:"{{count}} \u0C38\u0C02\u0C35\u0C24\u0C4D\u0C38\u0C30\u0C3E\u0C32\u0C41"},withPreposition:{one:"\u0C12\u0C15 \u0C38\u0C02\u0C35\u0C24\u0C4D\u0C38\u0C30\u0C02",other:"{{count}} \u0C38\u0C02\u0C35\u0C24\u0C4D\u0C38\u0C30\u0C3E\u0C32"}},overXYears:{standalone:{one:"\u0C12\u0C15 \u0C38\u0C02\u0C35\u0C24\u0C4D\u0C38\u0C30\u0C02 \u0C2A\u0C48\u0C17\u0C3E",other:"{{count}} \u0C38\u0C02\u0C35\u0C24\u0C4D\u0C38\u0C30\u0C3E\u0C32\u0C15\u0C41 \u0C2A\u0C48\u0C17\u0C3E"},withPreposition:{one:"\u0C12\u0C15 \u0C38\u0C02\u0C35\u0C24\u0C4D\u0C38\u0C30\u0C02",other:"{{count}} \u0C38\u0C02\u0C35\u0C24\u0C4D\u0C38\u0C30\u0C3E\u0C32"}},almostXYears:{standalone:{one:"\u0C26\u0C3E\u0C26\u0C3E\u0C2A\u0C41 \u0C12\u0C15 \u0C38\u0C02\u0C35\u0C24\u0C4D\u0C38\u0C30\u0C02",other:"\u0C26\u0C3E\u0C26\u0C3E\u0C2A\u0C41 {{count}} \u0C38\u0C02\u0C35\u0C24\u0C4D\u0C38\u0C30\u0C3E\u0C32\u0C41"},withPreposition:{one:"\u0C26\u0C3E\u0C26\u0C3E\u0C2A\u0C41 \u0C12\u0C15 \u0C38\u0C02\u0C35\u0C24\u0C4D\u0C38\u0C30\u0C02",other:"\u0C26\u0C3E\u0C26\u0C3E\u0C2A\u0C41 {{count}} \u0C38\u0C02\u0C35\u0C24\u0C4D\u0C38\u0C30\u0C3E\u0C32"}}},S=function B(C,G,H){var J,X=H!==null&&H!==void 0&&H.addSuffix?N[C].withPreposition:N[C].standalone;if(typeof X==="string")J=X;else if(G===1)J=X.one;else J=X.other.replace("{{count}}",String(G));if(H!==null&&H!==void 0&&H.addSuffix)if(H.comparison&&H.comparison>0)return J+"\u0C32\u0C4B";else return J+" \u0C15\u0C4D\u0C30\u0C3F\u0C24\u0C02";return J};function K(B){return function(){var C=arguments.length>0&&arguments[0]!==void 0?arguments[0]:{},G=C.width?String(C.width):B.defaultWidth,H=B.formats[G]||B.formats[B.defaultWidth];return H}}var M={full:"d, MMMM y, EEEE",long:"d MMMM, y",medium:"d MMM, y",short:"dd-MM-yy"},R={full:"h:mm:ss a zzzz",long:"h:mm:ss a z",medium:"h:mm:ss a",short:"h:mm a"},V={full:"{{date}} {{time}}'\u0C15\u0C3F'",long:"{{date}} {{time}}'\u0C15\u0C3F'",medium:"{{date}} {{time}}",short:"{{date}} {{time}}"},L={date:K({formats:M,defaultWidth:"full"}),time:K({formats:R,defaultWidth:"full"}),dateTime:K({formats:V,defaultWidth:"full"})},j={lastWeek:"'\u0C17\u0C24' eeee p",yesterday:"'\u0C28\u0C3F\u0C28\u0C4D\u0C28' p",today:"'\u0C08 \u0C30\u0C4B\u0C1C\u0C41' p",tomorrow:"'\u0C30\u0C47\u0C2A\u0C41' p",nextWeek:"'\u0C24\u0C26\u0C41\u0C2A\u0C30\u0C3F' eeee p",other:"P"},w=function B(C,G,H,J){return j[C]};function I(B){return function(C,G){var H=G!==null&&G!==void 0&&G.context?String(G.context):"standalone",J;if(H==="formatting"&&B.formattingValues){var X=B.defaultFormattingWidth||B.defaultWidth,Y=G!==null&&G!==void 0&&G.width?String(G.width):X;J=B.formattingValues[Y]||B.formattingValues[X]}else{var Z=B.defaultWidth,q=G!==null&&G!==void 0&&G.width?String(G.width):B.defaultWidth;J=B.values[q]||B.values[Z]}var T=B.argumentCallback?B.argumentCallback(C):C;return J[T]}}var _={narrow:["\u0C15\u0C4D\u0C30\u0C40.\u0C2A\u0C42.","\u0C15\u0C4D\u0C30\u0C40.\u0C36."],abbreviated:["\u0C15\u0C4D\u0C30\u0C40.\u0C2A\u0C42.","\u0C15\u0C4D\u0C30\u0C40.\u0C36."],wide:["\u0C15\u0C4D\u0C30\u0C40\u0C38\u0C4D\u0C24\u0C41 \u0C2A\u0C42\u0C30\u0C4D\u0C35\u0C02","\u0C15\u0C4D\u0C30\u0C40\u0C38\u0C4D\u0C24\u0C41\u0C36\u0C15\u0C02"]},f={narrow:["1","2","3","4"],abbreviated:["\u0C24\u0C4D\u0C30\u0C481","\u0C24\u0C4D\u0C30\u0C482","\u0C24\u0C4D\u0C30\u0C483","\u0C24\u0C4D\u0C30\u0C484"],wide:["1\u0C35 \u0C24\u0C4D\u0C30\u0C48\u0C2E\u0C3E\u0C38\u0C3F\u0C15\u0C02","2\u0C35 \u0C24\u0C4D\u0C30\u0C48\u0C2E\u0C3E\u0C38\u0C3F\u0C15\u0C02","3\u0C35 \u0C24\u0C4D\u0C30\u0C48\u0C2E\u0C3E\u0C38\u0C3F\u0C15\u0C02","4\u0C35 \u0C24\u0C4D\u0C30\u0C48\u0C2E\u0C3E\u0C38\u0C3F\u0C15\u0C02"]},v={narrow:["\u0C1C","\u0C2B\u0C3F","\u0C2E\u0C3E","\u0C0F","\u0C2E\u0C47","\u0C1C\u0C42","\u0C1C\u0C41","\u0C06","\u0C38\u0C46","\u0C05","\u0C28","\u0C21\u0C3F"],abbreviated:["\u0C1C\u0C28","\u0C2B\u0C3F\u0C2C\u0C4D\u0C30","\u0C2E\u0C3E\u0C30\u0C4D\u0C1A\u0C3F","\u0C0F\u0C2A\u0C4D\u0C30\u0C3F","\u0C2E\u0C47","\u0C1C\u0C42\u0C28\u0C4D","\u0C1C\u0C41\u0C32\u0C48","\u0C06\u0C17","\u0C38\u0C46\u0C2A\u0C4D\u0C1F\u0C46\u0C02","\u0C05\u0C15\u0C4D\u0C1F\u0C4B","\u0C28\u0C35\u0C02","\u0C21\u0C3F\u0C38\u0C46\u0C02"],wide:["\u0C1C\u0C28\u0C35\u0C30\u0C3F","\u0C2B\u0C3F\u0C2C\u0C4D\u0C30\u0C35\u0C30\u0C3F","\u0C2E\u0C3E\u0C30\u0C4D\u0C1A\u0C3F","\u0C0F\u0C2A\u0C4D\u0C30\u0C3F\u0C32\u0C4D","\u0C2E\u0C47","\u0C1C\u0C42\u0C28\u0C4D","\u0C1C\u0C41\u0C32\u0C48","\u0C06\u0C17\u0C38\u0C4D\u0C1F\u0C41","\u0C38\u0C46\u0C2A\u0C4D\u0C1F\u0C46\u0C02\u0C2C\u0C30\u0C4D","\u0C05\u0C15\u0C4D\u0C1F\u0C4B\u0C2C\u0C30\u0C4D","\u0C28\u0C35\u0C02\u0C2C\u0C30\u0C4D","\u0C21\u0C3F\u0C38\u0C46\u0C02\u0C2C\u0C30\u0C4D"]},F={narrow:["\u0C06","\u0C38\u0C4B","\u0C2E","\u0C2C\u0C41","\u0C17\u0C41","\u0C36\u0C41","\u0C36"],short:["\u0C06\u0C26\u0C3F","\u0C38\u0C4B\u0C2E","\u0C2E\u0C02\u0C17\u0C33","\u0C2C\u0C41\u0C27","\u0C17\u0C41\u0C30\u0C41","\u0C36\u0C41\u0C15\u0C4D\u0C30","\u0C36\u0C28\u0C3F"],abbreviated:["\u0C06\u0C26\u0C3F","\u0C38\u0C4B\u0C2E","\u0C2E\u0C02\u0C17\u0C33","\u0C2C\u0C41\u0C27","\u0C17\u0C41\u0C30\u0C41","\u0C36\u0C41\u0C15\u0C4D\u0C30","\u0C36\u0C28\u0C3F"],wide:["\u0C06\u0C26\u0C3F\u0C35\u0C3E\u0C30\u0C02","\u0C38\u0C4B\u0C2E\u0C35\u0C3E\u0C30\u0C02","\u0C2E\u0C02\u0C17\u0C33\u0C35\u0C3E\u0C30\u0C02","\u0C2C\u0C41\u0C27\u0C35\u0C3E\u0C30\u0C02","\u0C17\u0C41\u0C30\u0C41\u0C35\u0C3E\u0C30\u0C02","\u0C36\u0C41\u0C15\u0C4D\u0C30\u0C35\u0C3E\u0C30\u0C02","\u0C36\u0C28\u0C3F\u0C35\u0C3E\u0C30\u0C02"]},P={narrow:{am:"\u0C2A\u0C42\u0C30\u0C4D\u0C35\u0C3E\u0C39\u0C4D\u0C28\u0C02",pm:"\u0C05\u0C2A\u0C30\u0C3E\u0C39\u0C4D\u0C28\u0C02",midnight:"\u0C05\u0C30\u0C4D\u0C27\u0C30\u0C3E\u0C24\u0C4D\u0C30\u0C3F",noon:"\u0C2E\u0C3F\u0C1F\u0C4D\u0C1F\u0C2E\u0C27\u0C4D\u0C2F\u0C3E\u0C39\u0C4D\u0C28\u0C02",morning:"\u0C09\u0C26\u0C2F\u0C02",afternoon:"\u0C2E\u0C27\u0C4D\u0C2F\u0C3E\u0C39\u0C4D\u0C28\u0C02",evening:"\u0C38\u0C3E\u0C2F\u0C02\u0C24\u0C4D\u0C30\u0C02",night:"\u0C30\u0C3E\u0C24\u0C4D\u0C30\u0C3F"},abbreviated:{am:"\u0C2A\u0C42\u0C30\u0C4D\u0C35\u0C3E\u0C39\u0C4D\u0C28\u0C02",pm:"\u0C05\u0C2A\u0C30\u0C3E\u0C39\u0C4D\u0C28\u0C02",midnight:"\u0C05\u0C30\u0C4D\u0C27\u0C30\u0C3E\u0C24\u0C4D\u0C30\u0C3F",noon:"\u0C2E\u0C3F\u0C1F\u0C4D\u0C1F\u0C2E\u0C27\u0C4D\u0C2F\u0C3E\u0C39\u0C4D\u0C28\u0C02",morning:"\u0C09\u0C26\u0C2F\u0C02",afternoon:"\u0C2E\u0C27\u0C4D\u0C2F\u0C3E\u0C39\u0C4D\u0C28\u0C02",evening:"\u0C38\u0C3E\u0C2F\u0C02\u0C24\u0C4D\u0C30\u0C02",night:"\u0C30\u0C3E\u0C24\u0C4D\u0C30\u0C3F"},wide:{am:"\u0C2A\u0C42\u0C30\u0C4D\u0C35\u0C3E\u0C39\u0C4D\u0C28\u0C02",pm:"\u0C05\u0C2A\u0C30\u0C3E\u0C39\u0C4D\u0C28\u0C02",midnight:"\u0C05\u0C30\u0C4D\u0C27\u0C30\u0C3E\u0C24\u0C4D\u0C30\u0C3F",noon:"\u0C2E\u0C3F\u0C1F\u0C4D\u0C1F\u0C2E\u0C27\u0C4D\u0C2F\u0C3E\u0C39\u0C4D\u0C28\u0C02",morning:"\u0C09\u0C26\u0C2F\u0C02",afternoon:"\u0C2E\u0C27\u0C4D\u0C2F\u0C3E\u0C39\u0C4D\u0C28\u0C02",evening:"\u0C38\u0C3E\u0C2F\u0C02\u0C24\u0C4D\u0C30\u0C02",night:"\u0C30\u0C3E\u0C24\u0C4D\u0C30\u0C3F"}},k={narrow:{am:"\u0C2A\u0C42\u0C30\u0C4D\u0C35\u0C3E\u0C39\u0C4D\u0C28\u0C02",pm:"\u0C05\u0C2A\u0C30\u0C3E\u0C39\u0C4D\u0C28\u0C02",midnight:"\u0C05\u0C30\u0C4D\u0C27\u0C30\u0C3E\u0C24\u0C4D\u0C30\u0C3F",noon:"\u0C2E\u0C3F\u0C1F\u0C4D\u0C1F\u0C2E\u0C27\u0C4D\u0C2F\u0C3E\u0C39\u0C4D\u0C28\u0C02",morning:"\u0C09\u0C26\u0C2F\u0C02",afternoon:"\u0C2E\u0C27\u0C4D\u0C2F\u0C3E\u0C39\u0C4D\u0C28\u0C02",evening:"\u0C38\u0C3E\u0C2F\u0C02\u0C24\u0C4D\u0C30\u0C02",night:"\u0C30\u0C3E\u0C24\u0C4D\u0C30\u0C3F"},abbreviated:{am:"\u0C2A\u0C42\u0C30\u0C4D\u0C35\u0C3E\u0C39\u0C4D\u0C28\u0C02",pm:"\u0C05\u0C2A\u0C30\u0C3E\u0C39\u0C4D\u0C28\u0C02",midnight:"\u0C05\u0C30\u0C4D\u0C27\u0C30\u0C3E\u0C24\u0C4D\u0C30\u0C3F",noon:"\u0C2E\u0C3F\u0C1F\u0C4D\u0C1F\u0C2E\u0C27\u0C4D\u0C2F\u0C3E\u0C39\u0C4D\u0C28\u0C02",morning:"\u0C09\u0C26\u0C2F\u0C02",afternoon:"\u0C2E\u0C27\u0C4D\u0C2F\u0C3E\u0C39\u0C4D\u0C28\u0C02",evening:"\u0C38\u0C3E\u0C2F\u0C02\u0C24\u0C4D\u0C30\u0C02",night:"\u0C30\u0C3E\u0C24\u0C4D\u0C30\u0C3F"},wide:{am:"\u0C2A\u0C42\u0C30\u0C4D\u0C35\u0C3E\u0C39\u0C4D\u0C28\u0C02",pm:"\u0C05\u0C2A\u0C30\u0C3E\u0C39\u0C4D\u0C28\u0C02",midnight:"\u0C05\u0C30\u0C4D\u0C27\u0C30\u0C3E\u0C24\u0C4D\u0C30\u0C3F",noon:"\u0C2E\u0C3F\u0C1F\u0C4D\u0C1F\u0C2E\u0C27\u0C4D\u0C2F\u0C3E\u0C39\u0C4D\u0C28\u0C02",morning:"\u0C09\u0C26\u0C2F\u0C02",afternoon:"\u0C2E\u0C27\u0C4D\u0C2F\u0C3E\u0C39\u0C4D\u0C28\u0C02",evening:"\u0C38\u0C3E\u0C2F\u0C02\u0C24\u0C4D\u0C30\u0C02",night:"\u0C30\u0C3E\u0C24\u0C4D\u0C30\u0C3F"}},b=function B(C,G){var H=Number(C);return H+"\u0C35"},h={ordinalNumber:b,era:I({values:_,defaultWidth:"wide"}),quarter:I({values:f,defaultWidth:"wide",argumentCallback:function B(C){return C-1}}),month:I({values:v,defaultWidth:"wide"}),day:I({values:F,defaultWidth:"wide"}),dayPeriod:I({values:P,defaultWidth:"wide",formattingValues:k,defaultFormattingWidth:"wide"})};function O(B){return function(C){var G=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{},H=G.width,J=H&&B.matchPatterns[H]||B.matchPatterns[B.defaultMatchWidth],X=C.match(J);if(!X)return null;var Y=X[0],Z=H&&B.parsePatterns[H]||B.parsePatterns[B.defaultParseWidth],q=Array.isArray(Z)?y(Z,function(x){return x.test(Y)}):m(Z,function(x){return x.test(Y)}),T;T=B.valueCallback?B.valueCallback(q):q,T=G.valueCallback?G.valueCallback(T):T;var CB=C.slice(Y.length);return{value:T,rest:CB}}}function m(B,C){for(var G in B)if(Object.prototype.hasOwnProperty.call(B,G)&&C(B[G]))return G;return}function y(B,C){for(var G=0;G<B.length;G++)if(C(B[G]))return G;return}function c(B){return function(C){var G=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{},H=C.match(B.matchPattern);if(!H)return null;var J=H[0],X=C.match(B.parsePattern);if(!X)return null;var Y=B.valueCallback?B.valueCallback(X[0]):X[0];Y=G.valueCallback?G.valueCallback(Y):Y;var Z=C.slice(J.length);return{value:Y,rest:Z}}}var d=/^(\d+)(వ)?/i,g=/\d+/i,p={narrow:/^(క్రీ\.పూ\.|క్రీ\.శ\.)/i,abbreviated:/^(క్రీ\.?\s?పూ\.?|ప్ర\.?\s?శ\.?\s?పూ\.?|క్రీ\.?\s?శ\.?|సా\.?\s?శ\.?)/i,wide:/^(క్రీస్తు పూర్వం|ప్రస్తుత శకానికి పూర్వం|క్రీస్తు శకం|ప్రస్తుత శకం)/i},u={any:[/^(పూ|శ)/i,/^సా/i]},l={narrow:/^[1234]/i,abbreviated:/^త్రై[1234]/i,wide:/^[1234](వ)? త్రైమాసికం/i},i={any:[/1/i,/2/i,/3/i,/4/i]},n={narrow:/^(జూ|జు|జ|ఫి|మా|ఏ|మే|ఆ|సె|అ|న|డి)/i,abbreviated:/^(జన|ఫిబ్ర|మార్చి|ఏప్రి|మే|జూన్|జులై|ఆగ|సెప్|అక్టో|నవ|డిసె)/i,wide:/^(జనవరి|ఫిబ్రవరి|మార్చి|ఏప్రిల్|మే|జూన్|జులై|ఆగస్టు|సెప్టెంబర్|అక్టోబర్|నవంబర్|డిసెంబర్)/i},s={narrow:[/^జ/i,/^ఫి/i,/^మా/i,/^ఏ/i,/^మే/i,/^జూ/i,/^జు/i,/^ఆ/i,/^సె/i,/^అ/i,/^న/i,/^డి/i],any:[/^జన/i,/^ఫి/i,/^మా/i,/^ఏ/i,/^మే/i,/^జూన్/i,/^జులై/i,/^ఆగ/i,/^సె/i,/^అ/i,/^న/i,/^డి/i]},o={narrow:/^(ఆ|సో|మ|బు|గు|శు|శ)/i,short:/^(ఆది|సోమ|మం|బుధ|గురు|శుక్ర|శని)/i,abbreviated:/^(ఆది|సోమ|మం|బుధ|గురు|శుక్ర|శని)/i,wide:/^(ఆదివారం|సోమవారం|మంగళవారం|బుధవారం|గురువారం|శుక్రవారం|శనివారం)/i},r={narrow:[/^ఆ/i,/^సో/i,/^మ/i,/^బు/i,/^గు/i,/^శు/i,/^శ/i],any:[/^ఆది/i,/^సోమ/i,/^మం/i,/^బుధ/i,/^గురు/i,/^శుక్ర/i,/^శని/i]},a={narrow:/^(పూర్వాహ్నం|అపరాహ్నం|అర్ధరాత్రి|మిట్టమధ్యాహ్నం|ఉదయం|మధ్యాహ్నం|సాయంత్రం|రాత్రి)/i,any:/^(పూర్వాహ్నం|అపరాహ్నం|అర్ధరాత్రి|మిట్టమధ్యాహ్నం|ఉదయం|మధ్యాహ్నం|సాయంత్రం|రాత్రి)/i},e={any:{am:/^పూర్వాహ్నం/i,pm:/^అపరాహ్నం/i,midnight:/^అర్ధ/i,noon:/^మిట్ట/i,morning:/ఉదయం/i,afternoon:/మధ్యాహ్నం/i,evening:/సాయంత్రం/i,night:/రాత్రి/i}},t={ordinalNumber:c({matchPattern:d,parsePattern:g,valueCallback:function B(C){return parseInt(C,10)}}),era:O({matchPatterns:p,defaultMatchWidth:"wide",parsePatterns:u,defaultParseWidth:"any"}),quarter:O({matchPatterns:l,defaultMatchWidth:"wide",parsePatterns:i,defaultParseWidth:"any",valueCallback:function B(C){return C+1}}),month:O({matchPatterns:n,defaultMatchWidth:"wide",parsePatterns:s,defaultParseWidth:"any"}),day:O({matchPatterns:o,defaultMatchWidth:"wide",parsePatterns:r,defaultParseWidth:"any"}),dayPeriod:O({matchPatterns:a,defaultMatchWidth:"any",parsePatterns:e,defaultParseWidth:"any"})},BB={code:"te",formatDistance:S,formatLong:L,formatRelative:w,localize:h,match:t,options:{weekStartsOn:0,firstWeekContainsDate:1}};window.dateFns=Q(Q({},window.dateFns),{},{locale:Q(Q({},($=window.dateFns)===null||$===void 0?void 0:$.locale),{},{te:BB})})})();

//# debugId=E0BCEB32EBFD422564756E2164756E21
