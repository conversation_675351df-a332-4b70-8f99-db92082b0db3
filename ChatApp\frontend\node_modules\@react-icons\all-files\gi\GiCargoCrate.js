// THIS FILE IS AUTO GENERATED
var GenIcon = require('../lib').GenIcon
module.exports.GiCargoCrate = function GiCargoCrate (props) {
  return GenIcon({"tag":"svg","attr":{"viewBox":"0 0 512 512"},"child":[{"tag":"path","attr":{"d":"M247 16v89h9c14.9 0 23 11.7 23.8 23.6.4 6-1.2 11.5-4.7 15.4-3.5 4-9.1 7-19.1 7-5 0-11.1-2.8-15.6-7.4-4.6-4.5-7.4-10.6-7.4-15.6h-18c0 11 5.2 20.9 12.6 28.4 7.5 7.4 17.4 12.6 28.4 12.6 14 0 25.4-5 32.5-13 7.2-8.1 10-18.6 9.3-28.6-1.2-17.5-13.4-35.18-32.8-39.42V16h-18zm-47.9 140.5L61.34 247h32.7l114.86-75.5-9.8-15zm113.8 0l-9.8 15L418 247h32.6l-137.7-90.5zM41 265v222h430V265H41zm38 23h18v176H79V288zm48 0h18v176h-18V288zm48 0h18v176h-18V288zm48 0h18v176h-18V288zm48 0h18v176h-18V288zm48 0h18v176h-18V288zm48 0h18v176h-18V288zm48 0h18v176h-18V288z"}}]})(props);
};
