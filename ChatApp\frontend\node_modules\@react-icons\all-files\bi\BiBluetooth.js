// THIS FILE IS AUTO GENERATED
var GenIcon = require('../lib').GenIcon
module.exports.BiBluetooth = function BiBluetooth (props) {
  return GenIcon({"tag":"svg","attr":{"viewBox":"0 0 24 24"},"child":[{"tag":"path","attr":{"d":"M4.41,16.192l1.18,1.615L10,14.584V21c0,0.366,0.2,0.703,0.521,0.878C10.671,21.959,10.835,22,11,22 c0.188,0,0.377-0.053,0.541-0.159l7-4.5c0.277-0.178,0.448-0.482,0.459-0.812c0.01-0.33-0.144-0.643-0.41-0.837L13.537,12 l5.053-3.692C18.856,8.113,19.01,7.8,19,7.47c-0.011-0.33-0.182-0.633-0.459-0.812l-7-4.5c-0.308-0.198-0.699-0.212-1.021-0.037 C10.2,2.297,10,2.634,10,3v6.416L5.59,6.192L4.41,7.808L10,11.893v0.215L4.41,16.192z M12,4.832l4.232,2.721L12,10.646V4.832z M12,13.354l4.232,3.093L12,19.168V13.354z"}}]})(props);
};
