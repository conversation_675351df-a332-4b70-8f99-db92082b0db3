// THIS FILE IS AUTO GENERATED
var GenIcon = require('../lib').GenIcon
module.exports.GiPitchfork = function GiPitchfork (props) {
  return GenIcon({"tag":"svg","attr":{"viewBox":"0 0 512 512"},"child":[{"tag":"path","attr":{"d":"M105.9 19l25.7 58.8c.7.1 1.9.26 3.9 0 4.7-.57 11.8-2.8 18.5-5.87 6.6-3.07 13-7.02 16.8-10.3 1.5-1.33 2.2-2.27 2.7-2.96L156.2 19h-50.3zm75.3 57.39c-5.6 4.5-12.4 8.53-19.7 11.88-7.4 3.39-15 6.05-22.2 7.16l16.3 37.47 25.8 1.3 16.4-19.7-16.6-38.11zm110.1 18.32c-19.5-.35-46.8 5.79-75.6 15.59l3.1 7.2-26.5 31.7-2.9 3.4-45.8-2.3-3.3-7.5c-44.48 23.5-78.61 50.6-74.43 69C102.8 337 168.3 448.5 226 496.7l11.5-13.8c-51-42.7-115.5-149.4-152.01-268.6L127.5 196c37.4 121.2 101 228.2 157.2 275.1l11.5-13.8C245.1 414.6 180.6 308 144 188.8l42.1-18.4c37.4 121.2 101 228.2 157.3 275.1l11.4-13.8c-51-42.7-115.6-149.3-152.1-268.5l42.1-18.4C282.2 266 345.8 373 402 419.9l11.5-13.8c-51-42.7-115.6-149.3-152.1-268.5l42.1-18.4c37.4 121.1 101 228.3 157.2 275.1l11.4-13.8C420 336.9 353.8 226.6 317.7 104.4c-4.3-6.49-13.7-9.47-26.4-9.69z"}}]})(props);
};
