// THIS FILE IS AUTO GENERATED
var GenIcon = require('../lib').GenIcon
module.exports.GiPirateCoat = function GiPirateCoat (props) {
  return GenIcon({"tag":"svg","attr":{"viewBox":"0 0 512 512"},"child":[{"tag":"path","attr":{"d":"M192 32c0 16 0 32-16 48l-64 32-37.646 163.139c20.315 9.254 41.137 16.022 62.947 19.007L160 192c0 64 0 192-32 304 16.315-4.079 36.8-7.112 59.313-9.111 9.09-14.67 17.425-42.087 23.058-76.11.097-.585.188-1.19.283-1.779H192v-18h21.293c1.271-9.625 2.386-19.655 3.35-30H192v-18h26.148c.727-9.803 1.322-19.829 1.795-30H192v-18h28.646c.14-4.382.262-8.777.356-13.191.119-5.583.2-11.19.246-16.809H192v-18h29.275c-.051-10.022-.22-20.04-.49-30H192v-18h28.176c-.41-10.127-.928-20.153-1.557-30H192v-18h25.334a1048.454 1048.454 0 0 0-2.797-30H192v-18h20.404c-3.451-26.732-7.839-50.259-13.058-68.527l.199-.057A345.758 345.758 0 0 1 192 32zm128 0c-2.562.854-5.07 1.653-7.545 2.416l.2.057c-5.22 18.268-9.608 41.795-13.06 68.527H320v18h-22.537a1048.454 1048.454 0 0 0-2.797 30H320v18h-26.62c-.628 9.847-1.145 19.873-1.556 30H320v18h-28.785c-.27 9.96-.439 19.978-.49 30H320v18h-29.248c.046 5.619.127 11.226.246 16.809.094 4.414.217 8.809.356 13.191H320v18h-27.943a1096.285 1096.285 0 0 0 1.795 30H320v18h-24.643c.964 10.345 2.079 20.375 3.35 30H320v18h-18.654c.095.59.186 1.194.283 1.78 5.633 34.022 13.967 61.439 23.058 76.109C347.2 488.888 367.685 491.92 384 496c-32-112-32-240-32-304l22.7 102.146c21.809-2.985 42.63-9.753 62.946-19.007L400 112l-64-32c-16-16-16-32-16-48zm-100.639 7.72c14.695 59.028 21.537 153.15 19.637 242.471-1.007 47.323-4.517 93.164-10.87 131.53-4.77 28.82-10.831 53.327-19.462 71.619 30.778-1.773 63.89-1.773 94.668 0-8.631-18.292-14.691-42.798-19.463-71.62-6.352-38.365-9.862-84.206-10.869-131.529-1.9-89.32 4.942-183.443 19.637-242.47-25.553 5.69-47.725 5.69-73.278 0zM54.671 285.396c-1.49 7.72-2.976 15.435-3.648 22.43-1.022 10.64.299 18.44 3.645 22.13 17.186 18.957 41.688 25.915 67.963 28.494 7.592-15.13 12.333-30.41 12.566-46.512-28.605-3.87-55.172-13.757-80.525-26.542zm402.657 0c-25.353 12.785-51.92 22.672-80.525 26.543.233 16.102 4.974 31.382 12.566 46.511 26.275-2.579 50.777-9.537 67.963-28.494 3.346-3.69 4.667-11.49 3.645-22.13-.672-6.995-2.159-14.71-3.649-22.43z"}}]})(props);
};
