// THIS FILE IS AUTO GENERATED
import { GenIcon } from '../lib';
export function IoGitMergeOutline (props) {
  return GenIcon({"tag":"svg","attr":{"viewBox":"0 0 512 512"},"child":[{"tag":"circle","attr":{"cx":"129","cy":"96","r":"48","fill":"none","strokeLinecap":"round","strokeLinejoin":"round","strokeWidth":"32"}},{"tag":"circle","attr":{"cx":"129","cy":"416","r":"48","fill":"none","strokeLinecap":"round","strokeLinejoin":"round","strokeWidth":"32"}},{"tag":"path","attr":{"fill":"none","strokeLinecap":"round","strokeLinejoin":"round","strokeWidth":"32","d":"M129 144v224"}},{"tag":"circle","attr":{"cx":"385","cy":"288","r":"48","fill":"none","strokeLinecap":"round","strokeLinejoin":"round","strokeWidth":"32"}},{"tag":"path","attr":{"fill":"none","strokeLinecap":"round","strokeLinejoin":"round","strokeWidth":"32","d":"M129 144c0 96 112 144 208 144"}}]})(props);
};
