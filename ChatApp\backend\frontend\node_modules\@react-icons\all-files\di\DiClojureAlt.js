// THIS FILE IS AUTO GENERATED
var GenIcon = require('../lib').GenIcon
module.exports.DiClojureAlt = function DiClojureAlt (props) {
  return GenIcon({"tag":"svg","attr":{"version":"1.1","viewBox":"0 0 32 32"},"child":[{"tag":"path","attr":{"d":"M15.589 16.464c-0.098 0.213-0.207 0.453-0.319 0.708-0.398 0.901-0.838 1.997-0.999 2.699-0.058 0.25-0.094 0.56-0.093 0.904 0 0.136 0.007 0.279 0.019 0.424 0.562 0.207 1.169 0.32 1.803 0.321 0.577-0.001 1.132-0.096 1.651-0.269-0.122-0.112-0.238-0.23-0.344-0.364-0.704-0.897-1.096-2.212-1.717-4.424zM16 6.383c-0.186 0-0.37 0.006-0.554 0.016-0.023 0.001-0.047 0.002-0.070 0.003-0.072 0.005-0.143 0.011-0.215 0.017-0.033 0.003-0.066 0.005-0.099 0.008-0.067 0.006-0.134 0.014-0.201 0.022-0.037 0.004-0.073 0.008-0.11 0.013-0.065 0.008-0.129 0.018-0.194 0.027-0.038 0.006-0.076 0.011-0.114 0.017-0.063 0.010-0.126 0.021-0.189 0.032-0.038 0.007-0.077 0.014-0.115 0.021-0.062 0.012-0.124 0.025-0.186 0.038-0.038 0.008-0.076 0.016-0.114 0.025-0.062 0.014-0.123 0.028-0.184 0.043-0.037 0.009-0.075 0.018-0.112 0.028-0.061 0.016-0.123 0.032-0.184 0.049-0.036 0.010-0.072 0.020-0.109 0.031-0.062 0.018-0.123 0.036-0.184 0.055-0.035 0.011-0.069 0.022-0.103 0.033-0.062 0.020-0.124 0.040-0.185 0.061-0.032 0.011-0.064 0.023-0.096 0.034-0.063 0.022-0.126 0.045-0.189 0.069-0.029 0.011-0.057 0.022-0.085 0.033-0.065 0.026-0.131 0.051-0.196 0.078-0.023 0.009-0.045 0.019-0.068 0.029-0.070 0.030-0.14 0.060-0.209 0.091-0.011 0.005-0.022 0.010-0.032 0.015-0.424 0.193-0.832 0.416-1.222 0.665-0.010 0.006-0.019 0.012-0.029 0.018-0.067 0.043-0.132 0.087-0.198 0.131-0.016 0.011-0.033 0.022-0.049 0.033-0.062 0.042-0.122 0.086-0.183 0.129-0.020 0.014-0.039 0.028-0.059 0.042-0.058 0.043-0.116 0.086-0.173 0.13-0.021 0.016-0.042 0.032-0.063 0.049-0.056 0.043-0.111 0.087-0.165 0.132-0.022 0.018-0.043 0.035-0.065 0.053-0.054 0.044-0.107 0.089-0.159 0.135-0.022 0.019-0.043 0.038-0.065 0.057-0.052 0.046-0.103 0.092-0.154 0.139-0.021 0.020-0.043 0.039-0.064 0.059-0.051 0.047-0.101 0.095-0.15 0.143-0.021 0.020-0.041 0.040-0.061 0.060-0.049 0.049-0.098 0.098-0.147 0.148-0.020 0.020-0.039 0.041-0.058 0.061-0.048 0.051-0.097 0.102-0.144 0.154-0.018 0.020-0.036 0.040-0.053 0.059-0.048 0.053-0.096 0.107-0.143 0.161-0.016 0.018-0.031 0.037-0.046 0.055-0.048 0.057-0.096 0.114-0.143 0.172-0.011 0.014-0.022 0.028-0.033 0.042-0.059 0.073-0.116 0.147-0.173 0.222-0.004 0.006-0.008 0.012-0.013 0.017-0.045 0.060-0.090 0.121-0.134 0.182 0.935-0.585 1.889-0.796 2.722-0.789 1.151 0.003 2.056 0.36 2.49 0.604 0.105 0.061 0.204 0.126 0.302 0.192 0.775-0.341 1.631-0.532 2.532-0.532 3.475 0 6.291 2.816 6.292 6.291h-0c0 1.752-0.717 3.336-1.872 4.476 0.284 0.032 0.586 0.052 0.895 0.050 1.097 0 2.282-0.241 3.171-0.989 0.58-0.488 1.065-1.203 1.334-2.274 0.053-0.414 0.083-0.835 0.083-1.263 0-5.469-4.433-9.903-9.902-9.903zM20.56 22.291c-0.694-0.087-1.266-0.192-1.766-0.368-0.842 0.418-1.791 0.654-2.794 0.654-3.474 0-6.29-2.816-6.291-6.291 0-1.886 0.831-3.576 2.145-4.729-0.352-0.085-0.718-0.134-1.092-0.134-1.845 0.017-3.793 1.038-4.604 3.794-0.007 0.062-0.013 0.125-0.018 0.188-0.002 0.023-0.005 0.046-0.006 0.069-0.004 0.046-0.007 0.093-0.010 0.14-0.003 0.043-0.006 0.087-0.009 0.13-0.004 0.065-0.006 0.131-0.008 0.197-0.001 0.023-0.002 0.046-0.003 0.070-0.003 0.091-0.004 0.182-0.004 0.274 0 5.469 4.434 9.902 9.902 9.902 0.001 0 0.002-0 0.003-0 0.105 0 0.21-0.002 0.315-0.005 0.037-0.001 0.073-0.004 0.11-0.005 0.069-0.003 0.138-0.006 0.206-0.010 0.044-0.003 0.088-0.007 0.131-0.010 0.061-0.005 0.121-0.009 0.182-0.015 0.047-0.004 0.093-0.010 0.14-0.015 0.057-0.006 0.114-0.012 0.171-0.019 0.048-0.006 0.096-0.013 0.144-0.020 0.055-0.008 0.109-0.015 0.163-0.024 0.049-0.008 0.098-0.017 0.147-0.025 0.052-0.009 0.105-0.018 0.157-0.028 0.050-0.009 0.099-0.020 0.148-0.030 0.051-0.011 0.102-0.021 0.152-0.032s0.1-0.023 0.149-0.035c0.049-0.012 0.099-0.023 0.148-0.036 0.050-0.013 0.1-0.027 0.15-0.040 0.048-0.013 0.096-0.026 0.143-0.040 0.050-0.015 0.1-0.030 0.151-0.045 0.046-0.014 0.093-0.029 0.139-0.043 0.051-0.016 0.101-0.033 0.151-0.051 0.045-0.015 0.090-0.031 0.135-0.047 0.051-0.018 0.101-0.037 0.151-0.056 0.044-0.016 0.087-0.033 0.13-0.050 0.051-0.020 0.101-0.040 0.151-0.061 0.042-0.017 0.084-0.035 0.126-0.053 0.051-0.022 0.101-0.044 0.151-0.066 0.041-0.018 0.081-0.037 0.121-0.055 0.051-0.024 0.101-0.048 0.151-0.072 0.039-0.019 0.078-0.038 0.116-0.058 0.051-0.026 0.101-0.051 0.151-0.078 0.037-0.020 0.074-0.039 0.111-0.059 0.051-0.027 0.101-0.055 0.152-0.083 0.035-0.020 0.071-0.040 0.106-0.061 0.051-0.030 0.102-0.059 0.152-0.090 0.033-0.020 0.067-0.041 0.1-0.061 0.051-0.032 0.102-0.064 0.153-0.096 0.031-0.020 0.062-0.041 0.093-0.062 0.052-0.034 0.103-0.069 0.154-0.104 0.029-0.020 0.057-0.040 0.086-0.061 0.052-0.037 0.105-0.074 0.156-0.112 0.026-0.019 0.052-0.039 0.078-0.058 0.053-0.040 0.106-0.080 0.159-0.121 0.023-0.018 0.045-0.036 0.068-0.054 0.055-0.044 0.11-0.087 0.163-0.132 0.019-0.016 0.037-0.032 0.056-0.047 0.057-0.048 0.114-0.096 0.17-0.146 0.013-0.012 0.026-0.023 0.038-0.035 0.061-0.054 0.121-0.109 0.181-0.165 0.004-0.004 0.009-0.009 0.013-0.013 0.066-0.063 0.132-0.126 0.197-0.19 0.024-0.024 0.048-0.049 0.072-0.073 0.040-0.040 0.080-0.081 0.119-0.122 0.029-0.030 0.057-0.060 0.085-0.091 0.033-0.036 0.067-0.072 0.1-0.108 0.030-0.034 0.061-0.067 0.090-0.102s0.058-0.067 0.087-0.101c0.032-0.037 0.063-0.074 0.095-0.112 0.025-0.030 0.049-0.060 0.073-0.090 0.034-0.042 0.068-0.084 0.101-0.126 0.012-0.015 0.023-0.030 0.035-0.046 0.092-0.119 0.183-0.24 0.27-0.364 0-0 0-0 0-0.001-0.969 0.242-1.901 0.357-2.699 0.36-0.299 0-0.58-0.016-0.84-0.048zM18.699 20.372c0.061 0.030 0.2 0.080 0.393 0.134 1.298-0.953 2.141-2.487 2.144-4.221h-0c-0.005-2.891-2.344-5.23-5.236-5.236-0.575 0.001-1.127 0.096-1.644 0.267 1.063 1.212 1.574 2.944 2.069 4.838 0 0.001 0 0.002 0.001 0.002s0.158 0.526 0.428 1.222c0.268 0.695 0.65 1.555 1.067 2.182 0.274 0.42 0.574 0.722 0.779 0.811zM12.991 12.006c-1.344 0.947-2.223 2.509-2.227 4.28 0.003 1.744 0.856 3.286 2.166 4.237 0.321-1.339 1.127-2.564 2.335-5.021-0.072-0.197-0.153-0.413-0.245-0.639-0.335-0.839-0.818-1.814-1.249-2.256-0.22-0.231-0.486-0.43-0.781-0.6z"}}]})(props);
};
