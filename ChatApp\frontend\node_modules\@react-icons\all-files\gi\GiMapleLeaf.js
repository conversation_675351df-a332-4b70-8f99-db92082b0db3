// THIS FILE IS AUTO GENERATED
var GenIcon = require('../lib').GenIcon
module.exports.GiMapleLeaf = function GiMapleLeaf (props) {
  return GenIcon({"tag":"svg","attr":{"viewBox":"0 0 512 512"},"child":[{"tag":"path","attr":{"d":"M175.094 25.593c-37.263 98.702-18.844 171.333 29.812 231.78-55.864-32.94-102.02-39.746-176.562-29.5 36.104 103.52 114.96 147.68 199.53 147.72-11.347 26.98-13.91 56.395-4.374 88.938 36.643-23.08 58.91-47.936 68.906-78.468 35.98 50.032 94.496 84.814 134.625 96.844l14.595 4.687-2.344-15.187c-2.565-14.66-.2-24.85 5.845-35.063 6.046-10.21 15.88-20.01 28.03-30.937l21.032-18.688-28.03 2.344c-36.735 3.018-73.025-3.842-108.813-33.906 24.9-.342 49.864-6.29 84.843-16.157-18.744-22.37-40.422-35.795-64.468-42.594 51.884-67.147 81.588-166.79 52.936-233.063-82.263 37.32-123.16 89.803-138.75 152.406C280.17 141.16 244.118 77.825 175.094 25.592zm15.125 69.53c23.525 72.024 54.17 141.623 99.28 207.814C351.886 256.397 374.437 198.35 394.187 132c23.69 55.974-11.423 128.012-66.656 182.406 23.724 3.552 53.686 11.745 73.407 18.875-24.69 5.254-57.005 7.874-83.687 7.5 3.243 4.133 6.564 8.242 9.937 12.344l.563-.468c38.655 40.348 80.204 53.028 120.875 53.75-6.873 6.918-14.447 13.48-19.25 21.593-5.292 8.936-8.076 19.652-8.78 30.968-33.344-12.87-77.914-40.503-107.75-78.344l-.19.125c-9.727-12.486-18.972-24.776-27.718-36.938-5.692 29.708-28.95 66.377-48.406 88.625 9.978-28.618 19.846-65.56 23.376-98.72-69.03 10.948-128.245-7.166-162.562-64.28 55.824 29.773 106.504 51.892 161.906 36.594-42.415-66.643-67.75-132.218-69.03-210.906z"}}]})(props);
};
