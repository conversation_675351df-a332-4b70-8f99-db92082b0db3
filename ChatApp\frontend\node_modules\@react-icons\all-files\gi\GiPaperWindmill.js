// THIS FILE IS AUTO GENERATED
var GenIcon = require('../lib').GenIcon
module.exports.GiPaperWindmill = function GiPaperWindmill (props) {
  return GenIcon({"tag":"svg","attr":{"viewBox":"0 0 512 512"},"child":[{"tag":"path","attr":{"d":"M456.94 42.332c-62.326 8.638-108.47 14.454-139.52 21.62-35.254 8.135-55.44 18.14-76.61 38.98-8.608 18.737-4.073 43.725 6.444 67.388 8.828 19.863 21.663 38.372 31.652 50.047L456.94 42.332zM42.33 55.062c8.638 62.324 14.454 108.468 21.62 139.518 8.135 35.254 18.14 55.44 38.98 76.61 18.737 8.608 43.725 4.073 67.388-6.444 19.863-8.828 38.372-21.663 50.047-31.652L42.332 55.06zm175.84 70.475l-35.004 35.004c-4.363 4.364-3.523 4.776-3.395 ********** 1.615 2.58 4.125 5.352l50.192 50.19c5.57-3.51 11.813-5.558 18.174-6.132-7.634-10.833-15.25-23.628-21.46-37.6-7.305-16.436-12.647-34.487-12.632-52.093zm129.14 53.61c-.086-.008-.16-.003-.226.007-.178.03-.298.105-.424.137-.498.128-2.58 1.62-5.346 4.122l-50.68 50.68c3.486 5.527 5.535 11.72 6.126 18.03 10.935-7.746 23.91-15.496 38.088-21.798 16.437-7.305 34.49-12.648 52.095-12.633L351.94 182.69c-2.862-2.864-4.025-3.488-4.627-3.542zM256 232.942c-5.867 0-11.735 2.265-16.264 6.793-9.057 9.058-9.057 23.47 0 32.528 9.058 9.057 23.47 9.057 32.528 0 9.057-9.058 9.057-23.47 0-32.528-4.53-4.528-10.397-6.793-16.264-6.793zm131.205 2.86c-14.236-.038-29.912 4.245-45.045 10.97-19.862 8.828-38.372 21.662-50.047 31.65L470.148 456.46c-8.637-62.326-14.453-108.47-21.62-139.52-8.134-35.253-18.138-55.44-38.975-76.61-6.756-3.104-14.32-4.506-22.348-4.527zM215.23 259.736c-10.833 7.634-23.628 15.25-37.6 21.46-16.436 7.305-34.487 12.647-52.093 12.632l35.004 35.004c4.364 4.363 4.776 3.523 5.28 3.395.5-.128 2.58-1.615 5.352-4.125l50.19-50.192c-3.51-5.57-5.558-11.813-6.132-18.174zm62.68 30.9c-5.57 3.512-11.813 5.562-18.174 6.135 7.634 10.835 15.25 23.628 21.46 37.6 7.305 16.436 12.647 34.487 12.632 52.093l35.004-35.004c4.363-4.364 3.523-4.776 3.395-5.28-.128-.5-1.615-2.58-4.125-5.352l-50.192-50.19zm-44.816.997L55.06 469.668c62.326-8.638 108.47-14.454 139.52-21.62 35.254-8.135 55.44-18.14 76.61-38.98 8.608-18.737 4.073-43.725-6.444-67.388-8.828-19.863-21.663-38.372-31.652-50.047zM265 437.838c-5.73 4.104-11.683 7.74-18 10.986V496h18v-58.162z"}}]})(props);
};
