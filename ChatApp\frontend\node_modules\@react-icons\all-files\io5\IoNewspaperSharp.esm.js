// THIS FILE IS AUTO GENERATED
import { GenIcon } from '../lib';
export function IoNewspaperSharp (props) {
  return GenIcon({"tag":"svg","attr":{"viewBox":"0 0 512 512"},"child":[{"tag":"rect","attr":{"width":"96","height":"96","x":"96","y":"112","fill":"none","rx":"16","ry":"16"}},{"tag":"path","attr":{"d":"M468 112h-52v304a32 32 0 0032 32 32 32 0 0032-32V124a12 12 0 00-12-12z"}},{"tag":"path","attr":{"d":"M431.15 477.75A64.11 64.11 0 01384 416V44a12 12 0 00-12-12H44a12 12 0 00-12 12v380a56 56 0 0056 56h342.85a1.14 1.14 0 00.3-2.25zM96 208v-96h96v96zm224 192H96v-32h224zm0-64H96v-32h224zm0-64H96v-32h224zm0-64h-96v-32h96zm0-64h-96v-32h96z"}}]})(props);
};
