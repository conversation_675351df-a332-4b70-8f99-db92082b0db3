// THIS FILE IS AUTO GENERATED
import { GenIcon } from '../lib';
export function IoAnalyticsOutline (props) {
  return GenIcon({"tag":"svg","attr":{"viewBox":"0 0 512 512"},"child":[{"tag":"path","attr":{"fill":"none","strokeLinecap":"round","strokeLinejoin":"round","strokeWidth":"32","d":"M344 280l88-88m-200 24l64 64M80 320l104-104"}},{"tag":"circle","attr":{"cx":"456","cy":"168","r":"24","fill":"none","strokeLinecap":"round","strokeLinejoin":"round","strokeWidth":"32"}},{"tag":"circle","attr":{"cx":"320","cy":"304","r":"24","fill":"none","strokeLinecap":"round","strokeLinejoin":"round","strokeWidth":"32"}},{"tag":"circle","attr":{"cx":"208","cy":"192","r":"24","fill":"none","strokeLinecap":"round","strokeLinejoin":"round","strokeWidth":"32"}},{"tag":"circle","attr":{"cx":"56","cy":"344","r":"24","fill":"none","strokeLinecap":"round","strokeLinejoin":"round","strokeWidth":"32"}}]})(props);
};
