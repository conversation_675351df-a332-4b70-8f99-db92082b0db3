// THIS FILE IS AUTO GENERATED
var GenIcon = require('../lib').GenIcon
module.exports.BiIntersect = function BiIntersect (props) {
  return GenIcon({"tag":"svg","attr":{"viewBox":"0 0 24 24"},"child":[{"tag":"path","attr":{"d":"M5,16h3v3c0,1.103,0.897,2,2,2h9c1.103,0,2-0.897,2-2v-9c0-1.103-0.897-2-2-2h-3V5c0-1.103-0.897-2-2-2H5 C3.897,3,3,3.897,3,5v9C3,15.103,3.897,16,5,16z M14.001,14L14.001,14L14,10h0.001V14z M19,10l0.001,9H10v-3h4c1.103,0,2-0.897,2-2 v-4H19z M5,5h9v3h-4c-1.103,0-2,0.897-2,2v4H5V5z"}}]})(props);
};
