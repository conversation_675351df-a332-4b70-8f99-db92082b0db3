// THIS FILE IS AUTO GENERATED
import { GenIcon } from '../lib';
export function IoJournalOutline (props) {
  return GenIcon({"tag":"svg","attr":{"viewBox":"0 0 512 512"},"child":[{"tag":"rect","attr":{"width":"320","height":"416","x":"96","y":"48","fill":"none","strokeLinejoin":"round","strokeWidth":"32","rx":"48","ry":"48"}},{"tag":"path","attr":{"fill":"none","strokeLinejoin":"round","strokeWidth":"60","d":"M320 48v416"}}]})(props);
};
