// THIS FILE IS AUTO GENERATED
import { GenIcon } from '../lib';
export function IoSubwayOutline (props) {
  return GenIcon({"tag":"svg","attr":{"viewBox":"0 0 512 512"},"child":[{"tag":"rect","attr":{"width":"288","height":"352","x":"112","y":"32","fill":"none","strokeMiterlimit":"10","strokeWidth":"32","rx":"48","ry":"48"}},{"tag":"path","attr":{"fill":"none","strokeLinecap":"round","strokeLinejoin":"round","strokeWidth":"32","d":"M208 80h96"}},{"tag":"rect","attr":{"width":"288","height":"96","x":"112","y":"128","fill":"none","strokeLinecap":"round","strokeLinejoin":"round","strokeWidth":"32","rx":"32","ry":"32"}},{"tag":"circle","attr":{"cx":"176","cy":"320","r":"16","fill":"none","strokeLinejoin":"round","strokeWidth":"32"}},{"tag":"circle","attr":{"cx":"336","cy":"320","r":"16","fill":"none","strokeLinejoin":"round","strokeWidth":"32"}},{"tag":"path","attr":{"fill":"none","strokeLinecap":"round","strokeLinejoin":"round","strokeWidth":"32","d":"M144 464h224m-32-32l48 48m-208-48l-48 48"}}]})(props);
};
