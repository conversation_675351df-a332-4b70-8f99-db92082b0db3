// THIS FILE IS AUTO GENERATED
var GenIcon = require('../lib').GenIcon
module.exports.BiLoader = function BiLoader (props) {
  return GenIcon({"tag":"svg","attr":{"viewBox":"0 0 24 24"},"child":[{"tag":"path","attr":{"d":"M2 11H7V13H2zM17 11H22V13H17zM11 17H13V22H11zM11 2H13V7H11z"}},{"tag":"path","attr":{"transform":"rotate(-45.001 6.697 6.697)","d":"M5.697 4.197H7.697V9.197H5.697z"}},{"tag":"path","attr":{"transform":"rotate(134.999 17.303 17.303)","d":"M16.303 14.803H18.303V19.803H16.303z"}},{"tag":"path","attr":{"transform":"rotate(45.001 6.697 17.303)","d":"M5.697 14.803H7.697V19.803H5.697z"}},{"tag":"path","attr":{"transform":"rotate(-44.992 17.303 6.697)","d":"M14.803 5.697H19.803V7.697H14.803z"}}]})(props);
};
