// THIS FILE IS AUTO GENERATED
var GenIcon = require('../lib').GenIcon
module.exports.GiCurledLeaf = function GiCurledLeaf (props) {
  return GenIcon({"tag":"svg","attr":{"viewBox":"0 0 512 512"},"child":[{"tag":"path","attr":{"d":"M65.406 22.438C-32.612 123.463 45.7 279.385 140 313.03c110.753 39.518 281.45-48.678 329.688 68.314 2.887 11.273 2.674 21.795.312 31.47-4.584 18.77-17.86 34.842-35.094 45.405-17.233 10.562-38.186 15.343-56.875 12.25-18.688-3.095-35.237-13.374-46.06-34.845-7.256-14.418-7.834-25.936-5.25-34.72 2.582-8.782 8.476-15.232 16.092-19.06 15.233-7.66 36.52-4.87 49.22 17.624 7.496 13.276 2.582 22.546-3.782 26.53-3.182 1.992-6.634 2.58-10.313 1.375-3.678-1.206-8.1-4.4-12.062-12.156l-16.625 8.5c5.66 11.08 13.76 18.427 22.844 21.405 9.084 2.978 18.624 1.375 26.062-3.28 14.877-9.314 21.654-31.144 10.125-51.564-17.02-30.146-50.187-37.035-73.874-25.124-11.843 5.956-21.537 16.6-25.625 30.5-4.087 13.9-2.42 30.645 6.5 48.375 13.307 26.393 35.835 40.927 59.69 44.876 23.852 3.95 48.938-2.013 69.717-14.75 20.78-12.736 37.512-32.51 43.47-56.906 4.178-17.11 2.728-36.26-5.844-55.844.012-.196.02-.397.03-.594-.86-1.67-1.738-3.302-2.624-4.906-3.296-6.525-7.39-13.095-12.345-19.625l-.22.158c-15.118-20.04-33.1-33.376-53.624-43.282-29.513-14.245-64.506-20.966-102.374-27.937-75.736-13.944-163.31-29.618-236.344-115.533l14.22-12.093c69.026 81.2 149.666 95.258 225.5 109.22 37.916 6.98 74.522 13.763 107.124 29.5 22.7 10.955 43.322 26.472 60.406 48.967C464.098 111.956 141.36 206.986 65.406 22.44z","fillRule":"evenodd"}}]})(props);
};
