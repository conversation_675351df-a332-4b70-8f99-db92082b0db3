(function(){const l=document.createElement("link").relList;if(l&&l.supports&&l.supports("modulepreload"))return;for(const c of document.querySelectorAll('link[rel="modulepreload"]'))s(c);new MutationObserver(c=>{for(const f of c)if(f.type==="childList")for(const d of f.addedNodes)d.tagName==="LINK"&&d.rel==="modulepreload"&&s(d)}).observe(document,{childList:!0,subtree:!0});function r(c){const f={};return c.integrity&&(f.integrity=c.integrity),c.referrerPolicy&&(f.referrerPolicy=c.referrerPolicy),c.crossOrigin==="use-credentials"?f.credentials="include":c.crossOrigin==="anonymous"?f.credentials="omit":f.credentials="same-origin",f}function s(c){if(c.ep)return;c.ep=!0;const f=r(c);fetch(c.href,f)}})();function Hv(a){return a&&a.__esModule&&Object.prototype.hasOwnProperty.call(a,"default")?a.default:a}var Sc={exports:{}},ki={};/**
 * @license React
 * react-jsx-runtime.production.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var dy;function Vv(){if(dy)return ki;dy=1;var a=Symbol.for("react.transitional.element"),l=Symbol.for("react.fragment");function r(s,c,f){var d=null;if(f!==void 0&&(d=""+f),c.key!==void 0&&(d=""+c.key),"key"in c){f={};for(var m in c)m!=="key"&&(f[m]=c[m])}else f=c;return c=f.ref,{$$typeof:a,type:s,key:d,ref:c!==void 0?c:null,props:f}}return ki.Fragment=l,ki.jsx=r,ki.jsxs=r,ki}var hy;function Yv(){return hy||(hy=1,Sc.exports=Vv()),Sc.exports}var S=Yv(),xc={exports:{}},me={};/**
 * @license React
 * react.production.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var my;function Xv(){if(my)return me;my=1;var a=Symbol.for("react.transitional.element"),l=Symbol.for("react.portal"),r=Symbol.for("react.fragment"),s=Symbol.for("react.strict_mode"),c=Symbol.for("react.profiler"),f=Symbol.for("react.consumer"),d=Symbol.for("react.context"),m=Symbol.for("react.forward_ref"),g=Symbol.for("react.suspense"),y=Symbol.for("react.memo"),v=Symbol.for("react.lazy"),A=Symbol.iterator;function _(E){return E===null||typeof E!="object"?null:(E=A&&E[A]||E["@@iterator"],typeof E=="function"?E:null)}var Q={isMounted:function(){return!1},enqueueForceUpdate:function(){},enqueueReplaceState:function(){},enqueueSetState:function(){}},R=Object.assign,B={};function L(E,G,I){this.props=E,this.context=G,this.refs=B,this.updater=I||Q}L.prototype.isReactComponent={},L.prototype.setState=function(E,G){if(typeof E!="object"&&typeof E!="function"&&E!=null)throw Error("takes an object of state variables to update or a function which returns an object of state variables.");this.updater.enqueueSetState(this,E,G,"setState")},L.prototype.forceUpdate=function(E){this.updater.enqueueForceUpdate(this,E,"forceUpdate")};function X(){}X.prototype=L.prototype;function V(E,G,I){this.props=E,this.context=G,this.refs=B,this.updater=I||Q}var $=V.prototype=new X;$.constructor=V,R($,L.prototype),$.isPureReactComponent=!0;var ie=Array.isArray,K={H:null,A:null,T:null,S:null,V:null},Ae=Object.prototype.hasOwnProperty;function ye(E,G,I,W,re,Ee){return I=Ee.ref,{$$typeof:a,type:E,key:G,ref:I!==void 0?I:null,props:Ee}}function De(E,G){return ye(E.type,G,void 0,void 0,void 0,E.props)}function pe(E){return typeof E=="object"&&E!==null&&E.$$typeof===a}function Qe(E){var G={"=":"=0",":":"=2"};return"$"+E.replace(/[=:]/g,function(I){return G[I]})}var Ze=/\/+/g;function oe(E,G){return typeof E=="object"&&E!==null&&E.key!=null?Qe(""+E.key):G.toString(36)}function xe(){}function Me(E){switch(E.status){case"fulfilled":return E.value;case"rejected":throw E.reason;default:switch(typeof E.status=="string"?E.then(xe,xe):(E.status="pending",E.then(function(G){E.status==="pending"&&(E.status="fulfilled",E.value=G)},function(G){E.status==="pending"&&(E.status="rejected",E.reason=G)})),E.status){case"fulfilled":return E.value;case"rejected":throw E.reason}}throw E}function Oe(E,G,I,W,re){var Ee=typeof E;(Ee==="undefined"||Ee==="boolean")&&(E=null);var fe=!1;if(E===null)fe=!0;else switch(Ee){case"bigint":case"string":case"number":fe=!0;break;case"object":switch(E.$$typeof){case a:case l:fe=!0;break;case v:return fe=E._init,Oe(fe(E._payload),G,I,W,re)}}if(fe)return re=re(E),fe=W===""?"."+oe(E,0):W,ie(re)?(I="",fe!=null&&(I=fe.replace(Ze,"$&/")+"/"),Oe(re,G,I,"",function(qt){return qt})):re!=null&&(pe(re)&&(re=De(re,I+(re.key==null||E&&E.key===re.key?"":(""+re.key).replace(Ze,"$&/")+"/")+fe)),G.push(re)),1;fe=0;var ht=W===""?".":W+":";if(ie(E))for(var ke=0;ke<E.length;ke++)W=E[ke],Ee=ht+oe(W,ke),fe+=Oe(W,G,I,Ee,re);else if(ke=_(E),typeof ke=="function")for(E=ke.call(E),ke=0;!(W=E.next()).done;)W=W.value,Ee=ht+oe(W,ke++),fe+=Oe(W,G,I,Ee,re);else if(Ee==="object"){if(typeof E.then=="function")return Oe(Me(E),G,I,W,re);throw G=String(E),Error("Objects are not valid as a React child (found: "+(G==="[object Object]"?"object with keys {"+Object.keys(E).join(", ")+"}":G)+"). If you meant to render a collection of children, use an array instead.")}return fe}function j(E,G,I){if(E==null)return E;var W=[],re=0;return Oe(E,W,"","",function(Ee){return G.call(I,Ee,re++)}),W}function F(E){if(E._status===-1){var G=E._result;G=G(),G.then(function(I){(E._status===0||E._status===-1)&&(E._status=1,E._result=I)},function(I){(E._status===0||E._status===-1)&&(E._status=2,E._result=I)}),E._status===-1&&(E._status=0,E._result=G)}if(E._status===1)return E._result.default;throw E._result}var le=typeof reportError=="function"?reportError:function(E){if(typeof window=="object"&&typeof window.ErrorEvent=="function"){var G=new window.ErrorEvent("error",{bubbles:!0,cancelable:!0,message:typeof E=="object"&&E!==null&&typeof E.message=="string"?String(E.message):String(E),error:E});if(!window.dispatchEvent(G))return}else if(typeof process=="object"&&typeof process.emit=="function"){process.emit("uncaughtException",E);return}console.error(E)};function Te(){}return me.Children={map:j,forEach:function(E,G,I){j(E,function(){G.apply(this,arguments)},I)},count:function(E){var G=0;return j(E,function(){G++}),G},toArray:function(E){return j(E,function(G){return G})||[]},only:function(E){if(!pe(E))throw Error("React.Children.only expected to receive a single React element child.");return E}},me.Component=L,me.Fragment=r,me.Profiler=c,me.PureComponent=V,me.StrictMode=s,me.Suspense=g,me.__CLIENT_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE=K,me.__COMPILER_RUNTIME={__proto__:null,c:function(E){return K.H.useMemoCache(E)}},me.cache=function(E){return function(){return E.apply(null,arguments)}},me.cloneElement=function(E,G,I){if(E==null)throw Error("The argument must be a React element, but you passed "+E+".");var W=R({},E.props),re=E.key,Ee=void 0;if(G!=null)for(fe in G.ref!==void 0&&(Ee=void 0),G.key!==void 0&&(re=""+G.key),G)!Ae.call(G,fe)||fe==="key"||fe==="__self"||fe==="__source"||fe==="ref"&&G.ref===void 0||(W[fe]=G[fe]);var fe=arguments.length-2;if(fe===1)W.children=I;else if(1<fe){for(var ht=Array(fe),ke=0;ke<fe;ke++)ht[ke]=arguments[ke+2];W.children=ht}return ye(E.type,re,void 0,void 0,Ee,W)},me.createContext=function(E){return E={$$typeof:d,_currentValue:E,_currentValue2:E,_threadCount:0,Provider:null,Consumer:null},E.Provider=E,E.Consumer={$$typeof:f,_context:E},E},me.createElement=function(E,G,I){var W,re={},Ee=null;if(G!=null)for(W in G.key!==void 0&&(Ee=""+G.key),G)Ae.call(G,W)&&W!=="key"&&W!=="__self"&&W!=="__source"&&(re[W]=G[W]);var fe=arguments.length-2;if(fe===1)re.children=I;else if(1<fe){for(var ht=Array(fe),ke=0;ke<fe;ke++)ht[ke]=arguments[ke+2];re.children=ht}if(E&&E.defaultProps)for(W in fe=E.defaultProps,fe)re[W]===void 0&&(re[W]=fe[W]);return ye(E,Ee,void 0,void 0,null,re)},me.createRef=function(){return{current:null}},me.forwardRef=function(E){return{$$typeof:m,render:E}},me.isValidElement=pe,me.lazy=function(E){return{$$typeof:v,_payload:{_status:-1,_result:E},_init:F}},me.memo=function(E,G){return{$$typeof:y,type:E,compare:G===void 0?null:G}},me.startTransition=function(E){var G=K.T,I={};K.T=I;try{var W=E(),re=K.S;re!==null&&re(I,W),typeof W=="object"&&W!==null&&typeof W.then=="function"&&W.then(Te,le)}catch(Ee){le(Ee)}finally{K.T=G}},me.unstable_useCacheRefresh=function(){return K.H.useCacheRefresh()},me.use=function(E){return K.H.use(E)},me.useActionState=function(E,G,I){return K.H.useActionState(E,G,I)},me.useCallback=function(E,G){return K.H.useCallback(E,G)},me.useContext=function(E){return K.H.useContext(E)},me.useDebugValue=function(){},me.useDeferredValue=function(E,G){return K.H.useDeferredValue(E,G)},me.useEffect=function(E,G,I){var W=K.H;if(typeof I=="function")throw Error("useEffect CRUD overload is not enabled in this build of React.");return W.useEffect(E,G)},me.useId=function(){return K.H.useId()},me.useImperativeHandle=function(E,G,I){return K.H.useImperativeHandle(E,G,I)},me.useInsertionEffect=function(E,G){return K.H.useInsertionEffect(E,G)},me.useLayoutEffect=function(E,G){return K.H.useLayoutEffect(E,G)},me.useMemo=function(E,G){return K.H.useMemo(E,G)},me.useOptimistic=function(E,G){return K.H.useOptimistic(E,G)},me.useReducer=function(E,G,I){return K.H.useReducer(E,G,I)},me.useRef=function(E){return K.H.useRef(E)},me.useState=function(E){return K.H.useState(E)},me.useSyncExternalStore=function(E,G,I){return K.H.useSyncExternalStore(E,G,I)},me.useTransition=function(){return K.H.useTransition()},me.version="19.1.0",me}var yy;function Ic(){return yy||(yy=1,xc.exports=Xv()),xc.exports}var N=Ic();const at=Hv(N);var Ec={exports:{}},qi={},_c={exports:{}},wc={};/**
 * @license React
 * scheduler.production.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var py;function Gv(){return py||(py=1,function(a){function l(j,F){var le=j.length;j.push(F);e:for(;0<le;){var Te=le-1>>>1,E=j[Te];if(0<c(E,F))j[Te]=F,j[le]=E,le=Te;else break e}}function r(j){return j.length===0?null:j[0]}function s(j){if(j.length===0)return null;var F=j[0],le=j.pop();if(le!==F){j[0]=le;e:for(var Te=0,E=j.length,G=E>>>1;Te<G;){var I=2*(Te+1)-1,W=j[I],re=I+1,Ee=j[re];if(0>c(W,le))re<E&&0>c(Ee,W)?(j[Te]=Ee,j[re]=le,Te=re):(j[Te]=W,j[I]=le,Te=I);else if(re<E&&0>c(Ee,le))j[Te]=Ee,j[re]=le,Te=re;else break e}}return F}function c(j,F){var le=j.sortIndex-F.sortIndex;return le!==0?le:j.id-F.id}if(a.unstable_now=void 0,typeof performance=="object"&&typeof performance.now=="function"){var f=performance;a.unstable_now=function(){return f.now()}}else{var d=Date,m=d.now();a.unstable_now=function(){return d.now()-m}}var g=[],y=[],v=1,A=null,_=3,Q=!1,R=!1,B=!1,L=!1,X=typeof setTimeout=="function"?setTimeout:null,V=typeof clearTimeout=="function"?clearTimeout:null,$=typeof setImmediate<"u"?setImmediate:null;function ie(j){for(var F=r(y);F!==null;){if(F.callback===null)s(y);else if(F.startTime<=j)s(y),F.sortIndex=F.expirationTime,l(g,F);else break;F=r(y)}}function K(j){if(B=!1,ie(j),!R)if(r(g)!==null)R=!0,Ae||(Ae=!0,oe());else{var F=r(y);F!==null&&Oe(K,F.startTime-j)}}var Ae=!1,ye=-1,De=5,pe=-1;function Qe(){return L?!0:!(a.unstable_now()-pe<De)}function Ze(){if(L=!1,Ae){var j=a.unstable_now();pe=j;var F=!0;try{e:{R=!1,B&&(B=!1,V(ye),ye=-1),Q=!0;var le=_;try{t:{for(ie(j),A=r(g);A!==null&&!(A.expirationTime>j&&Qe());){var Te=A.callback;if(typeof Te=="function"){A.callback=null,_=A.priorityLevel;var E=Te(A.expirationTime<=j);if(j=a.unstable_now(),typeof E=="function"){A.callback=E,ie(j),F=!0;break t}A===r(g)&&s(g),ie(j)}else s(g);A=r(g)}if(A!==null)F=!0;else{var G=r(y);G!==null&&Oe(K,G.startTime-j),F=!1}}break e}finally{A=null,_=le,Q=!1}F=void 0}}finally{F?oe():Ae=!1}}}var oe;if(typeof $=="function")oe=function(){$(Ze)};else if(typeof MessageChannel<"u"){var xe=new MessageChannel,Me=xe.port2;xe.port1.onmessage=Ze,oe=function(){Me.postMessage(null)}}else oe=function(){X(Ze,0)};function Oe(j,F){ye=X(function(){j(a.unstable_now())},F)}a.unstable_IdlePriority=5,a.unstable_ImmediatePriority=1,a.unstable_LowPriority=4,a.unstable_NormalPriority=3,a.unstable_Profiling=null,a.unstable_UserBlockingPriority=2,a.unstable_cancelCallback=function(j){j.callback=null},a.unstable_forceFrameRate=function(j){0>j||125<j?console.error("forceFrameRate takes a positive int between 0 and 125, forcing frame rates higher than 125 fps is not supported"):De=0<j?Math.floor(1e3/j):5},a.unstable_getCurrentPriorityLevel=function(){return _},a.unstable_next=function(j){switch(_){case 1:case 2:case 3:var F=3;break;default:F=_}var le=_;_=F;try{return j()}finally{_=le}},a.unstable_requestPaint=function(){L=!0},a.unstable_runWithPriority=function(j,F){switch(j){case 1:case 2:case 3:case 4:case 5:break;default:j=3}var le=_;_=j;try{return F()}finally{_=le}},a.unstable_scheduleCallback=function(j,F,le){var Te=a.unstable_now();switch(typeof le=="object"&&le!==null?(le=le.delay,le=typeof le=="number"&&0<le?Te+le:Te):le=Te,j){case 1:var E=-1;break;case 2:E=250;break;case 5:E=1073741823;break;case 4:E=1e4;break;default:E=5e3}return E=le+E,j={id:v++,callback:F,priorityLevel:j,startTime:le,expirationTime:E,sortIndex:-1},le>Te?(j.sortIndex=le,l(y,j),r(g)===null&&j===r(y)&&(B?(V(ye),ye=-1):B=!0,Oe(K,le-Te))):(j.sortIndex=E,l(g,j),R||Q||(R=!0,Ae||(Ae=!0,oe()))),j},a.unstable_shouldYield=Qe,a.unstable_wrapCallback=function(j){var F=_;return function(){var le=_;_=F;try{return j.apply(this,arguments)}finally{_=le}}}}(wc)),wc}var gy;function Qv(){return gy||(gy=1,_c.exports=Gv()),_c.exports}var Ac={exports:{}},bt={};/**
 * @license React
 * react-dom.production.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var vy;function Zv(){if(vy)return bt;vy=1;var a=Ic();function l(g){var y="https://react.dev/errors/"+g;if(1<arguments.length){y+="?args[]="+encodeURIComponent(arguments[1]);for(var v=2;v<arguments.length;v++)y+="&args[]="+encodeURIComponent(arguments[v])}return"Minified React error #"+g+"; visit "+y+" for the full message or use the non-minified dev environment for full errors and additional helpful warnings."}function r(){}var s={d:{f:r,r:function(){throw Error(l(522))},D:r,C:r,L:r,m:r,X:r,S:r,M:r},p:0,findDOMNode:null},c=Symbol.for("react.portal");function f(g,y,v){var A=3<arguments.length&&arguments[3]!==void 0?arguments[3]:null;return{$$typeof:c,key:A==null?null:""+A,children:g,containerInfo:y,implementation:v}}var d=a.__CLIENT_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE;function m(g,y){if(g==="font")return"";if(typeof y=="string")return y==="use-credentials"?y:""}return bt.__DOM_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE=s,bt.createPortal=function(g,y){var v=2<arguments.length&&arguments[2]!==void 0?arguments[2]:null;if(!y||y.nodeType!==1&&y.nodeType!==9&&y.nodeType!==11)throw Error(l(299));return f(g,y,null,v)},bt.flushSync=function(g){var y=d.T,v=s.p;try{if(d.T=null,s.p=2,g)return g()}finally{d.T=y,s.p=v,s.d.f()}},bt.preconnect=function(g,y){typeof g=="string"&&(y?(y=y.crossOrigin,y=typeof y=="string"?y==="use-credentials"?y:"":void 0):y=null,s.d.C(g,y))},bt.prefetchDNS=function(g){typeof g=="string"&&s.d.D(g)},bt.preinit=function(g,y){if(typeof g=="string"&&y&&typeof y.as=="string"){var v=y.as,A=m(v,y.crossOrigin),_=typeof y.integrity=="string"?y.integrity:void 0,Q=typeof y.fetchPriority=="string"?y.fetchPriority:void 0;v==="style"?s.d.S(g,typeof y.precedence=="string"?y.precedence:void 0,{crossOrigin:A,integrity:_,fetchPriority:Q}):v==="script"&&s.d.X(g,{crossOrigin:A,integrity:_,fetchPriority:Q,nonce:typeof y.nonce=="string"?y.nonce:void 0})}},bt.preinitModule=function(g,y){if(typeof g=="string")if(typeof y=="object"&&y!==null){if(y.as==null||y.as==="script"){var v=m(y.as,y.crossOrigin);s.d.M(g,{crossOrigin:v,integrity:typeof y.integrity=="string"?y.integrity:void 0,nonce:typeof y.nonce=="string"?y.nonce:void 0})}}else y==null&&s.d.M(g)},bt.preload=function(g,y){if(typeof g=="string"&&typeof y=="object"&&y!==null&&typeof y.as=="string"){var v=y.as,A=m(v,y.crossOrigin);s.d.L(g,v,{crossOrigin:A,integrity:typeof y.integrity=="string"?y.integrity:void 0,nonce:typeof y.nonce=="string"?y.nonce:void 0,type:typeof y.type=="string"?y.type:void 0,fetchPriority:typeof y.fetchPriority=="string"?y.fetchPriority:void 0,referrerPolicy:typeof y.referrerPolicy=="string"?y.referrerPolicy:void 0,imageSrcSet:typeof y.imageSrcSet=="string"?y.imageSrcSet:void 0,imageSizes:typeof y.imageSizes=="string"?y.imageSizes:void 0,media:typeof y.media=="string"?y.media:void 0})}},bt.preloadModule=function(g,y){if(typeof g=="string")if(y){var v=m(y.as,y.crossOrigin);s.d.m(g,{as:typeof y.as=="string"&&y.as!=="script"?y.as:void 0,crossOrigin:v,integrity:typeof y.integrity=="string"?y.integrity:void 0})}else s.d.m(g)},bt.requestFormReset=function(g){s.d.r(g)},bt.unstable_batchedUpdates=function(g,y){return g(y)},bt.useFormState=function(g,y,v){return d.H.useFormState(g,y,v)},bt.useFormStatus=function(){return d.H.useHostTransitionStatus()},bt.version="19.1.0",bt}var by;function Kv(){if(by)return Ac.exports;by=1;function a(){if(!(typeof __REACT_DEVTOOLS_GLOBAL_HOOK__>"u"||typeof __REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE!="function"))try{__REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE(a)}catch(l){console.error(l)}}return a(),Ac.exports=Zv(),Ac.exports}/**
 * @license React
 * react-dom-client.production.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var Sy;function Fv(){if(Sy)return qi;Sy=1;var a=Qv(),l=Ic(),r=Kv();function s(e){var t="https://react.dev/errors/"+e;if(1<arguments.length){t+="?args[]="+encodeURIComponent(arguments[1]);for(var n=2;n<arguments.length;n++)t+="&args[]="+encodeURIComponent(arguments[n])}return"Minified React error #"+e+"; visit "+t+" for the full message or use the non-minified dev environment for full errors and additional helpful warnings."}function c(e){return!(!e||e.nodeType!==1&&e.nodeType!==9&&e.nodeType!==11)}function f(e){var t=e,n=e;if(e.alternate)for(;t.return;)t=t.return;else{e=t;do t=e,(t.flags&4098)!==0&&(n=t.return),e=t.return;while(e)}return t.tag===3?n:null}function d(e){if(e.tag===13){var t=e.memoizedState;if(t===null&&(e=e.alternate,e!==null&&(t=e.memoizedState)),t!==null)return t.dehydrated}return null}function m(e){if(f(e)!==e)throw Error(s(188))}function g(e){var t=e.alternate;if(!t){if(t=f(e),t===null)throw Error(s(188));return t!==e?null:e}for(var n=e,i=t;;){var u=n.return;if(u===null)break;var o=u.alternate;if(o===null){if(i=u.return,i!==null){n=i;continue}break}if(u.child===o.child){for(o=u.child;o;){if(o===n)return m(u),e;if(o===i)return m(u),t;o=o.sibling}throw Error(s(188))}if(n.return!==i.return)n=u,i=o;else{for(var h=!1,p=u.child;p;){if(p===n){h=!0,n=u,i=o;break}if(p===i){h=!0,i=u,n=o;break}p=p.sibling}if(!h){for(p=o.child;p;){if(p===n){h=!0,n=o,i=u;break}if(p===i){h=!0,i=o,n=u;break}p=p.sibling}if(!h)throw Error(s(189))}}if(n.alternate!==i)throw Error(s(190))}if(n.tag!==3)throw Error(s(188));return n.stateNode.current===n?e:t}function y(e){var t=e.tag;if(t===5||t===26||t===27||t===6)return e;for(e=e.child;e!==null;){if(t=y(e),t!==null)return t;e=e.sibling}return null}var v=Object.assign,A=Symbol.for("react.element"),_=Symbol.for("react.transitional.element"),Q=Symbol.for("react.portal"),R=Symbol.for("react.fragment"),B=Symbol.for("react.strict_mode"),L=Symbol.for("react.profiler"),X=Symbol.for("react.provider"),V=Symbol.for("react.consumer"),$=Symbol.for("react.context"),ie=Symbol.for("react.forward_ref"),K=Symbol.for("react.suspense"),Ae=Symbol.for("react.suspense_list"),ye=Symbol.for("react.memo"),De=Symbol.for("react.lazy"),pe=Symbol.for("react.activity"),Qe=Symbol.for("react.memo_cache_sentinel"),Ze=Symbol.iterator;function oe(e){return e===null||typeof e!="object"?null:(e=Ze&&e[Ze]||e["@@iterator"],typeof e=="function"?e:null)}var xe=Symbol.for("react.client.reference");function Me(e){if(e==null)return null;if(typeof e=="function")return e.$$typeof===xe?null:e.displayName||e.name||null;if(typeof e=="string")return e;switch(e){case R:return"Fragment";case L:return"Profiler";case B:return"StrictMode";case K:return"Suspense";case Ae:return"SuspenseList";case pe:return"Activity"}if(typeof e=="object")switch(e.$$typeof){case Q:return"Portal";case $:return(e.displayName||"Context")+".Provider";case V:return(e._context.displayName||"Context")+".Consumer";case ie:var t=e.render;return e=e.displayName,e||(e=t.displayName||t.name||"",e=e!==""?"ForwardRef("+e+")":"ForwardRef"),e;case ye:return t=e.displayName||null,t!==null?t:Me(e.type)||"Memo";case De:t=e._payload,e=e._init;try{return Me(e(t))}catch{}}return null}var Oe=Array.isArray,j=l.__CLIENT_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE,F=r.__DOM_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE,le={pending:!1,data:null,method:null,action:null},Te=[],E=-1;function G(e){return{current:e}}function I(e){0>E||(e.current=Te[E],Te[E]=null,E--)}function W(e,t){E++,Te[E]=e.current,e.current=t}var re=G(null),Ee=G(null),fe=G(null),ht=G(null);function ke(e,t){switch(W(fe,t),W(Ee,e),W(re,null),t.nodeType){case 9:case 11:e=(e=t.documentElement)&&(e=e.namespaceURI)?Hm(e):0;break;default:if(e=t.tagName,t=t.namespaceURI)t=Hm(t),e=Vm(t,e);else switch(e){case"svg":e=1;break;case"math":e=2;break;default:e=0}}I(re),W(re,e)}function qt(){I(re),I(Ee),I(fe)}function ga(e){e.memoizedState!==null&&W(ht,e);var t=re.current,n=Vm(t,e.type);t!==n&&(W(Ee,e),W(re,n))}function Ga(e){Ee.current===e&&(I(re),I(Ee)),ht.current===e&&(I(ht),Ui._currentValue=le)}var Qa=Object.prototype.hasOwnProperty,Vl=a.unstable_scheduleCallback,Za=a.unstable_cancelCallback,ur=a.unstable_shouldYield,ru=a.unstable_requestPaint,Ht=a.unstable_now,Ef=a.unstable_getCurrentPriorityLevel,Yl=a.unstable_ImmediatePriority,x=a.unstable_UserBlockingPriority,D=a.unstable_NormalPriority,q=a.unstable_LowPriority,P=a.unstable_IdlePriority,J=a.log,Z=a.unstable_setDisableYieldValue,ne=null,de=null;function _e(e){if(typeof J=="function"&&Z(e),de&&typeof de.setStrictMode=="function")try{de.setStrictMode(ne,e)}catch{}}var He=Math.clz32?Math.clz32:su,Ka=Math.log,sn=Math.LN2;function su(e){return e>>>=0,e===0?32:31-(Ka(e)/sn|0)|0}var Hn=256,Vn=4194304;function vn(e){var t=e&42;if(t!==0)return t;switch(e&-e){case 1:return 1;case 2:return 2;case 4:return 4;case 8:return 8;case 16:return 16;case 32:return 32;case 64:return 64;case 128:return 128;case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:return e&4194048;case 4194304:case 8388608:case 16777216:case 33554432:return e&62914560;case 67108864:return 67108864;case 134217728:return 134217728;case 268435456:return 268435456;case 536870912:return 536870912;case 1073741824:return 0;default:return e}}function va(e,t,n){var i=e.pendingLanes;if(i===0)return 0;var u=0,o=e.suspendedLanes,h=e.pingedLanes;e=e.warmLanes;var p=i&134217727;return p!==0?(i=p&~o,i!==0?u=vn(i):(h&=p,h!==0?u=vn(h):n||(n=p&~e,n!==0&&(u=vn(n))))):(p=i&~o,p!==0?u=vn(p):h!==0?u=vn(h):n||(n=i&~e,n!==0&&(u=vn(n)))),u===0?0:t!==0&&t!==u&&(t&o)===0&&(o=u&-u,n=t&-t,o>=n||o===32&&(n&4194048)!==0)?t:u}function ba(e,t){return(e.pendingLanes&~(e.suspendedLanes&~e.pingedLanes)&t)===0}function or(e,t){switch(e){case 1:case 2:case 4:case 8:case 64:return t+250;case 16:case 32:case 128:case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:return t+5e3;case 4194304:case 8388608:case 16777216:case 33554432:return-1;case 67108864:case 134217728:case 268435456:case 536870912:case 1073741824:return-1;default:return-1}}function _f(){var e=Hn;return Hn<<=1,(Hn&4194048)===0&&(Hn=256),e}function wf(){var e=Vn;return Vn<<=1,(Vn&62914560)===0&&(Vn=4194304),e}function uu(e){for(var t=[],n=0;31>n;n++)t.push(e);return t}function Xl(e,t){e.pendingLanes|=t,t!==268435456&&(e.suspendedLanes=0,e.pingedLanes=0,e.warmLanes=0)}function Op(e,t,n,i,u,o){var h=e.pendingLanes;e.pendingLanes=n,e.suspendedLanes=0,e.pingedLanes=0,e.warmLanes=0,e.expiredLanes&=n,e.entangledLanes&=n,e.errorRecoveryDisabledLanes&=n,e.shellSuspendCounter=0;var p=e.entanglements,b=e.expirationTimes,C=e.hiddenUpdates;for(n=h&~n;0<n;){var k=31-He(n),Y=1<<k;p[k]=0,b[k]=-1;var M=C[k];if(M!==null)for(C[k]=null,k=0;k<M.length;k++){var U=M[k];U!==null&&(U.lane&=-536870913)}n&=~Y}i!==0&&Af(e,i,0),o!==0&&u===0&&e.tag!==0&&(e.suspendedLanes|=o&~(h&~t))}function Af(e,t,n){e.pendingLanes|=t,e.suspendedLanes&=~t;var i=31-He(t);e.entangledLanes|=t,e.entanglements[i]=e.entanglements[i]|1073741824|n&4194090}function Tf(e,t){var n=e.entangledLanes|=t;for(e=e.entanglements;n;){var i=31-He(n),u=1<<i;u&t|e[i]&t&&(e[i]|=t),n&=~u}}function ou(e){switch(e){case 2:e=1;break;case 8:e=4;break;case 32:e=16;break;case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:case 4194304:case 8388608:case 16777216:case 33554432:e=128;break;case 268435456:e=134217728;break;default:e=0}return e}function cu(e){return e&=-e,2<e?8<e?(e&134217727)!==0?32:268435456:8:2}function Rf(){var e=F.p;return e!==0?e:(e=window.event,e===void 0?32:ry(e.type))}function Np(e,t){var n=F.p;try{return F.p=e,t()}finally{F.p=n}}var Yn=Math.random().toString(36).slice(2),gt="__reactFiber$"+Yn,wt="__reactProps$"+Yn,Fa="__reactContainer$"+Yn,fu="__reactEvents$"+Yn,Cp="__reactListeners$"+Yn,Dp="__reactHandles$"+Yn,Of="__reactResources$"+Yn,Gl="__reactMarker$"+Yn;function du(e){delete e[gt],delete e[wt],delete e[fu],delete e[Cp],delete e[Dp]}function Ja(e){var t=e[gt];if(t)return t;for(var n=e.parentNode;n;){if(t=n[Fa]||n[gt]){if(n=t.alternate,t.child!==null||n!==null&&n.child!==null)for(e=Qm(e);e!==null;){if(n=e[gt])return n;e=Qm(e)}return t}e=n,n=e.parentNode}return null}function $a(e){if(e=e[gt]||e[Fa]){var t=e.tag;if(t===5||t===6||t===13||t===26||t===27||t===3)return e}return null}function Ql(e){var t=e.tag;if(t===5||t===26||t===27||t===6)return e.stateNode;throw Error(s(33))}function Wa(e){var t=e[Of];return t||(t=e[Of]={hoistableStyles:new Map,hoistableScripts:new Map}),t}function ut(e){e[Gl]=!0}var Nf=new Set,Cf={};function Sa(e,t){Pa(e,t),Pa(e+"Capture",t)}function Pa(e,t){for(Cf[e]=t,e=0;e<t.length;e++)Nf.add(t[e])}var Mp=RegExp("^[:A-Z_a-z\\u00C0-\\u00D6\\u00D8-\\u00F6\\u00F8-\\u02FF\\u0370-\\u037D\\u037F-\\u1FFF\\u200C-\\u200D\\u2070-\\u218F\\u2C00-\\u2FEF\\u3001-\\uD7FF\\uF900-\\uFDCF\\uFDF0-\\uFFFD][:A-Z_a-z\\u00C0-\\u00D6\\u00D8-\\u00F6\\u00F8-\\u02FF\\u0370-\\u037D\\u037F-\\u1FFF\\u200C-\\u200D\\u2070-\\u218F\\u2C00-\\u2FEF\\u3001-\\uD7FF\\uF900-\\uFDCF\\uFDF0-\\uFFFD\\-.0-9\\u00B7\\u0300-\\u036F\\u203F-\\u2040]*$"),Df={},Mf={};function Up(e){return Qa.call(Mf,e)?!0:Qa.call(Df,e)?!1:Mp.test(e)?Mf[e]=!0:(Df[e]=!0,!1)}function cr(e,t,n){if(Up(t))if(n===null)e.removeAttribute(t);else{switch(typeof n){case"undefined":case"function":case"symbol":e.removeAttribute(t);return;case"boolean":var i=t.toLowerCase().slice(0,5);if(i!=="data-"&&i!=="aria-"){e.removeAttribute(t);return}}e.setAttribute(t,""+n)}}function fr(e,t,n){if(n===null)e.removeAttribute(t);else{switch(typeof n){case"undefined":case"function":case"symbol":case"boolean":e.removeAttribute(t);return}e.setAttribute(t,""+n)}}function bn(e,t,n,i){if(i===null)e.removeAttribute(n);else{switch(typeof i){case"undefined":case"function":case"symbol":case"boolean":e.removeAttribute(n);return}e.setAttributeNS(t,n,""+i)}}var hu,Uf;function Ia(e){if(hu===void 0)try{throw Error()}catch(n){var t=n.stack.trim().match(/\n( *(at )?)/);hu=t&&t[1]||"",Uf=-1<n.stack.indexOf(`
    at`)?" (<anonymous>)":-1<n.stack.indexOf("@")?"@unknown:0:0":""}return`
`+hu+e+Uf}var mu=!1;function yu(e,t){if(!e||mu)return"";mu=!0;var n=Error.prepareStackTrace;Error.prepareStackTrace=void 0;try{var i={DetermineComponentFrameRoot:function(){try{if(t){var Y=function(){throw Error()};if(Object.defineProperty(Y.prototype,"props",{set:function(){throw Error()}}),typeof Reflect=="object"&&Reflect.construct){try{Reflect.construct(Y,[])}catch(U){var M=U}Reflect.construct(e,[],Y)}else{try{Y.call()}catch(U){M=U}e.call(Y.prototype)}}else{try{throw Error()}catch(U){M=U}(Y=e())&&typeof Y.catch=="function"&&Y.catch(function(){})}}catch(U){if(U&&M&&typeof U.stack=="string")return[U.stack,M.stack]}return[null,null]}};i.DetermineComponentFrameRoot.displayName="DetermineComponentFrameRoot";var u=Object.getOwnPropertyDescriptor(i.DetermineComponentFrameRoot,"name");u&&u.configurable&&Object.defineProperty(i.DetermineComponentFrameRoot,"name",{value:"DetermineComponentFrameRoot"});var o=i.DetermineComponentFrameRoot(),h=o[0],p=o[1];if(h&&p){var b=h.split(`
`),C=p.split(`
`);for(u=i=0;i<b.length&&!b[i].includes("DetermineComponentFrameRoot");)i++;for(;u<C.length&&!C[u].includes("DetermineComponentFrameRoot");)u++;if(i===b.length||u===C.length)for(i=b.length-1,u=C.length-1;1<=i&&0<=u&&b[i]!==C[u];)u--;for(;1<=i&&0<=u;i--,u--)if(b[i]!==C[u]){if(i!==1||u!==1)do if(i--,u--,0>u||b[i]!==C[u]){var k=`
`+b[i].replace(" at new "," at ");return e.displayName&&k.includes("<anonymous>")&&(k=k.replace("<anonymous>",e.displayName)),k}while(1<=i&&0<=u);break}}}finally{mu=!1,Error.prepareStackTrace=n}return(n=e?e.displayName||e.name:"")?Ia(n):""}function zp(e){switch(e.tag){case 26:case 27:case 5:return Ia(e.type);case 16:return Ia("Lazy");case 13:return Ia("Suspense");case 19:return Ia("SuspenseList");case 0:case 15:return yu(e.type,!1);case 11:return yu(e.type.render,!1);case 1:return yu(e.type,!0);case 31:return Ia("Activity");default:return""}}function zf(e){try{var t="";do t+=zp(e),e=e.return;while(e);return t}catch(n){return`
Error generating stack: `+n.message+`
`+n.stack}}function Vt(e){switch(typeof e){case"bigint":case"boolean":case"number":case"string":case"undefined":return e;case"object":return e;default:return""}}function jf(e){var t=e.type;return(e=e.nodeName)&&e.toLowerCase()==="input"&&(t==="checkbox"||t==="radio")}function jp(e){var t=jf(e)?"checked":"value",n=Object.getOwnPropertyDescriptor(e.constructor.prototype,t),i=""+e[t];if(!e.hasOwnProperty(t)&&typeof n<"u"&&typeof n.get=="function"&&typeof n.set=="function"){var u=n.get,o=n.set;return Object.defineProperty(e,t,{configurable:!0,get:function(){return u.call(this)},set:function(h){i=""+h,o.call(this,h)}}),Object.defineProperty(e,t,{enumerable:n.enumerable}),{getValue:function(){return i},setValue:function(h){i=""+h},stopTracking:function(){e._valueTracker=null,delete e[t]}}}}function dr(e){e._valueTracker||(e._valueTracker=jp(e))}function Bf(e){if(!e)return!1;var t=e._valueTracker;if(!t)return!0;var n=t.getValue(),i="";return e&&(i=jf(e)?e.checked?"true":"false":e.value),e=i,e!==n?(t.setValue(e),!0):!1}function hr(e){if(e=e||(typeof document<"u"?document:void 0),typeof e>"u")return null;try{return e.activeElement||e.body}catch{return e.body}}var Bp=/[\n"\\]/g;function Yt(e){return e.replace(Bp,function(t){return"\\"+t.charCodeAt(0).toString(16)+" "})}function pu(e,t,n,i,u,o,h,p){e.name="",h!=null&&typeof h!="function"&&typeof h!="symbol"&&typeof h!="boolean"?e.type=h:e.removeAttribute("type"),t!=null?h==="number"?(t===0&&e.value===""||e.value!=t)&&(e.value=""+Vt(t)):e.value!==""+Vt(t)&&(e.value=""+Vt(t)):h!=="submit"&&h!=="reset"||e.removeAttribute("value"),t!=null?gu(e,h,Vt(t)):n!=null?gu(e,h,Vt(n)):i!=null&&e.removeAttribute("value"),u==null&&o!=null&&(e.defaultChecked=!!o),u!=null&&(e.checked=u&&typeof u!="function"&&typeof u!="symbol"),p!=null&&typeof p!="function"&&typeof p!="symbol"&&typeof p!="boolean"?e.name=""+Vt(p):e.removeAttribute("name")}function Lf(e,t,n,i,u,o,h,p){if(o!=null&&typeof o!="function"&&typeof o!="symbol"&&typeof o!="boolean"&&(e.type=o),t!=null||n!=null){if(!(o!=="submit"&&o!=="reset"||t!=null))return;n=n!=null?""+Vt(n):"",t=t!=null?""+Vt(t):n,p||t===e.value||(e.value=t),e.defaultValue=t}i=i??u,i=typeof i!="function"&&typeof i!="symbol"&&!!i,e.checked=p?e.checked:!!i,e.defaultChecked=!!i,h!=null&&typeof h!="function"&&typeof h!="symbol"&&typeof h!="boolean"&&(e.name=h)}function gu(e,t,n){t==="number"&&hr(e.ownerDocument)===e||e.defaultValue===""+n||(e.defaultValue=""+n)}function el(e,t,n,i){if(e=e.options,t){t={};for(var u=0;u<n.length;u++)t["$"+n[u]]=!0;for(n=0;n<e.length;n++)u=t.hasOwnProperty("$"+e[n].value),e[n].selected!==u&&(e[n].selected=u),u&&i&&(e[n].defaultSelected=!0)}else{for(n=""+Vt(n),t=null,u=0;u<e.length;u++){if(e[u].value===n){e[u].selected=!0,i&&(e[u].defaultSelected=!0);return}t!==null||e[u].disabled||(t=e[u])}t!==null&&(t.selected=!0)}}function kf(e,t,n){if(t!=null&&(t=""+Vt(t),t!==e.value&&(e.value=t),n==null)){e.defaultValue!==t&&(e.defaultValue=t);return}e.defaultValue=n!=null?""+Vt(n):""}function qf(e,t,n,i){if(t==null){if(i!=null){if(n!=null)throw Error(s(92));if(Oe(i)){if(1<i.length)throw Error(s(93));i=i[0]}n=i}n==null&&(n=""),t=n}n=Vt(t),e.defaultValue=n,i=e.textContent,i===n&&i!==""&&i!==null&&(e.value=i)}function tl(e,t){if(t){var n=e.firstChild;if(n&&n===e.lastChild&&n.nodeType===3){n.nodeValue=t;return}}e.textContent=t}var Lp=new Set("animationIterationCount aspectRatio borderImageOutset borderImageSlice borderImageWidth boxFlex boxFlexGroup boxOrdinalGroup columnCount columns flex flexGrow flexPositive flexShrink flexNegative flexOrder gridArea gridRow gridRowEnd gridRowSpan gridRowStart gridColumn gridColumnEnd gridColumnSpan gridColumnStart fontWeight lineClamp lineHeight opacity order orphans scale tabSize widows zIndex zoom fillOpacity floodOpacity stopOpacity strokeDasharray strokeDashoffset strokeMiterlimit strokeOpacity strokeWidth MozAnimationIterationCount MozBoxFlex MozBoxFlexGroup MozLineClamp msAnimationIterationCount msFlex msZoom msFlexGrow msFlexNegative msFlexOrder msFlexPositive msFlexShrink msGridColumn msGridColumnSpan msGridRow msGridRowSpan WebkitAnimationIterationCount WebkitBoxFlex WebKitBoxFlexGroup WebkitBoxOrdinalGroup WebkitColumnCount WebkitColumns WebkitFlex WebkitFlexGrow WebkitFlexPositive WebkitFlexShrink WebkitLineClamp".split(" "));function Hf(e,t,n){var i=t.indexOf("--")===0;n==null||typeof n=="boolean"||n===""?i?e.setProperty(t,""):t==="float"?e.cssFloat="":e[t]="":i?e.setProperty(t,n):typeof n!="number"||n===0||Lp.has(t)?t==="float"?e.cssFloat=n:e[t]=(""+n).trim():e[t]=n+"px"}function Vf(e,t,n){if(t!=null&&typeof t!="object")throw Error(s(62));if(e=e.style,n!=null){for(var i in n)!n.hasOwnProperty(i)||t!=null&&t.hasOwnProperty(i)||(i.indexOf("--")===0?e.setProperty(i,""):i==="float"?e.cssFloat="":e[i]="");for(var u in t)i=t[u],t.hasOwnProperty(u)&&n[u]!==i&&Hf(e,u,i)}else for(var o in t)t.hasOwnProperty(o)&&Hf(e,o,t[o])}function vu(e){if(e.indexOf("-")===-1)return!1;switch(e){case"annotation-xml":case"color-profile":case"font-face":case"font-face-src":case"font-face-uri":case"font-face-format":case"font-face-name":case"missing-glyph":return!1;default:return!0}}var kp=new Map([["acceptCharset","accept-charset"],["htmlFor","for"],["httpEquiv","http-equiv"],["crossOrigin","crossorigin"],["accentHeight","accent-height"],["alignmentBaseline","alignment-baseline"],["arabicForm","arabic-form"],["baselineShift","baseline-shift"],["capHeight","cap-height"],["clipPath","clip-path"],["clipRule","clip-rule"],["colorInterpolation","color-interpolation"],["colorInterpolationFilters","color-interpolation-filters"],["colorProfile","color-profile"],["colorRendering","color-rendering"],["dominantBaseline","dominant-baseline"],["enableBackground","enable-background"],["fillOpacity","fill-opacity"],["fillRule","fill-rule"],["floodColor","flood-color"],["floodOpacity","flood-opacity"],["fontFamily","font-family"],["fontSize","font-size"],["fontSizeAdjust","font-size-adjust"],["fontStretch","font-stretch"],["fontStyle","font-style"],["fontVariant","font-variant"],["fontWeight","font-weight"],["glyphName","glyph-name"],["glyphOrientationHorizontal","glyph-orientation-horizontal"],["glyphOrientationVertical","glyph-orientation-vertical"],["horizAdvX","horiz-adv-x"],["horizOriginX","horiz-origin-x"],["imageRendering","image-rendering"],["letterSpacing","letter-spacing"],["lightingColor","lighting-color"],["markerEnd","marker-end"],["markerMid","marker-mid"],["markerStart","marker-start"],["overlinePosition","overline-position"],["overlineThickness","overline-thickness"],["paintOrder","paint-order"],["panose-1","panose-1"],["pointerEvents","pointer-events"],["renderingIntent","rendering-intent"],["shapeRendering","shape-rendering"],["stopColor","stop-color"],["stopOpacity","stop-opacity"],["strikethroughPosition","strikethrough-position"],["strikethroughThickness","strikethrough-thickness"],["strokeDasharray","stroke-dasharray"],["strokeDashoffset","stroke-dashoffset"],["strokeLinecap","stroke-linecap"],["strokeLinejoin","stroke-linejoin"],["strokeMiterlimit","stroke-miterlimit"],["strokeOpacity","stroke-opacity"],["strokeWidth","stroke-width"],["textAnchor","text-anchor"],["textDecoration","text-decoration"],["textRendering","text-rendering"],["transformOrigin","transform-origin"],["underlinePosition","underline-position"],["underlineThickness","underline-thickness"],["unicodeBidi","unicode-bidi"],["unicodeRange","unicode-range"],["unitsPerEm","units-per-em"],["vAlphabetic","v-alphabetic"],["vHanging","v-hanging"],["vIdeographic","v-ideographic"],["vMathematical","v-mathematical"],["vectorEffect","vector-effect"],["vertAdvY","vert-adv-y"],["vertOriginX","vert-origin-x"],["vertOriginY","vert-origin-y"],["wordSpacing","word-spacing"],["writingMode","writing-mode"],["xmlnsXlink","xmlns:xlink"],["xHeight","x-height"]]),qp=/^[\u0000-\u001F ]*j[\r\n\t]*a[\r\n\t]*v[\r\n\t]*a[\r\n\t]*s[\r\n\t]*c[\r\n\t]*r[\r\n\t]*i[\r\n\t]*p[\r\n\t]*t[\r\n\t]*:/i;function mr(e){return qp.test(""+e)?"javascript:throw new Error('React has blocked a javascript: URL as a security precaution.')":e}var bu=null;function Su(e){return e=e.target||e.srcElement||window,e.correspondingUseElement&&(e=e.correspondingUseElement),e.nodeType===3?e.parentNode:e}var nl=null,al=null;function Yf(e){var t=$a(e);if(t&&(e=t.stateNode)){var n=e[wt]||null;e:switch(e=t.stateNode,t.type){case"input":if(pu(e,n.value,n.defaultValue,n.defaultValue,n.checked,n.defaultChecked,n.type,n.name),t=n.name,n.type==="radio"&&t!=null){for(n=e;n.parentNode;)n=n.parentNode;for(n=n.querySelectorAll('input[name="'+Yt(""+t)+'"][type="radio"]'),t=0;t<n.length;t++){var i=n[t];if(i!==e&&i.form===e.form){var u=i[wt]||null;if(!u)throw Error(s(90));pu(i,u.value,u.defaultValue,u.defaultValue,u.checked,u.defaultChecked,u.type,u.name)}}for(t=0;t<n.length;t++)i=n[t],i.form===e.form&&Bf(i)}break e;case"textarea":kf(e,n.value,n.defaultValue);break e;case"select":t=n.value,t!=null&&el(e,!!n.multiple,t,!1)}}}var xu=!1;function Xf(e,t,n){if(xu)return e(t,n);xu=!0;try{var i=e(t);return i}finally{if(xu=!1,(nl!==null||al!==null)&&(Ir(),nl&&(t=nl,e=al,al=nl=null,Yf(t),e)))for(t=0;t<e.length;t++)Yf(e[t])}}function Zl(e,t){var n=e.stateNode;if(n===null)return null;var i=n[wt]||null;if(i===null)return null;n=i[t];e:switch(t){case"onClick":case"onClickCapture":case"onDoubleClick":case"onDoubleClickCapture":case"onMouseDown":case"onMouseDownCapture":case"onMouseMove":case"onMouseMoveCapture":case"onMouseUp":case"onMouseUpCapture":case"onMouseEnter":(i=!i.disabled)||(e=e.type,i=!(e==="button"||e==="input"||e==="select"||e==="textarea")),e=!i;break e;default:e=!1}if(e)return null;if(n&&typeof n!="function")throw Error(s(231,t,typeof n));return n}var Sn=!(typeof window>"u"||typeof window.document>"u"||typeof window.document.createElement>"u"),Eu=!1;if(Sn)try{var Kl={};Object.defineProperty(Kl,"passive",{get:function(){Eu=!0}}),window.addEventListener("test",Kl,Kl),window.removeEventListener("test",Kl,Kl)}catch{Eu=!1}var Xn=null,_u=null,yr=null;function Gf(){if(yr)return yr;var e,t=_u,n=t.length,i,u="value"in Xn?Xn.value:Xn.textContent,o=u.length;for(e=0;e<n&&t[e]===u[e];e++);var h=n-e;for(i=1;i<=h&&t[n-i]===u[o-i];i++);return yr=u.slice(e,1<i?1-i:void 0)}function pr(e){var t=e.keyCode;return"charCode"in e?(e=e.charCode,e===0&&t===13&&(e=13)):e=t,e===10&&(e=13),32<=e||e===13?e:0}function gr(){return!0}function Qf(){return!1}function At(e){function t(n,i,u,o,h){this._reactName=n,this._targetInst=u,this.type=i,this.nativeEvent=o,this.target=h,this.currentTarget=null;for(var p in e)e.hasOwnProperty(p)&&(n=e[p],this[p]=n?n(o):o[p]);return this.isDefaultPrevented=(o.defaultPrevented!=null?o.defaultPrevented:o.returnValue===!1)?gr:Qf,this.isPropagationStopped=Qf,this}return v(t.prototype,{preventDefault:function(){this.defaultPrevented=!0;var n=this.nativeEvent;n&&(n.preventDefault?n.preventDefault():typeof n.returnValue!="unknown"&&(n.returnValue=!1),this.isDefaultPrevented=gr)},stopPropagation:function(){var n=this.nativeEvent;n&&(n.stopPropagation?n.stopPropagation():typeof n.cancelBubble!="unknown"&&(n.cancelBubble=!0),this.isPropagationStopped=gr)},persist:function(){},isPersistent:gr}),t}var xa={eventPhase:0,bubbles:0,cancelable:0,timeStamp:function(e){return e.timeStamp||Date.now()},defaultPrevented:0,isTrusted:0},vr=At(xa),Fl=v({},xa,{view:0,detail:0}),Hp=At(Fl),wu,Au,Jl,br=v({},Fl,{screenX:0,screenY:0,clientX:0,clientY:0,pageX:0,pageY:0,ctrlKey:0,shiftKey:0,altKey:0,metaKey:0,getModifierState:Ru,button:0,buttons:0,relatedTarget:function(e){return e.relatedTarget===void 0?e.fromElement===e.srcElement?e.toElement:e.fromElement:e.relatedTarget},movementX:function(e){return"movementX"in e?e.movementX:(e!==Jl&&(Jl&&e.type==="mousemove"?(wu=e.screenX-Jl.screenX,Au=e.screenY-Jl.screenY):Au=wu=0,Jl=e),wu)},movementY:function(e){return"movementY"in e?e.movementY:Au}}),Zf=At(br),Vp=v({},br,{dataTransfer:0}),Yp=At(Vp),Xp=v({},Fl,{relatedTarget:0}),Tu=At(Xp),Gp=v({},xa,{animationName:0,elapsedTime:0,pseudoElement:0}),Qp=At(Gp),Zp=v({},xa,{clipboardData:function(e){return"clipboardData"in e?e.clipboardData:window.clipboardData}}),Kp=At(Zp),Fp=v({},xa,{data:0}),Kf=At(Fp),Jp={Esc:"Escape",Spacebar:" ",Left:"ArrowLeft",Up:"ArrowUp",Right:"ArrowRight",Down:"ArrowDown",Del:"Delete",Win:"OS",Menu:"ContextMenu",Apps:"ContextMenu",Scroll:"ScrollLock",MozPrintableKey:"Unidentified"},$p={8:"Backspace",9:"Tab",12:"Clear",13:"Enter",16:"Shift",17:"Control",18:"Alt",19:"Pause",20:"CapsLock",27:"Escape",32:" ",33:"PageUp",34:"PageDown",35:"End",36:"Home",37:"ArrowLeft",38:"ArrowUp",39:"ArrowRight",40:"ArrowDown",45:"Insert",46:"Delete",112:"F1",113:"F2",114:"F3",115:"F4",116:"F5",117:"F6",118:"F7",119:"F8",120:"F9",121:"F10",122:"F11",123:"F12",144:"NumLock",145:"ScrollLock",224:"Meta"},Wp={Alt:"altKey",Control:"ctrlKey",Meta:"metaKey",Shift:"shiftKey"};function Pp(e){var t=this.nativeEvent;return t.getModifierState?t.getModifierState(e):(e=Wp[e])?!!t[e]:!1}function Ru(){return Pp}var Ip=v({},Fl,{key:function(e){if(e.key){var t=Jp[e.key]||e.key;if(t!=="Unidentified")return t}return e.type==="keypress"?(e=pr(e),e===13?"Enter":String.fromCharCode(e)):e.type==="keydown"||e.type==="keyup"?$p[e.keyCode]||"Unidentified":""},code:0,location:0,ctrlKey:0,shiftKey:0,altKey:0,metaKey:0,repeat:0,locale:0,getModifierState:Ru,charCode:function(e){return e.type==="keypress"?pr(e):0},keyCode:function(e){return e.type==="keydown"||e.type==="keyup"?e.keyCode:0},which:function(e){return e.type==="keypress"?pr(e):e.type==="keydown"||e.type==="keyup"?e.keyCode:0}}),eg=At(Ip),tg=v({},br,{pointerId:0,width:0,height:0,pressure:0,tangentialPressure:0,tiltX:0,tiltY:0,twist:0,pointerType:0,isPrimary:0}),Ff=At(tg),ng=v({},Fl,{touches:0,targetTouches:0,changedTouches:0,altKey:0,metaKey:0,ctrlKey:0,shiftKey:0,getModifierState:Ru}),ag=At(ng),lg=v({},xa,{propertyName:0,elapsedTime:0,pseudoElement:0}),ig=At(lg),rg=v({},br,{deltaX:function(e){return"deltaX"in e?e.deltaX:"wheelDeltaX"in e?-e.wheelDeltaX:0},deltaY:function(e){return"deltaY"in e?e.deltaY:"wheelDeltaY"in e?-e.wheelDeltaY:"wheelDelta"in e?-e.wheelDelta:0},deltaZ:0,deltaMode:0}),sg=At(rg),ug=v({},xa,{newState:0,oldState:0}),og=At(ug),cg=[9,13,27,32],Ou=Sn&&"CompositionEvent"in window,$l=null;Sn&&"documentMode"in document&&($l=document.documentMode);var fg=Sn&&"TextEvent"in window&&!$l,Jf=Sn&&(!Ou||$l&&8<$l&&11>=$l),$f=" ",Wf=!1;function Pf(e,t){switch(e){case"keyup":return cg.indexOf(t.keyCode)!==-1;case"keydown":return t.keyCode!==229;case"keypress":case"mousedown":case"focusout":return!0;default:return!1}}function If(e){return e=e.detail,typeof e=="object"&&"data"in e?e.data:null}var ll=!1;function dg(e,t){switch(e){case"compositionend":return If(t);case"keypress":return t.which!==32?null:(Wf=!0,$f);case"textInput":return e=t.data,e===$f&&Wf?null:e;default:return null}}function hg(e,t){if(ll)return e==="compositionend"||!Ou&&Pf(e,t)?(e=Gf(),yr=_u=Xn=null,ll=!1,e):null;switch(e){case"paste":return null;case"keypress":if(!(t.ctrlKey||t.altKey||t.metaKey)||t.ctrlKey&&t.altKey){if(t.char&&1<t.char.length)return t.char;if(t.which)return String.fromCharCode(t.which)}return null;case"compositionend":return Jf&&t.locale!=="ko"?null:t.data;default:return null}}var mg={color:!0,date:!0,datetime:!0,"datetime-local":!0,email:!0,month:!0,number:!0,password:!0,range:!0,search:!0,tel:!0,text:!0,time:!0,url:!0,week:!0};function ed(e){var t=e&&e.nodeName&&e.nodeName.toLowerCase();return t==="input"?!!mg[e.type]:t==="textarea"}function td(e,t,n,i){nl?al?al.push(i):al=[i]:nl=i,t=is(t,"onChange"),0<t.length&&(n=new vr("onChange","change",null,n,i),e.push({event:n,listeners:t}))}var Wl=null,Pl=null;function yg(e){jm(e,0)}function Sr(e){var t=Ql(e);if(Bf(t))return e}function nd(e,t){if(e==="change")return t}var ad=!1;if(Sn){var Nu;if(Sn){var Cu="oninput"in document;if(!Cu){var ld=document.createElement("div");ld.setAttribute("oninput","return;"),Cu=typeof ld.oninput=="function"}Nu=Cu}else Nu=!1;ad=Nu&&(!document.documentMode||9<document.documentMode)}function id(){Wl&&(Wl.detachEvent("onpropertychange",rd),Pl=Wl=null)}function rd(e){if(e.propertyName==="value"&&Sr(Pl)){var t=[];td(t,Pl,e,Su(e)),Xf(yg,t)}}function pg(e,t,n){e==="focusin"?(id(),Wl=t,Pl=n,Wl.attachEvent("onpropertychange",rd)):e==="focusout"&&id()}function gg(e){if(e==="selectionchange"||e==="keyup"||e==="keydown")return Sr(Pl)}function vg(e,t){if(e==="click")return Sr(t)}function bg(e,t){if(e==="input"||e==="change")return Sr(t)}function Sg(e,t){return e===t&&(e!==0||1/e===1/t)||e!==e&&t!==t}var Mt=typeof Object.is=="function"?Object.is:Sg;function Il(e,t){if(Mt(e,t))return!0;if(typeof e!="object"||e===null||typeof t!="object"||t===null)return!1;var n=Object.keys(e),i=Object.keys(t);if(n.length!==i.length)return!1;for(i=0;i<n.length;i++){var u=n[i];if(!Qa.call(t,u)||!Mt(e[u],t[u]))return!1}return!0}function sd(e){for(;e&&e.firstChild;)e=e.firstChild;return e}function ud(e,t){var n=sd(e);e=0;for(var i;n;){if(n.nodeType===3){if(i=e+n.textContent.length,e<=t&&i>=t)return{node:n,offset:t-e};e=i}e:{for(;n;){if(n.nextSibling){n=n.nextSibling;break e}n=n.parentNode}n=void 0}n=sd(n)}}function od(e,t){return e&&t?e===t?!0:e&&e.nodeType===3?!1:t&&t.nodeType===3?od(e,t.parentNode):"contains"in e?e.contains(t):e.compareDocumentPosition?!!(e.compareDocumentPosition(t)&16):!1:!1}function cd(e){e=e!=null&&e.ownerDocument!=null&&e.ownerDocument.defaultView!=null?e.ownerDocument.defaultView:window;for(var t=hr(e.document);t instanceof e.HTMLIFrameElement;){try{var n=typeof t.contentWindow.location.href=="string"}catch{n=!1}if(n)e=t.contentWindow;else break;t=hr(e.document)}return t}function Du(e){var t=e&&e.nodeName&&e.nodeName.toLowerCase();return t&&(t==="input"&&(e.type==="text"||e.type==="search"||e.type==="tel"||e.type==="url"||e.type==="password")||t==="textarea"||e.contentEditable==="true")}var xg=Sn&&"documentMode"in document&&11>=document.documentMode,il=null,Mu=null,ei=null,Uu=!1;function fd(e,t,n){var i=n.window===n?n.document:n.nodeType===9?n:n.ownerDocument;Uu||il==null||il!==hr(i)||(i=il,"selectionStart"in i&&Du(i)?i={start:i.selectionStart,end:i.selectionEnd}:(i=(i.ownerDocument&&i.ownerDocument.defaultView||window).getSelection(),i={anchorNode:i.anchorNode,anchorOffset:i.anchorOffset,focusNode:i.focusNode,focusOffset:i.focusOffset}),ei&&Il(ei,i)||(ei=i,i=is(Mu,"onSelect"),0<i.length&&(t=new vr("onSelect","select",null,t,n),e.push({event:t,listeners:i}),t.target=il)))}function Ea(e,t){var n={};return n[e.toLowerCase()]=t.toLowerCase(),n["Webkit"+e]="webkit"+t,n["Moz"+e]="moz"+t,n}var rl={animationend:Ea("Animation","AnimationEnd"),animationiteration:Ea("Animation","AnimationIteration"),animationstart:Ea("Animation","AnimationStart"),transitionrun:Ea("Transition","TransitionRun"),transitionstart:Ea("Transition","TransitionStart"),transitioncancel:Ea("Transition","TransitionCancel"),transitionend:Ea("Transition","TransitionEnd")},zu={},dd={};Sn&&(dd=document.createElement("div").style,"AnimationEvent"in window||(delete rl.animationend.animation,delete rl.animationiteration.animation,delete rl.animationstart.animation),"TransitionEvent"in window||delete rl.transitionend.transition);function _a(e){if(zu[e])return zu[e];if(!rl[e])return e;var t=rl[e],n;for(n in t)if(t.hasOwnProperty(n)&&n in dd)return zu[e]=t[n];return e}var hd=_a("animationend"),md=_a("animationiteration"),yd=_a("animationstart"),Eg=_a("transitionrun"),_g=_a("transitionstart"),wg=_a("transitioncancel"),pd=_a("transitionend"),gd=new Map,ju="abort auxClick beforeToggle cancel canPlay canPlayThrough click close contextMenu copy cut drag dragEnd dragEnter dragExit dragLeave dragOver dragStart drop durationChange emptied encrypted ended error gotPointerCapture input invalid keyDown keyPress keyUp load loadedData loadedMetadata loadStart lostPointerCapture mouseDown mouseMove mouseOut mouseOver mouseUp paste pause play playing pointerCancel pointerDown pointerMove pointerOut pointerOver pointerUp progress rateChange reset resize seeked seeking stalled submit suspend timeUpdate touchCancel touchEnd touchStart volumeChange scroll toggle touchMove waiting wheel".split(" ");ju.push("scrollEnd");function Wt(e,t){gd.set(e,t),Sa(t,[e])}var vd=new WeakMap;function Xt(e,t){if(typeof e=="object"&&e!==null){var n=vd.get(e);return n!==void 0?n:(t={value:e,source:t,stack:zf(t)},vd.set(e,t),t)}return{value:e,source:t,stack:zf(t)}}var Gt=[],sl=0,Bu=0;function xr(){for(var e=sl,t=Bu=sl=0;t<e;){var n=Gt[t];Gt[t++]=null;var i=Gt[t];Gt[t++]=null;var u=Gt[t];Gt[t++]=null;var o=Gt[t];if(Gt[t++]=null,i!==null&&u!==null){var h=i.pending;h===null?u.next=u:(u.next=h.next,h.next=u),i.pending=u}o!==0&&bd(n,u,o)}}function Er(e,t,n,i){Gt[sl++]=e,Gt[sl++]=t,Gt[sl++]=n,Gt[sl++]=i,Bu|=i,e.lanes|=i,e=e.alternate,e!==null&&(e.lanes|=i)}function Lu(e,t,n,i){return Er(e,t,n,i),_r(e)}function ul(e,t){return Er(e,null,null,t),_r(e)}function bd(e,t,n){e.lanes|=n;var i=e.alternate;i!==null&&(i.lanes|=n);for(var u=!1,o=e.return;o!==null;)o.childLanes|=n,i=o.alternate,i!==null&&(i.childLanes|=n),o.tag===22&&(e=o.stateNode,e===null||e._visibility&1||(u=!0)),e=o,o=o.return;return e.tag===3?(o=e.stateNode,u&&t!==null&&(u=31-He(n),e=o.hiddenUpdates,i=e[u],i===null?e[u]=[t]:i.push(t),t.lane=n|536870912),o):null}function _r(e){if(50<Ai)throw Ai=0,Go=null,Error(s(185));for(var t=e.return;t!==null;)e=t,t=e.return;return e.tag===3?e.stateNode:null}var ol={};function Ag(e,t,n,i){this.tag=e,this.key=n,this.sibling=this.child=this.return=this.stateNode=this.type=this.elementType=null,this.index=0,this.refCleanup=this.ref=null,this.pendingProps=t,this.dependencies=this.memoizedState=this.updateQueue=this.memoizedProps=null,this.mode=i,this.subtreeFlags=this.flags=0,this.deletions=null,this.childLanes=this.lanes=0,this.alternate=null}function Ut(e,t,n,i){return new Ag(e,t,n,i)}function ku(e){return e=e.prototype,!(!e||!e.isReactComponent)}function xn(e,t){var n=e.alternate;return n===null?(n=Ut(e.tag,t,e.key,e.mode),n.elementType=e.elementType,n.type=e.type,n.stateNode=e.stateNode,n.alternate=e,e.alternate=n):(n.pendingProps=t,n.type=e.type,n.flags=0,n.subtreeFlags=0,n.deletions=null),n.flags=e.flags&65011712,n.childLanes=e.childLanes,n.lanes=e.lanes,n.child=e.child,n.memoizedProps=e.memoizedProps,n.memoizedState=e.memoizedState,n.updateQueue=e.updateQueue,t=e.dependencies,n.dependencies=t===null?null:{lanes:t.lanes,firstContext:t.firstContext},n.sibling=e.sibling,n.index=e.index,n.ref=e.ref,n.refCleanup=e.refCleanup,n}function Sd(e,t){e.flags&=65011714;var n=e.alternate;return n===null?(e.childLanes=0,e.lanes=t,e.child=null,e.subtreeFlags=0,e.memoizedProps=null,e.memoizedState=null,e.updateQueue=null,e.dependencies=null,e.stateNode=null):(e.childLanes=n.childLanes,e.lanes=n.lanes,e.child=n.child,e.subtreeFlags=0,e.deletions=null,e.memoizedProps=n.memoizedProps,e.memoizedState=n.memoizedState,e.updateQueue=n.updateQueue,e.type=n.type,t=n.dependencies,e.dependencies=t===null?null:{lanes:t.lanes,firstContext:t.firstContext}),e}function wr(e,t,n,i,u,o){var h=0;if(i=e,typeof e=="function")ku(e)&&(h=1);else if(typeof e=="string")h=Rv(e,n,re.current)?26:e==="html"||e==="head"||e==="body"?27:5;else e:switch(e){case pe:return e=Ut(31,n,t,u),e.elementType=pe,e.lanes=o,e;case R:return wa(n.children,u,o,t);case B:h=8,u|=24;break;case L:return e=Ut(12,n,t,u|2),e.elementType=L,e.lanes=o,e;case K:return e=Ut(13,n,t,u),e.elementType=K,e.lanes=o,e;case Ae:return e=Ut(19,n,t,u),e.elementType=Ae,e.lanes=o,e;default:if(typeof e=="object"&&e!==null)switch(e.$$typeof){case X:case $:h=10;break e;case V:h=9;break e;case ie:h=11;break e;case ye:h=14;break e;case De:h=16,i=null;break e}h=29,n=Error(s(130,e===null?"null":typeof e,"")),i=null}return t=Ut(h,n,t,u),t.elementType=e,t.type=i,t.lanes=o,t}function wa(e,t,n,i){return e=Ut(7,e,i,t),e.lanes=n,e}function qu(e,t,n){return e=Ut(6,e,null,t),e.lanes=n,e}function Hu(e,t,n){return t=Ut(4,e.children!==null?e.children:[],e.key,t),t.lanes=n,t.stateNode={containerInfo:e.containerInfo,pendingChildren:null,implementation:e.implementation},t}var cl=[],fl=0,Ar=null,Tr=0,Qt=[],Zt=0,Aa=null,En=1,_n="";function Ta(e,t){cl[fl++]=Tr,cl[fl++]=Ar,Ar=e,Tr=t}function xd(e,t,n){Qt[Zt++]=En,Qt[Zt++]=_n,Qt[Zt++]=Aa,Aa=e;var i=En;e=_n;var u=32-He(i)-1;i&=~(1<<u),n+=1;var o=32-He(t)+u;if(30<o){var h=u-u%5;o=(i&(1<<h)-1).toString(32),i>>=h,u-=h,En=1<<32-He(t)+u|n<<u|i,_n=o+e}else En=1<<o|n<<u|i,_n=e}function Vu(e){e.return!==null&&(Ta(e,1),xd(e,1,0))}function Yu(e){for(;e===Ar;)Ar=cl[--fl],cl[fl]=null,Tr=cl[--fl],cl[fl]=null;for(;e===Aa;)Aa=Qt[--Zt],Qt[Zt]=null,_n=Qt[--Zt],Qt[Zt]=null,En=Qt[--Zt],Qt[Zt]=null}var xt=null,Fe=null,Ce=!1,Ra=null,un=!1,Xu=Error(s(519));function Oa(e){var t=Error(s(418,""));throw ai(Xt(t,e)),Xu}function Ed(e){var t=e.stateNode,n=e.type,i=e.memoizedProps;switch(t[gt]=e,t[wt]=i,n){case"dialog":Se("cancel",t),Se("close",t);break;case"iframe":case"object":case"embed":Se("load",t);break;case"video":case"audio":for(n=0;n<Ri.length;n++)Se(Ri[n],t);break;case"source":Se("error",t);break;case"img":case"image":case"link":Se("error",t),Se("load",t);break;case"details":Se("toggle",t);break;case"input":Se("invalid",t),Lf(t,i.value,i.defaultValue,i.checked,i.defaultChecked,i.type,i.name,!0),dr(t);break;case"select":Se("invalid",t);break;case"textarea":Se("invalid",t),qf(t,i.value,i.defaultValue,i.children),dr(t)}n=i.children,typeof n!="string"&&typeof n!="number"&&typeof n!="bigint"||t.textContent===""+n||i.suppressHydrationWarning===!0||qm(t.textContent,n)?(i.popover!=null&&(Se("beforetoggle",t),Se("toggle",t)),i.onScroll!=null&&Se("scroll",t),i.onScrollEnd!=null&&Se("scrollend",t),i.onClick!=null&&(t.onclick=rs),t=!0):t=!1,t||Oa(e)}function _d(e){for(xt=e.return;xt;)switch(xt.tag){case 5:case 13:un=!1;return;case 27:case 3:un=!0;return;default:xt=xt.return}}function ti(e){if(e!==xt)return!1;if(!Ce)return _d(e),Ce=!0,!1;var t=e.tag,n;if((n=t!==3&&t!==27)&&((n=t===5)&&(n=e.type,n=!(n!=="form"&&n!=="button")||rc(e.type,e.memoizedProps)),n=!n),n&&Fe&&Oa(e),_d(e),t===13){if(e=e.memoizedState,e=e!==null?e.dehydrated:null,!e)throw Error(s(317));e:{for(e=e.nextSibling,t=0;e;){if(e.nodeType===8)if(n=e.data,n==="/$"){if(t===0){Fe=It(e.nextSibling);break e}t--}else n!=="$"&&n!=="$!"&&n!=="$?"||t++;e=e.nextSibling}Fe=null}}else t===27?(t=Fe,ia(e.type)?(e=cc,cc=null,Fe=e):Fe=t):Fe=xt?It(e.stateNode.nextSibling):null;return!0}function ni(){Fe=xt=null,Ce=!1}function wd(){var e=Ra;return e!==null&&(Ot===null?Ot=e:Ot.push.apply(Ot,e),Ra=null),e}function ai(e){Ra===null?Ra=[e]:Ra.push(e)}var Gu=G(null),Na=null,wn=null;function Gn(e,t,n){W(Gu,t._currentValue),t._currentValue=n}function An(e){e._currentValue=Gu.current,I(Gu)}function Qu(e,t,n){for(;e!==null;){var i=e.alternate;if((e.childLanes&t)!==t?(e.childLanes|=t,i!==null&&(i.childLanes|=t)):i!==null&&(i.childLanes&t)!==t&&(i.childLanes|=t),e===n)break;e=e.return}}function Zu(e,t,n,i){var u=e.child;for(u!==null&&(u.return=e);u!==null;){var o=u.dependencies;if(o!==null){var h=u.child;o=o.firstContext;e:for(;o!==null;){var p=o;o=u;for(var b=0;b<t.length;b++)if(p.context===t[b]){o.lanes|=n,p=o.alternate,p!==null&&(p.lanes|=n),Qu(o.return,n,e),i||(h=null);break e}o=p.next}}else if(u.tag===18){if(h=u.return,h===null)throw Error(s(341));h.lanes|=n,o=h.alternate,o!==null&&(o.lanes|=n),Qu(h,n,e),h=null}else h=u.child;if(h!==null)h.return=u;else for(h=u;h!==null;){if(h===e){h=null;break}if(u=h.sibling,u!==null){u.return=h.return,h=u;break}h=h.return}u=h}}function li(e,t,n,i){e=null;for(var u=t,o=!1;u!==null;){if(!o){if((u.flags&524288)!==0)o=!0;else if((u.flags&262144)!==0)break}if(u.tag===10){var h=u.alternate;if(h===null)throw Error(s(387));if(h=h.memoizedProps,h!==null){var p=u.type;Mt(u.pendingProps.value,h.value)||(e!==null?e.push(p):e=[p])}}else if(u===ht.current){if(h=u.alternate,h===null)throw Error(s(387));h.memoizedState.memoizedState!==u.memoizedState.memoizedState&&(e!==null?e.push(Ui):e=[Ui])}u=u.return}e!==null&&Zu(t,e,n,i),t.flags|=262144}function Rr(e){for(e=e.firstContext;e!==null;){if(!Mt(e.context._currentValue,e.memoizedValue))return!0;e=e.next}return!1}function Ca(e){Na=e,wn=null,e=e.dependencies,e!==null&&(e.firstContext=null)}function vt(e){return Ad(Na,e)}function Or(e,t){return Na===null&&Ca(e),Ad(e,t)}function Ad(e,t){var n=t._currentValue;if(t={context:t,memoizedValue:n,next:null},wn===null){if(e===null)throw Error(s(308));wn=t,e.dependencies={lanes:0,firstContext:t},e.flags|=524288}else wn=wn.next=t;return n}var Tg=typeof AbortController<"u"?AbortController:function(){var e=[],t=this.signal={aborted:!1,addEventListener:function(n,i){e.push(i)}};this.abort=function(){t.aborted=!0,e.forEach(function(n){return n()})}},Rg=a.unstable_scheduleCallback,Og=a.unstable_NormalPriority,rt={$$typeof:$,Consumer:null,Provider:null,_currentValue:null,_currentValue2:null,_threadCount:0};function Ku(){return{controller:new Tg,data:new Map,refCount:0}}function ii(e){e.refCount--,e.refCount===0&&Rg(Og,function(){e.controller.abort()})}var ri=null,Fu=0,dl=0,hl=null;function Ng(e,t){if(ri===null){var n=ri=[];Fu=0,dl=Wo(),hl={status:"pending",value:void 0,then:function(i){n.push(i)}}}return Fu++,t.then(Td,Td),t}function Td(){if(--Fu===0&&ri!==null){hl!==null&&(hl.status="fulfilled");var e=ri;ri=null,dl=0,hl=null;for(var t=0;t<e.length;t++)(0,e[t])()}}function Cg(e,t){var n=[],i={status:"pending",value:null,reason:null,then:function(u){n.push(u)}};return e.then(function(){i.status="fulfilled",i.value=t;for(var u=0;u<n.length;u++)(0,n[u])(t)},function(u){for(i.status="rejected",i.reason=u,u=0;u<n.length;u++)(0,n[u])(void 0)}),i}var Rd=j.S;j.S=function(e,t){typeof t=="object"&&t!==null&&typeof t.then=="function"&&Ng(e,t),Rd!==null&&Rd(e,t)};var Da=G(null);function Ju(){var e=Da.current;return e!==null?e:Ve.pooledCache}function Nr(e,t){t===null?W(Da,Da.current):W(Da,t.pool)}function Od(){var e=Ju();return e===null?null:{parent:rt._currentValue,pool:e}}var si=Error(s(460)),Nd=Error(s(474)),Cr=Error(s(542)),$u={then:function(){}};function Cd(e){return e=e.status,e==="fulfilled"||e==="rejected"}function Dr(){}function Dd(e,t,n){switch(n=e[n],n===void 0?e.push(t):n!==t&&(t.then(Dr,Dr),t=n),t.status){case"fulfilled":return t.value;case"rejected":throw e=t.reason,Ud(e),e;default:if(typeof t.status=="string")t.then(Dr,Dr);else{if(e=Ve,e!==null&&100<e.shellSuspendCounter)throw Error(s(482));e=t,e.status="pending",e.then(function(i){if(t.status==="pending"){var u=t;u.status="fulfilled",u.value=i}},function(i){if(t.status==="pending"){var u=t;u.status="rejected",u.reason=i}})}switch(t.status){case"fulfilled":return t.value;case"rejected":throw e=t.reason,Ud(e),e}throw ui=t,si}}var ui=null;function Md(){if(ui===null)throw Error(s(459));var e=ui;return ui=null,e}function Ud(e){if(e===si||e===Cr)throw Error(s(483))}var Qn=!1;function Wu(e){e.updateQueue={baseState:e.memoizedState,firstBaseUpdate:null,lastBaseUpdate:null,shared:{pending:null,lanes:0,hiddenCallbacks:null},callbacks:null}}function Pu(e,t){e=e.updateQueue,t.updateQueue===e&&(t.updateQueue={baseState:e.baseState,firstBaseUpdate:e.firstBaseUpdate,lastBaseUpdate:e.lastBaseUpdate,shared:e.shared,callbacks:null})}function Zn(e){return{lane:e,tag:0,payload:null,callback:null,next:null}}function Kn(e,t,n){var i=e.updateQueue;if(i===null)return null;if(i=i.shared,(Ue&2)!==0){var u=i.pending;return u===null?t.next=t:(t.next=u.next,u.next=t),i.pending=t,t=_r(e),bd(e,null,n),t}return Er(e,i,t,n),_r(e)}function oi(e,t,n){if(t=t.updateQueue,t!==null&&(t=t.shared,(n&4194048)!==0)){var i=t.lanes;i&=e.pendingLanes,n|=i,t.lanes=n,Tf(e,n)}}function Iu(e,t){var n=e.updateQueue,i=e.alternate;if(i!==null&&(i=i.updateQueue,n===i)){var u=null,o=null;if(n=n.firstBaseUpdate,n!==null){do{var h={lane:n.lane,tag:n.tag,payload:n.payload,callback:null,next:null};o===null?u=o=h:o=o.next=h,n=n.next}while(n!==null);o===null?u=o=t:o=o.next=t}else u=o=t;n={baseState:i.baseState,firstBaseUpdate:u,lastBaseUpdate:o,shared:i.shared,callbacks:i.callbacks},e.updateQueue=n;return}e=n.lastBaseUpdate,e===null?n.firstBaseUpdate=t:e.next=t,n.lastBaseUpdate=t}var eo=!1;function ci(){if(eo){var e=hl;if(e!==null)throw e}}function fi(e,t,n,i){eo=!1;var u=e.updateQueue;Qn=!1;var o=u.firstBaseUpdate,h=u.lastBaseUpdate,p=u.shared.pending;if(p!==null){u.shared.pending=null;var b=p,C=b.next;b.next=null,h===null?o=C:h.next=C,h=b;var k=e.alternate;k!==null&&(k=k.updateQueue,p=k.lastBaseUpdate,p!==h&&(p===null?k.firstBaseUpdate=C:p.next=C,k.lastBaseUpdate=b))}if(o!==null){var Y=u.baseState;h=0,k=C=b=null,p=o;do{var M=p.lane&-536870913,U=M!==p.lane;if(U?(Re&M)===M:(i&M)===M){M!==0&&M===dl&&(eo=!0),k!==null&&(k=k.next={lane:0,tag:p.tag,payload:p.payload,callback:null,next:null});e:{var ce=e,se=p;M=t;var Le=n;switch(se.tag){case 1:if(ce=se.payload,typeof ce=="function"){Y=ce.call(Le,Y,M);break e}Y=ce;break e;case 3:ce.flags=ce.flags&-65537|128;case 0:if(ce=se.payload,M=typeof ce=="function"?ce.call(Le,Y,M):ce,M==null)break e;Y=v({},Y,M);break e;case 2:Qn=!0}}M=p.callback,M!==null&&(e.flags|=64,U&&(e.flags|=8192),U=u.callbacks,U===null?u.callbacks=[M]:U.push(M))}else U={lane:M,tag:p.tag,payload:p.payload,callback:p.callback,next:null},k===null?(C=k=U,b=Y):k=k.next=U,h|=M;if(p=p.next,p===null){if(p=u.shared.pending,p===null)break;U=p,p=U.next,U.next=null,u.lastBaseUpdate=U,u.shared.pending=null}}while(!0);k===null&&(b=Y),u.baseState=b,u.firstBaseUpdate=C,u.lastBaseUpdate=k,o===null&&(u.shared.lanes=0),ta|=h,e.lanes=h,e.memoizedState=Y}}function zd(e,t){if(typeof e!="function")throw Error(s(191,e));e.call(t)}function jd(e,t){var n=e.callbacks;if(n!==null)for(e.callbacks=null,e=0;e<n.length;e++)zd(n[e],t)}var ml=G(null),Mr=G(0);function Bd(e,t){e=Mn,W(Mr,e),W(ml,t),Mn=e|t.baseLanes}function to(){W(Mr,Mn),W(ml,ml.current)}function no(){Mn=Mr.current,I(ml),I(Mr)}var Fn=0,ge=null,je=null,et=null,Ur=!1,yl=!1,Ma=!1,zr=0,di=0,pl=null,Dg=0;function $e(){throw Error(s(321))}function ao(e,t){if(t===null)return!1;for(var n=0;n<t.length&&n<e.length;n++)if(!Mt(e[n],t[n]))return!1;return!0}function lo(e,t,n,i,u,o){return Fn=o,ge=t,t.memoizedState=null,t.updateQueue=null,t.lanes=0,j.H=e===null||e.memoizedState===null?bh:Sh,Ma=!1,o=n(i,u),Ma=!1,yl&&(o=kd(t,n,i,u)),Ld(e),o}function Ld(e){j.H=Hr;var t=je!==null&&je.next!==null;if(Fn=0,et=je=ge=null,Ur=!1,di=0,pl=null,t)throw Error(s(300));e===null||ot||(e=e.dependencies,e!==null&&Rr(e)&&(ot=!0))}function kd(e,t,n,i){ge=e;var u=0;do{if(yl&&(pl=null),di=0,yl=!1,25<=u)throw Error(s(301));if(u+=1,et=je=null,e.updateQueue!=null){var o=e.updateQueue;o.lastEffect=null,o.events=null,o.stores=null,o.memoCache!=null&&(o.memoCache.index=0)}j.H=kg,o=t(n,i)}while(yl);return o}function Mg(){var e=j.H,t=e.useState()[0];return t=typeof t.then=="function"?hi(t):t,e=e.useState()[0],(je!==null?je.memoizedState:null)!==e&&(ge.flags|=1024),t}function io(){var e=zr!==0;return zr=0,e}function ro(e,t,n){t.updateQueue=e.updateQueue,t.flags&=-2053,e.lanes&=~n}function so(e){if(Ur){for(e=e.memoizedState;e!==null;){var t=e.queue;t!==null&&(t.pending=null),e=e.next}Ur=!1}Fn=0,et=je=ge=null,yl=!1,di=zr=0,pl=null}function Tt(){var e={memoizedState:null,baseState:null,baseQueue:null,queue:null,next:null};return et===null?ge.memoizedState=et=e:et=et.next=e,et}function tt(){if(je===null){var e=ge.alternate;e=e!==null?e.memoizedState:null}else e=je.next;var t=et===null?ge.memoizedState:et.next;if(t!==null)et=t,je=e;else{if(e===null)throw ge.alternate===null?Error(s(467)):Error(s(310));je=e,e={memoizedState:je.memoizedState,baseState:je.baseState,baseQueue:je.baseQueue,queue:je.queue,next:null},et===null?ge.memoizedState=et=e:et=et.next=e}return et}function uo(){return{lastEffect:null,events:null,stores:null,memoCache:null}}function hi(e){var t=di;return di+=1,pl===null&&(pl=[]),e=Dd(pl,e,t),t=ge,(et===null?t.memoizedState:et.next)===null&&(t=t.alternate,j.H=t===null||t.memoizedState===null?bh:Sh),e}function jr(e){if(e!==null&&typeof e=="object"){if(typeof e.then=="function")return hi(e);if(e.$$typeof===$)return vt(e)}throw Error(s(438,String(e)))}function oo(e){var t=null,n=ge.updateQueue;if(n!==null&&(t=n.memoCache),t==null){var i=ge.alternate;i!==null&&(i=i.updateQueue,i!==null&&(i=i.memoCache,i!=null&&(t={data:i.data.map(function(u){return u.slice()}),index:0})))}if(t==null&&(t={data:[],index:0}),n===null&&(n=uo(),ge.updateQueue=n),n.memoCache=t,n=t.data[t.index],n===void 0)for(n=t.data[t.index]=Array(e),i=0;i<e;i++)n[i]=Qe;return t.index++,n}function Tn(e,t){return typeof t=="function"?t(e):t}function Br(e){var t=tt();return co(t,je,e)}function co(e,t,n){var i=e.queue;if(i===null)throw Error(s(311));i.lastRenderedReducer=n;var u=e.baseQueue,o=i.pending;if(o!==null){if(u!==null){var h=u.next;u.next=o.next,o.next=h}t.baseQueue=u=o,i.pending=null}if(o=e.baseState,u===null)e.memoizedState=o;else{t=u.next;var p=h=null,b=null,C=t,k=!1;do{var Y=C.lane&-536870913;if(Y!==C.lane?(Re&Y)===Y:(Fn&Y)===Y){var M=C.revertLane;if(M===0)b!==null&&(b=b.next={lane:0,revertLane:0,action:C.action,hasEagerState:C.hasEagerState,eagerState:C.eagerState,next:null}),Y===dl&&(k=!0);else if((Fn&M)===M){C=C.next,M===dl&&(k=!0);continue}else Y={lane:0,revertLane:C.revertLane,action:C.action,hasEagerState:C.hasEagerState,eagerState:C.eagerState,next:null},b===null?(p=b=Y,h=o):b=b.next=Y,ge.lanes|=M,ta|=M;Y=C.action,Ma&&n(o,Y),o=C.hasEagerState?C.eagerState:n(o,Y)}else M={lane:Y,revertLane:C.revertLane,action:C.action,hasEagerState:C.hasEagerState,eagerState:C.eagerState,next:null},b===null?(p=b=M,h=o):b=b.next=M,ge.lanes|=Y,ta|=Y;C=C.next}while(C!==null&&C!==t);if(b===null?h=o:b.next=p,!Mt(o,e.memoizedState)&&(ot=!0,k&&(n=hl,n!==null)))throw n;e.memoizedState=o,e.baseState=h,e.baseQueue=b,i.lastRenderedState=o}return u===null&&(i.lanes=0),[e.memoizedState,i.dispatch]}function fo(e){var t=tt(),n=t.queue;if(n===null)throw Error(s(311));n.lastRenderedReducer=e;var i=n.dispatch,u=n.pending,o=t.memoizedState;if(u!==null){n.pending=null;var h=u=u.next;do o=e(o,h.action),h=h.next;while(h!==u);Mt(o,t.memoizedState)||(ot=!0),t.memoizedState=o,t.baseQueue===null&&(t.baseState=o),n.lastRenderedState=o}return[o,i]}function qd(e,t,n){var i=ge,u=tt(),o=Ce;if(o){if(n===void 0)throw Error(s(407));n=n()}else n=t();var h=!Mt((je||u).memoizedState,n);h&&(u.memoizedState=n,ot=!0),u=u.queue;var p=Yd.bind(null,i,u,e);if(mi(2048,8,p,[e]),u.getSnapshot!==t||h||et!==null&&et.memoizedState.tag&1){if(i.flags|=2048,gl(9,Lr(),Vd.bind(null,i,u,n,t),null),Ve===null)throw Error(s(349));o||(Fn&124)!==0||Hd(i,t,n)}return n}function Hd(e,t,n){e.flags|=16384,e={getSnapshot:t,value:n},t=ge.updateQueue,t===null?(t=uo(),ge.updateQueue=t,t.stores=[e]):(n=t.stores,n===null?t.stores=[e]:n.push(e))}function Vd(e,t,n,i){t.value=n,t.getSnapshot=i,Xd(t)&&Gd(e)}function Yd(e,t,n){return n(function(){Xd(t)&&Gd(e)})}function Xd(e){var t=e.getSnapshot;e=e.value;try{var n=t();return!Mt(e,n)}catch{return!0}}function Gd(e){var t=ul(e,2);t!==null&&kt(t,e,2)}function ho(e){var t=Tt();if(typeof e=="function"){var n=e;if(e=n(),Ma){_e(!0);try{n()}finally{_e(!1)}}}return t.memoizedState=t.baseState=e,t.queue={pending:null,lanes:0,dispatch:null,lastRenderedReducer:Tn,lastRenderedState:e},t}function Qd(e,t,n,i){return e.baseState=n,co(e,je,typeof i=="function"?i:Tn)}function Ug(e,t,n,i,u){if(qr(e))throw Error(s(485));if(e=t.action,e!==null){var o={payload:u,action:e,next:null,isTransition:!0,status:"pending",value:null,reason:null,listeners:[],then:function(h){o.listeners.push(h)}};j.T!==null?n(!0):o.isTransition=!1,i(o),n=t.pending,n===null?(o.next=t.pending=o,Zd(t,o)):(o.next=n.next,t.pending=n.next=o)}}function Zd(e,t){var n=t.action,i=t.payload,u=e.state;if(t.isTransition){var o=j.T,h={};j.T=h;try{var p=n(u,i),b=j.S;b!==null&&b(h,p),Kd(e,t,p)}catch(C){mo(e,t,C)}finally{j.T=o}}else try{o=n(u,i),Kd(e,t,o)}catch(C){mo(e,t,C)}}function Kd(e,t,n){n!==null&&typeof n=="object"&&typeof n.then=="function"?n.then(function(i){Fd(e,t,i)},function(i){return mo(e,t,i)}):Fd(e,t,n)}function Fd(e,t,n){t.status="fulfilled",t.value=n,Jd(t),e.state=n,t=e.pending,t!==null&&(n=t.next,n===t?e.pending=null:(n=n.next,t.next=n,Zd(e,n)))}function mo(e,t,n){var i=e.pending;if(e.pending=null,i!==null){i=i.next;do t.status="rejected",t.reason=n,Jd(t),t=t.next;while(t!==i)}e.action=null}function Jd(e){e=e.listeners;for(var t=0;t<e.length;t++)(0,e[t])()}function $d(e,t){return t}function Wd(e,t){if(Ce){var n=Ve.formState;if(n!==null){e:{var i=ge;if(Ce){if(Fe){t:{for(var u=Fe,o=un;u.nodeType!==8;){if(!o){u=null;break t}if(u=It(u.nextSibling),u===null){u=null;break t}}o=u.data,u=o==="F!"||o==="F"?u:null}if(u){Fe=It(u.nextSibling),i=u.data==="F!";break e}}Oa(i)}i=!1}i&&(t=n[0])}}return n=Tt(),n.memoizedState=n.baseState=t,i={pending:null,lanes:0,dispatch:null,lastRenderedReducer:$d,lastRenderedState:t},n.queue=i,n=ph.bind(null,ge,i),i.dispatch=n,i=ho(!1),o=bo.bind(null,ge,!1,i.queue),i=Tt(),u={state:t,dispatch:null,action:e,pending:null},i.queue=u,n=Ug.bind(null,ge,u,o,n),u.dispatch=n,i.memoizedState=e,[t,n,!1]}function Pd(e){var t=tt();return Id(t,je,e)}function Id(e,t,n){if(t=co(e,t,$d)[0],e=Br(Tn)[0],typeof t=="object"&&t!==null&&typeof t.then=="function")try{var i=hi(t)}catch(h){throw h===si?Cr:h}else i=t;t=tt();var u=t.queue,o=u.dispatch;return n!==t.memoizedState&&(ge.flags|=2048,gl(9,Lr(),zg.bind(null,u,n),null)),[i,o,e]}function zg(e,t){e.action=t}function eh(e){var t=tt(),n=je;if(n!==null)return Id(t,n,e);tt(),t=t.memoizedState,n=tt();var i=n.queue.dispatch;return n.memoizedState=e,[t,i,!1]}function gl(e,t,n,i){return e={tag:e,create:n,deps:i,inst:t,next:null},t=ge.updateQueue,t===null&&(t=uo(),ge.updateQueue=t),n=t.lastEffect,n===null?t.lastEffect=e.next=e:(i=n.next,n.next=e,e.next=i,t.lastEffect=e),e}function Lr(){return{destroy:void 0,resource:void 0}}function th(){return tt().memoizedState}function kr(e,t,n,i){var u=Tt();i=i===void 0?null:i,ge.flags|=e,u.memoizedState=gl(1|t,Lr(),n,i)}function mi(e,t,n,i){var u=tt();i=i===void 0?null:i;var o=u.memoizedState.inst;je!==null&&i!==null&&ao(i,je.memoizedState.deps)?u.memoizedState=gl(t,o,n,i):(ge.flags|=e,u.memoizedState=gl(1|t,o,n,i))}function nh(e,t){kr(8390656,8,e,t)}function ah(e,t){mi(2048,8,e,t)}function lh(e,t){return mi(4,2,e,t)}function ih(e,t){return mi(4,4,e,t)}function rh(e,t){if(typeof t=="function"){e=e();var n=t(e);return function(){typeof n=="function"?n():t(null)}}if(t!=null)return e=e(),t.current=e,function(){t.current=null}}function sh(e,t,n){n=n!=null?n.concat([e]):null,mi(4,4,rh.bind(null,t,e),n)}function yo(){}function uh(e,t){var n=tt();t=t===void 0?null:t;var i=n.memoizedState;return t!==null&&ao(t,i[1])?i[0]:(n.memoizedState=[e,t],e)}function oh(e,t){var n=tt();t=t===void 0?null:t;var i=n.memoizedState;if(t!==null&&ao(t,i[1]))return i[0];if(i=e(),Ma){_e(!0);try{e()}finally{_e(!1)}}return n.memoizedState=[i,t],i}function po(e,t,n){return n===void 0||(Fn&1073741824)!==0?e.memoizedState=t:(e.memoizedState=n,e=dm(),ge.lanes|=e,ta|=e,n)}function ch(e,t,n,i){return Mt(n,t)?n:ml.current!==null?(e=po(e,n,i),Mt(e,t)||(ot=!0),e):(Fn&42)===0?(ot=!0,e.memoizedState=n):(e=dm(),ge.lanes|=e,ta|=e,t)}function fh(e,t,n,i,u){var o=F.p;F.p=o!==0&&8>o?o:8;var h=j.T,p={};j.T=p,bo(e,!1,t,n);try{var b=u(),C=j.S;if(C!==null&&C(p,b),b!==null&&typeof b=="object"&&typeof b.then=="function"){var k=Cg(b,i);yi(e,t,k,Lt(e))}else yi(e,t,i,Lt(e))}catch(Y){yi(e,t,{then:function(){},status:"rejected",reason:Y},Lt())}finally{F.p=o,j.T=h}}function jg(){}function go(e,t,n,i){if(e.tag!==5)throw Error(s(476));var u=dh(e).queue;fh(e,u,t,le,n===null?jg:function(){return hh(e),n(i)})}function dh(e){var t=e.memoizedState;if(t!==null)return t;t={memoizedState:le,baseState:le,baseQueue:null,queue:{pending:null,lanes:0,dispatch:null,lastRenderedReducer:Tn,lastRenderedState:le},next:null};var n={};return t.next={memoizedState:n,baseState:n,baseQueue:null,queue:{pending:null,lanes:0,dispatch:null,lastRenderedReducer:Tn,lastRenderedState:n},next:null},e.memoizedState=t,e=e.alternate,e!==null&&(e.memoizedState=t),t}function hh(e){var t=dh(e).next.queue;yi(e,t,{},Lt())}function vo(){return vt(Ui)}function mh(){return tt().memoizedState}function yh(){return tt().memoizedState}function Bg(e){for(var t=e.return;t!==null;){switch(t.tag){case 24:case 3:var n=Lt();e=Zn(n);var i=Kn(t,e,n);i!==null&&(kt(i,t,n),oi(i,t,n)),t={cache:Ku()},e.payload=t;return}t=t.return}}function Lg(e,t,n){var i=Lt();n={lane:i,revertLane:0,action:n,hasEagerState:!1,eagerState:null,next:null},qr(e)?gh(t,n):(n=Lu(e,t,n,i),n!==null&&(kt(n,e,i),vh(n,t,i)))}function ph(e,t,n){var i=Lt();yi(e,t,n,i)}function yi(e,t,n,i){var u={lane:i,revertLane:0,action:n,hasEagerState:!1,eagerState:null,next:null};if(qr(e))gh(t,u);else{var o=e.alternate;if(e.lanes===0&&(o===null||o.lanes===0)&&(o=t.lastRenderedReducer,o!==null))try{var h=t.lastRenderedState,p=o(h,n);if(u.hasEagerState=!0,u.eagerState=p,Mt(p,h))return Er(e,t,u,0),Ve===null&&xr(),!1}catch{}finally{}if(n=Lu(e,t,u,i),n!==null)return kt(n,e,i),vh(n,t,i),!0}return!1}function bo(e,t,n,i){if(i={lane:2,revertLane:Wo(),action:i,hasEagerState:!1,eagerState:null,next:null},qr(e)){if(t)throw Error(s(479))}else t=Lu(e,n,i,2),t!==null&&kt(t,e,2)}function qr(e){var t=e.alternate;return e===ge||t!==null&&t===ge}function gh(e,t){yl=Ur=!0;var n=e.pending;n===null?t.next=t:(t.next=n.next,n.next=t),e.pending=t}function vh(e,t,n){if((n&4194048)!==0){var i=t.lanes;i&=e.pendingLanes,n|=i,t.lanes=n,Tf(e,n)}}var Hr={readContext:vt,use:jr,useCallback:$e,useContext:$e,useEffect:$e,useImperativeHandle:$e,useLayoutEffect:$e,useInsertionEffect:$e,useMemo:$e,useReducer:$e,useRef:$e,useState:$e,useDebugValue:$e,useDeferredValue:$e,useTransition:$e,useSyncExternalStore:$e,useId:$e,useHostTransitionStatus:$e,useFormState:$e,useActionState:$e,useOptimistic:$e,useMemoCache:$e,useCacheRefresh:$e},bh={readContext:vt,use:jr,useCallback:function(e,t){return Tt().memoizedState=[e,t===void 0?null:t],e},useContext:vt,useEffect:nh,useImperativeHandle:function(e,t,n){n=n!=null?n.concat([e]):null,kr(4194308,4,rh.bind(null,t,e),n)},useLayoutEffect:function(e,t){return kr(4194308,4,e,t)},useInsertionEffect:function(e,t){kr(4,2,e,t)},useMemo:function(e,t){var n=Tt();t=t===void 0?null:t;var i=e();if(Ma){_e(!0);try{e()}finally{_e(!1)}}return n.memoizedState=[i,t],i},useReducer:function(e,t,n){var i=Tt();if(n!==void 0){var u=n(t);if(Ma){_e(!0);try{n(t)}finally{_e(!1)}}}else u=t;return i.memoizedState=i.baseState=u,e={pending:null,lanes:0,dispatch:null,lastRenderedReducer:e,lastRenderedState:u},i.queue=e,e=e.dispatch=Lg.bind(null,ge,e),[i.memoizedState,e]},useRef:function(e){var t=Tt();return e={current:e},t.memoizedState=e},useState:function(e){e=ho(e);var t=e.queue,n=ph.bind(null,ge,t);return t.dispatch=n,[e.memoizedState,n]},useDebugValue:yo,useDeferredValue:function(e,t){var n=Tt();return po(n,e,t)},useTransition:function(){var e=ho(!1);return e=fh.bind(null,ge,e.queue,!0,!1),Tt().memoizedState=e,[!1,e]},useSyncExternalStore:function(e,t,n){var i=ge,u=Tt();if(Ce){if(n===void 0)throw Error(s(407));n=n()}else{if(n=t(),Ve===null)throw Error(s(349));(Re&124)!==0||Hd(i,t,n)}u.memoizedState=n;var o={value:n,getSnapshot:t};return u.queue=o,nh(Yd.bind(null,i,o,e),[e]),i.flags|=2048,gl(9,Lr(),Vd.bind(null,i,o,n,t),null),n},useId:function(){var e=Tt(),t=Ve.identifierPrefix;if(Ce){var n=_n,i=En;n=(i&~(1<<32-He(i)-1)).toString(32)+n,t="«"+t+"R"+n,n=zr++,0<n&&(t+="H"+n.toString(32)),t+="»"}else n=Dg++,t="«"+t+"r"+n.toString(32)+"»";return e.memoizedState=t},useHostTransitionStatus:vo,useFormState:Wd,useActionState:Wd,useOptimistic:function(e){var t=Tt();t.memoizedState=t.baseState=e;var n={pending:null,lanes:0,dispatch:null,lastRenderedReducer:null,lastRenderedState:null};return t.queue=n,t=bo.bind(null,ge,!0,n),n.dispatch=t,[e,t]},useMemoCache:oo,useCacheRefresh:function(){return Tt().memoizedState=Bg.bind(null,ge)}},Sh={readContext:vt,use:jr,useCallback:uh,useContext:vt,useEffect:ah,useImperativeHandle:sh,useInsertionEffect:lh,useLayoutEffect:ih,useMemo:oh,useReducer:Br,useRef:th,useState:function(){return Br(Tn)},useDebugValue:yo,useDeferredValue:function(e,t){var n=tt();return ch(n,je.memoizedState,e,t)},useTransition:function(){var e=Br(Tn)[0],t=tt().memoizedState;return[typeof e=="boolean"?e:hi(e),t]},useSyncExternalStore:qd,useId:mh,useHostTransitionStatus:vo,useFormState:Pd,useActionState:Pd,useOptimistic:function(e,t){var n=tt();return Qd(n,je,e,t)},useMemoCache:oo,useCacheRefresh:yh},kg={readContext:vt,use:jr,useCallback:uh,useContext:vt,useEffect:ah,useImperativeHandle:sh,useInsertionEffect:lh,useLayoutEffect:ih,useMemo:oh,useReducer:fo,useRef:th,useState:function(){return fo(Tn)},useDebugValue:yo,useDeferredValue:function(e,t){var n=tt();return je===null?po(n,e,t):ch(n,je.memoizedState,e,t)},useTransition:function(){var e=fo(Tn)[0],t=tt().memoizedState;return[typeof e=="boolean"?e:hi(e),t]},useSyncExternalStore:qd,useId:mh,useHostTransitionStatus:vo,useFormState:eh,useActionState:eh,useOptimistic:function(e,t){var n=tt();return je!==null?Qd(n,je,e,t):(n.baseState=e,[e,n.queue.dispatch])},useMemoCache:oo,useCacheRefresh:yh},vl=null,pi=0;function Vr(e){var t=pi;return pi+=1,vl===null&&(vl=[]),Dd(vl,e,t)}function gi(e,t){t=t.props.ref,e.ref=t!==void 0?t:null}function Yr(e,t){throw t.$$typeof===A?Error(s(525)):(e=Object.prototype.toString.call(t),Error(s(31,e==="[object Object]"?"object with keys {"+Object.keys(t).join(", ")+"}":e)))}function xh(e){var t=e._init;return t(e._payload)}function Eh(e){function t(T,w){if(e){var O=T.deletions;O===null?(T.deletions=[w],T.flags|=16):O.push(w)}}function n(T,w){if(!e)return null;for(;w!==null;)t(T,w),w=w.sibling;return null}function i(T){for(var w=new Map;T!==null;)T.key!==null?w.set(T.key,T):w.set(T.index,T),T=T.sibling;return w}function u(T,w){return T=xn(T,w),T.index=0,T.sibling=null,T}function o(T,w,O){return T.index=O,e?(O=T.alternate,O!==null?(O=O.index,O<w?(T.flags|=67108866,w):O):(T.flags|=67108866,w)):(T.flags|=1048576,w)}function h(T){return e&&T.alternate===null&&(T.flags|=67108866),T}function p(T,w,O,H){return w===null||w.tag!==6?(w=qu(O,T.mode,H),w.return=T,w):(w=u(w,O),w.return=T,w)}function b(T,w,O,H){var ee=O.type;return ee===R?k(T,w,O.props.children,H,O.key):w!==null&&(w.elementType===ee||typeof ee=="object"&&ee!==null&&ee.$$typeof===De&&xh(ee)===w.type)?(w=u(w,O.props),gi(w,O),w.return=T,w):(w=wr(O.type,O.key,O.props,null,T.mode,H),gi(w,O),w.return=T,w)}function C(T,w,O,H){return w===null||w.tag!==4||w.stateNode.containerInfo!==O.containerInfo||w.stateNode.implementation!==O.implementation?(w=Hu(O,T.mode,H),w.return=T,w):(w=u(w,O.children||[]),w.return=T,w)}function k(T,w,O,H,ee){return w===null||w.tag!==7?(w=wa(O,T.mode,H,ee),w.return=T,w):(w=u(w,O),w.return=T,w)}function Y(T,w,O){if(typeof w=="string"&&w!==""||typeof w=="number"||typeof w=="bigint")return w=qu(""+w,T.mode,O),w.return=T,w;if(typeof w=="object"&&w!==null){switch(w.$$typeof){case _:return O=wr(w.type,w.key,w.props,null,T.mode,O),gi(O,w),O.return=T,O;case Q:return w=Hu(w,T.mode,O),w.return=T,w;case De:var H=w._init;return w=H(w._payload),Y(T,w,O)}if(Oe(w)||oe(w))return w=wa(w,T.mode,O,null),w.return=T,w;if(typeof w.then=="function")return Y(T,Vr(w),O);if(w.$$typeof===$)return Y(T,Or(T,w),O);Yr(T,w)}return null}function M(T,w,O,H){var ee=w!==null?w.key:null;if(typeof O=="string"&&O!==""||typeof O=="number"||typeof O=="bigint")return ee!==null?null:p(T,w,""+O,H);if(typeof O=="object"&&O!==null){switch(O.$$typeof){case _:return O.key===ee?b(T,w,O,H):null;case Q:return O.key===ee?C(T,w,O,H):null;case De:return ee=O._init,O=ee(O._payload),M(T,w,O,H)}if(Oe(O)||oe(O))return ee!==null?null:k(T,w,O,H,null);if(typeof O.then=="function")return M(T,w,Vr(O),H);if(O.$$typeof===$)return M(T,w,Or(T,O),H);Yr(T,O)}return null}function U(T,w,O,H,ee){if(typeof H=="string"&&H!==""||typeof H=="number"||typeof H=="bigint")return T=T.get(O)||null,p(w,T,""+H,ee);if(typeof H=="object"&&H!==null){switch(H.$$typeof){case _:return T=T.get(H.key===null?O:H.key)||null,b(w,T,H,ee);case Q:return T=T.get(H.key===null?O:H.key)||null,C(w,T,H,ee);case De:var ve=H._init;return H=ve(H._payload),U(T,w,O,H,ee)}if(Oe(H)||oe(H))return T=T.get(O)||null,k(w,T,H,ee,null);if(typeof H.then=="function")return U(T,w,O,Vr(H),ee);if(H.$$typeof===$)return U(T,w,O,Or(w,H),ee);Yr(w,H)}return null}function ce(T,w,O,H){for(var ee=null,ve=null,ae=w,ue=w=0,ft=null;ae!==null&&ue<O.length;ue++){ae.index>ue?(ft=ae,ae=null):ft=ae.sibling;var Ne=M(T,ae,O[ue],H);if(Ne===null){ae===null&&(ae=ft);break}e&&ae&&Ne.alternate===null&&t(T,ae),w=o(Ne,w,ue),ve===null?ee=Ne:ve.sibling=Ne,ve=Ne,ae=ft}if(ue===O.length)return n(T,ae),Ce&&Ta(T,ue),ee;if(ae===null){for(;ue<O.length;ue++)ae=Y(T,O[ue],H),ae!==null&&(w=o(ae,w,ue),ve===null?ee=ae:ve.sibling=ae,ve=ae);return Ce&&Ta(T,ue),ee}for(ae=i(ae);ue<O.length;ue++)ft=U(ae,T,ue,O[ue],H),ft!==null&&(e&&ft.alternate!==null&&ae.delete(ft.key===null?ue:ft.key),w=o(ft,w,ue),ve===null?ee=ft:ve.sibling=ft,ve=ft);return e&&ae.forEach(function(ca){return t(T,ca)}),Ce&&Ta(T,ue),ee}function se(T,w,O,H){if(O==null)throw Error(s(151));for(var ee=null,ve=null,ae=w,ue=w=0,ft=null,Ne=O.next();ae!==null&&!Ne.done;ue++,Ne=O.next()){ae.index>ue?(ft=ae,ae=null):ft=ae.sibling;var ca=M(T,ae,Ne.value,H);if(ca===null){ae===null&&(ae=ft);break}e&&ae&&ca.alternate===null&&t(T,ae),w=o(ca,w,ue),ve===null?ee=ca:ve.sibling=ca,ve=ca,ae=ft}if(Ne.done)return n(T,ae),Ce&&Ta(T,ue),ee;if(ae===null){for(;!Ne.done;ue++,Ne=O.next())Ne=Y(T,Ne.value,H),Ne!==null&&(w=o(Ne,w,ue),ve===null?ee=Ne:ve.sibling=Ne,ve=Ne);return Ce&&Ta(T,ue),ee}for(ae=i(ae);!Ne.done;ue++,Ne=O.next())Ne=U(ae,T,ue,Ne.value,H),Ne!==null&&(e&&Ne.alternate!==null&&ae.delete(Ne.key===null?ue:Ne.key),w=o(Ne,w,ue),ve===null?ee=Ne:ve.sibling=Ne,ve=Ne);return e&&ae.forEach(function(qv){return t(T,qv)}),Ce&&Ta(T,ue),ee}function Le(T,w,O,H){if(typeof O=="object"&&O!==null&&O.type===R&&O.key===null&&(O=O.props.children),typeof O=="object"&&O!==null){switch(O.$$typeof){case _:e:{for(var ee=O.key;w!==null;){if(w.key===ee){if(ee=O.type,ee===R){if(w.tag===7){n(T,w.sibling),H=u(w,O.props.children),H.return=T,T=H;break e}}else if(w.elementType===ee||typeof ee=="object"&&ee!==null&&ee.$$typeof===De&&xh(ee)===w.type){n(T,w.sibling),H=u(w,O.props),gi(H,O),H.return=T,T=H;break e}n(T,w);break}else t(T,w);w=w.sibling}O.type===R?(H=wa(O.props.children,T.mode,H,O.key),H.return=T,T=H):(H=wr(O.type,O.key,O.props,null,T.mode,H),gi(H,O),H.return=T,T=H)}return h(T);case Q:e:{for(ee=O.key;w!==null;){if(w.key===ee)if(w.tag===4&&w.stateNode.containerInfo===O.containerInfo&&w.stateNode.implementation===O.implementation){n(T,w.sibling),H=u(w,O.children||[]),H.return=T,T=H;break e}else{n(T,w);break}else t(T,w);w=w.sibling}H=Hu(O,T.mode,H),H.return=T,T=H}return h(T);case De:return ee=O._init,O=ee(O._payload),Le(T,w,O,H)}if(Oe(O))return ce(T,w,O,H);if(oe(O)){if(ee=oe(O),typeof ee!="function")throw Error(s(150));return O=ee.call(O),se(T,w,O,H)}if(typeof O.then=="function")return Le(T,w,Vr(O),H);if(O.$$typeof===$)return Le(T,w,Or(T,O),H);Yr(T,O)}return typeof O=="string"&&O!==""||typeof O=="number"||typeof O=="bigint"?(O=""+O,w!==null&&w.tag===6?(n(T,w.sibling),H=u(w,O),H.return=T,T=H):(n(T,w),H=qu(O,T.mode,H),H.return=T,T=H),h(T)):n(T,w)}return function(T,w,O,H){try{pi=0;var ee=Le(T,w,O,H);return vl=null,ee}catch(ae){if(ae===si||ae===Cr)throw ae;var ve=Ut(29,ae,null,T.mode);return ve.lanes=H,ve.return=T,ve}finally{}}}var bl=Eh(!0),_h=Eh(!1),Kt=G(null),on=null;function Jn(e){var t=e.alternate;W(st,st.current&1),W(Kt,e),on===null&&(t===null||ml.current!==null||t.memoizedState!==null)&&(on=e)}function wh(e){if(e.tag===22){if(W(st,st.current),W(Kt,e),on===null){var t=e.alternate;t!==null&&t.memoizedState!==null&&(on=e)}}else $n()}function $n(){W(st,st.current),W(Kt,Kt.current)}function Rn(e){I(Kt),on===e&&(on=null),I(st)}var st=G(0);function Xr(e){for(var t=e;t!==null;){if(t.tag===13){var n=t.memoizedState;if(n!==null&&(n=n.dehydrated,n===null||n.data==="$?"||oc(n)))return t}else if(t.tag===19&&t.memoizedProps.revealOrder!==void 0){if((t.flags&128)!==0)return t}else if(t.child!==null){t.child.return=t,t=t.child;continue}if(t===e)break;for(;t.sibling===null;){if(t.return===null||t.return===e)return null;t=t.return}t.sibling.return=t.return,t=t.sibling}return null}function So(e,t,n,i){t=e.memoizedState,n=n(i,t),n=n==null?t:v({},t,n),e.memoizedState=n,e.lanes===0&&(e.updateQueue.baseState=n)}var xo={enqueueSetState:function(e,t,n){e=e._reactInternals;var i=Lt(),u=Zn(i);u.payload=t,n!=null&&(u.callback=n),t=Kn(e,u,i),t!==null&&(kt(t,e,i),oi(t,e,i))},enqueueReplaceState:function(e,t,n){e=e._reactInternals;var i=Lt(),u=Zn(i);u.tag=1,u.payload=t,n!=null&&(u.callback=n),t=Kn(e,u,i),t!==null&&(kt(t,e,i),oi(t,e,i))},enqueueForceUpdate:function(e,t){e=e._reactInternals;var n=Lt(),i=Zn(n);i.tag=2,t!=null&&(i.callback=t),t=Kn(e,i,n),t!==null&&(kt(t,e,n),oi(t,e,n))}};function Ah(e,t,n,i,u,o,h){return e=e.stateNode,typeof e.shouldComponentUpdate=="function"?e.shouldComponentUpdate(i,o,h):t.prototype&&t.prototype.isPureReactComponent?!Il(n,i)||!Il(u,o):!0}function Th(e,t,n,i){e=t.state,typeof t.componentWillReceiveProps=="function"&&t.componentWillReceiveProps(n,i),typeof t.UNSAFE_componentWillReceiveProps=="function"&&t.UNSAFE_componentWillReceiveProps(n,i),t.state!==e&&xo.enqueueReplaceState(t,t.state,null)}function Ua(e,t){var n=t;if("ref"in t){n={};for(var i in t)i!=="ref"&&(n[i]=t[i])}if(e=e.defaultProps){n===t&&(n=v({},n));for(var u in e)n[u]===void 0&&(n[u]=e[u])}return n}var Gr=typeof reportError=="function"?reportError:function(e){if(typeof window=="object"&&typeof window.ErrorEvent=="function"){var t=new window.ErrorEvent("error",{bubbles:!0,cancelable:!0,message:typeof e=="object"&&e!==null&&typeof e.message=="string"?String(e.message):String(e),error:e});if(!window.dispatchEvent(t))return}else if(typeof process=="object"&&typeof process.emit=="function"){process.emit("uncaughtException",e);return}console.error(e)};function Rh(e){Gr(e)}function Oh(e){console.error(e)}function Nh(e){Gr(e)}function Qr(e,t){try{var n=e.onUncaughtError;n(t.value,{componentStack:t.stack})}catch(i){setTimeout(function(){throw i})}}function Ch(e,t,n){try{var i=e.onCaughtError;i(n.value,{componentStack:n.stack,errorBoundary:t.tag===1?t.stateNode:null})}catch(u){setTimeout(function(){throw u})}}function Eo(e,t,n){return n=Zn(n),n.tag=3,n.payload={element:null},n.callback=function(){Qr(e,t)},n}function Dh(e){return e=Zn(e),e.tag=3,e}function Mh(e,t,n,i){var u=n.type.getDerivedStateFromError;if(typeof u=="function"){var o=i.value;e.payload=function(){return u(o)},e.callback=function(){Ch(t,n,i)}}var h=n.stateNode;h!==null&&typeof h.componentDidCatch=="function"&&(e.callback=function(){Ch(t,n,i),typeof u!="function"&&(na===null?na=new Set([this]):na.add(this));var p=i.stack;this.componentDidCatch(i.value,{componentStack:p!==null?p:""})})}function qg(e,t,n,i,u){if(n.flags|=32768,i!==null&&typeof i=="object"&&typeof i.then=="function"){if(t=n.alternate,t!==null&&li(t,n,u,!0),n=Kt.current,n!==null){switch(n.tag){case 13:return on===null?Zo():n.alternate===null&&Je===0&&(Je=3),n.flags&=-257,n.flags|=65536,n.lanes=u,i===$u?n.flags|=16384:(t=n.updateQueue,t===null?n.updateQueue=new Set([i]):t.add(i),Fo(e,i,u)),!1;case 22:return n.flags|=65536,i===$u?n.flags|=16384:(t=n.updateQueue,t===null?(t={transitions:null,markerInstances:null,retryQueue:new Set([i])},n.updateQueue=t):(n=t.retryQueue,n===null?t.retryQueue=new Set([i]):n.add(i)),Fo(e,i,u)),!1}throw Error(s(435,n.tag))}return Fo(e,i,u),Zo(),!1}if(Ce)return t=Kt.current,t!==null?((t.flags&65536)===0&&(t.flags|=256),t.flags|=65536,t.lanes=u,i!==Xu&&(e=Error(s(422),{cause:i}),ai(Xt(e,n)))):(i!==Xu&&(t=Error(s(423),{cause:i}),ai(Xt(t,n))),e=e.current.alternate,e.flags|=65536,u&=-u,e.lanes|=u,i=Xt(i,n),u=Eo(e.stateNode,i,u),Iu(e,u),Je!==4&&(Je=2)),!1;var o=Error(s(520),{cause:i});if(o=Xt(o,n),wi===null?wi=[o]:wi.push(o),Je!==4&&(Je=2),t===null)return!0;i=Xt(i,n),n=t;do{switch(n.tag){case 3:return n.flags|=65536,e=u&-u,n.lanes|=e,e=Eo(n.stateNode,i,e),Iu(n,e),!1;case 1:if(t=n.type,o=n.stateNode,(n.flags&128)===0&&(typeof t.getDerivedStateFromError=="function"||o!==null&&typeof o.componentDidCatch=="function"&&(na===null||!na.has(o))))return n.flags|=65536,u&=-u,n.lanes|=u,u=Dh(u),Mh(u,e,n,i),Iu(n,u),!1}n=n.return}while(n!==null);return!1}var Uh=Error(s(461)),ot=!1;function mt(e,t,n,i){t.child=e===null?_h(t,null,n,i):bl(t,e.child,n,i)}function zh(e,t,n,i,u){n=n.render;var o=t.ref;if("ref"in i){var h={};for(var p in i)p!=="ref"&&(h[p]=i[p])}else h=i;return Ca(t),i=lo(e,t,n,h,o,u),p=io(),e!==null&&!ot?(ro(e,t,u),On(e,t,u)):(Ce&&p&&Vu(t),t.flags|=1,mt(e,t,i,u),t.child)}function jh(e,t,n,i,u){if(e===null){var o=n.type;return typeof o=="function"&&!ku(o)&&o.defaultProps===void 0&&n.compare===null?(t.tag=15,t.type=o,Bh(e,t,o,i,u)):(e=wr(n.type,null,i,t,t.mode,u),e.ref=t.ref,e.return=t,t.child=e)}if(o=e.child,!Co(e,u)){var h=o.memoizedProps;if(n=n.compare,n=n!==null?n:Il,n(h,i)&&e.ref===t.ref)return On(e,t,u)}return t.flags|=1,e=xn(o,i),e.ref=t.ref,e.return=t,t.child=e}function Bh(e,t,n,i,u){if(e!==null){var o=e.memoizedProps;if(Il(o,i)&&e.ref===t.ref)if(ot=!1,t.pendingProps=i=o,Co(e,u))(e.flags&131072)!==0&&(ot=!0);else return t.lanes=e.lanes,On(e,t,u)}return _o(e,t,n,i,u)}function Lh(e,t,n){var i=t.pendingProps,u=i.children,o=e!==null?e.memoizedState:null;if(i.mode==="hidden"){if((t.flags&128)!==0){if(i=o!==null?o.baseLanes|n:n,e!==null){for(u=t.child=e.child,o=0;u!==null;)o=o|u.lanes|u.childLanes,u=u.sibling;t.childLanes=o&~i}else t.childLanes=0,t.child=null;return kh(e,t,i,n)}if((n&536870912)!==0)t.memoizedState={baseLanes:0,cachePool:null},e!==null&&Nr(t,o!==null?o.cachePool:null),o!==null?Bd(t,o):to(),wh(t);else return t.lanes=t.childLanes=536870912,kh(e,t,o!==null?o.baseLanes|n:n,n)}else o!==null?(Nr(t,o.cachePool),Bd(t,o),$n(),t.memoizedState=null):(e!==null&&Nr(t,null),to(),$n());return mt(e,t,u,n),t.child}function kh(e,t,n,i){var u=Ju();return u=u===null?null:{parent:rt._currentValue,pool:u},t.memoizedState={baseLanes:n,cachePool:u},e!==null&&Nr(t,null),to(),wh(t),e!==null&&li(e,t,i,!0),null}function Zr(e,t){var n=t.ref;if(n===null)e!==null&&e.ref!==null&&(t.flags|=4194816);else{if(typeof n!="function"&&typeof n!="object")throw Error(s(284));(e===null||e.ref!==n)&&(t.flags|=4194816)}}function _o(e,t,n,i,u){return Ca(t),n=lo(e,t,n,i,void 0,u),i=io(),e!==null&&!ot?(ro(e,t,u),On(e,t,u)):(Ce&&i&&Vu(t),t.flags|=1,mt(e,t,n,u),t.child)}function qh(e,t,n,i,u,o){return Ca(t),t.updateQueue=null,n=kd(t,i,n,u),Ld(e),i=io(),e!==null&&!ot?(ro(e,t,o),On(e,t,o)):(Ce&&i&&Vu(t),t.flags|=1,mt(e,t,n,o),t.child)}function Hh(e,t,n,i,u){if(Ca(t),t.stateNode===null){var o=ol,h=n.contextType;typeof h=="object"&&h!==null&&(o=vt(h)),o=new n(i,o),t.memoizedState=o.state!==null&&o.state!==void 0?o.state:null,o.updater=xo,t.stateNode=o,o._reactInternals=t,o=t.stateNode,o.props=i,o.state=t.memoizedState,o.refs={},Wu(t),h=n.contextType,o.context=typeof h=="object"&&h!==null?vt(h):ol,o.state=t.memoizedState,h=n.getDerivedStateFromProps,typeof h=="function"&&(So(t,n,h,i),o.state=t.memoizedState),typeof n.getDerivedStateFromProps=="function"||typeof o.getSnapshotBeforeUpdate=="function"||typeof o.UNSAFE_componentWillMount!="function"&&typeof o.componentWillMount!="function"||(h=o.state,typeof o.componentWillMount=="function"&&o.componentWillMount(),typeof o.UNSAFE_componentWillMount=="function"&&o.UNSAFE_componentWillMount(),h!==o.state&&xo.enqueueReplaceState(o,o.state,null),fi(t,i,o,u),ci(),o.state=t.memoizedState),typeof o.componentDidMount=="function"&&(t.flags|=4194308),i=!0}else if(e===null){o=t.stateNode;var p=t.memoizedProps,b=Ua(n,p);o.props=b;var C=o.context,k=n.contextType;h=ol,typeof k=="object"&&k!==null&&(h=vt(k));var Y=n.getDerivedStateFromProps;k=typeof Y=="function"||typeof o.getSnapshotBeforeUpdate=="function",p=t.pendingProps!==p,k||typeof o.UNSAFE_componentWillReceiveProps!="function"&&typeof o.componentWillReceiveProps!="function"||(p||C!==h)&&Th(t,o,i,h),Qn=!1;var M=t.memoizedState;o.state=M,fi(t,i,o,u),ci(),C=t.memoizedState,p||M!==C||Qn?(typeof Y=="function"&&(So(t,n,Y,i),C=t.memoizedState),(b=Qn||Ah(t,n,b,i,M,C,h))?(k||typeof o.UNSAFE_componentWillMount!="function"&&typeof o.componentWillMount!="function"||(typeof o.componentWillMount=="function"&&o.componentWillMount(),typeof o.UNSAFE_componentWillMount=="function"&&o.UNSAFE_componentWillMount()),typeof o.componentDidMount=="function"&&(t.flags|=4194308)):(typeof o.componentDidMount=="function"&&(t.flags|=4194308),t.memoizedProps=i,t.memoizedState=C),o.props=i,o.state=C,o.context=h,i=b):(typeof o.componentDidMount=="function"&&(t.flags|=4194308),i=!1)}else{o=t.stateNode,Pu(e,t),h=t.memoizedProps,k=Ua(n,h),o.props=k,Y=t.pendingProps,M=o.context,C=n.contextType,b=ol,typeof C=="object"&&C!==null&&(b=vt(C)),p=n.getDerivedStateFromProps,(C=typeof p=="function"||typeof o.getSnapshotBeforeUpdate=="function")||typeof o.UNSAFE_componentWillReceiveProps!="function"&&typeof o.componentWillReceiveProps!="function"||(h!==Y||M!==b)&&Th(t,o,i,b),Qn=!1,M=t.memoizedState,o.state=M,fi(t,i,o,u),ci();var U=t.memoizedState;h!==Y||M!==U||Qn||e!==null&&e.dependencies!==null&&Rr(e.dependencies)?(typeof p=="function"&&(So(t,n,p,i),U=t.memoizedState),(k=Qn||Ah(t,n,k,i,M,U,b)||e!==null&&e.dependencies!==null&&Rr(e.dependencies))?(C||typeof o.UNSAFE_componentWillUpdate!="function"&&typeof o.componentWillUpdate!="function"||(typeof o.componentWillUpdate=="function"&&o.componentWillUpdate(i,U,b),typeof o.UNSAFE_componentWillUpdate=="function"&&o.UNSAFE_componentWillUpdate(i,U,b)),typeof o.componentDidUpdate=="function"&&(t.flags|=4),typeof o.getSnapshotBeforeUpdate=="function"&&(t.flags|=1024)):(typeof o.componentDidUpdate!="function"||h===e.memoizedProps&&M===e.memoizedState||(t.flags|=4),typeof o.getSnapshotBeforeUpdate!="function"||h===e.memoizedProps&&M===e.memoizedState||(t.flags|=1024),t.memoizedProps=i,t.memoizedState=U),o.props=i,o.state=U,o.context=b,i=k):(typeof o.componentDidUpdate!="function"||h===e.memoizedProps&&M===e.memoizedState||(t.flags|=4),typeof o.getSnapshotBeforeUpdate!="function"||h===e.memoizedProps&&M===e.memoizedState||(t.flags|=1024),i=!1)}return o=i,Zr(e,t),i=(t.flags&128)!==0,o||i?(o=t.stateNode,n=i&&typeof n.getDerivedStateFromError!="function"?null:o.render(),t.flags|=1,e!==null&&i?(t.child=bl(t,e.child,null,u),t.child=bl(t,null,n,u)):mt(e,t,n,u),t.memoizedState=o.state,e=t.child):e=On(e,t,u),e}function Vh(e,t,n,i){return ni(),t.flags|=256,mt(e,t,n,i),t.child}var wo={dehydrated:null,treeContext:null,retryLane:0,hydrationErrors:null};function Ao(e){return{baseLanes:e,cachePool:Od()}}function To(e,t,n){return e=e!==null?e.childLanes&~n:0,t&&(e|=Ft),e}function Yh(e,t,n){var i=t.pendingProps,u=!1,o=(t.flags&128)!==0,h;if((h=o)||(h=e!==null&&e.memoizedState===null?!1:(st.current&2)!==0),h&&(u=!0,t.flags&=-129),h=(t.flags&32)!==0,t.flags&=-33,e===null){if(Ce){if(u?Jn(t):$n(),Ce){var p=Fe,b;if(b=p){e:{for(b=p,p=un;b.nodeType!==8;){if(!p){p=null;break e}if(b=It(b.nextSibling),b===null){p=null;break e}}p=b}p!==null?(t.memoizedState={dehydrated:p,treeContext:Aa!==null?{id:En,overflow:_n}:null,retryLane:536870912,hydrationErrors:null},b=Ut(18,null,null,0),b.stateNode=p,b.return=t,t.child=b,xt=t,Fe=null,b=!0):b=!1}b||Oa(t)}if(p=t.memoizedState,p!==null&&(p=p.dehydrated,p!==null))return oc(p)?t.lanes=32:t.lanes=536870912,null;Rn(t)}return p=i.children,i=i.fallback,u?($n(),u=t.mode,p=Kr({mode:"hidden",children:p},u),i=wa(i,u,n,null),p.return=t,i.return=t,p.sibling=i,t.child=p,u=t.child,u.memoizedState=Ao(n),u.childLanes=To(e,h,n),t.memoizedState=wo,i):(Jn(t),Ro(t,p))}if(b=e.memoizedState,b!==null&&(p=b.dehydrated,p!==null)){if(o)t.flags&256?(Jn(t),t.flags&=-257,t=Oo(e,t,n)):t.memoizedState!==null?($n(),t.child=e.child,t.flags|=128,t=null):($n(),u=i.fallback,p=t.mode,i=Kr({mode:"visible",children:i.children},p),u=wa(u,p,n,null),u.flags|=2,i.return=t,u.return=t,i.sibling=u,t.child=i,bl(t,e.child,null,n),i=t.child,i.memoizedState=Ao(n),i.childLanes=To(e,h,n),t.memoizedState=wo,t=u);else if(Jn(t),oc(p)){if(h=p.nextSibling&&p.nextSibling.dataset,h)var C=h.dgst;h=C,i=Error(s(419)),i.stack="",i.digest=h,ai({value:i,source:null,stack:null}),t=Oo(e,t,n)}else if(ot||li(e,t,n,!1),h=(n&e.childLanes)!==0,ot||h){if(h=Ve,h!==null&&(i=n&-n,i=(i&42)!==0?1:ou(i),i=(i&(h.suspendedLanes|n))!==0?0:i,i!==0&&i!==b.retryLane))throw b.retryLane=i,ul(e,i),kt(h,e,i),Uh;p.data==="$?"||Zo(),t=Oo(e,t,n)}else p.data==="$?"?(t.flags|=192,t.child=e.child,t=null):(e=b.treeContext,Fe=It(p.nextSibling),xt=t,Ce=!0,Ra=null,un=!1,e!==null&&(Qt[Zt++]=En,Qt[Zt++]=_n,Qt[Zt++]=Aa,En=e.id,_n=e.overflow,Aa=t),t=Ro(t,i.children),t.flags|=4096);return t}return u?($n(),u=i.fallback,p=t.mode,b=e.child,C=b.sibling,i=xn(b,{mode:"hidden",children:i.children}),i.subtreeFlags=b.subtreeFlags&65011712,C!==null?u=xn(C,u):(u=wa(u,p,n,null),u.flags|=2),u.return=t,i.return=t,i.sibling=u,t.child=i,i=u,u=t.child,p=e.child.memoizedState,p===null?p=Ao(n):(b=p.cachePool,b!==null?(C=rt._currentValue,b=b.parent!==C?{parent:C,pool:C}:b):b=Od(),p={baseLanes:p.baseLanes|n,cachePool:b}),u.memoizedState=p,u.childLanes=To(e,h,n),t.memoizedState=wo,i):(Jn(t),n=e.child,e=n.sibling,n=xn(n,{mode:"visible",children:i.children}),n.return=t,n.sibling=null,e!==null&&(h=t.deletions,h===null?(t.deletions=[e],t.flags|=16):h.push(e)),t.child=n,t.memoizedState=null,n)}function Ro(e,t){return t=Kr({mode:"visible",children:t},e.mode),t.return=e,e.child=t}function Kr(e,t){return e=Ut(22,e,null,t),e.lanes=0,e.stateNode={_visibility:1,_pendingMarkers:null,_retryCache:null,_transitions:null},e}function Oo(e,t,n){return bl(t,e.child,null,n),e=Ro(t,t.pendingProps.children),e.flags|=2,t.memoizedState=null,e}function Xh(e,t,n){e.lanes|=t;var i=e.alternate;i!==null&&(i.lanes|=t),Qu(e.return,t,n)}function No(e,t,n,i,u){var o=e.memoizedState;o===null?e.memoizedState={isBackwards:t,rendering:null,renderingStartTime:0,last:i,tail:n,tailMode:u}:(o.isBackwards=t,o.rendering=null,o.renderingStartTime=0,o.last=i,o.tail=n,o.tailMode=u)}function Gh(e,t,n){var i=t.pendingProps,u=i.revealOrder,o=i.tail;if(mt(e,t,i.children,n),i=st.current,(i&2)!==0)i=i&1|2,t.flags|=128;else{if(e!==null&&(e.flags&128)!==0)e:for(e=t.child;e!==null;){if(e.tag===13)e.memoizedState!==null&&Xh(e,n,t);else if(e.tag===19)Xh(e,n,t);else if(e.child!==null){e.child.return=e,e=e.child;continue}if(e===t)break e;for(;e.sibling===null;){if(e.return===null||e.return===t)break e;e=e.return}e.sibling.return=e.return,e=e.sibling}i&=1}switch(W(st,i),u){case"forwards":for(n=t.child,u=null;n!==null;)e=n.alternate,e!==null&&Xr(e)===null&&(u=n),n=n.sibling;n=u,n===null?(u=t.child,t.child=null):(u=n.sibling,n.sibling=null),No(t,!1,u,n,o);break;case"backwards":for(n=null,u=t.child,t.child=null;u!==null;){if(e=u.alternate,e!==null&&Xr(e)===null){t.child=u;break}e=u.sibling,u.sibling=n,n=u,u=e}No(t,!0,n,null,o);break;case"together":No(t,!1,null,null,void 0);break;default:t.memoizedState=null}return t.child}function On(e,t,n){if(e!==null&&(t.dependencies=e.dependencies),ta|=t.lanes,(n&t.childLanes)===0)if(e!==null){if(li(e,t,n,!1),(n&t.childLanes)===0)return null}else return null;if(e!==null&&t.child!==e.child)throw Error(s(153));if(t.child!==null){for(e=t.child,n=xn(e,e.pendingProps),t.child=n,n.return=t;e.sibling!==null;)e=e.sibling,n=n.sibling=xn(e,e.pendingProps),n.return=t;n.sibling=null}return t.child}function Co(e,t){return(e.lanes&t)!==0?!0:(e=e.dependencies,!!(e!==null&&Rr(e)))}function Hg(e,t,n){switch(t.tag){case 3:ke(t,t.stateNode.containerInfo),Gn(t,rt,e.memoizedState.cache),ni();break;case 27:case 5:ga(t);break;case 4:ke(t,t.stateNode.containerInfo);break;case 10:Gn(t,t.type,t.memoizedProps.value);break;case 13:var i=t.memoizedState;if(i!==null)return i.dehydrated!==null?(Jn(t),t.flags|=128,null):(n&t.child.childLanes)!==0?Yh(e,t,n):(Jn(t),e=On(e,t,n),e!==null?e.sibling:null);Jn(t);break;case 19:var u=(e.flags&128)!==0;if(i=(n&t.childLanes)!==0,i||(li(e,t,n,!1),i=(n&t.childLanes)!==0),u){if(i)return Gh(e,t,n);t.flags|=128}if(u=t.memoizedState,u!==null&&(u.rendering=null,u.tail=null,u.lastEffect=null),W(st,st.current),i)break;return null;case 22:case 23:return t.lanes=0,Lh(e,t,n);case 24:Gn(t,rt,e.memoizedState.cache)}return On(e,t,n)}function Qh(e,t,n){if(e!==null)if(e.memoizedProps!==t.pendingProps)ot=!0;else{if(!Co(e,n)&&(t.flags&128)===0)return ot=!1,Hg(e,t,n);ot=(e.flags&131072)!==0}else ot=!1,Ce&&(t.flags&1048576)!==0&&xd(t,Tr,t.index);switch(t.lanes=0,t.tag){case 16:e:{e=t.pendingProps;var i=t.elementType,u=i._init;if(i=u(i._payload),t.type=i,typeof i=="function")ku(i)?(e=Ua(i,e),t.tag=1,t=Hh(null,t,i,e,n)):(t.tag=0,t=_o(null,t,i,e,n));else{if(i!=null){if(u=i.$$typeof,u===ie){t.tag=11,t=zh(null,t,i,e,n);break e}else if(u===ye){t.tag=14,t=jh(null,t,i,e,n);break e}}throw t=Me(i)||i,Error(s(306,t,""))}}return t;case 0:return _o(e,t,t.type,t.pendingProps,n);case 1:return i=t.type,u=Ua(i,t.pendingProps),Hh(e,t,i,u,n);case 3:e:{if(ke(t,t.stateNode.containerInfo),e===null)throw Error(s(387));i=t.pendingProps;var o=t.memoizedState;u=o.element,Pu(e,t),fi(t,i,null,n);var h=t.memoizedState;if(i=h.cache,Gn(t,rt,i),i!==o.cache&&Zu(t,[rt],n,!0),ci(),i=h.element,o.isDehydrated)if(o={element:i,isDehydrated:!1,cache:h.cache},t.updateQueue.baseState=o,t.memoizedState=o,t.flags&256){t=Vh(e,t,i,n);break e}else if(i!==u){u=Xt(Error(s(424)),t),ai(u),t=Vh(e,t,i,n);break e}else{switch(e=t.stateNode.containerInfo,e.nodeType){case 9:e=e.body;break;default:e=e.nodeName==="HTML"?e.ownerDocument.body:e}for(Fe=It(e.firstChild),xt=t,Ce=!0,Ra=null,un=!0,n=_h(t,null,i,n),t.child=n;n;)n.flags=n.flags&-3|4096,n=n.sibling}else{if(ni(),i===u){t=On(e,t,n);break e}mt(e,t,i,n)}t=t.child}return t;case 26:return Zr(e,t),e===null?(n=Jm(t.type,null,t.pendingProps,null))?t.memoizedState=n:Ce||(n=t.type,e=t.pendingProps,i=ss(fe.current).createElement(n),i[gt]=t,i[wt]=e,pt(i,n,e),ut(i),t.stateNode=i):t.memoizedState=Jm(t.type,e.memoizedProps,t.pendingProps,e.memoizedState),null;case 27:return ga(t),e===null&&Ce&&(i=t.stateNode=Zm(t.type,t.pendingProps,fe.current),xt=t,un=!0,u=Fe,ia(t.type)?(cc=u,Fe=It(i.firstChild)):Fe=u),mt(e,t,t.pendingProps.children,n),Zr(e,t),e===null&&(t.flags|=4194304),t.child;case 5:return e===null&&Ce&&((u=i=Fe)&&(i=mv(i,t.type,t.pendingProps,un),i!==null?(t.stateNode=i,xt=t,Fe=It(i.firstChild),un=!1,u=!0):u=!1),u||Oa(t)),ga(t),u=t.type,o=t.pendingProps,h=e!==null?e.memoizedProps:null,i=o.children,rc(u,o)?i=null:h!==null&&rc(u,h)&&(t.flags|=32),t.memoizedState!==null&&(u=lo(e,t,Mg,null,null,n),Ui._currentValue=u),Zr(e,t),mt(e,t,i,n),t.child;case 6:return e===null&&Ce&&((e=n=Fe)&&(n=yv(n,t.pendingProps,un),n!==null?(t.stateNode=n,xt=t,Fe=null,e=!0):e=!1),e||Oa(t)),null;case 13:return Yh(e,t,n);case 4:return ke(t,t.stateNode.containerInfo),i=t.pendingProps,e===null?t.child=bl(t,null,i,n):mt(e,t,i,n),t.child;case 11:return zh(e,t,t.type,t.pendingProps,n);case 7:return mt(e,t,t.pendingProps,n),t.child;case 8:return mt(e,t,t.pendingProps.children,n),t.child;case 12:return mt(e,t,t.pendingProps.children,n),t.child;case 10:return i=t.pendingProps,Gn(t,t.type,i.value),mt(e,t,i.children,n),t.child;case 9:return u=t.type._context,i=t.pendingProps.children,Ca(t),u=vt(u),i=i(u),t.flags|=1,mt(e,t,i,n),t.child;case 14:return jh(e,t,t.type,t.pendingProps,n);case 15:return Bh(e,t,t.type,t.pendingProps,n);case 19:return Gh(e,t,n);case 31:return i=t.pendingProps,n=t.mode,i={mode:i.mode,children:i.children},e===null?(n=Kr(i,n),n.ref=t.ref,t.child=n,n.return=t,t=n):(n=xn(e.child,i),n.ref=t.ref,t.child=n,n.return=t,t=n),t;case 22:return Lh(e,t,n);case 24:return Ca(t),i=vt(rt),e===null?(u=Ju(),u===null&&(u=Ve,o=Ku(),u.pooledCache=o,o.refCount++,o!==null&&(u.pooledCacheLanes|=n),u=o),t.memoizedState={parent:i,cache:u},Wu(t),Gn(t,rt,u)):((e.lanes&n)!==0&&(Pu(e,t),fi(t,null,null,n),ci()),u=e.memoizedState,o=t.memoizedState,u.parent!==i?(u={parent:i,cache:i},t.memoizedState=u,t.lanes===0&&(t.memoizedState=t.updateQueue.baseState=u),Gn(t,rt,i)):(i=o.cache,Gn(t,rt,i),i!==u.cache&&Zu(t,[rt],n,!0))),mt(e,t,t.pendingProps.children,n),t.child;case 29:throw t.pendingProps}throw Error(s(156,t.tag))}function Nn(e){e.flags|=4}function Zh(e,t){if(t.type!=="stylesheet"||(t.state.loading&4)!==0)e.flags&=-16777217;else if(e.flags|=16777216,!ey(t)){if(t=Kt.current,t!==null&&((Re&4194048)===Re?on!==null:(Re&62914560)!==Re&&(Re&536870912)===0||t!==on))throw ui=$u,Nd;e.flags|=8192}}function Fr(e,t){t!==null&&(e.flags|=4),e.flags&16384&&(t=e.tag!==22?wf():536870912,e.lanes|=t,_l|=t)}function vi(e,t){if(!Ce)switch(e.tailMode){case"hidden":t=e.tail;for(var n=null;t!==null;)t.alternate!==null&&(n=t),t=t.sibling;n===null?e.tail=null:n.sibling=null;break;case"collapsed":n=e.tail;for(var i=null;n!==null;)n.alternate!==null&&(i=n),n=n.sibling;i===null?t||e.tail===null?e.tail=null:e.tail.sibling=null:i.sibling=null}}function Ke(e){var t=e.alternate!==null&&e.alternate.child===e.child,n=0,i=0;if(t)for(var u=e.child;u!==null;)n|=u.lanes|u.childLanes,i|=u.subtreeFlags&65011712,i|=u.flags&65011712,u.return=e,u=u.sibling;else for(u=e.child;u!==null;)n|=u.lanes|u.childLanes,i|=u.subtreeFlags,i|=u.flags,u.return=e,u=u.sibling;return e.subtreeFlags|=i,e.childLanes=n,t}function Vg(e,t,n){var i=t.pendingProps;switch(Yu(t),t.tag){case 31:case 16:case 15:case 0:case 11:case 7:case 8:case 12:case 9:case 14:return Ke(t),null;case 1:return Ke(t),null;case 3:return n=t.stateNode,i=null,e!==null&&(i=e.memoizedState.cache),t.memoizedState.cache!==i&&(t.flags|=2048),An(rt),qt(),n.pendingContext&&(n.context=n.pendingContext,n.pendingContext=null),(e===null||e.child===null)&&(ti(t)?Nn(t):e===null||e.memoizedState.isDehydrated&&(t.flags&256)===0||(t.flags|=1024,wd())),Ke(t),null;case 26:return n=t.memoizedState,e===null?(Nn(t),n!==null?(Ke(t),Zh(t,n)):(Ke(t),t.flags&=-16777217)):n?n!==e.memoizedState?(Nn(t),Ke(t),Zh(t,n)):(Ke(t),t.flags&=-16777217):(e.memoizedProps!==i&&Nn(t),Ke(t),t.flags&=-16777217),null;case 27:Ga(t),n=fe.current;var u=t.type;if(e!==null&&t.stateNode!=null)e.memoizedProps!==i&&Nn(t);else{if(!i){if(t.stateNode===null)throw Error(s(166));return Ke(t),null}e=re.current,ti(t)?Ed(t):(e=Zm(u,i,n),t.stateNode=e,Nn(t))}return Ke(t),null;case 5:if(Ga(t),n=t.type,e!==null&&t.stateNode!=null)e.memoizedProps!==i&&Nn(t);else{if(!i){if(t.stateNode===null)throw Error(s(166));return Ke(t),null}if(e=re.current,ti(t))Ed(t);else{switch(u=ss(fe.current),e){case 1:e=u.createElementNS("http://www.w3.org/2000/svg",n);break;case 2:e=u.createElementNS("http://www.w3.org/1998/Math/MathML",n);break;default:switch(n){case"svg":e=u.createElementNS("http://www.w3.org/2000/svg",n);break;case"math":e=u.createElementNS("http://www.w3.org/1998/Math/MathML",n);break;case"script":e=u.createElement("div"),e.innerHTML="<script><\/script>",e=e.removeChild(e.firstChild);break;case"select":e=typeof i.is=="string"?u.createElement("select",{is:i.is}):u.createElement("select"),i.multiple?e.multiple=!0:i.size&&(e.size=i.size);break;default:e=typeof i.is=="string"?u.createElement(n,{is:i.is}):u.createElement(n)}}e[gt]=t,e[wt]=i;e:for(u=t.child;u!==null;){if(u.tag===5||u.tag===6)e.appendChild(u.stateNode);else if(u.tag!==4&&u.tag!==27&&u.child!==null){u.child.return=u,u=u.child;continue}if(u===t)break e;for(;u.sibling===null;){if(u.return===null||u.return===t)break e;u=u.return}u.sibling.return=u.return,u=u.sibling}t.stateNode=e;e:switch(pt(e,n,i),n){case"button":case"input":case"select":case"textarea":e=!!i.autoFocus;break e;case"img":e=!0;break e;default:e=!1}e&&Nn(t)}}return Ke(t),t.flags&=-16777217,null;case 6:if(e&&t.stateNode!=null)e.memoizedProps!==i&&Nn(t);else{if(typeof i!="string"&&t.stateNode===null)throw Error(s(166));if(e=fe.current,ti(t)){if(e=t.stateNode,n=t.memoizedProps,i=null,u=xt,u!==null)switch(u.tag){case 27:case 5:i=u.memoizedProps}e[gt]=t,e=!!(e.nodeValue===n||i!==null&&i.suppressHydrationWarning===!0||qm(e.nodeValue,n)),e||Oa(t)}else e=ss(e).createTextNode(i),e[gt]=t,t.stateNode=e}return Ke(t),null;case 13:if(i=t.memoizedState,e===null||e.memoizedState!==null&&e.memoizedState.dehydrated!==null){if(u=ti(t),i!==null&&i.dehydrated!==null){if(e===null){if(!u)throw Error(s(318));if(u=t.memoizedState,u=u!==null?u.dehydrated:null,!u)throw Error(s(317));u[gt]=t}else ni(),(t.flags&128)===0&&(t.memoizedState=null),t.flags|=4;Ke(t),u=!1}else u=wd(),e!==null&&e.memoizedState!==null&&(e.memoizedState.hydrationErrors=u),u=!0;if(!u)return t.flags&256?(Rn(t),t):(Rn(t),null)}if(Rn(t),(t.flags&128)!==0)return t.lanes=n,t;if(n=i!==null,e=e!==null&&e.memoizedState!==null,n){i=t.child,u=null,i.alternate!==null&&i.alternate.memoizedState!==null&&i.alternate.memoizedState.cachePool!==null&&(u=i.alternate.memoizedState.cachePool.pool);var o=null;i.memoizedState!==null&&i.memoizedState.cachePool!==null&&(o=i.memoizedState.cachePool.pool),o!==u&&(i.flags|=2048)}return n!==e&&n&&(t.child.flags|=8192),Fr(t,t.updateQueue),Ke(t),null;case 4:return qt(),e===null&&tc(t.stateNode.containerInfo),Ke(t),null;case 10:return An(t.type),Ke(t),null;case 19:if(I(st),u=t.memoizedState,u===null)return Ke(t),null;if(i=(t.flags&128)!==0,o=u.rendering,o===null)if(i)vi(u,!1);else{if(Je!==0||e!==null&&(e.flags&128)!==0)for(e=t.child;e!==null;){if(o=Xr(e),o!==null){for(t.flags|=128,vi(u,!1),e=o.updateQueue,t.updateQueue=e,Fr(t,e),t.subtreeFlags=0,e=n,n=t.child;n!==null;)Sd(n,e),n=n.sibling;return W(st,st.current&1|2),t.child}e=e.sibling}u.tail!==null&&Ht()>Wr&&(t.flags|=128,i=!0,vi(u,!1),t.lanes=4194304)}else{if(!i)if(e=Xr(o),e!==null){if(t.flags|=128,i=!0,e=e.updateQueue,t.updateQueue=e,Fr(t,e),vi(u,!0),u.tail===null&&u.tailMode==="hidden"&&!o.alternate&&!Ce)return Ke(t),null}else 2*Ht()-u.renderingStartTime>Wr&&n!==536870912&&(t.flags|=128,i=!0,vi(u,!1),t.lanes=4194304);u.isBackwards?(o.sibling=t.child,t.child=o):(e=u.last,e!==null?e.sibling=o:t.child=o,u.last=o)}return u.tail!==null?(t=u.tail,u.rendering=t,u.tail=t.sibling,u.renderingStartTime=Ht(),t.sibling=null,e=st.current,W(st,i?e&1|2:e&1),t):(Ke(t),null);case 22:case 23:return Rn(t),no(),i=t.memoizedState!==null,e!==null?e.memoizedState!==null!==i&&(t.flags|=8192):i&&(t.flags|=8192),i?(n&536870912)!==0&&(t.flags&128)===0&&(Ke(t),t.subtreeFlags&6&&(t.flags|=8192)):Ke(t),n=t.updateQueue,n!==null&&Fr(t,n.retryQueue),n=null,e!==null&&e.memoizedState!==null&&e.memoizedState.cachePool!==null&&(n=e.memoizedState.cachePool.pool),i=null,t.memoizedState!==null&&t.memoizedState.cachePool!==null&&(i=t.memoizedState.cachePool.pool),i!==n&&(t.flags|=2048),e!==null&&I(Da),null;case 24:return n=null,e!==null&&(n=e.memoizedState.cache),t.memoizedState.cache!==n&&(t.flags|=2048),An(rt),Ke(t),null;case 25:return null;case 30:return null}throw Error(s(156,t.tag))}function Yg(e,t){switch(Yu(t),t.tag){case 1:return e=t.flags,e&65536?(t.flags=e&-65537|128,t):null;case 3:return An(rt),qt(),e=t.flags,(e&65536)!==0&&(e&128)===0?(t.flags=e&-65537|128,t):null;case 26:case 27:case 5:return Ga(t),null;case 13:if(Rn(t),e=t.memoizedState,e!==null&&e.dehydrated!==null){if(t.alternate===null)throw Error(s(340));ni()}return e=t.flags,e&65536?(t.flags=e&-65537|128,t):null;case 19:return I(st),null;case 4:return qt(),null;case 10:return An(t.type),null;case 22:case 23:return Rn(t),no(),e!==null&&I(Da),e=t.flags,e&65536?(t.flags=e&-65537|128,t):null;case 24:return An(rt),null;case 25:return null;default:return null}}function Kh(e,t){switch(Yu(t),t.tag){case 3:An(rt),qt();break;case 26:case 27:case 5:Ga(t);break;case 4:qt();break;case 13:Rn(t);break;case 19:I(st);break;case 10:An(t.type);break;case 22:case 23:Rn(t),no(),e!==null&&I(Da);break;case 24:An(rt)}}function bi(e,t){try{var n=t.updateQueue,i=n!==null?n.lastEffect:null;if(i!==null){var u=i.next;n=u;do{if((n.tag&e)===e){i=void 0;var o=n.create,h=n.inst;i=o(),h.destroy=i}n=n.next}while(n!==u)}}catch(p){qe(t,t.return,p)}}function Wn(e,t,n){try{var i=t.updateQueue,u=i!==null?i.lastEffect:null;if(u!==null){var o=u.next;i=o;do{if((i.tag&e)===e){var h=i.inst,p=h.destroy;if(p!==void 0){h.destroy=void 0,u=t;var b=n,C=p;try{C()}catch(k){qe(u,b,k)}}}i=i.next}while(i!==o)}}catch(k){qe(t,t.return,k)}}function Fh(e){var t=e.updateQueue;if(t!==null){var n=e.stateNode;try{jd(t,n)}catch(i){qe(e,e.return,i)}}}function Jh(e,t,n){n.props=Ua(e.type,e.memoizedProps),n.state=e.memoizedState;try{n.componentWillUnmount()}catch(i){qe(e,t,i)}}function Si(e,t){try{var n=e.ref;if(n!==null){switch(e.tag){case 26:case 27:case 5:var i=e.stateNode;break;case 30:i=e.stateNode;break;default:i=e.stateNode}typeof n=="function"?e.refCleanup=n(i):n.current=i}}catch(u){qe(e,t,u)}}function cn(e,t){var n=e.ref,i=e.refCleanup;if(n!==null)if(typeof i=="function")try{i()}catch(u){qe(e,t,u)}finally{e.refCleanup=null,e=e.alternate,e!=null&&(e.refCleanup=null)}else if(typeof n=="function")try{n(null)}catch(u){qe(e,t,u)}else n.current=null}function $h(e){var t=e.type,n=e.memoizedProps,i=e.stateNode;try{e:switch(t){case"button":case"input":case"select":case"textarea":n.autoFocus&&i.focus();break e;case"img":n.src?i.src=n.src:n.srcSet&&(i.srcset=n.srcSet)}}catch(u){qe(e,e.return,u)}}function Do(e,t,n){try{var i=e.stateNode;ov(i,e.type,n,t),i[wt]=t}catch(u){qe(e,e.return,u)}}function Wh(e){return e.tag===5||e.tag===3||e.tag===26||e.tag===27&&ia(e.type)||e.tag===4}function Mo(e){e:for(;;){for(;e.sibling===null;){if(e.return===null||Wh(e.return))return null;e=e.return}for(e.sibling.return=e.return,e=e.sibling;e.tag!==5&&e.tag!==6&&e.tag!==18;){if(e.tag===27&&ia(e.type)||e.flags&2||e.child===null||e.tag===4)continue e;e.child.return=e,e=e.child}if(!(e.flags&2))return e.stateNode}}function Uo(e,t,n){var i=e.tag;if(i===5||i===6)e=e.stateNode,t?(n.nodeType===9?n.body:n.nodeName==="HTML"?n.ownerDocument.body:n).insertBefore(e,t):(t=n.nodeType===9?n.body:n.nodeName==="HTML"?n.ownerDocument.body:n,t.appendChild(e),n=n._reactRootContainer,n!=null||t.onclick!==null||(t.onclick=rs));else if(i!==4&&(i===27&&ia(e.type)&&(n=e.stateNode,t=null),e=e.child,e!==null))for(Uo(e,t,n),e=e.sibling;e!==null;)Uo(e,t,n),e=e.sibling}function Jr(e,t,n){var i=e.tag;if(i===5||i===6)e=e.stateNode,t?n.insertBefore(e,t):n.appendChild(e);else if(i!==4&&(i===27&&ia(e.type)&&(n=e.stateNode),e=e.child,e!==null))for(Jr(e,t,n),e=e.sibling;e!==null;)Jr(e,t,n),e=e.sibling}function Ph(e){var t=e.stateNode,n=e.memoizedProps;try{for(var i=e.type,u=t.attributes;u.length;)t.removeAttributeNode(u[0]);pt(t,i,n),t[gt]=e,t[wt]=n}catch(o){qe(e,e.return,o)}}var Cn=!1,We=!1,zo=!1,Ih=typeof WeakSet=="function"?WeakSet:Set,ct=null;function Xg(e,t){if(e=e.containerInfo,lc=hs,e=cd(e),Du(e)){if("selectionStart"in e)var n={start:e.selectionStart,end:e.selectionEnd};else e:{n=(n=e.ownerDocument)&&n.defaultView||window;var i=n.getSelection&&n.getSelection();if(i&&i.rangeCount!==0){n=i.anchorNode;var u=i.anchorOffset,o=i.focusNode;i=i.focusOffset;try{n.nodeType,o.nodeType}catch{n=null;break e}var h=0,p=-1,b=-1,C=0,k=0,Y=e,M=null;t:for(;;){for(var U;Y!==n||u!==0&&Y.nodeType!==3||(p=h+u),Y!==o||i!==0&&Y.nodeType!==3||(b=h+i),Y.nodeType===3&&(h+=Y.nodeValue.length),(U=Y.firstChild)!==null;)M=Y,Y=U;for(;;){if(Y===e)break t;if(M===n&&++C===u&&(p=h),M===o&&++k===i&&(b=h),(U=Y.nextSibling)!==null)break;Y=M,M=Y.parentNode}Y=U}n=p===-1||b===-1?null:{start:p,end:b}}else n=null}n=n||{start:0,end:0}}else n=null;for(ic={focusedElem:e,selectionRange:n},hs=!1,ct=t;ct!==null;)if(t=ct,e=t.child,(t.subtreeFlags&1024)!==0&&e!==null)e.return=t,ct=e;else for(;ct!==null;){switch(t=ct,o=t.alternate,e=t.flags,t.tag){case 0:break;case 11:case 15:break;case 1:if((e&1024)!==0&&o!==null){e=void 0,n=t,u=o.memoizedProps,o=o.memoizedState,i=n.stateNode;try{var ce=Ua(n.type,u,n.elementType===n.type);e=i.getSnapshotBeforeUpdate(ce,o),i.__reactInternalSnapshotBeforeUpdate=e}catch(se){qe(n,n.return,se)}}break;case 3:if((e&1024)!==0){if(e=t.stateNode.containerInfo,n=e.nodeType,n===9)uc(e);else if(n===1)switch(e.nodeName){case"HEAD":case"HTML":case"BODY":uc(e);break;default:e.textContent=""}}break;case 5:case 26:case 27:case 6:case 4:case 17:break;default:if((e&1024)!==0)throw Error(s(163))}if(e=t.sibling,e!==null){e.return=t.return,ct=e;break}ct=t.return}}function em(e,t,n){var i=n.flags;switch(n.tag){case 0:case 11:case 15:Pn(e,n),i&4&&bi(5,n);break;case 1:if(Pn(e,n),i&4)if(e=n.stateNode,t===null)try{e.componentDidMount()}catch(h){qe(n,n.return,h)}else{var u=Ua(n.type,t.memoizedProps);t=t.memoizedState;try{e.componentDidUpdate(u,t,e.__reactInternalSnapshotBeforeUpdate)}catch(h){qe(n,n.return,h)}}i&64&&Fh(n),i&512&&Si(n,n.return);break;case 3:if(Pn(e,n),i&64&&(e=n.updateQueue,e!==null)){if(t=null,n.child!==null)switch(n.child.tag){case 27:case 5:t=n.child.stateNode;break;case 1:t=n.child.stateNode}try{jd(e,t)}catch(h){qe(n,n.return,h)}}break;case 27:t===null&&i&4&&Ph(n);case 26:case 5:Pn(e,n),t===null&&i&4&&$h(n),i&512&&Si(n,n.return);break;case 12:Pn(e,n);break;case 13:Pn(e,n),i&4&&am(e,n),i&64&&(e=n.memoizedState,e!==null&&(e=e.dehydrated,e!==null&&(n=Pg.bind(null,n),pv(e,n))));break;case 22:if(i=n.memoizedState!==null||Cn,!i){t=t!==null&&t.memoizedState!==null||We,u=Cn;var o=We;Cn=i,(We=t)&&!o?In(e,n,(n.subtreeFlags&8772)!==0):Pn(e,n),Cn=u,We=o}break;case 30:break;default:Pn(e,n)}}function tm(e){var t=e.alternate;t!==null&&(e.alternate=null,tm(t)),e.child=null,e.deletions=null,e.sibling=null,e.tag===5&&(t=e.stateNode,t!==null&&du(t)),e.stateNode=null,e.return=null,e.dependencies=null,e.memoizedProps=null,e.memoizedState=null,e.pendingProps=null,e.stateNode=null,e.updateQueue=null}var Ye=null,Rt=!1;function Dn(e,t,n){for(n=n.child;n!==null;)nm(e,t,n),n=n.sibling}function nm(e,t,n){if(de&&typeof de.onCommitFiberUnmount=="function")try{de.onCommitFiberUnmount(ne,n)}catch{}switch(n.tag){case 26:We||cn(n,t),Dn(e,t,n),n.memoizedState?n.memoizedState.count--:n.stateNode&&(n=n.stateNode,n.parentNode.removeChild(n));break;case 27:We||cn(n,t);var i=Ye,u=Rt;ia(n.type)&&(Ye=n.stateNode,Rt=!1),Dn(e,t,n),Ni(n.stateNode),Ye=i,Rt=u;break;case 5:We||cn(n,t);case 6:if(i=Ye,u=Rt,Ye=null,Dn(e,t,n),Ye=i,Rt=u,Ye!==null)if(Rt)try{(Ye.nodeType===9?Ye.body:Ye.nodeName==="HTML"?Ye.ownerDocument.body:Ye).removeChild(n.stateNode)}catch(o){qe(n,t,o)}else try{Ye.removeChild(n.stateNode)}catch(o){qe(n,t,o)}break;case 18:Ye!==null&&(Rt?(e=Ye,Gm(e.nodeType===9?e.body:e.nodeName==="HTML"?e.ownerDocument.body:e,n.stateNode),Li(e)):Gm(Ye,n.stateNode));break;case 4:i=Ye,u=Rt,Ye=n.stateNode.containerInfo,Rt=!0,Dn(e,t,n),Ye=i,Rt=u;break;case 0:case 11:case 14:case 15:We||Wn(2,n,t),We||Wn(4,n,t),Dn(e,t,n);break;case 1:We||(cn(n,t),i=n.stateNode,typeof i.componentWillUnmount=="function"&&Jh(n,t,i)),Dn(e,t,n);break;case 21:Dn(e,t,n);break;case 22:We=(i=We)||n.memoizedState!==null,Dn(e,t,n),We=i;break;default:Dn(e,t,n)}}function am(e,t){if(t.memoizedState===null&&(e=t.alternate,e!==null&&(e=e.memoizedState,e!==null&&(e=e.dehydrated,e!==null))))try{Li(e)}catch(n){qe(t,t.return,n)}}function Gg(e){switch(e.tag){case 13:case 19:var t=e.stateNode;return t===null&&(t=e.stateNode=new Ih),t;case 22:return e=e.stateNode,t=e._retryCache,t===null&&(t=e._retryCache=new Ih),t;default:throw Error(s(435,e.tag))}}function jo(e,t){var n=Gg(e);t.forEach(function(i){var u=Ig.bind(null,e,i);n.has(i)||(n.add(i),i.then(u,u))})}function zt(e,t){var n=t.deletions;if(n!==null)for(var i=0;i<n.length;i++){var u=n[i],o=e,h=t,p=h;e:for(;p!==null;){switch(p.tag){case 27:if(ia(p.type)){Ye=p.stateNode,Rt=!1;break e}break;case 5:Ye=p.stateNode,Rt=!1;break e;case 3:case 4:Ye=p.stateNode.containerInfo,Rt=!0;break e}p=p.return}if(Ye===null)throw Error(s(160));nm(o,h,u),Ye=null,Rt=!1,o=u.alternate,o!==null&&(o.return=null),u.return=null}if(t.subtreeFlags&13878)for(t=t.child;t!==null;)lm(t,e),t=t.sibling}var Pt=null;function lm(e,t){var n=e.alternate,i=e.flags;switch(e.tag){case 0:case 11:case 14:case 15:zt(t,e),jt(e),i&4&&(Wn(3,e,e.return),bi(3,e),Wn(5,e,e.return));break;case 1:zt(t,e),jt(e),i&512&&(We||n===null||cn(n,n.return)),i&64&&Cn&&(e=e.updateQueue,e!==null&&(i=e.callbacks,i!==null&&(n=e.shared.hiddenCallbacks,e.shared.hiddenCallbacks=n===null?i:n.concat(i))));break;case 26:var u=Pt;if(zt(t,e),jt(e),i&512&&(We||n===null||cn(n,n.return)),i&4){var o=n!==null?n.memoizedState:null;if(i=e.memoizedState,n===null)if(i===null)if(e.stateNode===null){e:{i=e.type,n=e.memoizedProps,u=u.ownerDocument||u;t:switch(i){case"title":o=u.getElementsByTagName("title")[0],(!o||o[Gl]||o[gt]||o.namespaceURI==="http://www.w3.org/2000/svg"||o.hasAttribute("itemprop"))&&(o=u.createElement(i),u.head.insertBefore(o,u.querySelector("head > title"))),pt(o,i,n),o[gt]=e,ut(o),i=o;break e;case"link":var h=Pm("link","href",u).get(i+(n.href||""));if(h){for(var p=0;p<h.length;p++)if(o=h[p],o.getAttribute("href")===(n.href==null||n.href===""?null:n.href)&&o.getAttribute("rel")===(n.rel==null?null:n.rel)&&o.getAttribute("title")===(n.title==null?null:n.title)&&o.getAttribute("crossorigin")===(n.crossOrigin==null?null:n.crossOrigin)){h.splice(p,1);break t}}o=u.createElement(i),pt(o,i,n),u.head.appendChild(o);break;case"meta":if(h=Pm("meta","content",u).get(i+(n.content||""))){for(p=0;p<h.length;p++)if(o=h[p],o.getAttribute("content")===(n.content==null?null:""+n.content)&&o.getAttribute("name")===(n.name==null?null:n.name)&&o.getAttribute("property")===(n.property==null?null:n.property)&&o.getAttribute("http-equiv")===(n.httpEquiv==null?null:n.httpEquiv)&&o.getAttribute("charset")===(n.charSet==null?null:n.charSet)){h.splice(p,1);break t}}o=u.createElement(i),pt(o,i,n),u.head.appendChild(o);break;default:throw Error(s(468,i))}o[gt]=e,ut(o),i=o}e.stateNode=i}else Im(u,e.type,e.stateNode);else e.stateNode=Wm(u,i,e.memoizedProps);else o!==i?(o===null?n.stateNode!==null&&(n=n.stateNode,n.parentNode.removeChild(n)):o.count--,i===null?Im(u,e.type,e.stateNode):Wm(u,i,e.memoizedProps)):i===null&&e.stateNode!==null&&Do(e,e.memoizedProps,n.memoizedProps)}break;case 27:zt(t,e),jt(e),i&512&&(We||n===null||cn(n,n.return)),n!==null&&i&4&&Do(e,e.memoizedProps,n.memoizedProps);break;case 5:if(zt(t,e),jt(e),i&512&&(We||n===null||cn(n,n.return)),e.flags&32){u=e.stateNode;try{tl(u,"")}catch(U){qe(e,e.return,U)}}i&4&&e.stateNode!=null&&(u=e.memoizedProps,Do(e,u,n!==null?n.memoizedProps:u)),i&1024&&(zo=!0);break;case 6:if(zt(t,e),jt(e),i&4){if(e.stateNode===null)throw Error(s(162));i=e.memoizedProps,n=e.stateNode;try{n.nodeValue=i}catch(U){qe(e,e.return,U)}}break;case 3:if(cs=null,u=Pt,Pt=us(t.containerInfo),zt(t,e),Pt=u,jt(e),i&4&&n!==null&&n.memoizedState.isDehydrated)try{Li(t.containerInfo)}catch(U){qe(e,e.return,U)}zo&&(zo=!1,im(e));break;case 4:i=Pt,Pt=us(e.stateNode.containerInfo),zt(t,e),jt(e),Pt=i;break;case 12:zt(t,e),jt(e);break;case 13:zt(t,e),jt(e),e.child.flags&8192&&e.memoizedState!==null!=(n!==null&&n.memoizedState!==null)&&(Vo=Ht()),i&4&&(i=e.updateQueue,i!==null&&(e.updateQueue=null,jo(e,i)));break;case 22:u=e.memoizedState!==null;var b=n!==null&&n.memoizedState!==null,C=Cn,k=We;if(Cn=C||u,We=k||b,zt(t,e),We=k,Cn=C,jt(e),i&8192)e:for(t=e.stateNode,t._visibility=u?t._visibility&-2:t._visibility|1,u&&(n===null||b||Cn||We||za(e)),n=null,t=e;;){if(t.tag===5||t.tag===26){if(n===null){b=n=t;try{if(o=b.stateNode,u)h=o.style,typeof h.setProperty=="function"?h.setProperty("display","none","important"):h.display="none";else{p=b.stateNode;var Y=b.memoizedProps.style,M=Y!=null&&Y.hasOwnProperty("display")?Y.display:null;p.style.display=M==null||typeof M=="boolean"?"":(""+M).trim()}}catch(U){qe(b,b.return,U)}}}else if(t.tag===6){if(n===null){b=t;try{b.stateNode.nodeValue=u?"":b.memoizedProps}catch(U){qe(b,b.return,U)}}}else if((t.tag!==22&&t.tag!==23||t.memoizedState===null||t===e)&&t.child!==null){t.child.return=t,t=t.child;continue}if(t===e)break e;for(;t.sibling===null;){if(t.return===null||t.return===e)break e;n===t&&(n=null),t=t.return}n===t&&(n=null),t.sibling.return=t.return,t=t.sibling}i&4&&(i=e.updateQueue,i!==null&&(n=i.retryQueue,n!==null&&(i.retryQueue=null,jo(e,n))));break;case 19:zt(t,e),jt(e),i&4&&(i=e.updateQueue,i!==null&&(e.updateQueue=null,jo(e,i)));break;case 30:break;case 21:break;default:zt(t,e),jt(e)}}function jt(e){var t=e.flags;if(t&2){try{for(var n,i=e.return;i!==null;){if(Wh(i)){n=i;break}i=i.return}if(n==null)throw Error(s(160));switch(n.tag){case 27:var u=n.stateNode,o=Mo(e);Jr(e,o,u);break;case 5:var h=n.stateNode;n.flags&32&&(tl(h,""),n.flags&=-33);var p=Mo(e);Jr(e,p,h);break;case 3:case 4:var b=n.stateNode.containerInfo,C=Mo(e);Uo(e,C,b);break;default:throw Error(s(161))}}catch(k){qe(e,e.return,k)}e.flags&=-3}t&4096&&(e.flags&=-4097)}function im(e){if(e.subtreeFlags&1024)for(e=e.child;e!==null;){var t=e;im(t),t.tag===5&&t.flags&1024&&t.stateNode.reset(),e=e.sibling}}function Pn(e,t){if(t.subtreeFlags&8772)for(t=t.child;t!==null;)em(e,t.alternate,t),t=t.sibling}function za(e){for(e=e.child;e!==null;){var t=e;switch(t.tag){case 0:case 11:case 14:case 15:Wn(4,t,t.return),za(t);break;case 1:cn(t,t.return);var n=t.stateNode;typeof n.componentWillUnmount=="function"&&Jh(t,t.return,n),za(t);break;case 27:Ni(t.stateNode);case 26:case 5:cn(t,t.return),za(t);break;case 22:t.memoizedState===null&&za(t);break;case 30:za(t);break;default:za(t)}e=e.sibling}}function In(e,t,n){for(n=n&&(t.subtreeFlags&8772)!==0,t=t.child;t!==null;){var i=t.alternate,u=e,o=t,h=o.flags;switch(o.tag){case 0:case 11:case 15:In(u,o,n),bi(4,o);break;case 1:if(In(u,o,n),i=o,u=i.stateNode,typeof u.componentDidMount=="function")try{u.componentDidMount()}catch(C){qe(i,i.return,C)}if(i=o,u=i.updateQueue,u!==null){var p=i.stateNode;try{var b=u.shared.hiddenCallbacks;if(b!==null)for(u.shared.hiddenCallbacks=null,u=0;u<b.length;u++)zd(b[u],p)}catch(C){qe(i,i.return,C)}}n&&h&64&&Fh(o),Si(o,o.return);break;case 27:Ph(o);case 26:case 5:In(u,o,n),n&&i===null&&h&4&&$h(o),Si(o,o.return);break;case 12:In(u,o,n);break;case 13:In(u,o,n),n&&h&4&&am(u,o);break;case 22:o.memoizedState===null&&In(u,o,n),Si(o,o.return);break;case 30:break;default:In(u,o,n)}t=t.sibling}}function Bo(e,t){var n=null;e!==null&&e.memoizedState!==null&&e.memoizedState.cachePool!==null&&(n=e.memoizedState.cachePool.pool),e=null,t.memoizedState!==null&&t.memoizedState.cachePool!==null&&(e=t.memoizedState.cachePool.pool),e!==n&&(e!=null&&e.refCount++,n!=null&&ii(n))}function Lo(e,t){e=null,t.alternate!==null&&(e=t.alternate.memoizedState.cache),t=t.memoizedState.cache,t!==e&&(t.refCount++,e!=null&&ii(e))}function fn(e,t,n,i){if(t.subtreeFlags&10256)for(t=t.child;t!==null;)rm(e,t,n,i),t=t.sibling}function rm(e,t,n,i){var u=t.flags;switch(t.tag){case 0:case 11:case 15:fn(e,t,n,i),u&2048&&bi(9,t);break;case 1:fn(e,t,n,i);break;case 3:fn(e,t,n,i),u&2048&&(e=null,t.alternate!==null&&(e=t.alternate.memoizedState.cache),t=t.memoizedState.cache,t!==e&&(t.refCount++,e!=null&&ii(e)));break;case 12:if(u&2048){fn(e,t,n,i),e=t.stateNode;try{var o=t.memoizedProps,h=o.id,p=o.onPostCommit;typeof p=="function"&&p(h,t.alternate===null?"mount":"update",e.passiveEffectDuration,-0)}catch(b){qe(t,t.return,b)}}else fn(e,t,n,i);break;case 13:fn(e,t,n,i);break;case 23:break;case 22:o=t.stateNode,h=t.alternate,t.memoizedState!==null?o._visibility&2?fn(e,t,n,i):xi(e,t):o._visibility&2?fn(e,t,n,i):(o._visibility|=2,Sl(e,t,n,i,(t.subtreeFlags&10256)!==0)),u&2048&&Bo(h,t);break;case 24:fn(e,t,n,i),u&2048&&Lo(t.alternate,t);break;default:fn(e,t,n,i)}}function Sl(e,t,n,i,u){for(u=u&&(t.subtreeFlags&10256)!==0,t=t.child;t!==null;){var o=e,h=t,p=n,b=i,C=h.flags;switch(h.tag){case 0:case 11:case 15:Sl(o,h,p,b,u),bi(8,h);break;case 23:break;case 22:var k=h.stateNode;h.memoizedState!==null?k._visibility&2?Sl(o,h,p,b,u):xi(o,h):(k._visibility|=2,Sl(o,h,p,b,u)),u&&C&2048&&Bo(h.alternate,h);break;case 24:Sl(o,h,p,b,u),u&&C&2048&&Lo(h.alternate,h);break;default:Sl(o,h,p,b,u)}t=t.sibling}}function xi(e,t){if(t.subtreeFlags&10256)for(t=t.child;t!==null;){var n=e,i=t,u=i.flags;switch(i.tag){case 22:xi(n,i),u&2048&&Bo(i.alternate,i);break;case 24:xi(n,i),u&2048&&Lo(i.alternate,i);break;default:xi(n,i)}t=t.sibling}}var Ei=8192;function xl(e){if(e.subtreeFlags&Ei)for(e=e.child;e!==null;)sm(e),e=e.sibling}function sm(e){switch(e.tag){case 26:xl(e),e.flags&Ei&&e.memoizedState!==null&&Nv(Pt,e.memoizedState,e.memoizedProps);break;case 5:xl(e);break;case 3:case 4:var t=Pt;Pt=us(e.stateNode.containerInfo),xl(e),Pt=t;break;case 22:e.memoizedState===null&&(t=e.alternate,t!==null&&t.memoizedState!==null?(t=Ei,Ei=16777216,xl(e),Ei=t):xl(e));break;default:xl(e)}}function um(e){var t=e.alternate;if(t!==null&&(e=t.child,e!==null)){t.child=null;do t=e.sibling,e.sibling=null,e=t;while(e!==null)}}function _i(e){var t=e.deletions;if((e.flags&16)!==0){if(t!==null)for(var n=0;n<t.length;n++){var i=t[n];ct=i,cm(i,e)}um(e)}if(e.subtreeFlags&10256)for(e=e.child;e!==null;)om(e),e=e.sibling}function om(e){switch(e.tag){case 0:case 11:case 15:_i(e),e.flags&2048&&Wn(9,e,e.return);break;case 3:_i(e);break;case 12:_i(e);break;case 22:var t=e.stateNode;e.memoizedState!==null&&t._visibility&2&&(e.return===null||e.return.tag!==13)?(t._visibility&=-3,$r(e)):_i(e);break;default:_i(e)}}function $r(e){var t=e.deletions;if((e.flags&16)!==0){if(t!==null)for(var n=0;n<t.length;n++){var i=t[n];ct=i,cm(i,e)}um(e)}for(e=e.child;e!==null;){switch(t=e,t.tag){case 0:case 11:case 15:Wn(8,t,t.return),$r(t);break;case 22:n=t.stateNode,n._visibility&2&&(n._visibility&=-3,$r(t));break;default:$r(t)}e=e.sibling}}function cm(e,t){for(;ct!==null;){var n=ct;switch(n.tag){case 0:case 11:case 15:Wn(8,n,t);break;case 23:case 22:if(n.memoizedState!==null&&n.memoizedState.cachePool!==null){var i=n.memoizedState.cachePool.pool;i!=null&&i.refCount++}break;case 24:ii(n.memoizedState.cache)}if(i=n.child,i!==null)i.return=n,ct=i;else e:for(n=e;ct!==null;){i=ct;var u=i.sibling,o=i.return;if(tm(i),i===n){ct=null;break e}if(u!==null){u.return=o,ct=u;break e}ct=o}}}var Qg={getCacheForType:function(e){var t=vt(rt),n=t.data.get(e);return n===void 0&&(n=e(),t.data.set(e,n)),n}},Zg=typeof WeakMap=="function"?WeakMap:Map,Ue=0,Ve=null,be=null,Re=0,ze=0,Bt=null,ea=!1,El=!1,ko=!1,Mn=0,Je=0,ta=0,ja=0,qo=0,Ft=0,_l=0,wi=null,Ot=null,Ho=!1,Vo=0,Wr=1/0,Pr=null,na=null,yt=0,aa=null,wl=null,Al=0,Yo=0,Xo=null,fm=null,Ai=0,Go=null;function Lt(){if((Ue&2)!==0&&Re!==0)return Re&-Re;if(j.T!==null){var e=dl;return e!==0?e:Wo()}return Rf()}function dm(){Ft===0&&(Ft=(Re&536870912)===0||Ce?_f():536870912);var e=Kt.current;return e!==null&&(e.flags|=32),Ft}function kt(e,t,n){(e===Ve&&(ze===2||ze===9)||e.cancelPendingCommit!==null)&&(Tl(e,0),la(e,Re,Ft,!1)),Xl(e,n),((Ue&2)===0||e!==Ve)&&(e===Ve&&((Ue&2)===0&&(ja|=n),Je===4&&la(e,Re,Ft,!1)),dn(e))}function hm(e,t,n){if((Ue&6)!==0)throw Error(s(327));var i=!n&&(t&124)===0&&(t&e.expiredLanes)===0||ba(e,t),u=i?Jg(e,t):Ko(e,t,!0),o=i;do{if(u===0){El&&!i&&la(e,t,0,!1);break}else{if(n=e.current.alternate,o&&!Kg(n)){u=Ko(e,t,!1),o=!1;continue}if(u===2){if(o=t,e.errorRecoveryDisabledLanes&o)var h=0;else h=e.pendingLanes&-536870913,h=h!==0?h:h&536870912?536870912:0;if(h!==0){t=h;e:{var p=e;u=wi;var b=p.current.memoizedState.isDehydrated;if(b&&(Tl(p,h).flags|=256),h=Ko(p,h,!1),h!==2){if(ko&&!b){p.errorRecoveryDisabledLanes|=o,ja|=o,u=4;break e}o=Ot,Ot=u,o!==null&&(Ot===null?Ot=o:Ot.push.apply(Ot,o))}u=h}if(o=!1,u!==2)continue}}if(u===1){Tl(e,0),la(e,t,0,!0);break}e:{switch(i=e,o=u,o){case 0:case 1:throw Error(s(345));case 4:if((t&4194048)!==t)break;case 6:la(i,t,Ft,!ea);break e;case 2:Ot=null;break;case 3:case 5:break;default:throw Error(s(329))}if((t&62914560)===t&&(u=Vo+300-Ht(),10<u)){if(la(i,t,Ft,!ea),va(i,0,!0)!==0)break e;i.timeoutHandle=Ym(mm.bind(null,i,n,Ot,Pr,Ho,t,Ft,ja,_l,ea,o,2,-0,0),u);break e}mm(i,n,Ot,Pr,Ho,t,Ft,ja,_l,ea,o,0,-0,0)}}break}while(!0);dn(e)}function mm(e,t,n,i,u,o,h,p,b,C,k,Y,M,U){if(e.timeoutHandle=-1,Y=t.subtreeFlags,(Y&8192||(Y&16785408)===16785408)&&(Mi={stylesheets:null,count:0,unsuspend:Ov},sm(t),Y=Cv(),Y!==null)){e.cancelPendingCommit=Y(xm.bind(null,e,t,o,n,i,u,h,p,b,k,1,M,U)),la(e,o,h,!C);return}xm(e,t,o,n,i,u,h,p,b)}function Kg(e){for(var t=e;;){var n=t.tag;if((n===0||n===11||n===15)&&t.flags&16384&&(n=t.updateQueue,n!==null&&(n=n.stores,n!==null)))for(var i=0;i<n.length;i++){var u=n[i],o=u.getSnapshot;u=u.value;try{if(!Mt(o(),u))return!1}catch{return!1}}if(n=t.child,t.subtreeFlags&16384&&n!==null)n.return=t,t=n;else{if(t===e)break;for(;t.sibling===null;){if(t.return===null||t.return===e)return!0;t=t.return}t.sibling.return=t.return,t=t.sibling}}return!0}function la(e,t,n,i){t&=~qo,t&=~ja,e.suspendedLanes|=t,e.pingedLanes&=~t,i&&(e.warmLanes|=t),i=e.expirationTimes;for(var u=t;0<u;){var o=31-He(u),h=1<<o;i[o]=-1,u&=~h}n!==0&&Af(e,n,t)}function Ir(){return(Ue&6)===0?(Ti(0),!1):!0}function Qo(){if(be!==null){if(ze===0)var e=be.return;else e=be,wn=Na=null,so(e),vl=null,pi=0,e=be;for(;e!==null;)Kh(e.alternate,e),e=e.return;be=null}}function Tl(e,t){var n=e.timeoutHandle;n!==-1&&(e.timeoutHandle=-1,fv(n)),n=e.cancelPendingCommit,n!==null&&(e.cancelPendingCommit=null,n()),Qo(),Ve=e,be=n=xn(e.current,null),Re=t,ze=0,Bt=null,ea=!1,El=ba(e,t),ko=!1,_l=Ft=qo=ja=ta=Je=0,Ot=wi=null,Ho=!1,(t&8)!==0&&(t|=t&32);var i=e.entangledLanes;if(i!==0)for(e=e.entanglements,i&=t;0<i;){var u=31-He(i),o=1<<u;t|=e[u],i&=~o}return Mn=t,xr(),n}function ym(e,t){ge=null,j.H=Hr,t===si||t===Cr?(t=Md(),ze=3):t===Nd?(t=Md(),ze=4):ze=t===Uh?8:t!==null&&typeof t=="object"&&typeof t.then=="function"?6:1,Bt=t,be===null&&(Je=1,Qr(e,Xt(t,e.current)))}function pm(){var e=j.H;return j.H=Hr,e===null?Hr:e}function gm(){var e=j.A;return j.A=Qg,e}function Zo(){Je=4,ea||(Re&4194048)!==Re&&Kt.current!==null||(El=!0),(ta&134217727)===0&&(ja&134217727)===0||Ve===null||la(Ve,Re,Ft,!1)}function Ko(e,t,n){var i=Ue;Ue|=2;var u=pm(),o=gm();(Ve!==e||Re!==t)&&(Pr=null,Tl(e,t)),t=!1;var h=Je;e:do try{if(ze!==0&&be!==null){var p=be,b=Bt;switch(ze){case 8:Qo(),h=6;break e;case 3:case 2:case 9:case 6:Kt.current===null&&(t=!0);var C=ze;if(ze=0,Bt=null,Rl(e,p,b,C),n&&El){h=0;break e}break;default:C=ze,ze=0,Bt=null,Rl(e,p,b,C)}}Fg(),h=Je;break}catch(k){ym(e,k)}while(!0);return t&&e.shellSuspendCounter++,wn=Na=null,Ue=i,j.H=u,j.A=o,be===null&&(Ve=null,Re=0,xr()),h}function Fg(){for(;be!==null;)vm(be)}function Jg(e,t){var n=Ue;Ue|=2;var i=pm(),u=gm();Ve!==e||Re!==t?(Pr=null,Wr=Ht()+500,Tl(e,t)):El=ba(e,t);e:do try{if(ze!==0&&be!==null){t=be;var o=Bt;t:switch(ze){case 1:ze=0,Bt=null,Rl(e,t,o,1);break;case 2:case 9:if(Cd(o)){ze=0,Bt=null,bm(t);break}t=function(){ze!==2&&ze!==9||Ve!==e||(ze=7),dn(e)},o.then(t,t);break e;case 3:ze=7;break e;case 4:ze=5;break e;case 7:Cd(o)?(ze=0,Bt=null,bm(t)):(ze=0,Bt=null,Rl(e,t,o,7));break;case 5:var h=null;switch(be.tag){case 26:h=be.memoizedState;case 5:case 27:var p=be;if(!h||ey(h)){ze=0,Bt=null;var b=p.sibling;if(b!==null)be=b;else{var C=p.return;C!==null?(be=C,es(C)):be=null}break t}}ze=0,Bt=null,Rl(e,t,o,5);break;case 6:ze=0,Bt=null,Rl(e,t,o,6);break;case 8:Qo(),Je=6;break e;default:throw Error(s(462))}}$g();break}catch(k){ym(e,k)}while(!0);return wn=Na=null,j.H=i,j.A=u,Ue=n,be!==null?0:(Ve=null,Re=0,xr(),Je)}function $g(){for(;be!==null&&!ur();)vm(be)}function vm(e){var t=Qh(e.alternate,e,Mn);e.memoizedProps=e.pendingProps,t===null?es(e):be=t}function bm(e){var t=e,n=t.alternate;switch(t.tag){case 15:case 0:t=qh(n,t,t.pendingProps,t.type,void 0,Re);break;case 11:t=qh(n,t,t.pendingProps,t.type.render,t.ref,Re);break;case 5:so(t);default:Kh(n,t),t=be=Sd(t,Mn),t=Qh(n,t,Mn)}e.memoizedProps=e.pendingProps,t===null?es(e):be=t}function Rl(e,t,n,i){wn=Na=null,so(t),vl=null,pi=0;var u=t.return;try{if(qg(e,u,t,n,Re)){Je=1,Qr(e,Xt(n,e.current)),be=null;return}}catch(o){if(u!==null)throw be=u,o;Je=1,Qr(e,Xt(n,e.current)),be=null;return}t.flags&32768?(Ce||i===1?e=!0:El||(Re&536870912)!==0?e=!1:(ea=e=!0,(i===2||i===9||i===3||i===6)&&(i=Kt.current,i!==null&&i.tag===13&&(i.flags|=16384))),Sm(t,e)):es(t)}function es(e){var t=e;do{if((t.flags&32768)!==0){Sm(t,ea);return}e=t.return;var n=Vg(t.alternate,t,Mn);if(n!==null){be=n;return}if(t=t.sibling,t!==null){be=t;return}be=t=e}while(t!==null);Je===0&&(Je=5)}function Sm(e,t){do{var n=Yg(e.alternate,e);if(n!==null){n.flags&=32767,be=n;return}if(n=e.return,n!==null&&(n.flags|=32768,n.subtreeFlags=0,n.deletions=null),!t&&(e=e.sibling,e!==null)){be=e;return}be=e=n}while(e!==null);Je=6,be=null}function xm(e,t,n,i,u,o,h,p,b){e.cancelPendingCommit=null;do ts();while(yt!==0);if((Ue&6)!==0)throw Error(s(327));if(t!==null){if(t===e.current)throw Error(s(177));if(o=t.lanes|t.childLanes,o|=Bu,Op(e,n,o,h,p,b),e===Ve&&(be=Ve=null,Re=0),wl=t,aa=e,Al=n,Yo=o,Xo=u,fm=i,(t.subtreeFlags&10256)!==0||(t.flags&10256)!==0?(e.callbackNode=null,e.callbackPriority=0,ev(D,function(){return Tm(),null})):(e.callbackNode=null,e.callbackPriority=0),i=(t.flags&13878)!==0,(t.subtreeFlags&13878)!==0||i){i=j.T,j.T=null,u=F.p,F.p=2,h=Ue,Ue|=4;try{Xg(e,t,n)}finally{Ue=h,F.p=u,j.T=i}}yt=1,Em(),_m(),wm()}}function Em(){if(yt===1){yt=0;var e=aa,t=wl,n=(t.flags&13878)!==0;if((t.subtreeFlags&13878)!==0||n){n=j.T,j.T=null;var i=F.p;F.p=2;var u=Ue;Ue|=4;try{lm(t,e);var o=ic,h=cd(e.containerInfo),p=o.focusedElem,b=o.selectionRange;if(h!==p&&p&&p.ownerDocument&&od(p.ownerDocument.documentElement,p)){if(b!==null&&Du(p)){var C=b.start,k=b.end;if(k===void 0&&(k=C),"selectionStart"in p)p.selectionStart=C,p.selectionEnd=Math.min(k,p.value.length);else{var Y=p.ownerDocument||document,M=Y&&Y.defaultView||window;if(M.getSelection){var U=M.getSelection(),ce=p.textContent.length,se=Math.min(b.start,ce),Le=b.end===void 0?se:Math.min(b.end,ce);!U.extend&&se>Le&&(h=Le,Le=se,se=h);var T=ud(p,se),w=ud(p,Le);if(T&&w&&(U.rangeCount!==1||U.anchorNode!==T.node||U.anchorOffset!==T.offset||U.focusNode!==w.node||U.focusOffset!==w.offset)){var O=Y.createRange();O.setStart(T.node,T.offset),U.removeAllRanges(),se>Le?(U.addRange(O),U.extend(w.node,w.offset)):(O.setEnd(w.node,w.offset),U.addRange(O))}}}}for(Y=[],U=p;U=U.parentNode;)U.nodeType===1&&Y.push({element:U,left:U.scrollLeft,top:U.scrollTop});for(typeof p.focus=="function"&&p.focus(),p=0;p<Y.length;p++){var H=Y[p];H.element.scrollLeft=H.left,H.element.scrollTop=H.top}}hs=!!lc,ic=lc=null}finally{Ue=u,F.p=i,j.T=n}}e.current=t,yt=2}}function _m(){if(yt===2){yt=0;var e=aa,t=wl,n=(t.flags&8772)!==0;if((t.subtreeFlags&8772)!==0||n){n=j.T,j.T=null;var i=F.p;F.p=2;var u=Ue;Ue|=4;try{em(e,t.alternate,t)}finally{Ue=u,F.p=i,j.T=n}}yt=3}}function wm(){if(yt===4||yt===3){yt=0,ru();var e=aa,t=wl,n=Al,i=fm;(t.subtreeFlags&10256)!==0||(t.flags&10256)!==0?yt=5:(yt=0,wl=aa=null,Am(e,e.pendingLanes));var u=e.pendingLanes;if(u===0&&(na=null),cu(n),t=t.stateNode,de&&typeof de.onCommitFiberRoot=="function")try{de.onCommitFiberRoot(ne,t,void 0,(t.current.flags&128)===128)}catch{}if(i!==null){t=j.T,u=F.p,F.p=2,j.T=null;try{for(var o=e.onRecoverableError,h=0;h<i.length;h++){var p=i[h];o(p.value,{componentStack:p.stack})}}finally{j.T=t,F.p=u}}(Al&3)!==0&&ts(),dn(e),u=e.pendingLanes,(n&4194090)!==0&&(u&42)!==0?e===Go?Ai++:(Ai=0,Go=e):Ai=0,Ti(0)}}function Am(e,t){(e.pooledCacheLanes&=t)===0&&(t=e.pooledCache,t!=null&&(e.pooledCache=null,ii(t)))}function ts(e){return Em(),_m(),wm(),Tm()}function Tm(){if(yt!==5)return!1;var e=aa,t=Yo;Yo=0;var n=cu(Al),i=j.T,u=F.p;try{F.p=32>n?32:n,j.T=null,n=Xo,Xo=null;var o=aa,h=Al;if(yt=0,wl=aa=null,Al=0,(Ue&6)!==0)throw Error(s(331));var p=Ue;if(Ue|=4,om(o.current),rm(o,o.current,h,n),Ue=p,Ti(0,!1),de&&typeof de.onPostCommitFiberRoot=="function")try{de.onPostCommitFiberRoot(ne,o)}catch{}return!0}finally{F.p=u,j.T=i,Am(e,t)}}function Rm(e,t,n){t=Xt(n,t),t=Eo(e.stateNode,t,2),e=Kn(e,t,2),e!==null&&(Xl(e,2),dn(e))}function qe(e,t,n){if(e.tag===3)Rm(e,e,n);else for(;t!==null;){if(t.tag===3){Rm(t,e,n);break}else if(t.tag===1){var i=t.stateNode;if(typeof t.type.getDerivedStateFromError=="function"||typeof i.componentDidCatch=="function"&&(na===null||!na.has(i))){e=Xt(n,e),n=Dh(2),i=Kn(t,n,2),i!==null&&(Mh(n,i,t,e),Xl(i,2),dn(i));break}}t=t.return}}function Fo(e,t,n){var i=e.pingCache;if(i===null){i=e.pingCache=new Zg;var u=new Set;i.set(t,u)}else u=i.get(t),u===void 0&&(u=new Set,i.set(t,u));u.has(n)||(ko=!0,u.add(n),e=Wg.bind(null,e,t,n),t.then(e,e))}function Wg(e,t,n){var i=e.pingCache;i!==null&&i.delete(t),e.pingedLanes|=e.suspendedLanes&n,e.warmLanes&=~n,Ve===e&&(Re&n)===n&&(Je===4||Je===3&&(Re&62914560)===Re&&300>Ht()-Vo?(Ue&2)===0&&Tl(e,0):qo|=n,_l===Re&&(_l=0)),dn(e)}function Om(e,t){t===0&&(t=wf()),e=ul(e,t),e!==null&&(Xl(e,t),dn(e))}function Pg(e){var t=e.memoizedState,n=0;t!==null&&(n=t.retryLane),Om(e,n)}function Ig(e,t){var n=0;switch(e.tag){case 13:var i=e.stateNode,u=e.memoizedState;u!==null&&(n=u.retryLane);break;case 19:i=e.stateNode;break;case 22:i=e.stateNode._retryCache;break;default:throw Error(s(314))}i!==null&&i.delete(t),Om(e,n)}function ev(e,t){return Vl(e,t)}var ns=null,Ol=null,Jo=!1,as=!1,$o=!1,Ba=0;function dn(e){e!==Ol&&e.next===null&&(Ol===null?ns=Ol=e:Ol=Ol.next=e),as=!0,Jo||(Jo=!0,nv())}function Ti(e,t){if(!$o&&as){$o=!0;do for(var n=!1,i=ns;i!==null;){if(e!==0){var u=i.pendingLanes;if(u===0)var o=0;else{var h=i.suspendedLanes,p=i.pingedLanes;o=(1<<31-He(42|e)+1)-1,o&=u&~(h&~p),o=o&201326741?o&201326741|1:o?o|2:0}o!==0&&(n=!0,Mm(i,o))}else o=Re,o=va(i,i===Ve?o:0,i.cancelPendingCommit!==null||i.timeoutHandle!==-1),(o&3)===0||ba(i,o)||(n=!0,Mm(i,o));i=i.next}while(n);$o=!1}}function tv(){Nm()}function Nm(){as=Jo=!1;var e=0;Ba!==0&&(cv()&&(e=Ba),Ba=0);for(var t=Ht(),n=null,i=ns;i!==null;){var u=i.next,o=Cm(i,t);o===0?(i.next=null,n===null?ns=u:n.next=u,u===null&&(Ol=n)):(n=i,(e!==0||(o&3)!==0)&&(as=!0)),i=u}Ti(e)}function Cm(e,t){for(var n=e.suspendedLanes,i=e.pingedLanes,u=e.expirationTimes,o=e.pendingLanes&-62914561;0<o;){var h=31-He(o),p=1<<h,b=u[h];b===-1?((p&n)===0||(p&i)!==0)&&(u[h]=or(p,t)):b<=t&&(e.expiredLanes|=p),o&=~p}if(t=Ve,n=Re,n=va(e,e===t?n:0,e.cancelPendingCommit!==null||e.timeoutHandle!==-1),i=e.callbackNode,n===0||e===t&&(ze===2||ze===9)||e.cancelPendingCommit!==null)return i!==null&&i!==null&&Za(i),e.callbackNode=null,e.callbackPriority=0;if((n&3)===0||ba(e,n)){if(t=n&-n,t===e.callbackPriority)return t;switch(i!==null&&Za(i),cu(n)){case 2:case 8:n=x;break;case 32:n=D;break;case 268435456:n=P;break;default:n=D}return i=Dm.bind(null,e),n=Vl(n,i),e.callbackPriority=t,e.callbackNode=n,t}return i!==null&&i!==null&&Za(i),e.callbackPriority=2,e.callbackNode=null,2}function Dm(e,t){if(yt!==0&&yt!==5)return e.callbackNode=null,e.callbackPriority=0,null;var n=e.callbackNode;if(ts()&&e.callbackNode!==n)return null;var i=Re;return i=va(e,e===Ve?i:0,e.cancelPendingCommit!==null||e.timeoutHandle!==-1),i===0?null:(hm(e,i,t),Cm(e,Ht()),e.callbackNode!=null&&e.callbackNode===n?Dm.bind(null,e):null)}function Mm(e,t){if(ts())return null;hm(e,t,!0)}function nv(){dv(function(){(Ue&6)!==0?Vl(Yl,tv):Nm()})}function Wo(){return Ba===0&&(Ba=_f()),Ba}function Um(e){return e==null||typeof e=="symbol"||typeof e=="boolean"?null:typeof e=="function"?e:mr(""+e)}function zm(e,t){var n=t.ownerDocument.createElement("input");return n.name=t.name,n.value=t.value,e.id&&n.setAttribute("form",e.id),t.parentNode.insertBefore(n,t),e=new FormData(e),n.parentNode.removeChild(n),e}function av(e,t,n,i,u){if(t==="submit"&&n&&n.stateNode===u){var o=Um((u[wt]||null).action),h=i.submitter;h&&(t=(t=h[wt]||null)?Um(t.formAction):h.getAttribute("formAction"),t!==null&&(o=t,h=null));var p=new vr("action","action",null,i,u);e.push({event:p,listeners:[{instance:null,listener:function(){if(i.defaultPrevented){if(Ba!==0){var b=h?zm(u,h):new FormData(u);go(n,{pending:!0,data:b,method:u.method,action:o},null,b)}}else typeof o=="function"&&(p.preventDefault(),b=h?zm(u,h):new FormData(u),go(n,{pending:!0,data:b,method:u.method,action:o},o,b))},currentTarget:u}]})}}for(var Po=0;Po<ju.length;Po++){var Io=ju[Po],lv=Io.toLowerCase(),iv=Io[0].toUpperCase()+Io.slice(1);Wt(lv,"on"+iv)}Wt(hd,"onAnimationEnd"),Wt(md,"onAnimationIteration"),Wt(yd,"onAnimationStart"),Wt("dblclick","onDoubleClick"),Wt("focusin","onFocus"),Wt("focusout","onBlur"),Wt(Eg,"onTransitionRun"),Wt(_g,"onTransitionStart"),Wt(wg,"onTransitionCancel"),Wt(pd,"onTransitionEnd"),Pa("onMouseEnter",["mouseout","mouseover"]),Pa("onMouseLeave",["mouseout","mouseover"]),Pa("onPointerEnter",["pointerout","pointerover"]),Pa("onPointerLeave",["pointerout","pointerover"]),Sa("onChange","change click focusin focusout input keydown keyup selectionchange".split(" ")),Sa("onSelect","focusout contextmenu dragend focusin keydown keyup mousedown mouseup selectionchange".split(" ")),Sa("onBeforeInput",["compositionend","keypress","textInput","paste"]),Sa("onCompositionEnd","compositionend focusout keydown keypress keyup mousedown".split(" ")),Sa("onCompositionStart","compositionstart focusout keydown keypress keyup mousedown".split(" ")),Sa("onCompositionUpdate","compositionupdate focusout keydown keypress keyup mousedown".split(" "));var Ri="abort canplay canplaythrough durationchange emptied encrypted ended error loadeddata loadedmetadata loadstart pause play playing progress ratechange resize seeked seeking stalled suspend timeupdate volumechange waiting".split(" "),rv=new Set("beforetoggle cancel close invalid load scroll scrollend toggle".split(" ").concat(Ri));function jm(e,t){t=(t&4)!==0;for(var n=0;n<e.length;n++){var i=e[n],u=i.event;i=i.listeners;e:{var o=void 0;if(t)for(var h=i.length-1;0<=h;h--){var p=i[h],b=p.instance,C=p.currentTarget;if(p=p.listener,b!==o&&u.isPropagationStopped())break e;o=p,u.currentTarget=C;try{o(u)}catch(k){Gr(k)}u.currentTarget=null,o=b}else for(h=0;h<i.length;h++){if(p=i[h],b=p.instance,C=p.currentTarget,p=p.listener,b!==o&&u.isPropagationStopped())break e;o=p,u.currentTarget=C;try{o(u)}catch(k){Gr(k)}u.currentTarget=null,o=b}}}}function Se(e,t){var n=t[fu];n===void 0&&(n=t[fu]=new Set);var i=e+"__bubble";n.has(i)||(Bm(t,e,2,!1),n.add(i))}function ec(e,t,n){var i=0;t&&(i|=4),Bm(n,e,i,t)}var ls="_reactListening"+Math.random().toString(36).slice(2);function tc(e){if(!e[ls]){e[ls]=!0,Nf.forEach(function(n){n!=="selectionchange"&&(rv.has(n)||ec(n,!1,e),ec(n,!0,e))});var t=e.nodeType===9?e:e.ownerDocument;t===null||t[ls]||(t[ls]=!0,ec("selectionchange",!1,t))}}function Bm(e,t,n,i){switch(ry(t)){case 2:var u=Uv;break;case 8:u=zv;break;default:u=yc}n=u.bind(null,t,n,e),u=void 0,!Eu||t!=="touchstart"&&t!=="touchmove"&&t!=="wheel"||(u=!0),i?u!==void 0?e.addEventListener(t,n,{capture:!0,passive:u}):e.addEventListener(t,n,!0):u!==void 0?e.addEventListener(t,n,{passive:u}):e.addEventListener(t,n,!1)}function nc(e,t,n,i,u){var o=i;if((t&1)===0&&(t&2)===0&&i!==null)e:for(;;){if(i===null)return;var h=i.tag;if(h===3||h===4){var p=i.stateNode.containerInfo;if(p===u)break;if(h===4)for(h=i.return;h!==null;){var b=h.tag;if((b===3||b===4)&&h.stateNode.containerInfo===u)return;h=h.return}for(;p!==null;){if(h=Ja(p),h===null)return;if(b=h.tag,b===5||b===6||b===26||b===27){i=o=h;continue e}p=p.parentNode}}i=i.return}Xf(function(){var C=o,k=Su(n),Y=[];e:{var M=gd.get(e);if(M!==void 0){var U=vr,ce=e;switch(e){case"keypress":if(pr(n)===0)break e;case"keydown":case"keyup":U=eg;break;case"focusin":ce="focus",U=Tu;break;case"focusout":ce="blur",U=Tu;break;case"beforeblur":case"afterblur":U=Tu;break;case"click":if(n.button===2)break e;case"auxclick":case"dblclick":case"mousedown":case"mousemove":case"mouseup":case"mouseout":case"mouseover":case"contextmenu":U=Zf;break;case"drag":case"dragend":case"dragenter":case"dragexit":case"dragleave":case"dragover":case"dragstart":case"drop":U=Yp;break;case"touchcancel":case"touchend":case"touchmove":case"touchstart":U=ag;break;case hd:case md:case yd:U=Qp;break;case pd:U=ig;break;case"scroll":case"scrollend":U=Hp;break;case"wheel":U=sg;break;case"copy":case"cut":case"paste":U=Kp;break;case"gotpointercapture":case"lostpointercapture":case"pointercancel":case"pointerdown":case"pointermove":case"pointerout":case"pointerover":case"pointerup":U=Ff;break;case"toggle":case"beforetoggle":U=og}var se=(t&4)!==0,Le=!se&&(e==="scroll"||e==="scrollend"),T=se?M!==null?M+"Capture":null:M;se=[];for(var w=C,O;w!==null;){var H=w;if(O=H.stateNode,H=H.tag,H!==5&&H!==26&&H!==27||O===null||T===null||(H=Zl(w,T),H!=null&&se.push(Oi(w,H,O))),Le)break;w=w.return}0<se.length&&(M=new U(M,ce,null,n,k),Y.push({event:M,listeners:se}))}}if((t&7)===0){e:{if(M=e==="mouseover"||e==="pointerover",U=e==="mouseout"||e==="pointerout",M&&n!==bu&&(ce=n.relatedTarget||n.fromElement)&&(Ja(ce)||ce[Fa]))break e;if((U||M)&&(M=k.window===k?k:(M=k.ownerDocument)?M.defaultView||M.parentWindow:window,U?(ce=n.relatedTarget||n.toElement,U=C,ce=ce?Ja(ce):null,ce!==null&&(Le=f(ce),se=ce.tag,ce!==Le||se!==5&&se!==27&&se!==6)&&(ce=null)):(U=null,ce=C),U!==ce)){if(se=Zf,H="onMouseLeave",T="onMouseEnter",w="mouse",(e==="pointerout"||e==="pointerover")&&(se=Ff,H="onPointerLeave",T="onPointerEnter",w="pointer"),Le=U==null?M:Ql(U),O=ce==null?M:Ql(ce),M=new se(H,w+"leave",U,n,k),M.target=Le,M.relatedTarget=O,H=null,Ja(k)===C&&(se=new se(T,w+"enter",ce,n,k),se.target=O,se.relatedTarget=Le,H=se),Le=H,U&&ce)t:{for(se=U,T=ce,w=0,O=se;O;O=Nl(O))w++;for(O=0,H=T;H;H=Nl(H))O++;for(;0<w-O;)se=Nl(se),w--;for(;0<O-w;)T=Nl(T),O--;for(;w--;){if(se===T||T!==null&&se===T.alternate)break t;se=Nl(se),T=Nl(T)}se=null}else se=null;U!==null&&Lm(Y,M,U,se,!1),ce!==null&&Le!==null&&Lm(Y,Le,ce,se,!0)}}e:{if(M=C?Ql(C):window,U=M.nodeName&&M.nodeName.toLowerCase(),U==="select"||U==="input"&&M.type==="file")var ee=nd;else if(ed(M))if(ad)ee=bg;else{ee=gg;var ve=pg}else U=M.nodeName,!U||U.toLowerCase()!=="input"||M.type!=="checkbox"&&M.type!=="radio"?C&&vu(C.elementType)&&(ee=nd):ee=vg;if(ee&&(ee=ee(e,C))){td(Y,ee,n,k);break e}ve&&ve(e,M,C),e==="focusout"&&C&&M.type==="number"&&C.memoizedProps.value!=null&&gu(M,"number",M.value)}switch(ve=C?Ql(C):window,e){case"focusin":(ed(ve)||ve.contentEditable==="true")&&(il=ve,Mu=C,ei=null);break;case"focusout":ei=Mu=il=null;break;case"mousedown":Uu=!0;break;case"contextmenu":case"mouseup":case"dragend":Uu=!1,fd(Y,n,k);break;case"selectionchange":if(xg)break;case"keydown":case"keyup":fd(Y,n,k)}var ae;if(Ou)e:{switch(e){case"compositionstart":var ue="onCompositionStart";break e;case"compositionend":ue="onCompositionEnd";break e;case"compositionupdate":ue="onCompositionUpdate";break e}ue=void 0}else ll?Pf(e,n)&&(ue="onCompositionEnd"):e==="keydown"&&n.keyCode===229&&(ue="onCompositionStart");ue&&(Jf&&n.locale!=="ko"&&(ll||ue!=="onCompositionStart"?ue==="onCompositionEnd"&&ll&&(ae=Gf()):(Xn=k,_u="value"in Xn?Xn.value:Xn.textContent,ll=!0)),ve=is(C,ue),0<ve.length&&(ue=new Kf(ue,e,null,n,k),Y.push({event:ue,listeners:ve}),ae?ue.data=ae:(ae=If(n),ae!==null&&(ue.data=ae)))),(ae=fg?dg(e,n):hg(e,n))&&(ue=is(C,"onBeforeInput"),0<ue.length&&(ve=new Kf("onBeforeInput","beforeinput",null,n,k),Y.push({event:ve,listeners:ue}),ve.data=ae)),av(Y,e,C,n,k)}jm(Y,t)})}function Oi(e,t,n){return{instance:e,listener:t,currentTarget:n}}function is(e,t){for(var n=t+"Capture",i=[];e!==null;){var u=e,o=u.stateNode;if(u=u.tag,u!==5&&u!==26&&u!==27||o===null||(u=Zl(e,n),u!=null&&i.unshift(Oi(e,u,o)),u=Zl(e,t),u!=null&&i.push(Oi(e,u,o))),e.tag===3)return i;e=e.return}return[]}function Nl(e){if(e===null)return null;do e=e.return;while(e&&e.tag!==5&&e.tag!==27);return e||null}function Lm(e,t,n,i,u){for(var o=t._reactName,h=[];n!==null&&n!==i;){var p=n,b=p.alternate,C=p.stateNode;if(p=p.tag,b!==null&&b===i)break;p!==5&&p!==26&&p!==27||C===null||(b=C,u?(C=Zl(n,o),C!=null&&h.unshift(Oi(n,C,b))):u||(C=Zl(n,o),C!=null&&h.push(Oi(n,C,b)))),n=n.return}h.length!==0&&e.push({event:t,listeners:h})}var sv=/\r\n?/g,uv=/\u0000|\uFFFD/g;function km(e){return(typeof e=="string"?e:""+e).replace(sv,`
`).replace(uv,"")}function qm(e,t){return t=km(t),km(e)===t}function rs(){}function Be(e,t,n,i,u,o){switch(n){case"children":typeof i=="string"?t==="body"||t==="textarea"&&i===""||tl(e,i):(typeof i=="number"||typeof i=="bigint")&&t!=="body"&&tl(e,""+i);break;case"className":fr(e,"class",i);break;case"tabIndex":fr(e,"tabindex",i);break;case"dir":case"role":case"viewBox":case"width":case"height":fr(e,n,i);break;case"style":Vf(e,i,o);break;case"data":if(t!=="object"){fr(e,"data",i);break}case"src":case"href":if(i===""&&(t!=="a"||n!=="href")){e.removeAttribute(n);break}if(i==null||typeof i=="function"||typeof i=="symbol"||typeof i=="boolean"){e.removeAttribute(n);break}i=mr(""+i),e.setAttribute(n,i);break;case"action":case"formAction":if(typeof i=="function"){e.setAttribute(n,"javascript:throw new Error('A React form was unexpectedly submitted. If you called form.submit() manually, consider using form.requestSubmit() instead. If you\\'re trying to use event.stopPropagation() in a submit event handler, consider also calling event.preventDefault().')");break}else typeof o=="function"&&(n==="formAction"?(t!=="input"&&Be(e,t,"name",u.name,u,null),Be(e,t,"formEncType",u.formEncType,u,null),Be(e,t,"formMethod",u.formMethod,u,null),Be(e,t,"formTarget",u.formTarget,u,null)):(Be(e,t,"encType",u.encType,u,null),Be(e,t,"method",u.method,u,null),Be(e,t,"target",u.target,u,null)));if(i==null||typeof i=="symbol"||typeof i=="boolean"){e.removeAttribute(n);break}i=mr(""+i),e.setAttribute(n,i);break;case"onClick":i!=null&&(e.onclick=rs);break;case"onScroll":i!=null&&Se("scroll",e);break;case"onScrollEnd":i!=null&&Se("scrollend",e);break;case"dangerouslySetInnerHTML":if(i!=null){if(typeof i!="object"||!("__html"in i))throw Error(s(61));if(n=i.__html,n!=null){if(u.children!=null)throw Error(s(60));e.innerHTML=n}}break;case"multiple":e.multiple=i&&typeof i!="function"&&typeof i!="symbol";break;case"muted":e.muted=i&&typeof i!="function"&&typeof i!="symbol";break;case"suppressContentEditableWarning":case"suppressHydrationWarning":case"defaultValue":case"defaultChecked":case"innerHTML":case"ref":break;case"autoFocus":break;case"xlinkHref":if(i==null||typeof i=="function"||typeof i=="boolean"||typeof i=="symbol"){e.removeAttribute("xlink:href");break}n=mr(""+i),e.setAttributeNS("http://www.w3.org/1999/xlink","xlink:href",n);break;case"contentEditable":case"spellCheck":case"draggable":case"value":case"autoReverse":case"externalResourcesRequired":case"focusable":case"preserveAlpha":i!=null&&typeof i!="function"&&typeof i!="symbol"?e.setAttribute(n,""+i):e.removeAttribute(n);break;case"inert":case"allowFullScreen":case"async":case"autoPlay":case"controls":case"default":case"defer":case"disabled":case"disablePictureInPicture":case"disableRemotePlayback":case"formNoValidate":case"hidden":case"loop":case"noModule":case"noValidate":case"open":case"playsInline":case"readOnly":case"required":case"reversed":case"scoped":case"seamless":case"itemScope":i&&typeof i!="function"&&typeof i!="symbol"?e.setAttribute(n,""):e.removeAttribute(n);break;case"capture":case"download":i===!0?e.setAttribute(n,""):i!==!1&&i!=null&&typeof i!="function"&&typeof i!="symbol"?e.setAttribute(n,i):e.removeAttribute(n);break;case"cols":case"rows":case"size":case"span":i!=null&&typeof i!="function"&&typeof i!="symbol"&&!isNaN(i)&&1<=i?e.setAttribute(n,i):e.removeAttribute(n);break;case"rowSpan":case"start":i==null||typeof i=="function"||typeof i=="symbol"||isNaN(i)?e.removeAttribute(n):e.setAttribute(n,i);break;case"popover":Se("beforetoggle",e),Se("toggle",e),cr(e,"popover",i);break;case"xlinkActuate":bn(e,"http://www.w3.org/1999/xlink","xlink:actuate",i);break;case"xlinkArcrole":bn(e,"http://www.w3.org/1999/xlink","xlink:arcrole",i);break;case"xlinkRole":bn(e,"http://www.w3.org/1999/xlink","xlink:role",i);break;case"xlinkShow":bn(e,"http://www.w3.org/1999/xlink","xlink:show",i);break;case"xlinkTitle":bn(e,"http://www.w3.org/1999/xlink","xlink:title",i);break;case"xlinkType":bn(e,"http://www.w3.org/1999/xlink","xlink:type",i);break;case"xmlBase":bn(e,"http://www.w3.org/XML/1998/namespace","xml:base",i);break;case"xmlLang":bn(e,"http://www.w3.org/XML/1998/namespace","xml:lang",i);break;case"xmlSpace":bn(e,"http://www.w3.org/XML/1998/namespace","xml:space",i);break;case"is":cr(e,"is",i);break;case"innerText":case"textContent":break;default:(!(2<n.length)||n[0]!=="o"&&n[0]!=="O"||n[1]!=="n"&&n[1]!=="N")&&(n=kp.get(n)||n,cr(e,n,i))}}function ac(e,t,n,i,u,o){switch(n){case"style":Vf(e,i,o);break;case"dangerouslySetInnerHTML":if(i!=null){if(typeof i!="object"||!("__html"in i))throw Error(s(61));if(n=i.__html,n!=null){if(u.children!=null)throw Error(s(60));e.innerHTML=n}}break;case"children":typeof i=="string"?tl(e,i):(typeof i=="number"||typeof i=="bigint")&&tl(e,""+i);break;case"onScroll":i!=null&&Se("scroll",e);break;case"onScrollEnd":i!=null&&Se("scrollend",e);break;case"onClick":i!=null&&(e.onclick=rs);break;case"suppressContentEditableWarning":case"suppressHydrationWarning":case"innerHTML":case"ref":break;case"innerText":case"textContent":break;default:if(!Cf.hasOwnProperty(n))e:{if(n[0]==="o"&&n[1]==="n"&&(u=n.endsWith("Capture"),t=n.slice(2,u?n.length-7:void 0),o=e[wt]||null,o=o!=null?o[n]:null,typeof o=="function"&&e.removeEventListener(t,o,u),typeof i=="function")){typeof o!="function"&&o!==null&&(n in e?e[n]=null:e.hasAttribute(n)&&e.removeAttribute(n)),e.addEventListener(t,i,u);break e}n in e?e[n]=i:i===!0?e.setAttribute(n,""):cr(e,n,i)}}}function pt(e,t,n){switch(t){case"div":case"span":case"svg":case"path":case"a":case"g":case"p":case"li":break;case"img":Se("error",e),Se("load",e);var i=!1,u=!1,o;for(o in n)if(n.hasOwnProperty(o)){var h=n[o];if(h!=null)switch(o){case"src":i=!0;break;case"srcSet":u=!0;break;case"children":case"dangerouslySetInnerHTML":throw Error(s(137,t));default:Be(e,t,o,h,n,null)}}u&&Be(e,t,"srcSet",n.srcSet,n,null),i&&Be(e,t,"src",n.src,n,null);return;case"input":Se("invalid",e);var p=o=h=u=null,b=null,C=null;for(i in n)if(n.hasOwnProperty(i)){var k=n[i];if(k!=null)switch(i){case"name":u=k;break;case"type":h=k;break;case"checked":b=k;break;case"defaultChecked":C=k;break;case"value":o=k;break;case"defaultValue":p=k;break;case"children":case"dangerouslySetInnerHTML":if(k!=null)throw Error(s(137,t));break;default:Be(e,t,i,k,n,null)}}Lf(e,o,p,b,C,h,u,!1),dr(e);return;case"select":Se("invalid",e),i=h=o=null;for(u in n)if(n.hasOwnProperty(u)&&(p=n[u],p!=null))switch(u){case"value":o=p;break;case"defaultValue":h=p;break;case"multiple":i=p;default:Be(e,t,u,p,n,null)}t=o,n=h,e.multiple=!!i,t!=null?el(e,!!i,t,!1):n!=null&&el(e,!!i,n,!0);return;case"textarea":Se("invalid",e),o=u=i=null;for(h in n)if(n.hasOwnProperty(h)&&(p=n[h],p!=null))switch(h){case"value":i=p;break;case"defaultValue":u=p;break;case"children":o=p;break;case"dangerouslySetInnerHTML":if(p!=null)throw Error(s(91));break;default:Be(e,t,h,p,n,null)}qf(e,i,u,o),dr(e);return;case"option":for(b in n)if(n.hasOwnProperty(b)&&(i=n[b],i!=null))switch(b){case"selected":e.selected=i&&typeof i!="function"&&typeof i!="symbol";break;default:Be(e,t,b,i,n,null)}return;case"dialog":Se("beforetoggle",e),Se("toggle",e),Se("cancel",e),Se("close",e);break;case"iframe":case"object":Se("load",e);break;case"video":case"audio":for(i=0;i<Ri.length;i++)Se(Ri[i],e);break;case"image":Se("error",e),Se("load",e);break;case"details":Se("toggle",e);break;case"embed":case"source":case"link":Se("error",e),Se("load",e);case"area":case"base":case"br":case"col":case"hr":case"keygen":case"meta":case"param":case"track":case"wbr":case"menuitem":for(C in n)if(n.hasOwnProperty(C)&&(i=n[C],i!=null))switch(C){case"children":case"dangerouslySetInnerHTML":throw Error(s(137,t));default:Be(e,t,C,i,n,null)}return;default:if(vu(t)){for(k in n)n.hasOwnProperty(k)&&(i=n[k],i!==void 0&&ac(e,t,k,i,n,void 0));return}}for(p in n)n.hasOwnProperty(p)&&(i=n[p],i!=null&&Be(e,t,p,i,n,null))}function ov(e,t,n,i){switch(t){case"div":case"span":case"svg":case"path":case"a":case"g":case"p":case"li":break;case"input":var u=null,o=null,h=null,p=null,b=null,C=null,k=null;for(U in n){var Y=n[U];if(n.hasOwnProperty(U)&&Y!=null)switch(U){case"checked":break;case"value":break;case"defaultValue":b=Y;default:i.hasOwnProperty(U)||Be(e,t,U,null,i,Y)}}for(var M in i){var U=i[M];if(Y=n[M],i.hasOwnProperty(M)&&(U!=null||Y!=null))switch(M){case"type":o=U;break;case"name":u=U;break;case"checked":C=U;break;case"defaultChecked":k=U;break;case"value":h=U;break;case"defaultValue":p=U;break;case"children":case"dangerouslySetInnerHTML":if(U!=null)throw Error(s(137,t));break;default:U!==Y&&Be(e,t,M,U,i,Y)}}pu(e,h,p,b,C,k,o,u);return;case"select":U=h=p=M=null;for(o in n)if(b=n[o],n.hasOwnProperty(o)&&b!=null)switch(o){case"value":break;case"multiple":U=b;default:i.hasOwnProperty(o)||Be(e,t,o,null,i,b)}for(u in i)if(o=i[u],b=n[u],i.hasOwnProperty(u)&&(o!=null||b!=null))switch(u){case"value":M=o;break;case"defaultValue":p=o;break;case"multiple":h=o;default:o!==b&&Be(e,t,u,o,i,b)}t=p,n=h,i=U,M!=null?el(e,!!n,M,!1):!!i!=!!n&&(t!=null?el(e,!!n,t,!0):el(e,!!n,n?[]:"",!1));return;case"textarea":U=M=null;for(p in n)if(u=n[p],n.hasOwnProperty(p)&&u!=null&&!i.hasOwnProperty(p))switch(p){case"value":break;case"children":break;default:Be(e,t,p,null,i,u)}for(h in i)if(u=i[h],o=n[h],i.hasOwnProperty(h)&&(u!=null||o!=null))switch(h){case"value":M=u;break;case"defaultValue":U=u;break;case"children":break;case"dangerouslySetInnerHTML":if(u!=null)throw Error(s(91));break;default:u!==o&&Be(e,t,h,u,i,o)}kf(e,M,U);return;case"option":for(var ce in n)if(M=n[ce],n.hasOwnProperty(ce)&&M!=null&&!i.hasOwnProperty(ce))switch(ce){case"selected":e.selected=!1;break;default:Be(e,t,ce,null,i,M)}for(b in i)if(M=i[b],U=n[b],i.hasOwnProperty(b)&&M!==U&&(M!=null||U!=null))switch(b){case"selected":e.selected=M&&typeof M!="function"&&typeof M!="symbol";break;default:Be(e,t,b,M,i,U)}return;case"img":case"link":case"area":case"base":case"br":case"col":case"embed":case"hr":case"keygen":case"meta":case"param":case"source":case"track":case"wbr":case"menuitem":for(var se in n)M=n[se],n.hasOwnProperty(se)&&M!=null&&!i.hasOwnProperty(se)&&Be(e,t,se,null,i,M);for(C in i)if(M=i[C],U=n[C],i.hasOwnProperty(C)&&M!==U&&(M!=null||U!=null))switch(C){case"children":case"dangerouslySetInnerHTML":if(M!=null)throw Error(s(137,t));break;default:Be(e,t,C,M,i,U)}return;default:if(vu(t)){for(var Le in n)M=n[Le],n.hasOwnProperty(Le)&&M!==void 0&&!i.hasOwnProperty(Le)&&ac(e,t,Le,void 0,i,M);for(k in i)M=i[k],U=n[k],!i.hasOwnProperty(k)||M===U||M===void 0&&U===void 0||ac(e,t,k,M,i,U);return}}for(var T in n)M=n[T],n.hasOwnProperty(T)&&M!=null&&!i.hasOwnProperty(T)&&Be(e,t,T,null,i,M);for(Y in i)M=i[Y],U=n[Y],!i.hasOwnProperty(Y)||M===U||M==null&&U==null||Be(e,t,Y,M,i,U)}var lc=null,ic=null;function ss(e){return e.nodeType===9?e:e.ownerDocument}function Hm(e){switch(e){case"http://www.w3.org/2000/svg":return 1;case"http://www.w3.org/1998/Math/MathML":return 2;default:return 0}}function Vm(e,t){if(e===0)switch(t){case"svg":return 1;case"math":return 2;default:return 0}return e===1&&t==="foreignObject"?0:e}function rc(e,t){return e==="textarea"||e==="noscript"||typeof t.children=="string"||typeof t.children=="number"||typeof t.children=="bigint"||typeof t.dangerouslySetInnerHTML=="object"&&t.dangerouslySetInnerHTML!==null&&t.dangerouslySetInnerHTML.__html!=null}var sc=null;function cv(){var e=window.event;return e&&e.type==="popstate"?e===sc?!1:(sc=e,!0):(sc=null,!1)}var Ym=typeof setTimeout=="function"?setTimeout:void 0,fv=typeof clearTimeout=="function"?clearTimeout:void 0,Xm=typeof Promise=="function"?Promise:void 0,dv=typeof queueMicrotask=="function"?queueMicrotask:typeof Xm<"u"?function(e){return Xm.resolve(null).then(e).catch(hv)}:Ym;function hv(e){setTimeout(function(){throw e})}function ia(e){return e==="head"}function Gm(e,t){var n=t,i=0,u=0;do{var o=n.nextSibling;if(e.removeChild(n),o&&o.nodeType===8)if(n=o.data,n==="/$"){if(0<i&&8>i){n=i;var h=e.ownerDocument;if(n&1&&Ni(h.documentElement),n&2&&Ni(h.body),n&4)for(n=h.head,Ni(n),h=n.firstChild;h;){var p=h.nextSibling,b=h.nodeName;h[Gl]||b==="SCRIPT"||b==="STYLE"||b==="LINK"&&h.rel.toLowerCase()==="stylesheet"||n.removeChild(h),h=p}}if(u===0){e.removeChild(o),Li(t);return}u--}else n==="$"||n==="$?"||n==="$!"?u++:i=n.charCodeAt(0)-48;else i=0;n=o}while(n);Li(t)}function uc(e){var t=e.firstChild;for(t&&t.nodeType===10&&(t=t.nextSibling);t;){var n=t;switch(t=t.nextSibling,n.nodeName){case"HTML":case"HEAD":case"BODY":uc(n),du(n);continue;case"SCRIPT":case"STYLE":continue;case"LINK":if(n.rel.toLowerCase()==="stylesheet")continue}e.removeChild(n)}}function mv(e,t,n,i){for(;e.nodeType===1;){var u=n;if(e.nodeName.toLowerCase()!==t.toLowerCase()){if(!i&&(e.nodeName!=="INPUT"||e.type!=="hidden"))break}else if(i){if(!e[Gl])switch(t){case"meta":if(!e.hasAttribute("itemprop"))break;return e;case"link":if(o=e.getAttribute("rel"),o==="stylesheet"&&e.hasAttribute("data-precedence"))break;if(o!==u.rel||e.getAttribute("href")!==(u.href==null||u.href===""?null:u.href)||e.getAttribute("crossorigin")!==(u.crossOrigin==null?null:u.crossOrigin)||e.getAttribute("title")!==(u.title==null?null:u.title))break;return e;case"style":if(e.hasAttribute("data-precedence"))break;return e;case"script":if(o=e.getAttribute("src"),(o!==(u.src==null?null:u.src)||e.getAttribute("type")!==(u.type==null?null:u.type)||e.getAttribute("crossorigin")!==(u.crossOrigin==null?null:u.crossOrigin))&&o&&e.hasAttribute("async")&&!e.hasAttribute("itemprop"))break;return e;default:return e}}else if(t==="input"&&e.type==="hidden"){var o=u.name==null?null:""+u.name;if(u.type==="hidden"&&e.getAttribute("name")===o)return e}else return e;if(e=It(e.nextSibling),e===null)break}return null}function yv(e,t,n){if(t==="")return null;for(;e.nodeType!==3;)if((e.nodeType!==1||e.nodeName!=="INPUT"||e.type!=="hidden")&&!n||(e=It(e.nextSibling),e===null))return null;return e}function oc(e){return e.data==="$!"||e.data==="$?"&&e.ownerDocument.readyState==="complete"}function pv(e,t){var n=e.ownerDocument;if(e.data!=="$?"||n.readyState==="complete")t();else{var i=function(){t(),n.removeEventListener("DOMContentLoaded",i)};n.addEventListener("DOMContentLoaded",i),e._reactRetry=i}}function It(e){for(;e!=null;e=e.nextSibling){var t=e.nodeType;if(t===1||t===3)break;if(t===8){if(t=e.data,t==="$"||t==="$!"||t==="$?"||t==="F!"||t==="F")break;if(t==="/$")return null}}return e}var cc=null;function Qm(e){e=e.previousSibling;for(var t=0;e;){if(e.nodeType===8){var n=e.data;if(n==="$"||n==="$!"||n==="$?"){if(t===0)return e;t--}else n==="/$"&&t++}e=e.previousSibling}return null}function Zm(e,t,n){switch(t=ss(n),e){case"html":if(e=t.documentElement,!e)throw Error(s(452));return e;case"head":if(e=t.head,!e)throw Error(s(453));return e;case"body":if(e=t.body,!e)throw Error(s(454));return e;default:throw Error(s(451))}}function Ni(e){for(var t=e.attributes;t.length;)e.removeAttributeNode(t[0]);du(e)}var Jt=new Map,Km=new Set;function us(e){return typeof e.getRootNode=="function"?e.getRootNode():e.nodeType===9?e:e.ownerDocument}var Un=F.d;F.d={f:gv,r:vv,D:bv,C:Sv,L:xv,m:Ev,X:wv,S:_v,M:Av};function gv(){var e=Un.f(),t=Ir();return e||t}function vv(e){var t=$a(e);t!==null&&t.tag===5&&t.type==="form"?hh(t):Un.r(e)}var Cl=typeof document>"u"?null:document;function Fm(e,t,n){var i=Cl;if(i&&typeof t=="string"&&t){var u=Yt(t);u='link[rel="'+e+'"][href="'+u+'"]',typeof n=="string"&&(u+='[crossorigin="'+n+'"]'),Km.has(u)||(Km.add(u),e={rel:e,crossOrigin:n,href:t},i.querySelector(u)===null&&(t=i.createElement("link"),pt(t,"link",e),ut(t),i.head.appendChild(t)))}}function bv(e){Un.D(e),Fm("dns-prefetch",e,null)}function Sv(e,t){Un.C(e,t),Fm("preconnect",e,t)}function xv(e,t,n){Un.L(e,t,n);var i=Cl;if(i&&e&&t){var u='link[rel="preload"][as="'+Yt(t)+'"]';t==="image"&&n&&n.imageSrcSet?(u+='[imagesrcset="'+Yt(n.imageSrcSet)+'"]',typeof n.imageSizes=="string"&&(u+='[imagesizes="'+Yt(n.imageSizes)+'"]')):u+='[href="'+Yt(e)+'"]';var o=u;switch(t){case"style":o=Dl(e);break;case"script":o=Ml(e)}Jt.has(o)||(e=v({rel:"preload",href:t==="image"&&n&&n.imageSrcSet?void 0:e,as:t},n),Jt.set(o,e),i.querySelector(u)!==null||t==="style"&&i.querySelector(Ci(o))||t==="script"&&i.querySelector(Di(o))||(t=i.createElement("link"),pt(t,"link",e),ut(t),i.head.appendChild(t)))}}function Ev(e,t){Un.m(e,t);var n=Cl;if(n&&e){var i=t&&typeof t.as=="string"?t.as:"script",u='link[rel="modulepreload"][as="'+Yt(i)+'"][href="'+Yt(e)+'"]',o=u;switch(i){case"audioworklet":case"paintworklet":case"serviceworker":case"sharedworker":case"worker":case"script":o=Ml(e)}if(!Jt.has(o)&&(e=v({rel:"modulepreload",href:e},t),Jt.set(o,e),n.querySelector(u)===null)){switch(i){case"audioworklet":case"paintworklet":case"serviceworker":case"sharedworker":case"worker":case"script":if(n.querySelector(Di(o)))return}i=n.createElement("link"),pt(i,"link",e),ut(i),n.head.appendChild(i)}}}function _v(e,t,n){Un.S(e,t,n);var i=Cl;if(i&&e){var u=Wa(i).hoistableStyles,o=Dl(e);t=t||"default";var h=u.get(o);if(!h){var p={loading:0,preload:null};if(h=i.querySelector(Ci(o)))p.loading=5;else{e=v({rel:"stylesheet",href:e,"data-precedence":t},n),(n=Jt.get(o))&&fc(e,n);var b=h=i.createElement("link");ut(b),pt(b,"link",e),b._p=new Promise(function(C,k){b.onload=C,b.onerror=k}),b.addEventListener("load",function(){p.loading|=1}),b.addEventListener("error",function(){p.loading|=2}),p.loading|=4,os(h,t,i)}h={type:"stylesheet",instance:h,count:1,state:p},u.set(o,h)}}}function wv(e,t){Un.X(e,t);var n=Cl;if(n&&e){var i=Wa(n).hoistableScripts,u=Ml(e),o=i.get(u);o||(o=n.querySelector(Di(u)),o||(e=v({src:e,async:!0},t),(t=Jt.get(u))&&dc(e,t),o=n.createElement("script"),ut(o),pt(o,"link",e),n.head.appendChild(o)),o={type:"script",instance:o,count:1,state:null},i.set(u,o))}}function Av(e,t){Un.M(e,t);var n=Cl;if(n&&e){var i=Wa(n).hoistableScripts,u=Ml(e),o=i.get(u);o||(o=n.querySelector(Di(u)),o||(e=v({src:e,async:!0,type:"module"},t),(t=Jt.get(u))&&dc(e,t),o=n.createElement("script"),ut(o),pt(o,"link",e),n.head.appendChild(o)),o={type:"script",instance:o,count:1,state:null},i.set(u,o))}}function Jm(e,t,n,i){var u=(u=fe.current)?us(u):null;if(!u)throw Error(s(446));switch(e){case"meta":case"title":return null;case"style":return typeof n.precedence=="string"&&typeof n.href=="string"?(t=Dl(n.href),n=Wa(u).hoistableStyles,i=n.get(t),i||(i={type:"style",instance:null,count:0,state:null},n.set(t,i)),i):{type:"void",instance:null,count:0,state:null};case"link":if(n.rel==="stylesheet"&&typeof n.href=="string"&&typeof n.precedence=="string"){e=Dl(n.href);var o=Wa(u).hoistableStyles,h=o.get(e);if(h||(u=u.ownerDocument||u,h={type:"stylesheet",instance:null,count:0,state:{loading:0,preload:null}},o.set(e,h),(o=u.querySelector(Ci(e)))&&!o._p&&(h.instance=o,h.state.loading=5),Jt.has(e)||(n={rel:"preload",as:"style",href:n.href,crossOrigin:n.crossOrigin,integrity:n.integrity,media:n.media,hrefLang:n.hrefLang,referrerPolicy:n.referrerPolicy},Jt.set(e,n),o||Tv(u,e,n,h.state))),t&&i===null)throw Error(s(528,""));return h}if(t&&i!==null)throw Error(s(529,""));return null;case"script":return t=n.async,n=n.src,typeof n=="string"&&t&&typeof t!="function"&&typeof t!="symbol"?(t=Ml(n),n=Wa(u).hoistableScripts,i=n.get(t),i||(i={type:"script",instance:null,count:0,state:null},n.set(t,i)),i):{type:"void",instance:null,count:0,state:null};default:throw Error(s(444,e))}}function Dl(e){return'href="'+Yt(e)+'"'}function Ci(e){return'link[rel="stylesheet"]['+e+"]"}function $m(e){return v({},e,{"data-precedence":e.precedence,precedence:null})}function Tv(e,t,n,i){e.querySelector('link[rel="preload"][as="style"]['+t+"]")?i.loading=1:(t=e.createElement("link"),i.preload=t,t.addEventListener("load",function(){return i.loading|=1}),t.addEventListener("error",function(){return i.loading|=2}),pt(t,"link",n),ut(t),e.head.appendChild(t))}function Ml(e){return'[src="'+Yt(e)+'"]'}function Di(e){return"script[async]"+e}function Wm(e,t,n){if(t.count++,t.instance===null)switch(t.type){case"style":var i=e.querySelector('style[data-href~="'+Yt(n.href)+'"]');if(i)return t.instance=i,ut(i),i;var u=v({},n,{"data-href":n.href,"data-precedence":n.precedence,href:null,precedence:null});return i=(e.ownerDocument||e).createElement("style"),ut(i),pt(i,"style",u),os(i,n.precedence,e),t.instance=i;case"stylesheet":u=Dl(n.href);var o=e.querySelector(Ci(u));if(o)return t.state.loading|=4,t.instance=o,ut(o),o;i=$m(n),(u=Jt.get(u))&&fc(i,u),o=(e.ownerDocument||e).createElement("link"),ut(o);var h=o;return h._p=new Promise(function(p,b){h.onload=p,h.onerror=b}),pt(o,"link",i),t.state.loading|=4,os(o,n.precedence,e),t.instance=o;case"script":return o=Ml(n.src),(u=e.querySelector(Di(o)))?(t.instance=u,ut(u),u):(i=n,(u=Jt.get(o))&&(i=v({},n),dc(i,u)),e=e.ownerDocument||e,u=e.createElement("script"),ut(u),pt(u,"link",i),e.head.appendChild(u),t.instance=u);case"void":return null;default:throw Error(s(443,t.type))}else t.type==="stylesheet"&&(t.state.loading&4)===0&&(i=t.instance,t.state.loading|=4,os(i,n.precedence,e));return t.instance}function os(e,t,n){for(var i=n.querySelectorAll('link[rel="stylesheet"][data-precedence],style[data-precedence]'),u=i.length?i[i.length-1]:null,o=u,h=0;h<i.length;h++){var p=i[h];if(p.dataset.precedence===t)o=p;else if(o!==u)break}o?o.parentNode.insertBefore(e,o.nextSibling):(t=n.nodeType===9?n.head:n,t.insertBefore(e,t.firstChild))}function fc(e,t){e.crossOrigin==null&&(e.crossOrigin=t.crossOrigin),e.referrerPolicy==null&&(e.referrerPolicy=t.referrerPolicy),e.title==null&&(e.title=t.title)}function dc(e,t){e.crossOrigin==null&&(e.crossOrigin=t.crossOrigin),e.referrerPolicy==null&&(e.referrerPolicy=t.referrerPolicy),e.integrity==null&&(e.integrity=t.integrity)}var cs=null;function Pm(e,t,n){if(cs===null){var i=new Map,u=cs=new Map;u.set(n,i)}else u=cs,i=u.get(n),i||(i=new Map,u.set(n,i));if(i.has(e))return i;for(i.set(e,null),n=n.getElementsByTagName(e),u=0;u<n.length;u++){var o=n[u];if(!(o[Gl]||o[gt]||e==="link"&&o.getAttribute("rel")==="stylesheet")&&o.namespaceURI!=="http://www.w3.org/2000/svg"){var h=o.getAttribute(t)||"";h=e+h;var p=i.get(h);p?p.push(o):i.set(h,[o])}}return i}function Im(e,t,n){e=e.ownerDocument||e,e.head.insertBefore(n,t==="title"?e.querySelector("head > title"):null)}function Rv(e,t,n){if(n===1||t.itemProp!=null)return!1;switch(e){case"meta":case"title":return!0;case"style":if(typeof t.precedence!="string"||typeof t.href!="string"||t.href==="")break;return!0;case"link":if(typeof t.rel!="string"||typeof t.href!="string"||t.href===""||t.onLoad||t.onError)break;switch(t.rel){case"stylesheet":return e=t.disabled,typeof t.precedence=="string"&&e==null;default:return!0}case"script":if(t.async&&typeof t.async!="function"&&typeof t.async!="symbol"&&!t.onLoad&&!t.onError&&t.src&&typeof t.src=="string")return!0}return!1}function ey(e){return!(e.type==="stylesheet"&&(e.state.loading&3)===0)}var Mi=null;function Ov(){}function Nv(e,t,n){if(Mi===null)throw Error(s(475));var i=Mi;if(t.type==="stylesheet"&&(typeof n.media!="string"||matchMedia(n.media).matches!==!1)&&(t.state.loading&4)===0){if(t.instance===null){var u=Dl(n.href),o=e.querySelector(Ci(u));if(o){e=o._p,e!==null&&typeof e=="object"&&typeof e.then=="function"&&(i.count++,i=fs.bind(i),e.then(i,i)),t.state.loading|=4,t.instance=o,ut(o);return}o=e.ownerDocument||e,n=$m(n),(u=Jt.get(u))&&fc(n,u),o=o.createElement("link"),ut(o);var h=o;h._p=new Promise(function(p,b){h.onload=p,h.onerror=b}),pt(o,"link",n),t.instance=o}i.stylesheets===null&&(i.stylesheets=new Map),i.stylesheets.set(t,e),(e=t.state.preload)&&(t.state.loading&3)===0&&(i.count++,t=fs.bind(i),e.addEventListener("load",t),e.addEventListener("error",t))}}function Cv(){if(Mi===null)throw Error(s(475));var e=Mi;return e.stylesheets&&e.count===0&&hc(e,e.stylesheets),0<e.count?function(t){var n=setTimeout(function(){if(e.stylesheets&&hc(e,e.stylesheets),e.unsuspend){var i=e.unsuspend;e.unsuspend=null,i()}},6e4);return e.unsuspend=t,function(){e.unsuspend=null,clearTimeout(n)}}:null}function fs(){if(this.count--,this.count===0){if(this.stylesheets)hc(this,this.stylesheets);else if(this.unsuspend){var e=this.unsuspend;this.unsuspend=null,e()}}}var ds=null;function hc(e,t){e.stylesheets=null,e.unsuspend!==null&&(e.count++,ds=new Map,t.forEach(Dv,e),ds=null,fs.call(e))}function Dv(e,t){if(!(t.state.loading&4)){var n=ds.get(e);if(n)var i=n.get(null);else{n=new Map,ds.set(e,n);for(var u=e.querySelectorAll("link[data-precedence],style[data-precedence]"),o=0;o<u.length;o++){var h=u[o];(h.nodeName==="LINK"||h.getAttribute("media")!=="not all")&&(n.set(h.dataset.precedence,h),i=h)}i&&n.set(null,i)}u=t.instance,h=u.getAttribute("data-precedence"),o=n.get(h)||i,o===i&&n.set(null,u),n.set(h,u),this.count++,i=fs.bind(this),u.addEventListener("load",i),u.addEventListener("error",i),o?o.parentNode.insertBefore(u,o.nextSibling):(e=e.nodeType===9?e.head:e,e.insertBefore(u,e.firstChild)),t.state.loading|=4}}var Ui={$$typeof:$,Provider:null,Consumer:null,_currentValue:le,_currentValue2:le,_threadCount:0};function Mv(e,t,n,i,u,o,h,p){this.tag=1,this.containerInfo=e,this.pingCache=this.current=this.pendingChildren=null,this.timeoutHandle=-1,this.callbackNode=this.next=this.pendingContext=this.context=this.cancelPendingCommit=null,this.callbackPriority=0,this.expirationTimes=uu(-1),this.entangledLanes=this.shellSuspendCounter=this.errorRecoveryDisabledLanes=this.expiredLanes=this.warmLanes=this.pingedLanes=this.suspendedLanes=this.pendingLanes=0,this.entanglements=uu(0),this.hiddenUpdates=uu(null),this.identifierPrefix=i,this.onUncaughtError=u,this.onCaughtError=o,this.onRecoverableError=h,this.pooledCache=null,this.pooledCacheLanes=0,this.formState=p,this.incompleteTransitions=new Map}function ty(e,t,n,i,u,o,h,p,b,C,k,Y){return e=new Mv(e,t,n,h,p,b,C,Y),t=1,o===!0&&(t|=24),o=Ut(3,null,null,t),e.current=o,o.stateNode=e,t=Ku(),t.refCount++,e.pooledCache=t,t.refCount++,o.memoizedState={element:i,isDehydrated:n,cache:t},Wu(o),e}function ny(e){return e?(e=ol,e):ol}function ay(e,t,n,i,u,o){u=ny(u),i.context===null?i.context=u:i.pendingContext=u,i=Zn(t),i.payload={element:n},o=o===void 0?null:o,o!==null&&(i.callback=o),n=Kn(e,i,t),n!==null&&(kt(n,e,t),oi(n,e,t))}function ly(e,t){if(e=e.memoizedState,e!==null&&e.dehydrated!==null){var n=e.retryLane;e.retryLane=n!==0&&n<t?n:t}}function mc(e,t){ly(e,t),(e=e.alternate)&&ly(e,t)}function iy(e){if(e.tag===13){var t=ul(e,67108864);t!==null&&kt(t,e,67108864),mc(e,67108864)}}var hs=!0;function Uv(e,t,n,i){var u=j.T;j.T=null;var o=F.p;try{F.p=2,yc(e,t,n,i)}finally{F.p=o,j.T=u}}function zv(e,t,n,i){var u=j.T;j.T=null;var o=F.p;try{F.p=8,yc(e,t,n,i)}finally{F.p=o,j.T=u}}function yc(e,t,n,i){if(hs){var u=pc(i);if(u===null)nc(e,t,i,ms,n),sy(e,i);else if(Bv(u,e,t,n,i))i.stopPropagation();else if(sy(e,i),t&4&&-1<jv.indexOf(e)){for(;u!==null;){var o=$a(u);if(o!==null)switch(o.tag){case 3:if(o=o.stateNode,o.current.memoizedState.isDehydrated){var h=vn(o.pendingLanes);if(h!==0){var p=o;for(p.pendingLanes|=2,p.entangledLanes|=2;h;){var b=1<<31-He(h);p.entanglements[1]|=b,h&=~b}dn(o),(Ue&6)===0&&(Wr=Ht()+500,Ti(0))}}break;case 13:p=ul(o,2),p!==null&&kt(p,o,2),Ir(),mc(o,2)}if(o=pc(i),o===null&&nc(e,t,i,ms,n),o===u)break;u=o}u!==null&&i.stopPropagation()}else nc(e,t,i,null,n)}}function pc(e){return e=Su(e),gc(e)}var ms=null;function gc(e){if(ms=null,e=Ja(e),e!==null){var t=f(e);if(t===null)e=null;else{var n=t.tag;if(n===13){if(e=d(t),e!==null)return e;e=null}else if(n===3){if(t.stateNode.current.memoizedState.isDehydrated)return t.tag===3?t.stateNode.containerInfo:null;e=null}else t!==e&&(e=null)}}return ms=e,null}function ry(e){switch(e){case"beforetoggle":case"cancel":case"click":case"close":case"contextmenu":case"copy":case"cut":case"auxclick":case"dblclick":case"dragend":case"dragstart":case"drop":case"focusin":case"focusout":case"input":case"invalid":case"keydown":case"keypress":case"keyup":case"mousedown":case"mouseup":case"paste":case"pause":case"play":case"pointercancel":case"pointerdown":case"pointerup":case"ratechange":case"reset":case"resize":case"seeked":case"submit":case"toggle":case"touchcancel":case"touchend":case"touchstart":case"volumechange":case"change":case"selectionchange":case"textInput":case"compositionstart":case"compositionend":case"compositionupdate":case"beforeblur":case"afterblur":case"beforeinput":case"blur":case"fullscreenchange":case"focus":case"hashchange":case"popstate":case"select":case"selectstart":return 2;case"drag":case"dragenter":case"dragexit":case"dragleave":case"dragover":case"mousemove":case"mouseout":case"mouseover":case"pointermove":case"pointerout":case"pointerover":case"scroll":case"touchmove":case"wheel":case"mouseenter":case"mouseleave":case"pointerenter":case"pointerleave":return 8;case"message":switch(Ef()){case Yl:return 2;case x:return 8;case D:case q:return 32;case P:return 268435456;default:return 32}default:return 32}}var vc=!1,ra=null,sa=null,ua=null,zi=new Map,ji=new Map,oa=[],jv="mousedown mouseup touchcancel touchend touchstart auxclick dblclick pointercancel pointerdown pointerup dragend dragstart drop compositionend compositionstart keydown keypress keyup input textInput copy cut paste click change contextmenu reset".split(" ");function sy(e,t){switch(e){case"focusin":case"focusout":ra=null;break;case"dragenter":case"dragleave":sa=null;break;case"mouseover":case"mouseout":ua=null;break;case"pointerover":case"pointerout":zi.delete(t.pointerId);break;case"gotpointercapture":case"lostpointercapture":ji.delete(t.pointerId)}}function Bi(e,t,n,i,u,o){return e===null||e.nativeEvent!==o?(e={blockedOn:t,domEventName:n,eventSystemFlags:i,nativeEvent:o,targetContainers:[u]},t!==null&&(t=$a(t),t!==null&&iy(t)),e):(e.eventSystemFlags|=i,t=e.targetContainers,u!==null&&t.indexOf(u)===-1&&t.push(u),e)}function Bv(e,t,n,i,u){switch(t){case"focusin":return ra=Bi(ra,e,t,n,i,u),!0;case"dragenter":return sa=Bi(sa,e,t,n,i,u),!0;case"mouseover":return ua=Bi(ua,e,t,n,i,u),!0;case"pointerover":var o=u.pointerId;return zi.set(o,Bi(zi.get(o)||null,e,t,n,i,u)),!0;case"gotpointercapture":return o=u.pointerId,ji.set(o,Bi(ji.get(o)||null,e,t,n,i,u)),!0}return!1}function uy(e){var t=Ja(e.target);if(t!==null){var n=f(t);if(n!==null){if(t=n.tag,t===13){if(t=d(n),t!==null){e.blockedOn=t,Np(e.priority,function(){if(n.tag===13){var i=Lt();i=ou(i);var u=ul(n,i);u!==null&&kt(u,n,i),mc(n,i)}});return}}else if(t===3&&n.stateNode.current.memoizedState.isDehydrated){e.blockedOn=n.tag===3?n.stateNode.containerInfo:null;return}}}e.blockedOn=null}function ys(e){if(e.blockedOn!==null)return!1;for(var t=e.targetContainers;0<t.length;){var n=pc(e.nativeEvent);if(n===null){n=e.nativeEvent;var i=new n.constructor(n.type,n);bu=i,n.target.dispatchEvent(i),bu=null}else return t=$a(n),t!==null&&iy(t),e.blockedOn=n,!1;t.shift()}return!0}function oy(e,t,n){ys(e)&&n.delete(t)}function Lv(){vc=!1,ra!==null&&ys(ra)&&(ra=null),sa!==null&&ys(sa)&&(sa=null),ua!==null&&ys(ua)&&(ua=null),zi.forEach(oy),ji.forEach(oy)}function ps(e,t){e.blockedOn===t&&(e.blockedOn=null,vc||(vc=!0,a.unstable_scheduleCallback(a.unstable_NormalPriority,Lv)))}var gs=null;function cy(e){gs!==e&&(gs=e,a.unstable_scheduleCallback(a.unstable_NormalPriority,function(){gs===e&&(gs=null);for(var t=0;t<e.length;t+=3){var n=e[t],i=e[t+1],u=e[t+2];if(typeof i!="function"){if(gc(i||n)===null)continue;break}var o=$a(n);o!==null&&(e.splice(t,3),t-=3,go(o,{pending:!0,data:u,method:n.method,action:i},i,u))}}))}function Li(e){function t(b){return ps(b,e)}ra!==null&&ps(ra,e),sa!==null&&ps(sa,e),ua!==null&&ps(ua,e),zi.forEach(t),ji.forEach(t);for(var n=0;n<oa.length;n++){var i=oa[n];i.blockedOn===e&&(i.blockedOn=null)}for(;0<oa.length&&(n=oa[0],n.blockedOn===null);)uy(n),n.blockedOn===null&&oa.shift();if(n=(e.ownerDocument||e).$$reactFormReplay,n!=null)for(i=0;i<n.length;i+=3){var u=n[i],o=n[i+1],h=u[wt]||null;if(typeof o=="function")h||cy(n);else if(h){var p=null;if(o&&o.hasAttribute("formAction")){if(u=o,h=o[wt]||null)p=h.formAction;else if(gc(u)!==null)continue}else p=h.action;typeof p=="function"?n[i+1]=p:(n.splice(i,3),i-=3),cy(n)}}}function bc(e){this._internalRoot=e}vs.prototype.render=bc.prototype.render=function(e){var t=this._internalRoot;if(t===null)throw Error(s(409));var n=t.current,i=Lt();ay(n,i,e,t,null,null)},vs.prototype.unmount=bc.prototype.unmount=function(){var e=this._internalRoot;if(e!==null){this._internalRoot=null;var t=e.containerInfo;ay(e.current,2,null,e,null,null),Ir(),t[Fa]=null}};function vs(e){this._internalRoot=e}vs.prototype.unstable_scheduleHydration=function(e){if(e){var t=Rf();e={blockedOn:null,target:e,priority:t};for(var n=0;n<oa.length&&t!==0&&t<oa[n].priority;n++);oa.splice(n,0,e),n===0&&uy(e)}};var fy=l.version;if(fy!=="19.1.0")throw Error(s(527,fy,"19.1.0"));F.findDOMNode=function(e){var t=e._reactInternals;if(t===void 0)throw typeof e.render=="function"?Error(s(188)):(e=Object.keys(e).join(","),Error(s(268,e)));return e=g(t),e=e!==null?y(e):null,e=e===null?null:e.stateNode,e};var kv={bundleType:0,version:"19.1.0",rendererPackageName:"react-dom",currentDispatcherRef:j,reconcilerVersion:"19.1.0"};if(typeof __REACT_DEVTOOLS_GLOBAL_HOOK__<"u"){var bs=__REACT_DEVTOOLS_GLOBAL_HOOK__;if(!bs.isDisabled&&bs.supportsFiber)try{ne=bs.inject(kv),de=bs}catch{}}return qi.createRoot=function(e,t){if(!c(e))throw Error(s(299));var n=!1,i="",u=Rh,o=Oh,h=Nh,p=null;return t!=null&&(t.unstable_strictMode===!0&&(n=!0),t.identifierPrefix!==void 0&&(i=t.identifierPrefix),t.onUncaughtError!==void 0&&(u=t.onUncaughtError),t.onCaughtError!==void 0&&(o=t.onCaughtError),t.onRecoverableError!==void 0&&(h=t.onRecoverableError),t.unstable_transitionCallbacks!==void 0&&(p=t.unstable_transitionCallbacks)),t=ty(e,1,!1,null,null,n,i,u,o,h,p,null),e[Fa]=t.current,tc(e),new bc(t)},qi.hydrateRoot=function(e,t,n){if(!c(e))throw Error(s(299));var i=!1,u="",o=Rh,h=Oh,p=Nh,b=null,C=null;return n!=null&&(n.unstable_strictMode===!0&&(i=!0),n.identifierPrefix!==void 0&&(u=n.identifierPrefix),n.onUncaughtError!==void 0&&(o=n.onUncaughtError),n.onCaughtError!==void 0&&(h=n.onCaughtError),n.onRecoverableError!==void 0&&(p=n.onRecoverableError),n.unstable_transitionCallbacks!==void 0&&(b=n.unstable_transitionCallbacks),n.formState!==void 0&&(C=n.formState)),t=ty(e,1,!0,t,n??null,i,u,o,h,p,b,C),t.context=ny(null),n=t.current,i=Lt(),i=ou(i),u=Zn(i),u.callback=null,Kn(n,u,i),n=i,t.current.lanes=n,Xl(t,n),dn(t),e[Fa]=t.current,tc(e),new vs(t)},qi.version="19.1.0",qi}var xy;function Jv(){if(xy)return Ec.exports;xy=1;function a(){if(!(typeof __REACT_DEVTOOLS_GLOBAL_HOOK__>"u"||typeof __REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE!="function"))try{__REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE(a)}catch(l){console.error(l)}}return a(),Ec.exports=Fv(),Ec.exports}var $v=Jv();const Ey=a=>{let l;const r=new Set,s=(y,v)=>{const A=typeof y=="function"?y(l):y;if(!Object.is(A,l)){const _=l;l=v??(typeof A!="object"||A===null)?A:Object.assign({},l,A),r.forEach(Q=>Q(l,_))}},c=()=>l,m={setState:s,getState:c,getInitialState:()=>g,subscribe:y=>(r.add(y),()=>r.delete(y))},g=l=a(s,c,m);return m},Wv=a=>a?Ey(a):Ey,Pv=a=>a;function Iv(a,l=Pv){const r=at.useSyncExternalStore(a.subscribe,()=>l(a.getState()),()=>l(a.getInitialState()));return at.useDebugValue(r),r}const _y=a=>{const l=Wv(a),r=s=>Iv(l,s);return Object.assign(r,l),r},eb=a=>a?_y(a):_y,ma=eb(a=>({selectedConversation:null,setSelectedConversation:l=>a({selectedConversation:l}),message:[],setMessage:l=>a({message:l})}));function f0(a,l){return function(){return a.apply(l,arguments)}}const{toString:tb}=Object.prototype,{getPrototypeOf:ef}=Object,{iterator:Zs,toStringTag:d0}=Symbol,Ks=(a=>l=>{const r=tb.call(l);return a[r]||(a[r]=r.slice(8,-1).toLowerCase())})(Object.create(null)),ln=a=>(a=a.toLowerCase(),l=>Ks(l)===a),Fs=a=>l=>typeof l===a,{isArray:jl}=Array,Wi=Fs("undefined");function nb(a){return a!==null&&!Wi(a)&&a.constructor!==null&&!Wi(a.constructor)&&Ct(a.constructor.isBuffer)&&a.constructor.isBuffer(a)}const h0=ln("ArrayBuffer");function ab(a){let l;return typeof ArrayBuffer<"u"&&ArrayBuffer.isView?l=ArrayBuffer.isView(a):l=a&&a.buffer&&h0(a.buffer),l}const lb=Fs("string"),Ct=Fs("function"),m0=Fs("number"),Js=a=>a!==null&&typeof a=="object",ib=a=>a===!0||a===!1,As=a=>{if(Ks(a)!=="object")return!1;const l=ef(a);return(l===null||l===Object.prototype||Object.getPrototypeOf(l)===null)&&!(d0 in a)&&!(Zs in a)},rb=ln("Date"),sb=ln("File"),ub=ln("Blob"),ob=ln("FileList"),cb=a=>Js(a)&&Ct(a.pipe),fb=a=>{let l;return a&&(typeof FormData=="function"&&a instanceof FormData||Ct(a.append)&&((l=Ks(a))==="formdata"||l==="object"&&Ct(a.toString)&&a.toString()==="[object FormData]"))},db=ln("URLSearchParams"),[hb,mb,yb,pb]=["ReadableStream","Request","Response","Headers"].map(ln),gb=a=>a.trim?a.trim():a.replace(/^[\s\uFEFF\xA0]+|[\s\uFEFF\xA0]+$/g,"");function er(a,l,{allOwnKeys:r=!1}={}){if(a===null||typeof a>"u")return;let s,c;if(typeof a!="object"&&(a=[a]),jl(a))for(s=0,c=a.length;s<c;s++)l.call(null,a[s],s,a);else{const f=r?Object.getOwnPropertyNames(a):Object.keys(a),d=f.length;let m;for(s=0;s<d;s++)m=f[s],l.call(null,a[m],m,a)}}function y0(a,l){l=l.toLowerCase();const r=Object.keys(a);let s=r.length,c;for(;s-- >0;)if(c=r[s],l===c.toLowerCase())return c;return null}const La=typeof globalThis<"u"?globalThis:typeof self<"u"?self:typeof window<"u"?window:global,p0=a=>!Wi(a)&&a!==La;function jc(){const{caseless:a}=p0(this)&&this||{},l={},r=(s,c)=>{const f=a&&y0(l,c)||c;As(l[f])&&As(s)?l[f]=jc(l[f],s):As(s)?l[f]=jc({},s):jl(s)?l[f]=s.slice():l[f]=s};for(let s=0,c=arguments.length;s<c;s++)arguments[s]&&er(arguments[s],r);return l}const vb=(a,l,r,{allOwnKeys:s}={})=>(er(l,(c,f)=>{r&&Ct(c)?a[f]=f0(c,r):a[f]=c},{allOwnKeys:s}),a),bb=a=>(a.charCodeAt(0)===65279&&(a=a.slice(1)),a),Sb=(a,l,r,s)=>{a.prototype=Object.create(l.prototype,s),a.prototype.constructor=a,Object.defineProperty(a,"super",{value:l.prototype}),r&&Object.assign(a.prototype,r)},xb=(a,l,r,s)=>{let c,f,d;const m={};if(l=l||{},a==null)return l;do{for(c=Object.getOwnPropertyNames(a),f=c.length;f-- >0;)d=c[f],(!s||s(d,a,l))&&!m[d]&&(l[d]=a[d],m[d]=!0);a=r!==!1&&ef(a)}while(a&&(!r||r(a,l))&&a!==Object.prototype);return l},Eb=(a,l,r)=>{a=String(a),(r===void 0||r>a.length)&&(r=a.length),r-=l.length;const s=a.indexOf(l,r);return s!==-1&&s===r},_b=a=>{if(!a)return null;if(jl(a))return a;let l=a.length;if(!m0(l))return null;const r=new Array(l);for(;l-- >0;)r[l]=a[l];return r},wb=(a=>l=>a&&l instanceof a)(typeof Uint8Array<"u"&&ef(Uint8Array)),Ab=(a,l)=>{const s=(a&&a[Zs]).call(a);let c;for(;(c=s.next())&&!c.done;){const f=c.value;l.call(a,f[0],f[1])}},Tb=(a,l)=>{let r;const s=[];for(;(r=a.exec(l))!==null;)s.push(r);return s},Rb=ln("HTMLFormElement"),Ob=a=>a.toLowerCase().replace(/[-_\s]([a-z\d])(\w*)/g,function(r,s,c){return s.toUpperCase()+c}),wy=(({hasOwnProperty:a})=>(l,r)=>a.call(l,r))(Object.prototype),Nb=ln("RegExp"),g0=(a,l)=>{const r=Object.getOwnPropertyDescriptors(a),s={};er(r,(c,f)=>{let d;(d=l(c,f,a))!==!1&&(s[f]=d||c)}),Object.defineProperties(a,s)},Cb=a=>{g0(a,(l,r)=>{if(Ct(a)&&["arguments","caller","callee"].indexOf(r)!==-1)return!1;const s=a[r];if(Ct(s)){if(l.enumerable=!1,"writable"in l){l.writable=!1;return}l.set||(l.set=()=>{throw Error("Can not rewrite read-only method '"+r+"'")})}})},Db=(a,l)=>{const r={},s=c=>{c.forEach(f=>{r[f]=!0})};return jl(a)?s(a):s(String(a).split(l)),r},Mb=()=>{},Ub=(a,l)=>a!=null&&Number.isFinite(a=+a)?a:l;function zb(a){return!!(a&&Ct(a.append)&&a[d0]==="FormData"&&a[Zs])}const jb=a=>{const l=new Array(10),r=(s,c)=>{if(Js(s)){if(l.indexOf(s)>=0)return;if(!("toJSON"in s)){l[c]=s;const f=jl(s)?[]:{};return er(s,(d,m)=>{const g=r(d,c+1);!Wi(g)&&(f[m]=g)}),l[c]=void 0,f}}return s};return r(a,0)},Bb=ln("AsyncFunction"),Lb=a=>a&&(Js(a)||Ct(a))&&Ct(a.then)&&Ct(a.catch),v0=((a,l)=>a?setImmediate:l?((r,s)=>(La.addEventListener("message",({source:c,data:f})=>{c===La&&f===r&&s.length&&s.shift()()},!1),c=>{s.push(c),La.postMessage(r,"*")}))(`axios@${Math.random()}`,[]):r=>setTimeout(r))(typeof setImmediate=="function",Ct(La.postMessage)),kb=typeof queueMicrotask<"u"?queueMicrotask.bind(La):typeof process<"u"&&process.nextTick||v0,qb=a=>a!=null&&Ct(a[Zs]),z={isArray:jl,isArrayBuffer:h0,isBuffer:nb,isFormData:fb,isArrayBufferView:ab,isString:lb,isNumber:m0,isBoolean:ib,isObject:Js,isPlainObject:As,isReadableStream:hb,isRequest:mb,isResponse:yb,isHeaders:pb,isUndefined:Wi,isDate:rb,isFile:sb,isBlob:ub,isRegExp:Nb,isFunction:Ct,isStream:cb,isURLSearchParams:db,isTypedArray:wb,isFileList:ob,forEach:er,merge:jc,extend:vb,trim:gb,stripBOM:bb,inherits:Sb,toFlatObject:xb,kindOf:Ks,kindOfTest:ln,endsWith:Eb,toArray:_b,forEachEntry:Ab,matchAll:Tb,isHTMLForm:Rb,hasOwnProperty:wy,hasOwnProp:wy,reduceDescriptors:g0,freezeMethods:Cb,toObjectSet:Db,toCamelCase:Ob,noop:Mb,toFiniteNumber:Ub,findKey:y0,global:La,isContextDefined:p0,isSpecCompliantForm:zb,toJSONObject:jb,isAsyncFn:Bb,isThenable:Lb,setImmediate:v0,asap:kb,isIterable:qb};function he(a,l,r,s,c){Error.call(this),Error.captureStackTrace?Error.captureStackTrace(this,this.constructor):this.stack=new Error().stack,this.message=a,this.name="AxiosError",l&&(this.code=l),r&&(this.config=r),s&&(this.request=s),c&&(this.response=c,this.status=c.status?c.status:null)}z.inherits(he,Error,{toJSON:function(){return{message:this.message,name:this.name,description:this.description,number:this.number,fileName:this.fileName,lineNumber:this.lineNumber,columnNumber:this.columnNumber,stack:this.stack,config:z.toJSONObject(this.config),code:this.code,status:this.status}}});const b0=he.prototype,S0={};["ERR_BAD_OPTION_VALUE","ERR_BAD_OPTION","ECONNABORTED","ETIMEDOUT","ERR_NETWORK","ERR_FR_TOO_MANY_REDIRECTS","ERR_DEPRECATED","ERR_BAD_RESPONSE","ERR_BAD_REQUEST","ERR_CANCELED","ERR_NOT_SUPPORT","ERR_INVALID_URL"].forEach(a=>{S0[a]={value:a}});Object.defineProperties(he,S0);Object.defineProperty(b0,"isAxiosError",{value:!0});he.from=(a,l,r,s,c,f)=>{const d=Object.create(b0);return z.toFlatObject(a,d,function(g){return g!==Error.prototype},m=>m!=="isAxiosError"),he.call(d,a.message,l,r,s,c),d.cause=a,d.name=a.name,f&&Object.assign(d,f),d};const Hb=null;function Bc(a){return z.isPlainObject(a)||z.isArray(a)}function x0(a){return z.endsWith(a,"[]")?a.slice(0,-2):a}function Ay(a,l,r){return a?a.concat(l).map(function(c,f){return c=x0(c),!r&&f?"["+c+"]":c}).join(r?".":""):l}function Vb(a){return z.isArray(a)&&!a.some(Bc)}const Yb=z.toFlatObject(z,{},null,function(l){return/^is[A-Z]/.test(l)});function $s(a,l,r){if(!z.isObject(a))throw new TypeError("target must be an object");l=l||new FormData,r=z.toFlatObject(r,{metaTokens:!0,dots:!1,indexes:!1},!1,function(B,L){return!z.isUndefined(L[B])});const s=r.metaTokens,c=r.visitor||v,f=r.dots,d=r.indexes,g=(r.Blob||typeof Blob<"u"&&Blob)&&z.isSpecCompliantForm(l);if(!z.isFunction(c))throw new TypeError("visitor must be a function");function y(R){if(R===null)return"";if(z.isDate(R))return R.toISOString();if(z.isBoolean(R))return R.toString();if(!g&&z.isBlob(R))throw new he("Blob is not supported. Use a Buffer instead.");return z.isArrayBuffer(R)||z.isTypedArray(R)?g&&typeof Blob=="function"?new Blob([R]):Buffer.from(R):R}function v(R,B,L){let X=R;if(R&&!L&&typeof R=="object"){if(z.endsWith(B,"{}"))B=s?B:B.slice(0,-2),R=JSON.stringify(R);else if(z.isArray(R)&&Vb(R)||(z.isFileList(R)||z.endsWith(B,"[]"))&&(X=z.toArray(R)))return B=x0(B),X.forEach(function($,ie){!(z.isUndefined($)||$===null)&&l.append(d===!0?Ay([B],ie,f):d===null?B:B+"[]",y($))}),!1}return Bc(R)?!0:(l.append(Ay(L,B,f),y(R)),!1)}const A=[],_=Object.assign(Yb,{defaultVisitor:v,convertValue:y,isVisitable:Bc});function Q(R,B){if(!z.isUndefined(R)){if(A.indexOf(R)!==-1)throw Error("Circular reference detected in "+B.join("."));A.push(R),z.forEach(R,function(X,V){(!(z.isUndefined(X)||X===null)&&c.call(l,X,z.isString(V)?V.trim():V,B,_))===!0&&Q(X,B?B.concat(V):[V])}),A.pop()}}if(!z.isObject(a))throw new TypeError("data must be an object");return Q(a),l}function Ty(a){const l={"!":"%21","'":"%27","(":"%28",")":"%29","~":"%7E","%20":"+","%00":"\0"};return encodeURIComponent(a).replace(/[!'()~]|%20|%00/g,function(s){return l[s]})}function tf(a,l){this._pairs=[],a&&$s(a,this,l)}const E0=tf.prototype;E0.append=function(l,r){this._pairs.push([l,r])};E0.toString=function(l){const r=l?function(s){return l.call(this,s,Ty)}:Ty;return this._pairs.map(function(c){return r(c[0])+"="+r(c[1])},"").join("&")};function Xb(a){return encodeURIComponent(a).replace(/%3A/gi,":").replace(/%24/g,"$").replace(/%2C/gi,",").replace(/%20/g,"+").replace(/%5B/gi,"[").replace(/%5D/gi,"]")}function _0(a,l,r){if(!l)return a;const s=r&&r.encode||Xb;z.isFunction(r)&&(r={serialize:r});const c=r&&r.serialize;let f;if(c?f=c(l,r):f=z.isURLSearchParams(l)?l.toString():new tf(l,r).toString(s),f){const d=a.indexOf("#");d!==-1&&(a=a.slice(0,d)),a+=(a.indexOf("?")===-1?"?":"&")+f}return a}class Ry{constructor(){this.handlers=[]}use(l,r,s){return this.handlers.push({fulfilled:l,rejected:r,synchronous:s?s.synchronous:!1,runWhen:s?s.runWhen:null}),this.handlers.length-1}eject(l){this.handlers[l]&&(this.handlers[l]=null)}clear(){this.handlers&&(this.handlers=[])}forEach(l){z.forEach(this.handlers,function(s){s!==null&&l(s)})}}const w0={silentJSONParsing:!0,forcedJSONParsing:!0,clarifyTimeoutError:!1},Gb=typeof URLSearchParams<"u"?URLSearchParams:tf,Qb=typeof FormData<"u"?FormData:null,Zb=typeof Blob<"u"?Blob:null,Kb={isBrowser:!0,classes:{URLSearchParams:Gb,FormData:Qb,Blob:Zb},protocols:["http","https","file","blob","url","data"]},nf=typeof window<"u"&&typeof document<"u",Lc=typeof navigator=="object"&&navigator||void 0,Fb=nf&&(!Lc||["ReactNative","NativeScript","NS"].indexOf(Lc.product)<0),Jb=typeof WorkerGlobalScope<"u"&&self instanceof WorkerGlobalScope&&typeof self.importScripts=="function",$b=nf&&window.location.href||"http://localhost",Wb=Object.freeze(Object.defineProperty({__proto__:null,hasBrowserEnv:nf,hasStandardBrowserEnv:Fb,hasStandardBrowserWebWorkerEnv:Jb,navigator:Lc,origin:$b},Symbol.toStringTag,{value:"Module"})),St={...Wb,...Kb};function Pb(a,l){return $s(a,new St.classes.URLSearchParams,Object.assign({visitor:function(r,s,c,f){return St.isNode&&z.isBuffer(r)?(this.append(s,r.toString("base64")),!1):f.defaultVisitor.apply(this,arguments)}},l))}function Ib(a){return z.matchAll(/\w+|\[(\w*)]/g,a).map(l=>l[0]==="[]"?"":l[1]||l[0])}function e1(a){const l={},r=Object.keys(a);let s;const c=r.length;let f;for(s=0;s<c;s++)f=r[s],l[f]=a[f];return l}function A0(a){function l(r,s,c,f){let d=r[f++];if(d==="__proto__")return!0;const m=Number.isFinite(+d),g=f>=r.length;return d=!d&&z.isArray(c)?c.length:d,g?(z.hasOwnProp(c,d)?c[d]=[c[d],s]:c[d]=s,!m):((!c[d]||!z.isObject(c[d]))&&(c[d]=[]),l(r,s,c[d],f)&&z.isArray(c[d])&&(c[d]=e1(c[d])),!m)}if(z.isFormData(a)&&z.isFunction(a.entries)){const r={};return z.forEachEntry(a,(s,c)=>{l(Ib(s),c,r,0)}),r}return null}function t1(a,l,r){if(z.isString(a))try{return(l||JSON.parse)(a),z.trim(a)}catch(s){if(s.name!=="SyntaxError")throw s}return(r||JSON.stringify)(a)}const tr={transitional:w0,adapter:["xhr","http","fetch"],transformRequest:[function(l,r){const s=r.getContentType()||"",c=s.indexOf("application/json")>-1,f=z.isObject(l);if(f&&z.isHTMLForm(l)&&(l=new FormData(l)),z.isFormData(l))return c?JSON.stringify(A0(l)):l;if(z.isArrayBuffer(l)||z.isBuffer(l)||z.isStream(l)||z.isFile(l)||z.isBlob(l)||z.isReadableStream(l))return l;if(z.isArrayBufferView(l))return l.buffer;if(z.isURLSearchParams(l))return r.setContentType("application/x-www-form-urlencoded;charset=utf-8",!1),l.toString();let m;if(f){if(s.indexOf("application/x-www-form-urlencoded")>-1)return Pb(l,this.formSerializer).toString();if((m=z.isFileList(l))||s.indexOf("multipart/form-data")>-1){const g=this.env&&this.env.FormData;return $s(m?{"files[]":l}:l,g&&new g,this.formSerializer)}}return f||c?(r.setContentType("application/json",!1),t1(l)):l}],transformResponse:[function(l){const r=this.transitional||tr.transitional,s=r&&r.forcedJSONParsing,c=this.responseType==="json";if(z.isResponse(l)||z.isReadableStream(l))return l;if(l&&z.isString(l)&&(s&&!this.responseType||c)){const d=!(r&&r.silentJSONParsing)&&c;try{return JSON.parse(l)}catch(m){if(d)throw m.name==="SyntaxError"?he.from(m,he.ERR_BAD_RESPONSE,this,null,this.response):m}}return l}],timeout:0,xsrfCookieName:"XSRF-TOKEN",xsrfHeaderName:"X-XSRF-TOKEN",maxContentLength:-1,maxBodyLength:-1,env:{FormData:St.classes.FormData,Blob:St.classes.Blob},validateStatus:function(l){return l>=200&&l<300},headers:{common:{Accept:"application/json, text/plain, */*","Content-Type":void 0}}};z.forEach(["delete","get","head","post","put","patch"],a=>{tr.headers[a]={}});const n1=z.toObjectSet(["age","authorization","content-length","content-type","etag","expires","from","host","if-modified-since","if-unmodified-since","last-modified","location","max-forwards","proxy-authorization","referer","retry-after","user-agent"]),a1=a=>{const l={};let r,s,c;return a&&a.split(`
`).forEach(function(d){c=d.indexOf(":"),r=d.substring(0,c).trim().toLowerCase(),s=d.substring(c+1).trim(),!(!r||l[r]&&n1[r])&&(r==="set-cookie"?l[r]?l[r].push(s):l[r]=[s]:l[r]=l[r]?l[r]+", "+s:s)}),l},Oy=Symbol("internals");function Hi(a){return a&&String(a).trim().toLowerCase()}function Ts(a){return a===!1||a==null?a:z.isArray(a)?a.map(Ts):String(a)}function l1(a){const l=Object.create(null),r=/([^\s,;=]+)\s*(?:=\s*([^,;]+))?/g;let s;for(;s=r.exec(a);)l[s[1]]=s[2];return l}const i1=a=>/^[-_a-zA-Z0-9^`|~,!#$%&'*+.]+$/.test(a.trim());function Tc(a,l,r,s,c){if(z.isFunction(s))return s.call(this,l,r);if(c&&(l=r),!!z.isString(l)){if(z.isString(s))return l.indexOf(s)!==-1;if(z.isRegExp(s))return s.test(l)}}function r1(a){return a.trim().toLowerCase().replace(/([a-z\d])(\w*)/g,(l,r,s)=>r.toUpperCase()+s)}function s1(a,l){const r=z.toCamelCase(" "+l);["get","set","has"].forEach(s=>{Object.defineProperty(a,s+r,{value:function(c,f,d){return this[s].call(this,l,c,f,d)},configurable:!0})})}let Dt=class{constructor(l){l&&this.set(l)}set(l,r,s){const c=this;function f(m,g,y){const v=Hi(g);if(!v)throw new Error("header name must be a non-empty string");const A=z.findKey(c,v);(!A||c[A]===void 0||y===!0||y===void 0&&c[A]!==!1)&&(c[A||g]=Ts(m))}const d=(m,g)=>z.forEach(m,(y,v)=>f(y,v,g));if(z.isPlainObject(l)||l instanceof this.constructor)d(l,r);else if(z.isString(l)&&(l=l.trim())&&!i1(l))d(a1(l),r);else if(z.isObject(l)&&z.isIterable(l)){let m={},g,y;for(const v of l){if(!z.isArray(v))throw TypeError("Object iterator must return a key-value pair");m[y=v[0]]=(g=m[y])?z.isArray(g)?[...g,v[1]]:[g,v[1]]:v[1]}d(m,r)}else l!=null&&f(r,l,s);return this}get(l,r){if(l=Hi(l),l){const s=z.findKey(this,l);if(s){const c=this[s];if(!r)return c;if(r===!0)return l1(c);if(z.isFunction(r))return r.call(this,c,s);if(z.isRegExp(r))return r.exec(c);throw new TypeError("parser must be boolean|regexp|function")}}}has(l,r){if(l=Hi(l),l){const s=z.findKey(this,l);return!!(s&&this[s]!==void 0&&(!r||Tc(this,this[s],s,r)))}return!1}delete(l,r){const s=this;let c=!1;function f(d){if(d=Hi(d),d){const m=z.findKey(s,d);m&&(!r||Tc(s,s[m],m,r))&&(delete s[m],c=!0)}}return z.isArray(l)?l.forEach(f):f(l),c}clear(l){const r=Object.keys(this);let s=r.length,c=!1;for(;s--;){const f=r[s];(!l||Tc(this,this[f],f,l,!0))&&(delete this[f],c=!0)}return c}normalize(l){const r=this,s={};return z.forEach(this,(c,f)=>{const d=z.findKey(s,f);if(d){r[d]=Ts(c),delete r[f];return}const m=l?r1(f):String(f).trim();m!==f&&delete r[f],r[m]=Ts(c),s[m]=!0}),this}concat(...l){return this.constructor.concat(this,...l)}toJSON(l){const r=Object.create(null);return z.forEach(this,(s,c)=>{s!=null&&s!==!1&&(r[c]=l&&z.isArray(s)?s.join(", "):s)}),r}[Symbol.iterator](){return Object.entries(this.toJSON())[Symbol.iterator]()}toString(){return Object.entries(this.toJSON()).map(([l,r])=>l+": "+r).join(`
`)}getSetCookie(){return this.get("set-cookie")||[]}get[Symbol.toStringTag](){return"AxiosHeaders"}static from(l){return l instanceof this?l:new this(l)}static concat(l,...r){const s=new this(l);return r.forEach(c=>s.set(c)),s}static accessor(l){const s=(this[Oy]=this[Oy]={accessors:{}}).accessors,c=this.prototype;function f(d){const m=Hi(d);s[m]||(s1(c,d),s[m]=!0)}return z.isArray(l)?l.forEach(f):f(l),this}};Dt.accessor(["Content-Type","Content-Length","Accept","Accept-Encoding","User-Agent","Authorization"]);z.reduceDescriptors(Dt.prototype,({value:a},l)=>{let r=l[0].toUpperCase()+l.slice(1);return{get:()=>a,set(s){this[r]=s}}});z.freezeMethods(Dt);function Rc(a,l){const r=this||tr,s=l||r,c=Dt.from(s.headers);let f=s.data;return z.forEach(a,function(m){f=m.call(r,f,c.normalize(),l?l.status:void 0)}),c.normalize(),f}function T0(a){return!!(a&&a.__CANCEL__)}function Bl(a,l,r){he.call(this,a??"canceled",he.ERR_CANCELED,l,r),this.name="CanceledError"}z.inherits(Bl,he,{__CANCEL__:!0});function R0(a,l,r){const s=r.config.validateStatus;!r.status||!s||s(r.status)?a(r):l(new he("Request failed with status code "+r.status,[he.ERR_BAD_REQUEST,he.ERR_BAD_RESPONSE][Math.floor(r.status/100)-4],r.config,r.request,r))}function u1(a){const l=/^([-+\w]{1,25})(:?\/\/|:)/.exec(a);return l&&l[1]||""}function o1(a,l){a=a||10;const r=new Array(a),s=new Array(a);let c=0,f=0,d;return l=l!==void 0?l:1e3,function(g){const y=Date.now(),v=s[f];d||(d=y),r[c]=g,s[c]=y;let A=f,_=0;for(;A!==c;)_+=r[A++],A=A%a;if(c=(c+1)%a,c===f&&(f=(f+1)%a),y-d<l)return;const Q=v&&y-v;return Q?Math.round(_*1e3/Q):void 0}}function c1(a,l){let r=0,s=1e3/l,c,f;const d=(y,v=Date.now())=>{r=v,c=null,f&&(clearTimeout(f),f=null),a.apply(null,y)};return[(...y)=>{const v=Date.now(),A=v-r;A>=s?d(y,v):(c=y,f||(f=setTimeout(()=>{f=null,d(c)},s-A)))},()=>c&&d(c)]}const Ls=(a,l,r=3)=>{let s=0;const c=o1(50,250);return c1(f=>{const d=f.loaded,m=f.lengthComputable?f.total:void 0,g=d-s,y=c(g),v=d<=m;s=d;const A={loaded:d,total:m,progress:m?d/m:void 0,bytes:g,rate:y||void 0,estimated:y&&m&&v?(m-d)/y:void 0,event:f,lengthComputable:m!=null,[l?"download":"upload"]:!0};a(A)},r)},Ny=(a,l)=>{const r=a!=null;return[s=>l[0]({lengthComputable:r,total:a,loaded:s}),l[1]]},Cy=a=>(...l)=>z.asap(()=>a(...l)),f1=St.hasStandardBrowserEnv?((a,l)=>r=>(r=new URL(r,St.origin),a.protocol===r.protocol&&a.host===r.host&&(l||a.port===r.port)))(new URL(St.origin),St.navigator&&/(msie|trident)/i.test(St.navigator.userAgent)):()=>!0,d1=St.hasStandardBrowserEnv?{write(a,l,r,s,c,f){const d=[a+"="+encodeURIComponent(l)];z.isNumber(r)&&d.push("expires="+new Date(r).toGMTString()),z.isString(s)&&d.push("path="+s),z.isString(c)&&d.push("domain="+c),f===!0&&d.push("secure"),document.cookie=d.join("; ")},read(a){const l=document.cookie.match(new RegExp("(^|;\\s*)("+a+")=([^;]*)"));return l?decodeURIComponent(l[3]):null},remove(a){this.write(a,"",Date.now()-864e5)}}:{write(){},read(){return null},remove(){}};function h1(a){return/^([a-z][a-z\d+\-.]*:)?\/\//i.test(a)}function m1(a,l){return l?a.replace(/\/?\/$/,"")+"/"+l.replace(/^\/+/,""):a}function O0(a,l,r){let s=!h1(l);return a&&(s||r==!1)?m1(a,l):l}const Dy=a=>a instanceof Dt?{...a}:a;function Va(a,l){l=l||{};const r={};function s(y,v,A,_){return z.isPlainObject(y)&&z.isPlainObject(v)?z.merge.call({caseless:_},y,v):z.isPlainObject(v)?z.merge({},v):z.isArray(v)?v.slice():v}function c(y,v,A,_){if(z.isUndefined(v)){if(!z.isUndefined(y))return s(void 0,y,A,_)}else return s(y,v,A,_)}function f(y,v){if(!z.isUndefined(v))return s(void 0,v)}function d(y,v){if(z.isUndefined(v)){if(!z.isUndefined(y))return s(void 0,y)}else return s(void 0,v)}function m(y,v,A){if(A in l)return s(y,v);if(A in a)return s(void 0,y)}const g={url:f,method:f,data:f,baseURL:d,transformRequest:d,transformResponse:d,paramsSerializer:d,timeout:d,timeoutMessage:d,withCredentials:d,withXSRFToken:d,adapter:d,responseType:d,xsrfCookieName:d,xsrfHeaderName:d,onUploadProgress:d,onDownloadProgress:d,decompress:d,maxContentLength:d,maxBodyLength:d,beforeRedirect:d,transport:d,httpAgent:d,httpsAgent:d,cancelToken:d,socketPath:d,responseEncoding:d,validateStatus:m,headers:(y,v,A)=>c(Dy(y),Dy(v),A,!0)};return z.forEach(Object.keys(Object.assign({},a,l)),function(v){const A=g[v]||c,_=A(a[v],l[v],v);z.isUndefined(_)&&A!==m||(r[v]=_)}),r}const N0=a=>{const l=Va({},a);let{data:r,withXSRFToken:s,xsrfHeaderName:c,xsrfCookieName:f,headers:d,auth:m}=l;l.headers=d=Dt.from(d),l.url=_0(O0(l.baseURL,l.url,l.allowAbsoluteUrls),a.params,a.paramsSerializer),m&&d.set("Authorization","Basic "+btoa((m.username||"")+":"+(m.password?unescape(encodeURIComponent(m.password)):"")));let g;if(z.isFormData(r)){if(St.hasStandardBrowserEnv||St.hasStandardBrowserWebWorkerEnv)d.setContentType(void 0);else if((g=d.getContentType())!==!1){const[y,...v]=g?g.split(";").map(A=>A.trim()).filter(Boolean):[];d.setContentType([y||"multipart/form-data",...v].join("; "))}}if(St.hasStandardBrowserEnv&&(s&&z.isFunction(s)&&(s=s(l)),s||s!==!1&&f1(l.url))){const y=c&&f&&d1.read(f);y&&d.set(c,y)}return l},y1=typeof XMLHttpRequest<"u",p1=y1&&function(a){return new Promise(function(r,s){const c=N0(a);let f=c.data;const d=Dt.from(c.headers).normalize();let{responseType:m,onUploadProgress:g,onDownloadProgress:y}=c,v,A,_,Q,R;function B(){Q&&Q(),R&&R(),c.cancelToken&&c.cancelToken.unsubscribe(v),c.signal&&c.signal.removeEventListener("abort",v)}let L=new XMLHttpRequest;L.open(c.method.toUpperCase(),c.url,!0),L.timeout=c.timeout;function X(){if(!L)return;const $=Dt.from("getAllResponseHeaders"in L&&L.getAllResponseHeaders()),K={data:!m||m==="text"||m==="json"?L.responseText:L.response,status:L.status,statusText:L.statusText,headers:$,config:a,request:L};R0(function(ye){r(ye),B()},function(ye){s(ye),B()},K),L=null}"onloadend"in L?L.onloadend=X:L.onreadystatechange=function(){!L||L.readyState!==4||L.status===0&&!(L.responseURL&&L.responseURL.indexOf("file:")===0)||setTimeout(X)},L.onabort=function(){L&&(s(new he("Request aborted",he.ECONNABORTED,a,L)),L=null)},L.onerror=function(){s(new he("Network Error",he.ERR_NETWORK,a,L)),L=null},L.ontimeout=function(){let ie=c.timeout?"timeout of "+c.timeout+"ms exceeded":"timeout exceeded";const K=c.transitional||w0;c.timeoutErrorMessage&&(ie=c.timeoutErrorMessage),s(new he(ie,K.clarifyTimeoutError?he.ETIMEDOUT:he.ECONNABORTED,a,L)),L=null},f===void 0&&d.setContentType(null),"setRequestHeader"in L&&z.forEach(d.toJSON(),function(ie,K){L.setRequestHeader(K,ie)}),z.isUndefined(c.withCredentials)||(L.withCredentials=!!c.withCredentials),m&&m!=="json"&&(L.responseType=c.responseType),y&&([_,R]=Ls(y,!0),L.addEventListener("progress",_)),g&&L.upload&&([A,Q]=Ls(g),L.upload.addEventListener("progress",A),L.upload.addEventListener("loadend",Q)),(c.cancelToken||c.signal)&&(v=$=>{L&&(s(!$||$.type?new Bl(null,a,L):$),L.abort(),L=null)},c.cancelToken&&c.cancelToken.subscribe(v),c.signal&&(c.signal.aborted?v():c.signal.addEventListener("abort",v)));const V=u1(c.url);if(V&&St.protocols.indexOf(V)===-1){s(new he("Unsupported protocol "+V+":",he.ERR_BAD_REQUEST,a));return}L.send(f||null)})},g1=(a,l)=>{const{length:r}=a=a?a.filter(Boolean):[];if(l||r){let s=new AbortController,c;const f=function(y){if(!c){c=!0,m();const v=y instanceof Error?y:this.reason;s.abort(v instanceof he?v:new Bl(v instanceof Error?v.message:v))}};let d=l&&setTimeout(()=>{d=null,f(new he(`timeout ${l} of ms exceeded`,he.ETIMEDOUT))},l);const m=()=>{a&&(d&&clearTimeout(d),d=null,a.forEach(y=>{y.unsubscribe?y.unsubscribe(f):y.removeEventListener("abort",f)}),a=null)};a.forEach(y=>y.addEventListener("abort",f));const{signal:g}=s;return g.unsubscribe=()=>z.asap(m),g}},v1=function*(a,l){let r=a.byteLength;if(r<l){yield a;return}let s=0,c;for(;s<r;)c=s+l,yield a.slice(s,c),s=c},b1=async function*(a,l){for await(const r of S1(a))yield*v1(r,l)},S1=async function*(a){if(a[Symbol.asyncIterator]){yield*a;return}const l=a.getReader();try{for(;;){const{done:r,value:s}=await l.read();if(r)break;yield s}}finally{await l.cancel()}},My=(a,l,r,s)=>{const c=b1(a,l);let f=0,d,m=g=>{d||(d=!0,s&&s(g))};return new ReadableStream({async pull(g){try{const{done:y,value:v}=await c.next();if(y){m(),g.close();return}let A=v.byteLength;if(r){let _=f+=A;r(_)}g.enqueue(new Uint8Array(v))}catch(y){throw m(y),y}},cancel(g){return m(g),c.return()}},{highWaterMark:2})},Ws=typeof fetch=="function"&&typeof Request=="function"&&typeof Response=="function",C0=Ws&&typeof ReadableStream=="function",x1=Ws&&(typeof TextEncoder=="function"?(a=>l=>a.encode(l))(new TextEncoder):async a=>new Uint8Array(await new Response(a).arrayBuffer())),D0=(a,...l)=>{try{return!!a(...l)}catch{return!1}},E1=C0&&D0(()=>{let a=!1;const l=new Request(St.origin,{body:new ReadableStream,method:"POST",get duplex(){return a=!0,"half"}}).headers.has("Content-Type");return a&&!l}),Uy=64*1024,kc=C0&&D0(()=>z.isReadableStream(new Response("").body)),ks={stream:kc&&(a=>a.body)};Ws&&(a=>{["text","arrayBuffer","blob","formData","stream"].forEach(l=>{!ks[l]&&(ks[l]=z.isFunction(a[l])?r=>r[l]():(r,s)=>{throw new he(`Response type '${l}' is not supported`,he.ERR_NOT_SUPPORT,s)})})})(new Response);const _1=async a=>{if(a==null)return 0;if(z.isBlob(a))return a.size;if(z.isSpecCompliantForm(a))return(await new Request(St.origin,{method:"POST",body:a}).arrayBuffer()).byteLength;if(z.isArrayBufferView(a)||z.isArrayBuffer(a))return a.byteLength;if(z.isURLSearchParams(a)&&(a=a+""),z.isString(a))return(await x1(a)).byteLength},w1=async(a,l)=>{const r=z.toFiniteNumber(a.getContentLength());return r??_1(l)},A1=Ws&&(async a=>{let{url:l,method:r,data:s,signal:c,cancelToken:f,timeout:d,onDownloadProgress:m,onUploadProgress:g,responseType:y,headers:v,withCredentials:A="same-origin",fetchOptions:_}=N0(a);y=y?(y+"").toLowerCase():"text";let Q=g1([c,f&&f.toAbortSignal()],d),R;const B=Q&&Q.unsubscribe&&(()=>{Q.unsubscribe()});let L;try{if(g&&E1&&r!=="get"&&r!=="head"&&(L=await w1(v,s))!==0){let K=new Request(l,{method:"POST",body:s,duplex:"half"}),Ae;if(z.isFormData(s)&&(Ae=K.headers.get("content-type"))&&v.setContentType(Ae),K.body){const[ye,De]=Ny(L,Ls(Cy(g)));s=My(K.body,Uy,ye,De)}}z.isString(A)||(A=A?"include":"omit");const X="credentials"in Request.prototype;R=new Request(l,{..._,signal:Q,method:r.toUpperCase(),headers:v.normalize().toJSON(),body:s,duplex:"half",credentials:X?A:void 0});let V=await fetch(R,_);const $=kc&&(y==="stream"||y==="response");if(kc&&(m||$&&B)){const K={};["status","statusText","headers"].forEach(pe=>{K[pe]=V[pe]});const Ae=z.toFiniteNumber(V.headers.get("content-length")),[ye,De]=m&&Ny(Ae,Ls(Cy(m),!0))||[];V=new Response(My(V.body,Uy,ye,()=>{De&&De(),B&&B()}),K)}y=y||"text";let ie=await ks[z.findKey(ks,y)||"text"](V,a);return!$&&B&&B(),await new Promise((K,Ae)=>{R0(K,Ae,{data:ie,headers:Dt.from(V.headers),status:V.status,statusText:V.statusText,config:a,request:R})})}catch(X){throw B&&B(),X&&X.name==="TypeError"&&/Load failed|fetch/i.test(X.message)?Object.assign(new he("Network Error",he.ERR_NETWORK,a,R),{cause:X.cause||X}):he.from(X,X&&X.code,a,R)}}),qc={http:Hb,xhr:p1,fetch:A1};z.forEach(qc,(a,l)=>{if(a){try{Object.defineProperty(a,"name",{value:l})}catch{}Object.defineProperty(a,"adapterName",{value:l})}});const zy=a=>`- ${a}`,T1=a=>z.isFunction(a)||a===null||a===!1,M0={getAdapter:a=>{a=z.isArray(a)?a:[a];const{length:l}=a;let r,s;const c={};for(let f=0;f<l;f++){r=a[f];let d;if(s=r,!T1(r)&&(s=qc[(d=String(r)).toLowerCase()],s===void 0))throw new he(`Unknown adapter '${d}'`);if(s)break;c[d||"#"+f]=s}if(!s){const f=Object.entries(c).map(([m,g])=>`adapter ${m} `+(g===!1?"is not supported by the environment":"is not available in the build"));let d=l?f.length>1?`since :
`+f.map(zy).join(`
`):" "+zy(f[0]):"as no adapter specified";throw new he("There is no suitable adapter to dispatch the request "+d,"ERR_NOT_SUPPORT")}return s},adapters:qc};function Oc(a){if(a.cancelToken&&a.cancelToken.throwIfRequested(),a.signal&&a.signal.aborted)throw new Bl(null,a)}function jy(a){return Oc(a),a.headers=Dt.from(a.headers),a.data=Rc.call(a,a.transformRequest),["post","put","patch"].indexOf(a.method)!==-1&&a.headers.setContentType("application/x-www-form-urlencoded",!1),M0.getAdapter(a.adapter||tr.adapter)(a).then(function(s){return Oc(a),s.data=Rc.call(a,a.transformResponse,s),s.headers=Dt.from(s.headers),s},function(s){return T0(s)||(Oc(a),s&&s.response&&(s.response.data=Rc.call(a,a.transformResponse,s.response),s.response.headers=Dt.from(s.response.headers))),Promise.reject(s)})}const U0="1.10.0",Ps={};["object","boolean","number","function","string","symbol"].forEach((a,l)=>{Ps[a]=function(s){return typeof s===a||"a"+(l<1?"n ":" ")+a}});const By={};Ps.transitional=function(l,r,s){function c(f,d){return"[Axios v"+U0+"] Transitional option '"+f+"'"+d+(s?". "+s:"")}return(f,d,m)=>{if(l===!1)throw new he(c(d," has been removed"+(r?" in "+r:"")),he.ERR_DEPRECATED);return r&&!By[d]&&(By[d]=!0,console.warn(c(d," has been deprecated since v"+r+" and will be removed in the near future"))),l?l(f,d,m):!0}};Ps.spelling=function(l){return(r,s)=>(console.warn(`${s} is likely a misspelling of ${l}`),!0)};function R1(a,l,r){if(typeof a!="object")throw new he("options must be an object",he.ERR_BAD_OPTION_VALUE);const s=Object.keys(a);let c=s.length;for(;c-- >0;){const f=s[c],d=l[f];if(d){const m=a[f],g=m===void 0||d(m,f,a);if(g!==!0)throw new he("option "+f+" must be "+g,he.ERR_BAD_OPTION_VALUE);continue}if(r!==!0)throw new he("Unknown option "+f,he.ERR_BAD_OPTION)}}const Rs={assertOptions:R1,validators:Ps},hn=Rs.validators;let Ha=class{constructor(l){this.defaults=l||{},this.interceptors={request:new Ry,response:new Ry}}async request(l,r){try{return await this._request(l,r)}catch(s){if(s instanceof Error){let c={};Error.captureStackTrace?Error.captureStackTrace(c):c=new Error;const f=c.stack?c.stack.replace(/^.+\n/,""):"";try{s.stack?f&&!String(s.stack).endsWith(f.replace(/^.+\n.+\n/,""))&&(s.stack+=`
`+f):s.stack=f}catch{}}throw s}}_request(l,r){typeof l=="string"?(r=r||{},r.url=l):r=l||{},r=Va(this.defaults,r);const{transitional:s,paramsSerializer:c,headers:f}=r;s!==void 0&&Rs.assertOptions(s,{silentJSONParsing:hn.transitional(hn.boolean),forcedJSONParsing:hn.transitional(hn.boolean),clarifyTimeoutError:hn.transitional(hn.boolean)},!1),c!=null&&(z.isFunction(c)?r.paramsSerializer={serialize:c}:Rs.assertOptions(c,{encode:hn.function,serialize:hn.function},!0)),r.allowAbsoluteUrls!==void 0||(this.defaults.allowAbsoluteUrls!==void 0?r.allowAbsoluteUrls=this.defaults.allowAbsoluteUrls:r.allowAbsoluteUrls=!0),Rs.assertOptions(r,{baseUrl:hn.spelling("baseURL"),withXsrfToken:hn.spelling("withXSRFToken")},!0),r.method=(r.method||this.defaults.method||"get").toLowerCase();let d=f&&z.merge(f.common,f[r.method]);f&&z.forEach(["delete","get","head","post","put","patch","common"],R=>{delete f[R]}),r.headers=Dt.concat(d,f);const m=[];let g=!0;this.interceptors.request.forEach(function(B){typeof B.runWhen=="function"&&B.runWhen(r)===!1||(g=g&&B.synchronous,m.unshift(B.fulfilled,B.rejected))});const y=[];this.interceptors.response.forEach(function(B){y.push(B.fulfilled,B.rejected)});let v,A=0,_;if(!g){const R=[jy.bind(this),void 0];for(R.unshift.apply(R,m),R.push.apply(R,y),_=R.length,v=Promise.resolve(r);A<_;)v=v.then(R[A++],R[A++]);return v}_=m.length;let Q=r;for(A=0;A<_;){const R=m[A++],B=m[A++];try{Q=R(Q)}catch(L){B.call(this,L);break}}try{v=jy.call(this,Q)}catch(R){return Promise.reject(R)}for(A=0,_=y.length;A<_;)v=v.then(y[A++],y[A++]);return v}getUri(l){l=Va(this.defaults,l);const r=O0(l.baseURL,l.url,l.allowAbsoluteUrls);return _0(r,l.params,l.paramsSerializer)}};z.forEach(["delete","get","head","options"],function(l){Ha.prototype[l]=function(r,s){return this.request(Va(s||{},{method:l,url:r,data:(s||{}).data}))}});z.forEach(["post","put","patch"],function(l){function r(s){return function(f,d,m){return this.request(Va(m||{},{method:l,headers:s?{"Content-Type":"multipart/form-data"}:{},url:f,data:d}))}}Ha.prototype[l]=r(),Ha.prototype[l+"Form"]=r(!0)});let O1=class z0{constructor(l){if(typeof l!="function")throw new TypeError("executor must be a function.");let r;this.promise=new Promise(function(f){r=f});const s=this;this.promise.then(c=>{if(!s._listeners)return;let f=s._listeners.length;for(;f-- >0;)s._listeners[f](c);s._listeners=null}),this.promise.then=c=>{let f;const d=new Promise(m=>{s.subscribe(m),f=m}).then(c);return d.cancel=function(){s.unsubscribe(f)},d},l(function(f,d,m){s.reason||(s.reason=new Bl(f,d,m),r(s.reason))})}throwIfRequested(){if(this.reason)throw this.reason}subscribe(l){if(this.reason){l(this.reason);return}this._listeners?this._listeners.push(l):this._listeners=[l]}unsubscribe(l){if(!this._listeners)return;const r=this._listeners.indexOf(l);r!==-1&&this._listeners.splice(r,1)}toAbortSignal(){const l=new AbortController,r=s=>{l.abort(s)};return this.subscribe(r),l.signal.unsubscribe=()=>this.unsubscribe(r),l.signal}static source(){let l;return{token:new z0(function(c){l=c}),cancel:l}}};function N1(a){return function(r){return a.apply(null,r)}}function C1(a){return z.isObject(a)&&a.isAxiosError===!0}const Hc={Continue:100,SwitchingProtocols:101,Processing:102,EarlyHints:103,Ok:200,Created:201,Accepted:202,NonAuthoritativeInformation:203,NoContent:204,ResetContent:205,PartialContent:206,MultiStatus:207,AlreadyReported:208,ImUsed:226,MultipleChoices:300,MovedPermanently:301,Found:302,SeeOther:303,NotModified:304,UseProxy:305,Unused:306,TemporaryRedirect:307,PermanentRedirect:308,BadRequest:400,Unauthorized:401,PaymentRequired:402,Forbidden:403,NotFound:404,MethodNotAllowed:405,NotAcceptable:406,ProxyAuthenticationRequired:407,RequestTimeout:408,Conflict:409,Gone:410,LengthRequired:411,PreconditionFailed:412,PayloadTooLarge:413,UriTooLong:414,UnsupportedMediaType:415,RangeNotSatisfiable:416,ExpectationFailed:417,ImATeapot:418,MisdirectedRequest:421,UnprocessableEntity:422,Locked:423,FailedDependency:424,TooEarly:425,UpgradeRequired:426,PreconditionRequired:428,TooManyRequests:429,RequestHeaderFieldsTooLarge:431,UnavailableForLegalReasons:451,InternalServerError:500,NotImplemented:501,BadGateway:502,ServiceUnavailable:503,GatewayTimeout:504,HttpVersionNotSupported:505,VariantAlsoNegotiates:506,InsufficientStorage:507,LoopDetected:508,NotExtended:510,NetworkAuthenticationRequired:511};Object.entries(Hc).forEach(([a,l])=>{Hc[l]=a});function j0(a){const l=new Ha(a),r=f0(Ha.prototype.request,l);return z.extend(r,Ha.prototype,l,{allOwnKeys:!0}),z.extend(r,l,null,{allOwnKeys:!0}),r.create=function(c){return j0(Va(a,c))},r}const Ie=j0(tr);Ie.Axios=Ha;Ie.CanceledError=Bl;Ie.CancelToken=O1;Ie.isCancel=T0;Ie.VERSION=U0;Ie.toFormData=$s;Ie.AxiosError=he;Ie.Cancel=Ie.CanceledError;Ie.all=function(l){return Promise.all(l)};Ie.spread=N1;Ie.isAxiosError=C1;Ie.mergeConfig=Va;Ie.AxiosHeaders=Dt;Ie.formToJSON=a=>A0(z.isHTMLForm(a)?new FormData(a):a);Ie.getAdapter=M0.getAdapter;Ie.HttpStatusCode=Hc;Ie.default=Ie;const{Axios:B2,AxiosError:L2,CanceledError:k2,isCancel:q2,CancelToken:H2,VERSION:V2,all:Y2,Cancel:X2,isAxiosError:G2,spread:Q2,toFormData:Z2,AxiosHeaders:K2,HttpStatusCode:F2,formToJSON:J2,getAdapter:$2,mergeConfig:W2}=Ie,Ya=Ie.create({baseURL:"http://localhost:3000",withCredentials:!0}),B0=()=>{const[a,l]=N.useState(!1),{message:r,setMessage:s,selectedConversation:c}=ma();return console.log(c),N.useEffect(()=>{(async()=>{if(l(!0),c&&c._id)try{const d=await Ya.get(`/api/message/get/${c._id}`,{headers:{"Content-Type":"application/json"}});s(d.data),l(!1)}catch(d){console.log("Error in getMessage ",d)}})()},[c,s]),{message:r,loading:a}};let D1={data:""},M1=a=>typeof window=="object"?((a?a.querySelector("#_goober"):window._goober)||Object.assign((a||document.head).appendChild(document.createElement("style")),{innerHTML:" ",id:"_goober"})).firstChild:a||D1,U1=/(?:([\u0080-\uFFFF\w-%@]+) *:? *([^{;]+?);|([^;}{]*?) *{)|(}\s*)/g,z1=/\/\*[^]*?\*\/|  +/g,Ly=/\n+/g,fa=(a,l)=>{let r="",s="",c="";for(let f in a){let d=a[f];f[0]=="@"?f[1]=="i"?r=f+" "+d+";":s+=f[1]=="f"?fa(d,f):f+"{"+fa(d,f[1]=="k"?"":l)+"}":typeof d=="object"?s+=fa(d,l?l.replace(/([^,])+/g,m=>f.replace(/([^,]*:\S+\([^)]*\))|([^,])+/g,g=>/&/.test(g)?g.replace(/&/g,m):m?m+" "+g:g)):f):d!=null&&(f=/^--/.test(f)?f:f.replace(/[A-Z]/g,"-$&").toLowerCase(),c+=fa.p?fa.p(f,d):f+":"+d+";")}return r+(l&&c?l+"{"+c+"}":c)+s},zn={},L0=a=>{if(typeof a=="object"){let l="";for(let r in a)l+=r+L0(a[r]);return l}return a},j1=(a,l,r,s,c)=>{let f=L0(a),d=zn[f]||(zn[f]=(g=>{let y=0,v=11;for(;y<g.length;)v=101*v+g.charCodeAt(y++)>>>0;return"go"+v})(f));if(!zn[d]){let g=f!==a?a:(y=>{let v,A,_=[{}];for(;v=U1.exec(y.replace(z1,""));)v[4]?_.shift():v[3]?(A=v[3].replace(Ly," ").trim(),_.unshift(_[0][A]=_[0][A]||{})):_[0][v[1]]=v[2].replace(Ly," ").trim();return _[0]})(a);zn[d]=fa(c?{["@keyframes "+d]:g}:g,r?"":"."+d)}let m=r&&zn.g?zn.g:null;return r&&(zn.g=zn[d]),((g,y,v,A)=>{A?y.data=y.data.replace(A,g):y.data.indexOf(g)===-1&&(y.data=v?g+y.data:y.data+g)})(zn[d],l,s,m),d},B1=(a,l,r)=>a.reduce((s,c,f)=>{let d=l[f];if(d&&d.call){let m=d(r),g=m&&m.props&&m.props.className||/^go/.test(m)&&m;d=g?"."+g:m&&typeof m=="object"?m.props?"":fa(m,""):m===!1?"":m}return s+c+(d??"")},"");function Is(a){let l=this||{},r=a.call?a(l.p):a;return j1(r.unshift?r.raw?B1(r,[].slice.call(arguments,1),l.p):r.reduce((s,c)=>Object.assign(s,c&&c.call?c(l.p):c),{}):r,M1(l.target),l.g,l.o,l.k)}let k0,Vc,Yc;Is.bind({g:1});let Ln=Is.bind({k:1});function L1(a,l,r,s){fa.p=l,k0=a,Vc=r,Yc=s}function ya(a,l){let r=this||{};return function(){let s=arguments;function c(f,d){let m=Object.assign({},f),g=m.className||c.className;r.p=Object.assign({theme:Vc&&Vc()},m),r.o=/ *go\d+/.test(g),m.className=Is.apply(r,s)+(g?" "+g:"");let y=a;return a[0]&&(y=m.as||a,delete m.as),Yc&&y[0]&&Yc(m),k0(y,m)}return c}}var k1=a=>typeof a=="function",qs=(a,l)=>k1(a)?a(l):a,q1=(()=>{let a=0;return()=>(++a).toString()})(),q0=(()=>{let a;return()=>{if(a===void 0&&typeof window<"u"){let l=matchMedia("(prefers-reduced-motion: reduce)");a=!l||l.matches}return a}})(),H1=20,H0=(a,l)=>{switch(l.type){case 0:return{...a,toasts:[l.toast,...a.toasts].slice(0,H1)};case 1:return{...a,toasts:a.toasts.map(f=>f.id===l.toast.id?{...f,...l.toast}:f)};case 2:let{toast:r}=l;return H0(a,{type:a.toasts.find(f=>f.id===r.id)?1:0,toast:r});case 3:let{toastId:s}=l;return{...a,toasts:a.toasts.map(f=>f.id===s||s===void 0?{...f,dismissed:!0,visible:!1}:f)};case 4:return l.toastId===void 0?{...a,toasts:[]}:{...a,toasts:a.toasts.filter(f=>f.id!==l.toastId)};case 5:return{...a,pausedAt:l.time};case 6:let c=l.time-(a.pausedAt||0);return{...a,pausedAt:void 0,toasts:a.toasts.map(f=>({...f,pauseDuration:f.pauseDuration+c}))}}},Os=[],ka={toasts:[],pausedAt:void 0},Xa=a=>{ka=H0(ka,a),Os.forEach(l=>{l(ka)})},V1={blank:4e3,error:4e3,success:2e3,loading:1/0,custom:4e3},Y1=(a={})=>{let[l,r]=N.useState(ka),s=N.useRef(ka);N.useEffect(()=>(s.current!==ka&&r(ka),Os.push(r),()=>{let f=Os.indexOf(r);f>-1&&Os.splice(f,1)}),[]);let c=l.toasts.map(f=>{var d,m,g;return{...a,...a[f.type],...f,removeDelay:f.removeDelay||((d=a[f.type])==null?void 0:d.removeDelay)||(a==null?void 0:a.removeDelay),duration:f.duration||((m=a[f.type])==null?void 0:m.duration)||(a==null?void 0:a.duration)||V1[f.type],style:{...a.style,...(g=a[f.type])==null?void 0:g.style,...f.style}}});return{...l,toasts:c}},X1=(a,l="blank",r)=>({createdAt:Date.now(),visible:!0,dismissed:!1,type:l,ariaProps:{role:"status","aria-live":"polite"},message:a,pauseDuration:0,...r,id:(r==null?void 0:r.id)||q1()}),nr=a=>(l,r)=>{let s=X1(l,a,r);return Xa({type:2,toast:s}),s.id},_t=(a,l)=>nr("blank")(a,l);_t.error=nr("error");_t.success=nr("success");_t.loading=nr("loading");_t.custom=nr("custom");_t.dismiss=a=>{Xa({type:3,toastId:a})};_t.remove=a=>Xa({type:4,toastId:a});_t.promise=(a,l,r)=>{let s=_t.loading(l.loading,{...r,...r==null?void 0:r.loading});return typeof a=="function"&&(a=a()),a.then(c=>{let f=l.success?qs(l.success,c):void 0;return f?_t.success(f,{id:s,...r,...r==null?void 0:r.success}):_t.dismiss(s),c}).catch(c=>{let f=l.error?qs(l.error,c):void 0;f?_t.error(f,{id:s,...r,...r==null?void 0:r.error}):_t.dismiss(s)}),a};var G1=(a,l)=>{Xa({type:1,toast:{id:a,height:l}})},Q1=()=>{Xa({type:5,time:Date.now()})},Fi=new Map,Z1=1e3,K1=(a,l=Z1)=>{if(Fi.has(a))return;let r=setTimeout(()=>{Fi.delete(a),Xa({type:4,toastId:a})},l);Fi.set(a,r)},F1=a=>{let{toasts:l,pausedAt:r}=Y1(a);N.useEffect(()=>{if(r)return;let f=Date.now(),d=l.map(m=>{if(m.duration===1/0)return;let g=(m.duration||0)+m.pauseDuration-(f-m.createdAt);if(g<0){m.visible&&_t.dismiss(m.id);return}return setTimeout(()=>_t.dismiss(m.id),g)});return()=>{d.forEach(m=>m&&clearTimeout(m))}},[l,r]);let s=N.useCallback(()=>{r&&Xa({type:6,time:Date.now()})},[r]),c=N.useCallback((f,d)=>{let{reverseOrder:m=!1,gutter:g=8,defaultPosition:y}=d||{},v=l.filter(Q=>(Q.position||y)===(f.position||y)&&Q.height),A=v.findIndex(Q=>Q.id===f.id),_=v.filter((Q,R)=>R<A&&Q.visible).length;return v.filter(Q=>Q.visible).slice(...m?[_+1]:[0,_]).reduce((Q,R)=>Q+(R.height||0)+g,0)},[l]);return N.useEffect(()=>{l.forEach(f=>{if(f.dismissed)K1(f.id,f.removeDelay);else{let d=Fi.get(f.id);d&&(clearTimeout(d),Fi.delete(f.id))}})},[l]),{toasts:l,handlers:{updateHeight:G1,startPause:Q1,endPause:s,calculateOffset:c}}},J1=Ln`
from {
  transform: scale(0) rotate(45deg);
	opacity: 0;
}
to {
 transform: scale(1) rotate(45deg);
  opacity: 1;
}`,$1=Ln`
from {
  transform: scale(0);
  opacity: 0;
}
to {
  transform: scale(1);
  opacity: 1;
}`,W1=Ln`
from {
  transform: scale(0) rotate(90deg);
	opacity: 0;
}
to {
  transform: scale(1) rotate(90deg);
	opacity: 1;
}`,P1=ya("div")`
  width: 20px;
  opacity: 0;
  height: 20px;
  border-radius: 10px;
  background: ${a=>a.primary||"#ff4b4b"};
  position: relative;
  transform: rotate(45deg);

  animation: ${J1} 0.3s cubic-bezier(0.175, 0.885, 0.32, 1.275)
    forwards;
  animation-delay: 100ms;

  &:after,
  &:before {
    content: '';
    animation: ${$1} 0.15s ease-out forwards;
    animation-delay: 150ms;
    position: absolute;
    border-radius: 3px;
    opacity: 0;
    background: ${a=>a.secondary||"#fff"};
    bottom: 9px;
    left: 4px;
    height: 2px;
    width: 12px;
  }

  &:before {
    animation: ${W1} 0.15s ease-out forwards;
    animation-delay: 180ms;
    transform: rotate(90deg);
  }
`,I1=Ln`
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
`,eS=ya("div")`
  width: 12px;
  height: 12px;
  box-sizing: border-box;
  border: 2px solid;
  border-radius: 100%;
  border-color: ${a=>a.secondary||"#e0e0e0"};
  border-right-color: ${a=>a.primary||"#616161"};
  animation: ${I1} 1s linear infinite;
`,tS=Ln`
from {
  transform: scale(0) rotate(45deg);
	opacity: 0;
}
to {
  transform: scale(1) rotate(45deg);
	opacity: 1;
}`,nS=Ln`
0% {
	height: 0;
	width: 0;
	opacity: 0;
}
40% {
  height: 0;
	width: 6px;
	opacity: 1;
}
100% {
  opacity: 1;
  height: 10px;
}`,aS=ya("div")`
  width: 20px;
  opacity: 0;
  height: 20px;
  border-radius: 10px;
  background: ${a=>a.primary||"#61d345"};
  position: relative;
  transform: rotate(45deg);

  animation: ${tS} 0.3s cubic-bezier(0.175, 0.885, 0.32, 1.275)
    forwards;
  animation-delay: 100ms;
  &:after {
    content: '';
    box-sizing: border-box;
    animation: ${nS} 0.2s ease-out forwards;
    opacity: 0;
    animation-delay: 200ms;
    position: absolute;
    border-right: 2px solid;
    border-bottom: 2px solid;
    border-color: ${a=>a.secondary||"#fff"};
    bottom: 6px;
    left: 6px;
    height: 10px;
    width: 6px;
  }
`,lS=ya("div")`
  position: absolute;
`,iS=ya("div")`
  position: relative;
  display: flex;
  justify-content: center;
  align-items: center;
  min-width: 20px;
  min-height: 20px;
`,rS=Ln`
from {
  transform: scale(0.6);
  opacity: 0.4;
}
to {
  transform: scale(1);
  opacity: 1;
}`,sS=ya("div")`
  position: relative;
  transform: scale(0.6);
  opacity: 0.4;
  min-width: 20px;
  animation: ${rS} 0.3s 0.12s cubic-bezier(0.175, 0.885, 0.32, 1.275)
    forwards;
`,uS=({toast:a})=>{let{icon:l,type:r,iconTheme:s}=a;return l!==void 0?typeof l=="string"?N.createElement(sS,null,l):l:r==="blank"?null:N.createElement(iS,null,N.createElement(eS,{...s}),r!=="loading"&&N.createElement(lS,null,r==="error"?N.createElement(P1,{...s}):N.createElement(aS,{...s})))},oS=a=>`
0% {transform: translate3d(0,${a*-200}%,0) scale(.6); opacity:.5;}
100% {transform: translate3d(0,0,0) scale(1); opacity:1;}
`,cS=a=>`
0% {transform: translate3d(0,0,-1px) scale(1); opacity:1;}
100% {transform: translate3d(0,${a*-150}%,-1px) scale(.6); opacity:0;}
`,fS="0%{opacity:0;} 100%{opacity:1;}",dS="0%{opacity:1;} 100%{opacity:0;}",hS=ya("div")`
  display: flex;
  align-items: center;
  background: #fff;
  color: #363636;
  line-height: 1.3;
  will-change: transform;
  box-shadow: 0 3px 10px rgba(0, 0, 0, 0.1), 0 3px 3px rgba(0, 0, 0, 0.05);
  max-width: 350px;
  pointer-events: auto;
  padding: 8px 10px;
  border-radius: 8px;
`,mS=ya("div")`
  display: flex;
  justify-content: center;
  margin: 4px 10px;
  color: inherit;
  flex: 1 1 auto;
  white-space: pre-line;
`,yS=(a,l)=>{let r=a.includes("top")?1:-1,[s,c]=q0()?[fS,dS]:[oS(r),cS(r)];return{animation:l?`${Ln(s)} 0.35s cubic-bezier(.21,1.02,.73,1) forwards`:`${Ln(c)} 0.4s forwards cubic-bezier(.06,.71,.55,1)`}},pS=N.memo(({toast:a,position:l,style:r,children:s})=>{let c=a.height?yS(a.position||l||"top-center",a.visible):{opacity:0},f=N.createElement(uS,{toast:a}),d=N.createElement(mS,{...a.ariaProps},qs(a.message,a));return N.createElement(hS,{className:a.className,style:{...c,...r,...a.style}},typeof s=="function"?s({icon:f,message:d}):N.createElement(N.Fragment,null,f,d))});L1(N.createElement);var gS=({id:a,className:l,style:r,onHeightUpdate:s,children:c})=>{let f=N.useCallback(d=>{if(d){let m=()=>{let g=d.getBoundingClientRect().height;s(a,g)};m(),new MutationObserver(m).observe(d,{subtree:!0,childList:!0,characterData:!0})}},[a,s]);return N.createElement("div",{ref:f,className:l,style:r},c)},vS=(a,l)=>{let r=a.includes("top"),s=r?{top:0}:{bottom:0},c=a.includes("center")?{justifyContent:"center"}:a.includes("right")?{justifyContent:"flex-end"}:{};return{left:0,right:0,display:"flex",position:"absolute",transition:q0()?void 0:"all 230ms cubic-bezier(.21,1.02,.73,1)",transform:`translateY(${l*(r?1:-1)}px)`,...s,...c}},bS=Is`
  z-index: 9999;
  > * {
    pointer-events: auto;
  }
`,Ss=16,SS=({reverseOrder:a,position:l="top-center",toastOptions:r,gutter:s,children:c,containerStyle:f,containerClassName:d})=>{let{toasts:m,handlers:g}=F1(r);return N.createElement("div",{id:"_rht_toaster",style:{position:"fixed",zIndex:9999,top:Ss,left:Ss,right:Ss,bottom:Ss,pointerEvents:"none",...f},className:d,onMouseEnter:g.startPause,onMouseLeave:g.endPause},m.map(y=>{let v=y.position||l,A=g.calculateOffset(y,{reverseOrder:a,gutter:s,defaultPosition:l}),_=vS(v,A);return N.createElement(gS,{id:y.id,key:y.id,onHeightUpdate:g.updateHeight,className:y.visible?bS:"",style:_},y.type==="custom"?qs(y.message,y):c?c(y):N.createElement(pS,{toast:y,position:v}))}))},kn=_t;const xS=({onMobileSelect:a})=>{const[l,r]=N.useState(""),{allUsers:s}=B0(),{setSelectedConversation:c}=ma(),f=d=>{if(d.preventDefault(),!l)return;const m=s.find(g=>{var y;return(y=g.fullname)==null?void 0:y.toLowerCase().includes(l.toLowerCase())});m?(c(m),r(""),a&&a()):kn.error("User not found")};return S.jsx("div",{className:"py-2",children:S.jsx("form",{onSubmit:f,children:S.jsxs("div",{className:"relative",children:[S.jsx("div",{className:"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none",children:S.jsx("svg",{className:"h-5 w-5 text-gray-400",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:S.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"})})}),S.jsx("input",{type:"text",className:"w-full pl-10 pr-4 py-2 bg-gray-100 dark:bg-gray-600 border border-gray-200 dark:border-gray-500 rounded-lg focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-transparent text-gray-900 dark:text-white placeholder-gray-500 dark:placeholder-gray-400",placeholder:"Search or start new chat",value:l,onChange:d=>r(d.target.value)}),l&&S.jsx("button",{type:"button",onClick:()=>r(""),className:"absolute inset-y-0 right-0 pr-3 flex items-center",children:S.jsx("svg",{className:"h-4 w-4 text-gray-400 hover:text-gray-600",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:S.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M6 18L18 6M6 6l12 12"})})})]})})})};/*! js-cookie v3.0.5 | MIT */function xs(a){for(var l=1;l<arguments.length;l++){var r=arguments[l];for(var s in r)a[s]=r[s]}return a}var ES={read:function(a){return a[0]==='"'&&(a=a.slice(1,-1)),a.replace(/(%[\dA-F]{2})+/gi,decodeURIComponent)},write:function(a){return encodeURIComponent(a).replace(/%(2[346BF]|3[AC-F]|40|5[BDE]|60|7[BCD])/g,decodeURIComponent)}};function Xc(a,l){function r(c,f,d){if(!(typeof document>"u")){d=xs({},l,d),typeof d.expires=="number"&&(d.expires=new Date(Date.now()+d.expires*864e5)),d.expires&&(d.expires=d.expires.toUTCString()),c=encodeURIComponent(c).replace(/%(2[346B]|5E|60|7C)/g,decodeURIComponent).replace(/[()]/g,escape);var m="";for(var g in d)d[g]&&(m+="; "+g,d[g]!==!0&&(m+="="+d[g].split(";")[0]));return document.cookie=c+"="+a.write(f,c)+m}}function s(c){if(!(typeof document>"u"||arguments.length&&!c)){for(var f=document.cookie?document.cookie.split("; "):[],d={},m=0;m<f.length;m++){var g=f[m].split("="),y=g.slice(1).join("=");try{var v=decodeURIComponent(g[0]);if(d[v]=a.read(y,v),c===v)break}catch{}}return c?d[c]:d}}return Object.create({set:r,get:s,remove:function(c,f){r(c,"",xs({},f,{expires:-1}))},withAttributes:function(c){return Xc(this.converter,xs({},this.attributes,c))},withConverter:function(c){return Xc(xs({},this.converter,c),this.attributes)}},{attributes:{value:Object.freeze(l)},converter:{value:Object.freeze(a)}})}var Pi=Xc(ES,{path:"/"});const V0=N.createContext(),_S=({children:a})=>{const l=Pi.get("token")||localStorage.getItem("message");console.log("initial",l),console.log(Pi.get("token"));const[r,s]=N.useState(l?JSON.parse(l):void 0);return console.log(r),S.jsx("div",{children:S.jsx(V0.Provider,{value:{authUser:r,setAuthUser:s},children:a})})},ar=()=>N.useContext(V0),pn=Object.create(null);pn.open="0";pn.close="1";pn.ping="2";pn.pong="3";pn.message="4";pn.upgrade="5";pn.noop="6";const Ns=Object.create(null);Object.keys(pn).forEach(a=>{Ns[pn[a]]=a});const Gc={type:"error",data:"parser error"},Y0=typeof Blob=="function"||typeof Blob<"u"&&Object.prototype.toString.call(Blob)==="[object BlobConstructor]",X0=typeof ArrayBuffer=="function",G0=a=>typeof ArrayBuffer.isView=="function"?ArrayBuffer.isView(a):a&&a.buffer instanceof ArrayBuffer,af=({type:a,data:l},r,s)=>Y0&&l instanceof Blob?r?s(l):ky(l,s):X0&&(l instanceof ArrayBuffer||G0(l))?r?s(l):ky(new Blob([l]),s):s(pn[a]+(l||"")),ky=(a,l)=>{const r=new FileReader;return r.onload=function(){const s=r.result.split(",")[1];l("b"+(s||""))},r.readAsDataURL(a)};function qy(a){return a instanceof Uint8Array?a:a instanceof ArrayBuffer?new Uint8Array(a):new Uint8Array(a.buffer,a.byteOffset,a.byteLength)}let Nc;function wS(a,l){if(Y0&&a.data instanceof Blob)return a.data.arrayBuffer().then(qy).then(l);if(X0&&(a.data instanceof ArrayBuffer||G0(a.data)))return l(qy(a.data));af(a,!1,r=>{Nc||(Nc=new TextEncoder),l(Nc.encode(r))})}const Hy="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/",Zi=typeof Uint8Array>"u"?[]:new Uint8Array(256);for(let a=0;a<Hy.length;a++)Zi[Hy.charCodeAt(a)]=a;const AS=a=>{let l=a.length*.75,r=a.length,s,c=0,f,d,m,g;a[a.length-1]==="="&&(l--,a[a.length-2]==="="&&l--);const y=new ArrayBuffer(l),v=new Uint8Array(y);for(s=0;s<r;s+=4)f=Zi[a.charCodeAt(s)],d=Zi[a.charCodeAt(s+1)],m=Zi[a.charCodeAt(s+2)],g=Zi[a.charCodeAt(s+3)],v[c++]=f<<2|d>>4,v[c++]=(d&15)<<4|m>>2,v[c++]=(m&3)<<6|g&63;return y},TS=typeof ArrayBuffer=="function",lf=(a,l)=>{if(typeof a!="string")return{type:"message",data:Q0(a,l)};const r=a.charAt(0);return r==="b"?{type:"message",data:RS(a.substring(1),l)}:Ns[r]?a.length>1?{type:Ns[r],data:a.substring(1)}:{type:Ns[r]}:Gc},RS=(a,l)=>{if(TS){const r=AS(a);return Q0(r,l)}else return{base64:!0,data:a}},Q0=(a,l)=>{switch(l){case"blob":return a instanceof Blob?a:new Blob([a]);case"arraybuffer":default:return a instanceof ArrayBuffer?a:a.buffer}},Z0="",OS=(a,l)=>{const r=a.length,s=new Array(r);let c=0;a.forEach((f,d)=>{af(f,!1,m=>{s[d]=m,++c===r&&l(s.join(Z0))})})},NS=(a,l)=>{const r=a.split(Z0),s=[];for(let c=0;c<r.length;c++){const f=lf(r[c],l);if(s.push(f),f.type==="error")break}return s};function CS(){return new TransformStream({transform(a,l){wS(a,r=>{const s=r.length;let c;if(s<126)c=new Uint8Array(1),new DataView(c.buffer).setUint8(0,s);else if(s<65536){c=new Uint8Array(3);const f=new DataView(c.buffer);f.setUint8(0,126),f.setUint16(1,s)}else{c=new Uint8Array(9);const f=new DataView(c.buffer);f.setUint8(0,127),f.setBigUint64(1,BigInt(s))}a.data&&typeof a.data!="string"&&(c[0]|=128),l.enqueue(c),l.enqueue(r)})}})}let Cc;function Es(a){return a.reduce((l,r)=>l+r.length,0)}function _s(a,l){if(a[0].length===l)return a.shift();const r=new Uint8Array(l);let s=0;for(let c=0;c<l;c++)r[c]=a[0][s++],s===a[0].length&&(a.shift(),s=0);return a.length&&s<a[0].length&&(a[0]=a[0].slice(s)),r}function DS(a,l){Cc||(Cc=new TextDecoder);const r=[];let s=0,c=-1,f=!1;return new TransformStream({transform(d,m){for(r.push(d);;){if(s===0){if(Es(r)<1)break;const g=_s(r,1);f=(g[0]&128)===128,c=g[0]&127,c<126?s=3:c===126?s=1:s=2}else if(s===1){if(Es(r)<2)break;const g=_s(r,2);c=new DataView(g.buffer,g.byteOffset,g.length).getUint16(0),s=3}else if(s===2){if(Es(r)<8)break;const g=_s(r,8),y=new DataView(g.buffer,g.byteOffset,g.length),v=y.getUint32(0);if(v>Math.pow(2,21)-1){m.enqueue(Gc);break}c=v*Math.pow(2,32)+y.getUint32(4),s=3}else{if(Es(r)<c)break;const g=_s(r,c);m.enqueue(lf(f?g:Cc.decode(g),l)),s=0}if(c===0||c>a){m.enqueue(Gc);break}}}})}const K0=4;function it(a){if(a)return MS(a)}function MS(a){for(var l in it.prototype)a[l]=it.prototype[l];return a}it.prototype.on=it.prototype.addEventListener=function(a,l){return this._callbacks=this._callbacks||{},(this._callbacks["$"+a]=this._callbacks["$"+a]||[]).push(l),this};it.prototype.once=function(a,l){function r(){this.off(a,r),l.apply(this,arguments)}return r.fn=l,this.on(a,r),this};it.prototype.off=it.prototype.removeListener=it.prototype.removeAllListeners=it.prototype.removeEventListener=function(a,l){if(this._callbacks=this._callbacks||{},arguments.length==0)return this._callbacks={},this;var r=this._callbacks["$"+a];if(!r)return this;if(arguments.length==1)return delete this._callbacks["$"+a],this;for(var s,c=0;c<r.length;c++)if(s=r[c],s===l||s.fn===l){r.splice(c,1);break}return r.length===0&&delete this._callbacks["$"+a],this};it.prototype.emit=function(a){this._callbacks=this._callbacks||{};for(var l=new Array(arguments.length-1),r=this._callbacks["$"+a],s=1;s<arguments.length;s++)l[s-1]=arguments[s];if(r){r=r.slice(0);for(var s=0,c=r.length;s<c;++s)r[s].apply(this,l)}return this};it.prototype.emitReserved=it.prototype.emit;it.prototype.listeners=function(a){return this._callbacks=this._callbacks||{},this._callbacks["$"+a]||[]};it.prototype.hasListeners=function(a){return!!this.listeners(a).length};const eu=typeof Promise=="function"&&typeof Promise.resolve=="function"?l=>Promise.resolve().then(l):(l,r)=>r(l,0),$t=typeof self<"u"?self:typeof window<"u"?window:Function("return this")(),US="arraybuffer";function F0(a,...l){return l.reduce((r,s)=>(a.hasOwnProperty(s)&&(r[s]=a[s]),r),{})}const zS=$t.setTimeout,jS=$t.clearTimeout;function tu(a,l){l.useNativeTimers?(a.setTimeoutFn=zS.bind($t),a.clearTimeoutFn=jS.bind($t)):(a.setTimeoutFn=$t.setTimeout.bind($t),a.clearTimeoutFn=$t.clearTimeout.bind($t))}const BS=1.33;function LS(a){return typeof a=="string"?kS(a):Math.ceil((a.byteLength||a.size)*BS)}function kS(a){let l=0,r=0;for(let s=0,c=a.length;s<c;s++)l=a.charCodeAt(s),l<128?r+=1:l<2048?r+=2:l<55296||l>=57344?r+=3:(s++,r+=4);return r}function J0(){return Date.now().toString(36).substring(3)+Math.random().toString(36).substring(2,5)}function qS(a){let l="";for(let r in a)a.hasOwnProperty(r)&&(l.length&&(l+="&"),l+=encodeURIComponent(r)+"="+encodeURIComponent(a[r]));return l}function HS(a){let l={},r=a.split("&");for(let s=0,c=r.length;s<c;s++){let f=r[s].split("=");l[decodeURIComponent(f[0])]=decodeURIComponent(f[1])}return l}class VS extends Error{constructor(l,r,s){super(l),this.description=r,this.context=s,this.type="TransportError"}}class rf extends it{constructor(l){super(),this.writable=!1,tu(this,l),this.opts=l,this.query=l.query,this.socket=l.socket,this.supportsBinary=!l.forceBase64}onError(l,r,s){return super.emitReserved("error",new VS(l,r,s)),this}open(){return this.readyState="opening",this.doOpen(),this}close(){return(this.readyState==="opening"||this.readyState==="open")&&(this.doClose(),this.onClose()),this}send(l){this.readyState==="open"&&this.write(l)}onOpen(){this.readyState="open",this.writable=!0,super.emitReserved("open")}onData(l){const r=lf(l,this.socket.binaryType);this.onPacket(r)}onPacket(l){super.emitReserved("packet",l)}onClose(l){this.readyState="closed",super.emitReserved("close",l)}pause(l){}createUri(l,r={}){return l+"://"+this._hostname()+this._port()+this.opts.path+this._query(r)}_hostname(){const l=this.opts.hostname;return l.indexOf(":")===-1?l:"["+l+"]"}_port(){return this.opts.port&&(this.opts.secure&&+(this.opts.port!==443)||!this.opts.secure&&Number(this.opts.port)!==80)?":"+this.opts.port:""}_query(l){const r=qS(l);return r.length?"?"+r:""}}class YS extends rf{constructor(){super(...arguments),this._polling=!1}get name(){return"polling"}doOpen(){this._poll()}pause(l){this.readyState="pausing";const r=()=>{this.readyState="paused",l()};if(this._polling||!this.writable){let s=0;this._polling&&(s++,this.once("pollComplete",function(){--s||r()})),this.writable||(s++,this.once("drain",function(){--s||r()}))}else r()}_poll(){this._polling=!0,this.doPoll(),this.emitReserved("poll")}onData(l){const r=s=>{if(this.readyState==="opening"&&s.type==="open"&&this.onOpen(),s.type==="close")return this.onClose({description:"transport closed by the server"}),!1;this.onPacket(s)};NS(l,this.socket.binaryType).forEach(r),this.readyState!=="closed"&&(this._polling=!1,this.emitReserved("pollComplete"),this.readyState==="open"&&this._poll())}doClose(){const l=()=>{this.write([{type:"close"}])};this.readyState==="open"?l():this.once("open",l)}write(l){this.writable=!1,OS(l,r=>{this.doWrite(r,()=>{this.writable=!0,this.emitReserved("drain")})})}uri(){const l=this.opts.secure?"https":"http",r=this.query||{};return this.opts.timestampRequests!==!1&&(r[this.opts.timestampParam]=J0()),!this.supportsBinary&&!r.sid&&(r.b64=1),this.createUri(l,r)}}let $0=!1;try{$0=typeof XMLHttpRequest<"u"&&"withCredentials"in new XMLHttpRequest}catch{}const XS=$0;function GS(){}class QS extends YS{constructor(l){if(super(l),typeof location<"u"){const r=location.protocol==="https:";let s=location.port;s||(s=r?"443":"80"),this.xd=typeof location<"u"&&l.hostname!==location.hostname||s!==l.port}}doWrite(l,r){const s=this.request({method:"POST",data:l});s.on("success",r),s.on("error",(c,f)=>{this.onError("xhr post error",c,f)})}doPoll(){const l=this.request();l.on("data",this.onData.bind(this)),l.on("error",(r,s)=>{this.onError("xhr poll error",r,s)}),this.pollXhr=l}}let zl=class Cs extends it{constructor(l,r,s){super(),this.createRequest=l,tu(this,s),this._opts=s,this._method=s.method||"GET",this._uri=r,this._data=s.data!==void 0?s.data:null,this._create()}_create(){var l;const r=F0(this._opts,"agent","pfx","key","passphrase","cert","ca","ciphers","rejectUnauthorized","autoUnref");r.xdomain=!!this._opts.xd;const s=this._xhr=this.createRequest(r);try{s.open(this._method,this._uri,!0);try{if(this._opts.extraHeaders){s.setDisableHeaderCheck&&s.setDisableHeaderCheck(!0);for(let c in this._opts.extraHeaders)this._opts.extraHeaders.hasOwnProperty(c)&&s.setRequestHeader(c,this._opts.extraHeaders[c])}}catch{}if(this._method==="POST")try{s.setRequestHeader("Content-type","text/plain;charset=UTF-8")}catch{}try{s.setRequestHeader("Accept","*/*")}catch{}(l=this._opts.cookieJar)===null||l===void 0||l.addCookies(s),"withCredentials"in s&&(s.withCredentials=this._opts.withCredentials),this._opts.requestTimeout&&(s.timeout=this._opts.requestTimeout),s.onreadystatechange=()=>{var c;s.readyState===3&&((c=this._opts.cookieJar)===null||c===void 0||c.parseCookies(s.getResponseHeader("set-cookie"))),s.readyState===4&&(s.status===200||s.status===1223?this._onLoad():this.setTimeoutFn(()=>{this._onError(typeof s.status=="number"?s.status:0)},0))},s.send(this._data)}catch(c){this.setTimeoutFn(()=>{this._onError(c)},0);return}typeof document<"u"&&(this._index=Cs.requestsCount++,Cs.requests[this._index]=this)}_onError(l){this.emitReserved("error",l,this._xhr),this._cleanup(!0)}_cleanup(l){if(!(typeof this._xhr>"u"||this._xhr===null)){if(this._xhr.onreadystatechange=GS,l)try{this._xhr.abort()}catch{}typeof document<"u"&&delete Cs.requests[this._index],this._xhr=null}}_onLoad(){const l=this._xhr.responseText;l!==null&&(this.emitReserved("data",l),this.emitReserved("success"),this._cleanup())}abort(){this._cleanup()}};zl.requestsCount=0;zl.requests={};if(typeof document<"u"){if(typeof attachEvent=="function")attachEvent("onunload",Vy);else if(typeof addEventListener=="function"){const a="onpagehide"in $t?"pagehide":"unload";addEventListener(a,Vy,!1)}}function Vy(){for(let a in zl.requests)zl.requests.hasOwnProperty(a)&&zl.requests[a].abort()}const ZS=function(){const a=W0({xdomain:!1});return a&&a.responseType!==null}();class KS extends QS{constructor(l){super(l);const r=l&&l.forceBase64;this.supportsBinary=ZS&&!r}request(l={}){return Object.assign(l,{xd:this.xd},this.opts),new zl(W0,this.uri(),l)}}function W0(a){const l=a.xdomain;try{if(typeof XMLHttpRequest<"u"&&(!l||XS))return new XMLHttpRequest}catch{}if(!l)try{return new $t[["Active"].concat("Object").join("X")]("Microsoft.XMLHTTP")}catch{}}const P0=typeof navigator<"u"&&typeof navigator.product=="string"&&navigator.product.toLowerCase()==="reactnative";class FS extends rf{get name(){return"websocket"}doOpen(){const l=this.uri(),r=this.opts.protocols,s=P0?{}:F0(this.opts,"agent","perMessageDeflate","pfx","key","passphrase","cert","ca","ciphers","rejectUnauthorized","localAddress","protocolVersion","origin","maxPayload","family","checkServerIdentity");this.opts.extraHeaders&&(s.headers=this.opts.extraHeaders);try{this.ws=this.createSocket(l,r,s)}catch(c){return this.emitReserved("error",c)}this.ws.binaryType=this.socket.binaryType,this.addEventListeners()}addEventListeners(){this.ws.onopen=()=>{this.opts.autoUnref&&this.ws._socket.unref(),this.onOpen()},this.ws.onclose=l=>this.onClose({description:"websocket connection closed",context:l}),this.ws.onmessage=l=>this.onData(l.data),this.ws.onerror=l=>this.onError("websocket error",l)}write(l){this.writable=!1;for(let r=0;r<l.length;r++){const s=l[r],c=r===l.length-1;af(s,this.supportsBinary,f=>{try{this.doWrite(s,f)}catch{}c&&eu(()=>{this.writable=!0,this.emitReserved("drain")},this.setTimeoutFn)})}}doClose(){typeof this.ws<"u"&&(this.ws.onerror=()=>{},this.ws.close(),this.ws=null)}uri(){const l=this.opts.secure?"wss":"ws",r=this.query||{};return this.opts.timestampRequests&&(r[this.opts.timestampParam]=J0()),this.supportsBinary||(r.b64=1),this.createUri(l,r)}}const Dc=$t.WebSocket||$t.MozWebSocket;class JS extends FS{createSocket(l,r,s){return P0?new Dc(l,r,s):r?new Dc(l,r):new Dc(l)}doWrite(l,r){this.ws.send(r)}}class $S extends rf{get name(){return"webtransport"}doOpen(){try{this._transport=new WebTransport(this.createUri("https"),this.opts.transportOptions[this.name])}catch(l){return this.emitReserved("error",l)}this._transport.closed.then(()=>{this.onClose()}).catch(l=>{this.onError("webtransport error",l)}),this._transport.ready.then(()=>{this._transport.createBidirectionalStream().then(l=>{const r=DS(Number.MAX_SAFE_INTEGER,this.socket.binaryType),s=l.readable.pipeThrough(r).getReader(),c=CS();c.readable.pipeTo(l.writable),this._writer=c.writable.getWriter();const f=()=>{s.read().then(({done:m,value:g})=>{m||(this.onPacket(g),f())}).catch(m=>{})};f();const d={type:"open"};this.query.sid&&(d.data=`{"sid":"${this.query.sid}"}`),this._writer.write(d).then(()=>this.onOpen())})})}write(l){this.writable=!1;for(let r=0;r<l.length;r++){const s=l[r],c=r===l.length-1;this._writer.write(s).then(()=>{c&&eu(()=>{this.writable=!0,this.emitReserved("drain")},this.setTimeoutFn)})}}doClose(){var l;(l=this._transport)===null||l===void 0||l.close()}}const WS={websocket:JS,webtransport:$S,polling:KS},PS=/^(?:(?![^:@\/?#]+:[^:@\/]*@)(http|https|ws|wss):\/\/)?((?:(([^:@\/?#]*)(?::([^:@\/?#]*))?)?@)?((?:[a-f0-9]{0,4}:){2,7}[a-f0-9]{0,4}|[^:\/?#]*)(?::(\d*))?)(((\/(?:[^?#](?![^?#\/]*\.[^?#\/.]+(?:[?#]|$)))*\/?)?([^?#\/]*))(?:\?([^#]*))?(?:#(.*))?)/,IS=["source","protocol","authority","userInfo","user","password","host","port","relative","path","directory","file","query","anchor"];function Qc(a){if(a.length>8e3)throw"URI too long";const l=a,r=a.indexOf("["),s=a.indexOf("]");r!=-1&&s!=-1&&(a=a.substring(0,r)+a.substring(r,s).replace(/:/g,";")+a.substring(s,a.length));let c=PS.exec(a||""),f={},d=14;for(;d--;)f[IS[d]]=c[d]||"";return r!=-1&&s!=-1&&(f.source=l,f.host=f.host.substring(1,f.host.length-1).replace(/;/g,":"),f.authority=f.authority.replace("[","").replace("]","").replace(/;/g,":"),f.ipv6uri=!0),f.pathNames=ex(f,f.path),f.queryKey=tx(f,f.query),f}function ex(a,l){const r=/\/{2,9}/g,s=l.replace(r,"/").split("/");return(l.slice(0,1)=="/"||l.length===0)&&s.splice(0,1),l.slice(-1)=="/"&&s.splice(s.length-1,1),s}function tx(a,l){const r={};return l.replace(/(?:^|&)([^&=]*)=?([^&]*)/g,function(s,c,f){c&&(r[c]=f)}),r}const Zc=typeof addEventListener=="function"&&typeof removeEventListener=="function",Ds=[];Zc&&addEventListener("offline",()=>{Ds.forEach(a=>a())},!1);class ha extends it{constructor(l,r){if(super(),this.binaryType=US,this.writeBuffer=[],this._prevBufferLen=0,this._pingInterval=-1,this._pingTimeout=-1,this._maxPayload=-1,this._pingTimeoutTime=1/0,l&&typeof l=="object"&&(r=l,l=null),l){const s=Qc(l);r.hostname=s.host,r.secure=s.protocol==="https"||s.protocol==="wss",r.port=s.port,s.query&&(r.query=s.query)}else r.host&&(r.hostname=Qc(r.host).host);tu(this,r),this.secure=r.secure!=null?r.secure:typeof location<"u"&&location.protocol==="https:",r.hostname&&!r.port&&(r.port=this.secure?"443":"80"),this.hostname=r.hostname||(typeof location<"u"?location.hostname:"localhost"),this.port=r.port||(typeof location<"u"&&location.port?location.port:this.secure?"443":"80"),this.transports=[],this._transportsByName={},r.transports.forEach(s=>{const c=s.prototype.name;this.transports.push(c),this._transportsByName[c]=s}),this.opts=Object.assign({path:"/engine.io",agent:!1,withCredentials:!1,upgrade:!0,timestampParam:"t",rememberUpgrade:!1,addTrailingSlash:!0,rejectUnauthorized:!0,perMessageDeflate:{threshold:1024},transportOptions:{},closeOnBeforeunload:!1},r),this.opts.path=this.opts.path.replace(/\/$/,"")+(this.opts.addTrailingSlash?"/":""),typeof this.opts.query=="string"&&(this.opts.query=HS(this.opts.query)),Zc&&(this.opts.closeOnBeforeunload&&(this._beforeunloadEventListener=()=>{this.transport&&(this.transport.removeAllListeners(),this.transport.close())},addEventListener("beforeunload",this._beforeunloadEventListener,!1)),this.hostname!=="localhost"&&(this._offlineEventListener=()=>{this._onClose("transport close",{description:"network connection lost"})},Ds.push(this._offlineEventListener))),this.opts.withCredentials&&(this._cookieJar=void 0),this._open()}createTransport(l){const r=Object.assign({},this.opts.query);r.EIO=K0,r.transport=l,this.id&&(r.sid=this.id);const s=Object.assign({},this.opts,{query:r,socket:this,hostname:this.hostname,secure:this.secure,port:this.port},this.opts.transportOptions[l]);return new this._transportsByName[l](s)}_open(){if(this.transports.length===0){this.setTimeoutFn(()=>{this.emitReserved("error","No transports available")},0);return}const l=this.opts.rememberUpgrade&&ha.priorWebsocketSuccess&&this.transports.indexOf("websocket")!==-1?"websocket":this.transports[0];this.readyState="opening";const r=this.createTransport(l);r.open(),this.setTransport(r)}setTransport(l){this.transport&&this.transport.removeAllListeners(),this.transport=l,l.on("drain",this._onDrain.bind(this)).on("packet",this._onPacket.bind(this)).on("error",this._onError.bind(this)).on("close",r=>this._onClose("transport close",r))}onOpen(){this.readyState="open",ha.priorWebsocketSuccess=this.transport.name==="websocket",this.emitReserved("open"),this.flush()}_onPacket(l){if(this.readyState==="opening"||this.readyState==="open"||this.readyState==="closing")switch(this.emitReserved("packet",l),this.emitReserved("heartbeat"),l.type){case"open":this.onHandshake(JSON.parse(l.data));break;case"ping":this._sendPacket("pong"),this.emitReserved("ping"),this.emitReserved("pong"),this._resetPingTimeout();break;case"error":const r=new Error("server error");r.code=l.data,this._onError(r);break;case"message":this.emitReserved("data",l.data),this.emitReserved("message",l.data);break}}onHandshake(l){this.emitReserved("handshake",l),this.id=l.sid,this.transport.query.sid=l.sid,this._pingInterval=l.pingInterval,this._pingTimeout=l.pingTimeout,this._maxPayload=l.maxPayload,this.onOpen(),this.readyState!=="closed"&&this._resetPingTimeout()}_resetPingTimeout(){this.clearTimeoutFn(this._pingTimeoutTimer);const l=this._pingInterval+this._pingTimeout;this._pingTimeoutTime=Date.now()+l,this._pingTimeoutTimer=this.setTimeoutFn(()=>{this._onClose("ping timeout")},l),this.opts.autoUnref&&this._pingTimeoutTimer.unref()}_onDrain(){this.writeBuffer.splice(0,this._prevBufferLen),this._prevBufferLen=0,this.writeBuffer.length===0?this.emitReserved("drain"):this.flush()}flush(){if(this.readyState!=="closed"&&this.transport.writable&&!this.upgrading&&this.writeBuffer.length){const l=this._getWritablePackets();this.transport.send(l),this._prevBufferLen=l.length,this.emitReserved("flush")}}_getWritablePackets(){if(!(this._maxPayload&&this.transport.name==="polling"&&this.writeBuffer.length>1))return this.writeBuffer;let r=1;for(let s=0;s<this.writeBuffer.length;s++){const c=this.writeBuffer[s].data;if(c&&(r+=LS(c)),s>0&&r>this._maxPayload)return this.writeBuffer.slice(0,s);r+=2}return this.writeBuffer}_hasPingExpired(){if(!this._pingTimeoutTime)return!0;const l=Date.now()>this._pingTimeoutTime;return l&&(this._pingTimeoutTime=0,eu(()=>{this._onClose("ping timeout")},this.setTimeoutFn)),l}write(l,r,s){return this._sendPacket("message",l,r,s),this}send(l,r,s){return this._sendPacket("message",l,r,s),this}_sendPacket(l,r,s,c){if(typeof r=="function"&&(c=r,r=void 0),typeof s=="function"&&(c=s,s=null),this.readyState==="closing"||this.readyState==="closed")return;s=s||{},s.compress=s.compress!==!1;const f={type:l,data:r,options:s};this.emitReserved("packetCreate",f),this.writeBuffer.push(f),c&&this.once("flush",c),this.flush()}close(){const l=()=>{this._onClose("forced close"),this.transport.close()},r=()=>{this.off("upgrade",r),this.off("upgradeError",r),l()},s=()=>{this.once("upgrade",r),this.once("upgradeError",r)};return(this.readyState==="opening"||this.readyState==="open")&&(this.readyState="closing",this.writeBuffer.length?this.once("drain",()=>{this.upgrading?s():l()}):this.upgrading?s():l()),this}_onError(l){if(ha.priorWebsocketSuccess=!1,this.opts.tryAllTransports&&this.transports.length>1&&this.readyState==="opening")return this.transports.shift(),this._open();this.emitReserved("error",l),this._onClose("transport error",l)}_onClose(l,r){if(this.readyState==="opening"||this.readyState==="open"||this.readyState==="closing"){if(this.clearTimeoutFn(this._pingTimeoutTimer),this.transport.removeAllListeners("close"),this.transport.close(),this.transport.removeAllListeners(),Zc&&(this._beforeunloadEventListener&&removeEventListener("beforeunload",this._beforeunloadEventListener,!1),this._offlineEventListener)){const s=Ds.indexOf(this._offlineEventListener);s!==-1&&Ds.splice(s,1)}this.readyState="closed",this.id=null,this.emitReserved("close",l,r),this.writeBuffer=[],this._prevBufferLen=0}}}ha.protocol=K0;class nx extends ha{constructor(){super(...arguments),this._upgrades=[]}onOpen(){if(super.onOpen(),this.readyState==="open"&&this.opts.upgrade)for(let l=0;l<this._upgrades.length;l++)this._probe(this._upgrades[l])}_probe(l){let r=this.createTransport(l),s=!1;ha.priorWebsocketSuccess=!1;const c=()=>{s||(r.send([{type:"ping",data:"probe"}]),r.once("packet",A=>{if(!s)if(A.type==="pong"&&A.data==="probe"){if(this.upgrading=!0,this.emitReserved("upgrading",r),!r)return;ha.priorWebsocketSuccess=r.name==="websocket",this.transport.pause(()=>{s||this.readyState!=="closed"&&(v(),this.setTransport(r),r.send([{type:"upgrade"}]),this.emitReserved("upgrade",r),r=null,this.upgrading=!1,this.flush())})}else{const _=new Error("probe error");_.transport=r.name,this.emitReserved("upgradeError",_)}}))};function f(){s||(s=!0,v(),r.close(),r=null)}const d=A=>{const _=new Error("probe error: "+A);_.transport=r.name,f(),this.emitReserved("upgradeError",_)};function m(){d("transport closed")}function g(){d("socket closed")}function y(A){r&&A.name!==r.name&&f()}const v=()=>{r.removeListener("open",c),r.removeListener("error",d),r.removeListener("close",m),this.off("close",g),this.off("upgrading",y)};r.once("open",c),r.once("error",d),r.once("close",m),this.once("close",g),this.once("upgrading",y),this._upgrades.indexOf("webtransport")!==-1&&l!=="webtransport"?this.setTimeoutFn(()=>{s||r.open()},200):r.open()}onHandshake(l){this._upgrades=this._filterUpgrades(l.upgrades),super.onHandshake(l)}_filterUpgrades(l){const r=[];for(let s=0;s<l.length;s++)~this.transports.indexOf(l[s])&&r.push(l[s]);return r}}let ax=class extends nx{constructor(l,r={}){const s=typeof l=="object"?l:r;(!s.transports||s.transports&&typeof s.transports[0]=="string")&&(s.transports=(s.transports||["polling","websocket","webtransport"]).map(c=>WS[c]).filter(c=>!!c)),super(l,s)}};function lx(a,l="",r){let s=a;r=r||typeof location<"u"&&location,a==null&&(a=r.protocol+"//"+r.host),typeof a=="string"&&(a.charAt(0)==="/"&&(a.charAt(1)==="/"?a=r.protocol+a:a=r.host+a),/^(https?|wss?):\/\//.test(a)||(typeof r<"u"?a=r.protocol+"//"+a:a="https://"+a),s=Qc(a)),s.port||(/^(http|ws)$/.test(s.protocol)?s.port="80":/^(http|ws)s$/.test(s.protocol)&&(s.port="443")),s.path=s.path||"/";const f=s.host.indexOf(":")!==-1?"["+s.host+"]":s.host;return s.id=s.protocol+"://"+f+":"+s.port+l,s.href=s.protocol+"://"+f+(r&&r.port===s.port?"":":"+s.port),s}const ix=typeof ArrayBuffer=="function",rx=a=>typeof ArrayBuffer.isView=="function"?ArrayBuffer.isView(a):a.buffer instanceof ArrayBuffer,I0=Object.prototype.toString,sx=typeof Blob=="function"||typeof Blob<"u"&&I0.call(Blob)==="[object BlobConstructor]",ux=typeof File=="function"||typeof File<"u"&&I0.call(File)==="[object FileConstructor]";function sf(a){return ix&&(a instanceof ArrayBuffer||rx(a))||sx&&a instanceof Blob||ux&&a instanceof File}function Ms(a,l){if(!a||typeof a!="object")return!1;if(Array.isArray(a)){for(let r=0,s=a.length;r<s;r++)if(Ms(a[r]))return!0;return!1}if(sf(a))return!0;if(a.toJSON&&typeof a.toJSON=="function"&&arguments.length===1)return Ms(a.toJSON(),!0);for(const r in a)if(Object.prototype.hasOwnProperty.call(a,r)&&Ms(a[r]))return!0;return!1}function ox(a){const l=[],r=a.data,s=a;return s.data=Kc(r,l),s.attachments=l.length,{packet:s,buffers:l}}function Kc(a,l){if(!a)return a;if(sf(a)){const r={_placeholder:!0,num:l.length};return l.push(a),r}else if(Array.isArray(a)){const r=new Array(a.length);for(let s=0;s<a.length;s++)r[s]=Kc(a[s],l);return r}else if(typeof a=="object"&&!(a instanceof Date)){const r={};for(const s in a)Object.prototype.hasOwnProperty.call(a,s)&&(r[s]=Kc(a[s],l));return r}return a}function cx(a,l){return a.data=Fc(a.data,l),delete a.attachments,a}function Fc(a,l){if(!a)return a;if(a&&a._placeholder===!0){if(typeof a.num=="number"&&a.num>=0&&a.num<l.length)return l[a.num];throw new Error("illegal attachments")}else if(Array.isArray(a))for(let r=0;r<a.length;r++)a[r]=Fc(a[r],l);else if(typeof a=="object")for(const r in a)Object.prototype.hasOwnProperty.call(a,r)&&(a[r]=Fc(a[r],l));return a}const fx=["connect","connect_error","disconnect","disconnecting","newListener","removeListener"],dx=5;var we;(function(a){a[a.CONNECT=0]="CONNECT",a[a.DISCONNECT=1]="DISCONNECT",a[a.EVENT=2]="EVENT",a[a.ACK=3]="ACK",a[a.CONNECT_ERROR=4]="CONNECT_ERROR",a[a.BINARY_EVENT=5]="BINARY_EVENT",a[a.BINARY_ACK=6]="BINARY_ACK"})(we||(we={}));class hx{constructor(l){this.replacer=l}encode(l){return(l.type===we.EVENT||l.type===we.ACK)&&Ms(l)?this.encodeAsBinary({type:l.type===we.EVENT?we.BINARY_EVENT:we.BINARY_ACK,nsp:l.nsp,data:l.data,id:l.id}):[this.encodeAsString(l)]}encodeAsString(l){let r=""+l.type;return(l.type===we.BINARY_EVENT||l.type===we.BINARY_ACK)&&(r+=l.attachments+"-"),l.nsp&&l.nsp!=="/"&&(r+=l.nsp+","),l.id!=null&&(r+=l.id),l.data!=null&&(r+=JSON.stringify(l.data,this.replacer)),r}encodeAsBinary(l){const r=ox(l),s=this.encodeAsString(r.packet),c=r.buffers;return c.unshift(s),c}}function Yy(a){return Object.prototype.toString.call(a)==="[object Object]"}class uf extends it{constructor(l){super(),this.reviver=l}add(l){let r;if(typeof l=="string"){if(this.reconstructor)throw new Error("got plaintext data when reconstructing a packet");r=this.decodeString(l);const s=r.type===we.BINARY_EVENT;s||r.type===we.BINARY_ACK?(r.type=s?we.EVENT:we.ACK,this.reconstructor=new mx(r),r.attachments===0&&super.emitReserved("decoded",r)):super.emitReserved("decoded",r)}else if(sf(l)||l.base64)if(this.reconstructor)r=this.reconstructor.takeBinaryData(l),r&&(this.reconstructor=null,super.emitReserved("decoded",r));else throw new Error("got binary data when not reconstructing a packet");else throw new Error("Unknown type: "+l)}decodeString(l){let r=0;const s={type:Number(l.charAt(0))};if(we[s.type]===void 0)throw new Error("unknown packet type "+s.type);if(s.type===we.BINARY_EVENT||s.type===we.BINARY_ACK){const f=r+1;for(;l.charAt(++r)!=="-"&&r!=l.length;);const d=l.substring(f,r);if(d!=Number(d)||l.charAt(r)!=="-")throw new Error("Illegal attachments");s.attachments=Number(d)}if(l.charAt(r+1)==="/"){const f=r+1;for(;++r&&!(l.charAt(r)===","||r===l.length););s.nsp=l.substring(f,r)}else s.nsp="/";const c=l.charAt(r+1);if(c!==""&&Number(c)==c){const f=r+1;for(;++r;){const d=l.charAt(r);if(d==null||Number(d)!=d){--r;break}if(r===l.length)break}s.id=Number(l.substring(f,r+1))}if(l.charAt(++r)){const f=this.tryParse(l.substr(r));if(uf.isPayloadValid(s.type,f))s.data=f;else throw new Error("invalid payload")}return s}tryParse(l){try{return JSON.parse(l,this.reviver)}catch{return!1}}static isPayloadValid(l,r){switch(l){case we.CONNECT:return Yy(r);case we.DISCONNECT:return r===void 0;case we.CONNECT_ERROR:return typeof r=="string"||Yy(r);case we.EVENT:case we.BINARY_EVENT:return Array.isArray(r)&&(typeof r[0]=="number"||typeof r[0]=="string"&&fx.indexOf(r[0])===-1);case we.ACK:case we.BINARY_ACK:return Array.isArray(r)}}destroy(){this.reconstructor&&(this.reconstructor.finishedReconstruction(),this.reconstructor=null)}}class mx{constructor(l){this.packet=l,this.buffers=[],this.reconPack=l}takeBinaryData(l){if(this.buffers.push(l),this.buffers.length===this.reconPack.attachments){const r=cx(this.reconPack,this.buffers);return this.finishedReconstruction(),r}return null}finishedReconstruction(){this.reconPack=null,this.buffers=[]}}const yx=Object.freeze(Object.defineProperty({__proto__:null,Decoder:uf,Encoder:hx,get PacketType(){return we},protocol:dx},Symbol.toStringTag,{value:"Module"}));function en(a,l,r){return a.on(l,r),function(){a.off(l,r)}}const px=Object.freeze({connect:1,connect_error:1,disconnect:1,disconnecting:1,newListener:1,removeListener:1});class ep extends it{constructor(l,r,s){super(),this.connected=!1,this.recovered=!1,this.receiveBuffer=[],this.sendBuffer=[],this._queue=[],this._queueSeq=0,this.ids=0,this.acks={},this.flags={},this.io=l,this.nsp=r,s&&s.auth&&(this.auth=s.auth),this._opts=Object.assign({},s),this.io._autoConnect&&this.open()}get disconnected(){return!this.connected}subEvents(){if(this.subs)return;const l=this.io;this.subs=[en(l,"open",this.onopen.bind(this)),en(l,"packet",this.onpacket.bind(this)),en(l,"error",this.onerror.bind(this)),en(l,"close",this.onclose.bind(this))]}get active(){return!!this.subs}connect(){return this.connected?this:(this.subEvents(),this.io._reconnecting||this.io.open(),this.io._readyState==="open"&&this.onopen(),this)}open(){return this.connect()}send(...l){return l.unshift("message"),this.emit.apply(this,l),this}emit(l,...r){var s,c,f;if(px.hasOwnProperty(l))throw new Error('"'+l.toString()+'" is a reserved event name');if(r.unshift(l),this._opts.retries&&!this.flags.fromQueue&&!this.flags.volatile)return this._addToQueue(r),this;const d={type:we.EVENT,data:r};if(d.options={},d.options.compress=this.flags.compress!==!1,typeof r[r.length-1]=="function"){const v=this.ids++,A=r.pop();this._registerAckCallback(v,A),d.id=v}const m=(c=(s=this.io.engine)===null||s===void 0?void 0:s.transport)===null||c===void 0?void 0:c.writable,g=this.connected&&!(!((f=this.io.engine)===null||f===void 0)&&f._hasPingExpired());return this.flags.volatile&&!m||(g?(this.notifyOutgoingListeners(d),this.packet(d)):this.sendBuffer.push(d)),this.flags={},this}_registerAckCallback(l,r){var s;const c=(s=this.flags.timeout)!==null&&s!==void 0?s:this._opts.ackTimeout;if(c===void 0){this.acks[l]=r;return}const f=this.io.setTimeoutFn(()=>{delete this.acks[l];for(let m=0;m<this.sendBuffer.length;m++)this.sendBuffer[m].id===l&&this.sendBuffer.splice(m,1);r.call(this,new Error("operation has timed out"))},c),d=(...m)=>{this.io.clearTimeoutFn(f),r.apply(this,m)};d.withError=!0,this.acks[l]=d}emitWithAck(l,...r){return new Promise((s,c)=>{const f=(d,m)=>d?c(d):s(m);f.withError=!0,r.push(f),this.emit(l,...r)})}_addToQueue(l){let r;typeof l[l.length-1]=="function"&&(r=l.pop());const s={id:this._queueSeq++,tryCount:0,pending:!1,args:l,flags:Object.assign({fromQueue:!0},this.flags)};l.push((c,...f)=>s!==this._queue[0]?void 0:(c!==null?s.tryCount>this._opts.retries&&(this._queue.shift(),r&&r(c)):(this._queue.shift(),r&&r(null,...f)),s.pending=!1,this._drainQueue())),this._queue.push(s),this._drainQueue()}_drainQueue(l=!1){if(!this.connected||this._queue.length===0)return;const r=this._queue[0];r.pending&&!l||(r.pending=!0,r.tryCount++,this.flags=r.flags,this.emit.apply(this,r.args))}packet(l){l.nsp=this.nsp,this.io._packet(l)}onopen(){typeof this.auth=="function"?this.auth(l=>{this._sendConnectPacket(l)}):this._sendConnectPacket(this.auth)}_sendConnectPacket(l){this.packet({type:we.CONNECT,data:this._pid?Object.assign({pid:this._pid,offset:this._lastOffset},l):l})}onerror(l){this.connected||this.emitReserved("connect_error",l)}onclose(l,r){this.connected=!1,delete this.id,this.emitReserved("disconnect",l,r),this._clearAcks()}_clearAcks(){Object.keys(this.acks).forEach(l=>{if(!this.sendBuffer.some(s=>String(s.id)===l)){const s=this.acks[l];delete this.acks[l],s.withError&&s.call(this,new Error("socket has been disconnected"))}})}onpacket(l){if(l.nsp===this.nsp)switch(l.type){case we.CONNECT:l.data&&l.data.sid?this.onconnect(l.data.sid,l.data.pid):this.emitReserved("connect_error",new Error("It seems you are trying to reach a Socket.IO server in v2.x with a v3.x client, but they are not compatible (more information here: https://socket.io/docs/v3/migrating-from-2-x-to-3-0/)"));break;case we.EVENT:case we.BINARY_EVENT:this.onevent(l);break;case we.ACK:case we.BINARY_ACK:this.onack(l);break;case we.DISCONNECT:this.ondisconnect();break;case we.CONNECT_ERROR:this.destroy();const s=new Error(l.data.message);s.data=l.data.data,this.emitReserved("connect_error",s);break}}onevent(l){const r=l.data||[];l.id!=null&&r.push(this.ack(l.id)),this.connected?this.emitEvent(r):this.receiveBuffer.push(Object.freeze(r))}emitEvent(l){if(this._anyListeners&&this._anyListeners.length){const r=this._anyListeners.slice();for(const s of r)s.apply(this,l)}super.emit.apply(this,l),this._pid&&l.length&&typeof l[l.length-1]=="string"&&(this._lastOffset=l[l.length-1])}ack(l){const r=this;let s=!1;return function(...c){s||(s=!0,r.packet({type:we.ACK,id:l,data:c}))}}onack(l){const r=this.acks[l.id];typeof r=="function"&&(delete this.acks[l.id],r.withError&&l.data.unshift(null),r.apply(this,l.data))}onconnect(l,r){this.id=l,this.recovered=r&&this._pid===r,this._pid=r,this.connected=!0,this.emitBuffered(),this.emitReserved("connect"),this._drainQueue(!0)}emitBuffered(){this.receiveBuffer.forEach(l=>this.emitEvent(l)),this.receiveBuffer=[],this.sendBuffer.forEach(l=>{this.notifyOutgoingListeners(l),this.packet(l)}),this.sendBuffer=[]}ondisconnect(){this.destroy(),this.onclose("io server disconnect")}destroy(){this.subs&&(this.subs.forEach(l=>l()),this.subs=void 0),this.io._destroy(this)}disconnect(){return this.connected&&this.packet({type:we.DISCONNECT}),this.destroy(),this.connected&&this.onclose("io client disconnect"),this}close(){return this.disconnect()}compress(l){return this.flags.compress=l,this}get volatile(){return this.flags.volatile=!0,this}timeout(l){return this.flags.timeout=l,this}onAny(l){return this._anyListeners=this._anyListeners||[],this._anyListeners.push(l),this}prependAny(l){return this._anyListeners=this._anyListeners||[],this._anyListeners.unshift(l),this}offAny(l){if(!this._anyListeners)return this;if(l){const r=this._anyListeners;for(let s=0;s<r.length;s++)if(l===r[s])return r.splice(s,1),this}else this._anyListeners=[];return this}listenersAny(){return this._anyListeners||[]}onAnyOutgoing(l){return this._anyOutgoingListeners=this._anyOutgoingListeners||[],this._anyOutgoingListeners.push(l),this}prependAnyOutgoing(l){return this._anyOutgoingListeners=this._anyOutgoingListeners||[],this._anyOutgoingListeners.unshift(l),this}offAnyOutgoing(l){if(!this._anyOutgoingListeners)return this;if(l){const r=this._anyOutgoingListeners;for(let s=0;s<r.length;s++)if(l===r[s])return r.splice(s,1),this}else this._anyOutgoingListeners=[];return this}listenersAnyOutgoing(){return this._anyOutgoingListeners||[]}notifyOutgoingListeners(l){if(this._anyOutgoingListeners&&this._anyOutgoingListeners.length){const r=this._anyOutgoingListeners.slice();for(const s of r)s.apply(this,l.data)}}}function Ll(a){a=a||{},this.ms=a.min||100,this.max=a.max||1e4,this.factor=a.factor||2,this.jitter=a.jitter>0&&a.jitter<=1?a.jitter:0,this.attempts=0}Ll.prototype.duration=function(){var a=this.ms*Math.pow(this.factor,this.attempts++);if(this.jitter){var l=Math.random(),r=Math.floor(l*this.jitter*a);a=(Math.floor(l*10)&1)==0?a-r:a+r}return Math.min(a,this.max)|0};Ll.prototype.reset=function(){this.attempts=0};Ll.prototype.setMin=function(a){this.ms=a};Ll.prototype.setMax=function(a){this.max=a};Ll.prototype.setJitter=function(a){this.jitter=a};class Jc extends it{constructor(l,r){var s;super(),this.nsps={},this.subs=[],l&&typeof l=="object"&&(r=l,l=void 0),r=r||{},r.path=r.path||"/socket.io",this.opts=r,tu(this,r),this.reconnection(r.reconnection!==!1),this.reconnectionAttempts(r.reconnectionAttempts||1/0),this.reconnectionDelay(r.reconnectionDelay||1e3),this.reconnectionDelayMax(r.reconnectionDelayMax||5e3),this.randomizationFactor((s=r.randomizationFactor)!==null&&s!==void 0?s:.5),this.backoff=new Ll({min:this.reconnectionDelay(),max:this.reconnectionDelayMax(),jitter:this.randomizationFactor()}),this.timeout(r.timeout==null?2e4:r.timeout),this._readyState="closed",this.uri=l;const c=r.parser||yx;this.encoder=new c.Encoder,this.decoder=new c.Decoder,this._autoConnect=r.autoConnect!==!1,this._autoConnect&&this.open()}reconnection(l){return arguments.length?(this._reconnection=!!l,l||(this.skipReconnect=!0),this):this._reconnection}reconnectionAttempts(l){return l===void 0?this._reconnectionAttempts:(this._reconnectionAttempts=l,this)}reconnectionDelay(l){var r;return l===void 0?this._reconnectionDelay:(this._reconnectionDelay=l,(r=this.backoff)===null||r===void 0||r.setMin(l),this)}randomizationFactor(l){var r;return l===void 0?this._randomizationFactor:(this._randomizationFactor=l,(r=this.backoff)===null||r===void 0||r.setJitter(l),this)}reconnectionDelayMax(l){var r;return l===void 0?this._reconnectionDelayMax:(this._reconnectionDelayMax=l,(r=this.backoff)===null||r===void 0||r.setMax(l),this)}timeout(l){return arguments.length?(this._timeout=l,this):this._timeout}maybeReconnectOnOpen(){!this._reconnecting&&this._reconnection&&this.backoff.attempts===0&&this.reconnect()}open(l){if(~this._readyState.indexOf("open"))return this;this.engine=new ax(this.uri,this.opts);const r=this.engine,s=this;this._readyState="opening",this.skipReconnect=!1;const c=en(r,"open",function(){s.onopen(),l&&l()}),f=m=>{this.cleanup(),this._readyState="closed",this.emitReserved("error",m),l?l(m):this.maybeReconnectOnOpen()},d=en(r,"error",f);if(this._timeout!==!1){const m=this._timeout,g=this.setTimeoutFn(()=>{c(),f(new Error("timeout")),r.close()},m);this.opts.autoUnref&&g.unref(),this.subs.push(()=>{this.clearTimeoutFn(g)})}return this.subs.push(c),this.subs.push(d),this}connect(l){return this.open(l)}onopen(){this.cleanup(),this._readyState="open",this.emitReserved("open");const l=this.engine;this.subs.push(en(l,"ping",this.onping.bind(this)),en(l,"data",this.ondata.bind(this)),en(l,"error",this.onerror.bind(this)),en(l,"close",this.onclose.bind(this)),en(this.decoder,"decoded",this.ondecoded.bind(this)))}onping(){this.emitReserved("ping")}ondata(l){try{this.decoder.add(l)}catch(r){this.onclose("parse error",r)}}ondecoded(l){eu(()=>{this.emitReserved("packet",l)},this.setTimeoutFn)}onerror(l){this.emitReserved("error",l)}socket(l,r){let s=this.nsps[l];return s?this._autoConnect&&!s.active&&s.connect():(s=new ep(this,l,r),this.nsps[l]=s),s}_destroy(l){const r=Object.keys(this.nsps);for(const s of r)if(this.nsps[s].active)return;this._close()}_packet(l){const r=this.encoder.encode(l);for(let s=0;s<r.length;s++)this.engine.write(r[s],l.options)}cleanup(){this.subs.forEach(l=>l()),this.subs.length=0,this.decoder.destroy()}_close(){this.skipReconnect=!0,this._reconnecting=!1,this.onclose("forced close")}disconnect(){return this._close()}onclose(l,r){var s;this.cleanup(),(s=this.engine)===null||s===void 0||s.close(),this.backoff.reset(),this._readyState="closed",this.emitReserved("close",l,r),this._reconnection&&!this.skipReconnect&&this.reconnect()}reconnect(){if(this._reconnecting||this.skipReconnect)return this;const l=this;if(this.backoff.attempts>=this._reconnectionAttempts)this.backoff.reset(),this.emitReserved("reconnect_failed"),this._reconnecting=!1;else{const r=this.backoff.duration();this._reconnecting=!0;const s=this.setTimeoutFn(()=>{l.skipReconnect||(this.emitReserved("reconnect_attempt",l.backoff.attempts),!l.skipReconnect&&l.open(c=>{c?(l._reconnecting=!1,l.reconnect(),this.emitReserved("reconnect_error",c)):l.onreconnect()}))},r);this.opts.autoUnref&&s.unref(),this.subs.push(()=>{this.clearTimeoutFn(s)})}}onreconnect(){const l=this.backoff.attempts;this._reconnecting=!1,this.backoff.reset(),this.emitReserved("reconnect",l)}}const Vi={};function Us(a,l){typeof a=="object"&&(l=a,a=void 0),l=l||{};const r=lx(a,l.path||"/socket.io"),s=r.source,c=r.id,f=r.path,d=Vi[c]&&f in Vi[c].nsps,m=l.forceNew||l["force new connection"]||l.multiplex===!1||d;let g;return m?g=new Jc(s,l):(Vi[c]||(Vi[c]=new Jc(s,l)),g=Vi[c]),r.query&&!l.query&&(l.query=r.queryKey),g.socket(r.path,l)}Object.assign(Us,{Manager:Jc,Socket:ep,io:Us,connect:Us});const tp=N.createContext(),gx=({children:a})=>{const[l,r]=N.useState(null),[s,c]=N.useState([]),f=ar();return N.useEffect(()=>{var d,m;if(f){const g=Us("http://localhost:3000",{withCredentials:!0,query:{userId:(m=(d=f==null?void 0:f.authUser)==null?void 0:d.user)==null?void 0:m.userId}});return r(g),g.on("getonline",y=>{c(y),console.log("client disconnect",g.id)}),()=>g.close()}else l&&(l.close(),r(null))},[f]),S.jsx(tp.Provider,{value:{socket:l,onlineUsers:s},children:a})},of=()=>N.useContext(tp),vx=({user:a,onMobileSelect:l})=>{const{selectedConversation:r,setSelectedConversation:s}=ma(),c=(r==null?void 0:r._id)===a._id,{onlineUsers:f}=of(),d=f.includes(a._id),m=()=>{s(a),l&&l()};return S.jsx("div",{className:`cursor-pointer hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors ${c?"bg-gray-100 dark:bg-gray-700":""}`,onClick:m,children:S.jsxs("div",{className:"flex items-center px-4 py-4 lg:py-3",children:[S.jsxs("div",{className:"relative flex-shrink-0",children:[S.jsx("div",{className:"w-12 h-12 lg:w-14 lg:h-14 rounded-full overflow-hidden bg-gray-300 dark:bg-gray-600",children:S.jsx("img",{src:"https://img.daisyui.com/images/profile/demo/<EMAIL>",alt:a.username,className:"w-full h-full object-cover"})}),d&&S.jsx("div",{className:"absolute bottom-0 right-0 w-3 h-3 lg:w-4 lg:h-4 bg-green-500 border-2 border-white dark:border-gray-800 rounded-full"})]}),S.jsxs("div",{className:"flex-1 min-w-0 ml-3 lg:ml-4",children:[S.jsxs("div",{className:"flex items-center justify-between",children:[S.jsx("h3",{className:"font-medium text-gray-900 dark:text-white truncate text-sm lg:text-base",children:a.username}),S.jsx("span",{className:"text-xs text-gray-500 dark:text-gray-400"})]}),S.jsx("p",{className:"text-sm lg:text-sm text-gray-500 dark:text-gray-400 truncate mt-1",children:a.email})]}),S.jsx("div",{className:"flex-shrink-0 ml-2"})]})})};var Yi={},Xy;function bx(){if(Xy)return Yi;Xy=1,Object.defineProperty(Yi,"__esModule",{value:!0}),Yi.parse=d,Yi.serialize=y;const a=/^[\u0021-\u003A\u003C\u003E-\u007E]+$/,l=/^[\u0021-\u003A\u003C-\u007E]*$/,r=/^([.]?[a-z0-9]([a-z0-9-]{0,61}[a-z0-9])?)([.][a-z0-9]([a-z0-9-]{0,61}[a-z0-9])?)*$/i,s=/^[\u0020-\u003A\u003D-\u007E]*$/,c=Object.prototype.toString,f=(()=>{const _=function(){};return _.prototype=Object.create(null),_})();function d(_,Q){const R=new f,B=_.length;if(B<2)return R;const L=(Q==null?void 0:Q.decode)||v;let X=0;do{const V=_.indexOf("=",X);if(V===-1)break;const $=_.indexOf(";",X),ie=$===-1?B:$;if(V>ie){X=_.lastIndexOf(";",V-1)+1;continue}const K=m(_,X,V),Ae=g(_,V,K),ye=_.slice(K,Ae);if(R[ye]===void 0){let De=m(_,V+1,ie),pe=g(_,ie,De);const Qe=L(_.slice(De,pe));R[ye]=Qe}X=ie+1}while(X<B);return R}function m(_,Q,R){do{const B=_.charCodeAt(Q);if(B!==32&&B!==9)return Q}while(++Q<R);return R}function g(_,Q,R){for(;Q>R;){const B=_.charCodeAt(--Q);if(B!==32&&B!==9)return Q+1}return R}function y(_,Q,R){const B=(R==null?void 0:R.encode)||encodeURIComponent;if(!a.test(_))throw new TypeError(`argument name is invalid: ${_}`);const L=B(Q);if(!l.test(L))throw new TypeError(`argument val is invalid: ${Q}`);let X=_+"="+L;if(!R)return X;if(R.maxAge!==void 0){if(!Number.isInteger(R.maxAge))throw new TypeError(`option maxAge is invalid: ${R.maxAge}`);X+="; Max-Age="+R.maxAge}if(R.domain){if(!r.test(R.domain))throw new TypeError(`option domain is invalid: ${R.domain}`);X+="; Domain="+R.domain}if(R.path){if(!s.test(R.path))throw new TypeError(`option path is invalid: ${R.path}`);X+="; Path="+R.path}if(R.expires){if(!A(R.expires)||!Number.isFinite(R.expires.valueOf()))throw new TypeError(`option expires is invalid: ${R.expires}`);X+="; Expires="+R.expires.toUTCString()}if(R.httpOnly&&(X+="; HttpOnly"),R.secure&&(X+="; Secure"),R.partitioned&&(X+="; Partitioned"),R.priority)switch(typeof R.priority=="string"?R.priority.toLowerCase():void 0){case"low":X+="; Priority=Low";break;case"medium":X+="; Priority=Medium";break;case"high":X+="; Priority=High";break;default:throw new TypeError(`option priority is invalid: ${R.priority}`)}if(R.sameSite)switch(typeof R.sameSite=="string"?R.sameSite.toLowerCase():R.sameSite){case!0:case"strict":X+="; SameSite=Strict";break;case"lax":X+="; SameSite=Lax";break;case"none":X+="; SameSite=None";break;default:throw new TypeError(`option sameSite is invalid: ${R.sameSite}`)}return X}function v(_){if(_.indexOf("%")===-1)return _;try{return decodeURIComponent(_)}catch{return _}}function A(_){return c.call(_)==="[object Date]"}return Yi}bx();var Gy="popstate";function Sx(a={}){function l(s,c){let{pathname:f,search:d,hash:m}=s.location;return $c("",{pathname:f,search:d,hash:m},c.state&&c.state.usr||null,c.state&&c.state.key||"default")}function r(s,c){return typeof c=="string"?c:Ii(c)}return Ex(l,r,null,a)}function Ge(a,l){if(a===!1||a===null||typeof a>"u")throw new Error(l)}function an(a,l){if(!a){typeof console<"u"&&console.warn(l);try{throw new Error(l)}catch{}}}function xx(){return Math.random().toString(36).substring(2,10)}function Qy(a,l){return{usr:a.state,key:a.key,idx:l}}function $c(a,l,r=null,s){return{pathname:typeof a=="string"?a:a.pathname,search:"",hash:"",...typeof l=="string"?kl(l):l,state:r,key:l&&l.key||s||xx()}}function Ii({pathname:a="/",search:l="",hash:r=""}){return l&&l!=="?"&&(a+=l.charAt(0)==="?"?l:"?"+l),r&&r!=="#"&&(a+=r.charAt(0)==="#"?r:"#"+r),a}function kl(a){let l={};if(a){let r=a.indexOf("#");r>=0&&(l.hash=a.substring(r),a=a.substring(0,r));let s=a.indexOf("?");s>=0&&(l.search=a.substring(s),a=a.substring(0,s)),a&&(l.pathname=a)}return l}function Ex(a,l,r,s={}){let{window:c=document.defaultView,v5Compat:f=!1}=s,d=c.history,m="POP",g=null,y=v();y==null&&(y=0,d.replaceState({...d.state,idx:y},""));function v(){return(d.state||{idx:null}).idx}function A(){m="POP";let L=v(),X=L==null?null:L-y;y=L,g&&g({action:m,location:B.location,delta:X})}function _(L,X){m="PUSH";let V=$c(B.location,L,X);y=v()+1;let $=Qy(V,y),ie=B.createHref(V);try{d.pushState($,"",ie)}catch(K){if(K instanceof DOMException&&K.name==="DataCloneError")throw K;c.location.assign(ie)}f&&g&&g({action:m,location:B.location,delta:1})}function Q(L,X){m="REPLACE";let V=$c(B.location,L,X);y=v();let $=Qy(V,y),ie=B.createHref(V);d.replaceState($,"",ie),f&&g&&g({action:m,location:B.location,delta:0})}function R(L){return _x(L)}let B={get action(){return m},get location(){return a(c,d)},listen(L){if(g)throw new Error("A history only accepts one active listener");return c.addEventListener(Gy,A),g=L,()=>{c.removeEventListener(Gy,A),g=null}},createHref(L){return l(c,L)},createURL:R,encodeLocation(L){let X=R(L);return{pathname:X.pathname,search:X.search,hash:X.hash}},push:_,replace:Q,go(L){return d.go(L)}};return B}function _x(a,l=!1){let r="http://localhost";typeof window<"u"&&(r=window.location.origin!=="null"?window.location.origin:window.location.href),Ge(r,"No window.location.(origin|href) available to create URL");let s=typeof a=="string"?a:Ii(a);return s=s.replace(/ $/,"%20"),!l&&s.startsWith("//")&&(s=r+s),new URL(s,r)}function np(a,l,r="/"){return wx(a,l,r,!1)}function wx(a,l,r,s){let c=typeof l=="string"?kl(l):l,f=qn(c.pathname||"/",r);if(f==null)return null;let d=ap(a);Ax(d);let m=null;for(let g=0;m==null&&g<d.length;++g){let y=Bx(f);m=zx(d[g],y,s)}return m}function ap(a,l=[],r=[],s=""){let c=(f,d,m)=>{let g={relativePath:m===void 0?f.path||"":m,caseSensitive:f.caseSensitive===!0,childrenIndex:d,route:f};g.relativePath.startsWith("/")&&(Ge(g.relativePath.startsWith(s),`Absolute route path "${g.relativePath}" nested under path "${s}" is not valid. An absolute child route path must start with the combined path of all its parent routes.`),g.relativePath=g.relativePath.slice(s.length));let y=Bn([s,g.relativePath]),v=r.concat(g);f.children&&f.children.length>0&&(Ge(f.index!==!0,`Index routes must not have child routes. Please remove all child routes from route path "${y}".`),ap(f.children,l,v,y)),!(f.path==null&&!f.index)&&l.push({path:y,score:Mx(y,f.index),routesMeta:v})};return a.forEach((f,d)=>{var m;if(f.path===""||!((m=f.path)!=null&&m.includes("?")))c(f,d);else for(let g of lp(f.path))c(f,d,g)}),l}function lp(a){let l=a.split("/");if(l.length===0)return[];let[r,...s]=l,c=r.endsWith("?"),f=r.replace(/\?$/,"");if(s.length===0)return c?[f,""]:[f];let d=lp(s.join("/")),m=[];return m.push(...d.map(g=>g===""?f:[f,g].join("/"))),c&&m.push(...d),m.map(g=>a.startsWith("/")&&g===""?"/":g)}function Ax(a){a.sort((l,r)=>l.score!==r.score?r.score-l.score:Ux(l.routesMeta.map(s=>s.childrenIndex),r.routesMeta.map(s=>s.childrenIndex)))}var Tx=/^:[\w-]+$/,Rx=3,Ox=2,Nx=1,Cx=10,Dx=-2,Zy=a=>a==="*";function Mx(a,l){let r=a.split("/"),s=r.length;return r.some(Zy)&&(s+=Dx),l&&(s+=Ox),r.filter(c=>!Zy(c)).reduce((c,f)=>c+(Tx.test(f)?Rx:f===""?Nx:Cx),s)}function Ux(a,l){return a.length===l.length&&a.slice(0,-1).every((s,c)=>s===l[c])?a[a.length-1]-l[l.length-1]:0}function zx(a,l,r=!1){let{routesMeta:s}=a,c={},f="/",d=[];for(let m=0;m<s.length;++m){let g=s[m],y=m===s.length-1,v=f==="/"?l:l.slice(f.length)||"/",A=Hs({path:g.relativePath,caseSensitive:g.caseSensitive,end:y},v),_=g.route;if(!A&&y&&r&&!s[s.length-1].route.index&&(A=Hs({path:g.relativePath,caseSensitive:g.caseSensitive,end:!1},v)),!A)return null;Object.assign(c,A.params),d.push({params:c,pathname:Bn([f,A.pathname]),pathnameBase:Hx(Bn([f,A.pathnameBase])),route:_}),A.pathnameBase!=="/"&&(f=Bn([f,A.pathnameBase]))}return d}function Hs(a,l){typeof a=="string"&&(a={path:a,caseSensitive:!1,end:!0});let[r,s]=jx(a.path,a.caseSensitive,a.end),c=l.match(r);if(!c)return null;let f=c[0],d=f.replace(/(.)\/+$/,"$1"),m=c.slice(1);return{params:s.reduce((y,{paramName:v,isOptional:A},_)=>{if(v==="*"){let R=m[_]||"";d=f.slice(0,f.length-R.length).replace(/(.)\/+$/,"$1")}const Q=m[_];return A&&!Q?y[v]=void 0:y[v]=(Q||"").replace(/%2F/g,"/"),y},{}),pathname:f,pathnameBase:d,pattern:a}}function jx(a,l=!1,r=!0){an(a==="*"||!a.endsWith("*")||a.endsWith("/*"),`Route path "${a}" will be treated as if it were "${a.replace(/\*$/,"/*")}" because the \`*\` character must always follow a \`/\` in the pattern. To get rid of this warning, please change the route path to "${a.replace(/\*$/,"/*")}".`);let s=[],c="^"+a.replace(/\/*\*?$/,"").replace(/^\/*/,"/").replace(/[\\.*+^${}|()[\]]/g,"\\$&").replace(/\/:([\w-]+)(\?)?/g,(d,m,g)=>(s.push({paramName:m,isOptional:g!=null}),g?"/?([^\\/]+)?":"/([^\\/]+)"));return a.endsWith("*")?(s.push({paramName:"*"}),c+=a==="*"||a==="/*"?"(.*)$":"(?:\\/(.+)|\\/*)$"):r?c+="\\/*$":a!==""&&a!=="/"&&(c+="(?:(?=\\/|$))"),[new RegExp(c,l?void 0:"i"),s]}function Bx(a){try{return a.split("/").map(l=>decodeURIComponent(l).replace(/\//g,"%2F")).join("/")}catch(l){return an(!1,`The URL path "${a}" could not be decoded because it is a malformed URL segment. This is probably due to a bad percent encoding (${l}).`),a}}function qn(a,l){if(l==="/")return a;if(!a.toLowerCase().startsWith(l.toLowerCase()))return null;let r=l.endsWith("/")?l.length-1:l.length,s=a.charAt(r);return s&&s!=="/"?null:a.slice(r)||"/"}function Lx(a,l="/"){let{pathname:r,search:s="",hash:c=""}=typeof a=="string"?kl(a):a;return{pathname:r?r.startsWith("/")?r:kx(r,l):l,search:Vx(s),hash:Yx(c)}}function kx(a,l){let r=l.replace(/\/+$/,"").split("/");return a.split("/").forEach(c=>{c===".."?r.length>1&&r.pop():c!=="."&&r.push(c)}),r.length>1?r.join("/"):"/"}function Mc(a,l,r,s){return`Cannot include a '${a}' character in a manually specified \`to.${l}\` field [${JSON.stringify(s)}].  Please separate it out to the \`to.${r}\` field. Alternatively you may provide the full path as a string in <Link to="..."> and the router will parse it for you.`}function qx(a){return a.filter((l,r)=>r===0||l.route.path&&l.route.path.length>0)}function cf(a){let l=qx(a);return l.map((r,s)=>s===l.length-1?r.pathname:r.pathnameBase)}function ff(a,l,r,s=!1){let c;typeof a=="string"?c=kl(a):(c={...a},Ge(!c.pathname||!c.pathname.includes("?"),Mc("?","pathname","search",c)),Ge(!c.pathname||!c.pathname.includes("#"),Mc("#","pathname","hash",c)),Ge(!c.search||!c.search.includes("#"),Mc("#","search","hash",c)));let f=a===""||c.pathname==="",d=f?"/":c.pathname,m;if(d==null)m=r;else{let A=l.length-1;if(!s&&d.startsWith("..")){let _=d.split("/");for(;_[0]==="..";)_.shift(),A-=1;c.pathname=_.join("/")}m=A>=0?l[A]:"/"}let g=Lx(c,m),y=d&&d!=="/"&&d.endsWith("/"),v=(f||d===".")&&r.endsWith("/");return!g.pathname.endsWith("/")&&(y||v)&&(g.pathname+="/"),g}var Bn=a=>a.join("/").replace(/\/\/+/g,"/"),Hx=a=>a.replace(/\/+$/,"").replace(/^\/*/,"/"),Vx=a=>!a||a==="?"?"":a.startsWith("?")?a:"?"+a,Yx=a=>!a||a==="#"?"":a.startsWith("#")?a:"#"+a;function Xx(a){return a!=null&&typeof a.status=="number"&&typeof a.statusText=="string"&&typeof a.internal=="boolean"&&"data"in a}var ip=["POST","PUT","PATCH","DELETE"];new Set(ip);var Gx=["GET",...ip];new Set(Gx);var ql=N.createContext(null);ql.displayName="DataRouter";var nu=N.createContext(null);nu.displayName="DataRouterState";var rp=N.createContext({isTransitioning:!1});rp.displayName="ViewTransition";var Qx=N.createContext(new Map);Qx.displayName="Fetchers";var Zx=N.createContext(null);Zx.displayName="Await";var rn=N.createContext(null);rn.displayName="Navigation";var lr=N.createContext(null);lr.displayName="Location";var gn=N.createContext({outlet:null,matches:[],isDataRoute:!1});gn.displayName="Route";var df=N.createContext(null);df.displayName="RouteError";function Kx(a,{relative:l}={}){Ge(Hl(),"useHref() may be used only in the context of a <Router> component.");let{basename:r,navigator:s}=N.useContext(rn),{hash:c,pathname:f,search:d}=rr(a,{relative:l}),m=f;return r!=="/"&&(m=f==="/"?r:Bn([r,f])),s.createHref({pathname:m,search:d,hash:c})}function Hl(){return N.useContext(lr)!=null}function pa(){return Ge(Hl(),"useLocation() may be used only in the context of a <Router> component."),N.useContext(lr).location}var sp="You should call navigate() in a React.useEffect(), not when your component is first rendered.";function up(a){N.useContext(rn).static||N.useLayoutEffect(a)}function ir(){let{isDataRoute:a}=N.useContext(gn);return a?rE():Fx()}function Fx(){Ge(Hl(),"useNavigate() may be used only in the context of a <Router> component.");let a=N.useContext(ql),{basename:l,navigator:r}=N.useContext(rn),{matches:s}=N.useContext(gn),{pathname:c}=pa(),f=JSON.stringify(cf(s)),d=N.useRef(!1);return up(()=>{d.current=!0}),N.useCallback((g,y={})=>{if(an(d.current,sp),!d.current)return;if(typeof g=="number"){r.go(g);return}let v=ff(g,JSON.parse(f),c,y.relative==="path");a==null&&l!=="/"&&(v.pathname=v.pathname==="/"?l:Bn([l,v.pathname])),(y.replace?r.replace:r.push)(v,y.state,y)},[l,r,f,c,a])}N.createContext(null);function rr(a,{relative:l}={}){let{matches:r}=N.useContext(gn),{pathname:s}=pa(),c=JSON.stringify(cf(r));return N.useMemo(()=>ff(a,JSON.parse(c),s,l==="path"),[a,c,s,l])}function Jx(a,l){return op(a,l)}function op(a,l,r,s){var X;Ge(Hl(),"useRoutes() may be used only in the context of a <Router> component.");let{navigator:c}=N.useContext(rn),{matches:f}=N.useContext(gn),d=f[f.length-1],m=d?d.params:{},g=d?d.pathname:"/",y=d?d.pathnameBase:"/",v=d&&d.route;{let V=v&&v.path||"";cp(g,!v||V.endsWith("*")||V.endsWith("*?"),`You rendered descendant <Routes> (or called \`useRoutes()\`) at "${g}" (under <Route path="${V}">) but the parent route path has no trailing "*". This means if you navigate deeper, the parent won't match anymore and therefore the child routes will never render.

Please change the parent <Route path="${V}"> to <Route path="${V==="/"?"*":`${V}/*`}">.`)}let A=pa(),_;if(l){let V=typeof l=="string"?kl(l):l;Ge(y==="/"||((X=V.pathname)==null?void 0:X.startsWith(y)),`When overriding the location using \`<Routes location>\` or \`useRoutes(routes, location)\`, the location pathname must begin with the portion of the URL pathname that was matched by all parent routes. The current pathname base is "${y}" but pathname "${V.pathname}" was given in the \`location\` prop.`),_=V}else _=A;let Q=_.pathname||"/",R=Q;if(y!=="/"){let V=y.replace(/^\//,"").split("/");R="/"+Q.replace(/^\//,"").split("/").slice(V.length).join("/")}let B=np(a,{pathname:R});an(v||B!=null,`No routes matched location "${_.pathname}${_.search}${_.hash}" `),an(B==null||B[B.length-1].route.element!==void 0||B[B.length-1].route.Component!==void 0||B[B.length-1].route.lazy!==void 0,`Matched leaf route at location "${_.pathname}${_.search}${_.hash}" does not have an element or Component. This means it will render an <Outlet /> with a null value by default resulting in an "empty" page.`);let L=eE(B&&B.map(V=>Object.assign({},V,{params:Object.assign({},m,V.params),pathname:Bn([y,c.encodeLocation?c.encodeLocation(V.pathname).pathname:V.pathname]),pathnameBase:V.pathnameBase==="/"?y:Bn([y,c.encodeLocation?c.encodeLocation(V.pathnameBase).pathname:V.pathnameBase])})),f,r,s);return l&&L?N.createElement(lr.Provider,{value:{location:{pathname:"/",search:"",hash:"",state:null,key:"default",..._},navigationType:"POP"}},L):L}function $x(){let a=iE(),l=Xx(a)?`${a.status} ${a.statusText}`:a instanceof Error?a.message:JSON.stringify(a),r=a instanceof Error?a.stack:null,s="rgba(200,200,200, 0.5)",c={padding:"0.5rem",backgroundColor:s},f={padding:"2px 4px",backgroundColor:s},d=null;return console.error("Error handled by React Router default ErrorBoundary:",a),d=N.createElement(N.Fragment,null,N.createElement("p",null,"💿 Hey developer 👋"),N.createElement("p",null,"You can provide a way better UX than this when your app throws errors by providing your own ",N.createElement("code",{style:f},"ErrorBoundary")," or"," ",N.createElement("code",{style:f},"errorElement")," prop on your route.")),N.createElement(N.Fragment,null,N.createElement("h2",null,"Unexpected Application Error!"),N.createElement("h3",{style:{fontStyle:"italic"}},l),r?N.createElement("pre",{style:c},r):null,d)}var Wx=N.createElement($x,null),Px=class extends N.Component{constructor(a){super(a),this.state={location:a.location,revalidation:a.revalidation,error:a.error}}static getDerivedStateFromError(a){return{error:a}}static getDerivedStateFromProps(a,l){return l.location!==a.location||l.revalidation!=="idle"&&a.revalidation==="idle"?{error:a.error,location:a.location,revalidation:a.revalidation}:{error:a.error!==void 0?a.error:l.error,location:l.location,revalidation:a.revalidation||l.revalidation}}componentDidCatch(a,l){console.error("React Router caught the following error during render",a,l)}render(){return this.state.error!==void 0?N.createElement(gn.Provider,{value:this.props.routeContext},N.createElement(df.Provider,{value:this.state.error,children:this.props.component})):this.props.children}};function Ix({routeContext:a,match:l,children:r}){let s=N.useContext(ql);return s&&s.static&&s.staticContext&&(l.route.errorElement||l.route.ErrorBoundary)&&(s.staticContext._deepestRenderedBoundaryId=l.route.id),N.createElement(gn.Provider,{value:a},r)}function eE(a,l=[],r=null,s=null){if(a==null){if(!r)return null;if(r.errors)a=r.matches;else if(l.length===0&&!r.initialized&&r.matches.length>0)a=r.matches;else return null}let c=a,f=r==null?void 0:r.errors;if(f!=null){let g=c.findIndex(y=>y.route.id&&(f==null?void 0:f[y.route.id])!==void 0);Ge(g>=0,`Could not find a matching route for errors on route IDs: ${Object.keys(f).join(",")}`),c=c.slice(0,Math.min(c.length,g+1))}let d=!1,m=-1;if(r)for(let g=0;g<c.length;g++){let y=c[g];if((y.route.HydrateFallback||y.route.hydrateFallbackElement)&&(m=g),y.route.id){let{loaderData:v,errors:A}=r,_=y.route.loader&&!v.hasOwnProperty(y.route.id)&&(!A||A[y.route.id]===void 0);if(y.route.lazy||_){d=!0,m>=0?c=c.slice(0,m+1):c=[c[0]];break}}}return c.reduceRight((g,y,v)=>{let A,_=!1,Q=null,R=null;r&&(A=f&&y.route.id?f[y.route.id]:void 0,Q=y.route.errorElement||Wx,d&&(m<0&&v===0?(cp("route-fallback",!1,"No `HydrateFallback` element provided to render during initial hydration"),_=!0,R=null):m===v&&(_=!0,R=y.route.hydrateFallbackElement||null)));let B=l.concat(c.slice(0,v+1)),L=()=>{let X;return A?X=Q:_?X=R:y.route.Component?X=N.createElement(y.route.Component,null):y.route.element?X=y.route.element:X=g,N.createElement(Ix,{match:y,routeContext:{outlet:g,matches:B,isDataRoute:r!=null},children:X})};return r&&(y.route.ErrorBoundary||y.route.errorElement||v===0)?N.createElement(Px,{location:r.location,revalidation:r.revalidation,component:Q,error:A,children:L(),routeContext:{outlet:null,matches:B,isDataRoute:!0}}):L()},null)}function hf(a){return`${a} must be used within a data router.  See https://reactrouter.com/en/main/routers/picking-a-router.`}function tE(a){let l=N.useContext(ql);return Ge(l,hf(a)),l}function nE(a){let l=N.useContext(nu);return Ge(l,hf(a)),l}function aE(a){let l=N.useContext(gn);return Ge(l,hf(a)),l}function mf(a){let l=aE(a),r=l.matches[l.matches.length-1];return Ge(r.route.id,`${a} can only be used on routes that contain a unique "id"`),r.route.id}function lE(){return mf("useRouteId")}function iE(){var s;let a=N.useContext(df),l=nE("useRouteError"),r=mf("useRouteError");return a!==void 0?a:(s=l.errors)==null?void 0:s[r]}function rE(){let{router:a}=tE("useNavigate"),l=mf("useNavigate"),r=N.useRef(!1);return up(()=>{r.current=!0}),N.useCallback(async(c,f={})=>{an(r.current,sp),r.current&&(typeof c=="number"?a.navigate(c):await a.navigate(c,{fromRouteId:l,...f}))},[a,l])}var Ky={};function cp(a,l,r){!l&&!Ky[a]&&(Ky[a]=!0,an(!1,r))}N.memo(sE);function sE({routes:a,future:l,state:r}){return op(a,void 0,r,l)}function Fy({to:a,replace:l,state:r,relative:s}){Ge(Hl(),"<Navigate> may be used only in the context of a <Router> component.");let{static:c}=N.useContext(rn);an(!c,"<Navigate> must not be used on the initial render in a <StaticRouter>. This is a no-op, but you should modify your code so the <Navigate> is only ever rendered in response to some user interaction or state change.");let{matches:f}=N.useContext(gn),{pathname:d}=pa(),m=ir(),g=ff(a,cf(f),d,s==="path"),y=JSON.stringify(g);return N.useEffect(()=>{m(JSON.parse(y),{replace:l,state:r,relative:s})},[m,y,s,l,r]),null}function Ki(a){Ge(!1,"A <Route> is only ever to be used as the child of <Routes> element, never rendered directly. Please wrap your <Route> in a <Routes>.")}function uE({basename:a="/",children:l=null,location:r,navigationType:s="POP",navigator:c,static:f=!1}){Ge(!Hl(),"You cannot render a <Router> inside another <Router>. You should never have more than one in your app.");let d=a.replace(/^\/*/,"/"),m=N.useMemo(()=>({basename:d,navigator:c,static:f,future:{}}),[d,c,f]);typeof r=="string"&&(r=kl(r));let{pathname:g="/",search:y="",hash:v="",state:A=null,key:_="default"}=r,Q=N.useMemo(()=>{let R=qn(g,d);return R==null?null:{location:{pathname:R,search:y,hash:v,state:A,key:_},navigationType:s}},[d,g,y,v,A,_,s]);return an(Q!=null,`<Router basename="${d}"> is not able to match the URL "${g}${y}${v}" because it does not start with the basename, so the <Router> won't render anything.`),Q==null?null:N.createElement(rn.Provider,{value:m},N.createElement(lr.Provider,{children:l,value:Q}))}function oE({children:a,location:l}){return Jx(Wc(a),l)}function Wc(a,l=[]){let r=[];return N.Children.forEach(a,(s,c)=>{if(!N.isValidElement(s))return;let f=[...l,c];if(s.type===N.Fragment){r.push.apply(r,Wc(s.props.children,f));return}Ge(s.type===Ki,`[${typeof s.type=="string"?s.type:s.type.name}] is not a <Route> component. All component children of <Routes> must be a <Route> or <React.Fragment>`),Ge(!s.props.index||!s.props.children,"An index route cannot have child routes.");let d={id:s.props.id||f.join("-"),caseSensitive:s.props.caseSensitive,element:s.props.element,Component:s.props.Component,index:s.props.index,path:s.props.path,loader:s.props.loader,action:s.props.action,hydrateFallbackElement:s.props.hydrateFallbackElement,HydrateFallback:s.props.HydrateFallback,errorElement:s.props.errorElement,ErrorBoundary:s.props.ErrorBoundary,hasErrorBoundary:s.props.hasErrorBoundary===!0||s.props.ErrorBoundary!=null||s.props.errorElement!=null,shouldRevalidate:s.props.shouldRevalidate,handle:s.props.handle,lazy:s.props.lazy};s.props.children&&(d.children=Wc(s.props.children,f)),r.push(d)}),r}var zs="get",js="application/x-www-form-urlencoded";function au(a){return a!=null&&typeof a.tagName=="string"}function cE(a){return au(a)&&a.tagName.toLowerCase()==="button"}function fE(a){return au(a)&&a.tagName.toLowerCase()==="form"}function dE(a){return au(a)&&a.tagName.toLowerCase()==="input"}function hE(a){return!!(a.metaKey||a.altKey||a.ctrlKey||a.shiftKey)}function mE(a,l){return a.button===0&&(!l||l==="_self")&&!hE(a)}var ws=null;function yE(){if(ws===null)try{new FormData(document.createElement("form"),0),ws=!1}catch{ws=!0}return ws}var pE=new Set(["application/x-www-form-urlencoded","multipart/form-data","text/plain"]);function Uc(a){return a!=null&&!pE.has(a)?(an(!1,`"${a}" is not a valid \`encType\` for \`<Form>\`/\`<fetcher.Form>\` and will default to "${js}"`),null):a}function gE(a,l){let r,s,c,f,d;if(fE(a)){let m=a.getAttribute("action");s=m?qn(m,l):null,r=a.getAttribute("method")||zs,c=Uc(a.getAttribute("enctype"))||js,f=new FormData(a)}else if(cE(a)||dE(a)&&(a.type==="submit"||a.type==="image")){let m=a.form;if(m==null)throw new Error('Cannot submit a <button> or <input type="submit"> without a <form>');let g=a.getAttribute("formaction")||m.getAttribute("action");if(s=g?qn(g,l):null,r=a.getAttribute("formmethod")||m.getAttribute("method")||zs,c=Uc(a.getAttribute("formenctype"))||Uc(m.getAttribute("enctype"))||js,f=new FormData(m,a),!yE()){let{name:y,type:v,value:A}=a;if(v==="image"){let _=y?`${y}.`:"";f.append(`${_}x`,"0"),f.append(`${_}y`,"0")}else y&&f.append(y,A)}}else{if(au(a))throw new Error('Cannot submit element that is not <form>, <button>, or <input type="submit|image">');r=zs,s=null,c=js,d=a}return f&&c==="text/plain"&&(d=f,f=void 0),{action:s,method:r.toLowerCase(),encType:c,formData:f,body:d}}function yf(a,l){if(a===!1||a===null||typeof a>"u")throw new Error(l)}async function vE(a,l){if(a.id in l)return l[a.id];try{let r=await import(a.module);return l[a.id]=r,r}catch(r){return console.error(`Error loading route module \`${a.module}\`, reloading page...`),console.error(r),window.__reactRouterContext&&window.__reactRouterContext.isSpaMode,window.location.reload(),new Promise(()=>{})}}function bE(a){return a==null?!1:a.href==null?a.rel==="preload"&&typeof a.imageSrcSet=="string"&&typeof a.imageSizes=="string":typeof a.rel=="string"&&typeof a.href=="string"}async function SE(a,l,r){let s=await Promise.all(a.map(async c=>{let f=l.routes[c.route.id];if(f){let d=await vE(f,r);return d.links?d.links():[]}return[]}));return wE(s.flat(1).filter(bE).filter(c=>c.rel==="stylesheet"||c.rel==="preload").map(c=>c.rel==="stylesheet"?{...c,rel:"prefetch",as:"style"}:{...c,rel:"prefetch"}))}function Jy(a,l,r,s,c,f){let d=(g,y)=>r[y]?g.route.id!==r[y].route.id:!0,m=(g,y)=>{var v;return r[y].pathname!==g.pathname||((v=r[y].route.path)==null?void 0:v.endsWith("*"))&&r[y].params["*"]!==g.params["*"]};return f==="assets"?l.filter((g,y)=>d(g,y)||m(g,y)):f==="data"?l.filter((g,y)=>{var A;let v=s.routes[g.route.id];if(!v||!v.hasLoader)return!1;if(d(g,y)||m(g,y))return!0;if(g.route.shouldRevalidate){let _=g.route.shouldRevalidate({currentUrl:new URL(c.pathname+c.search+c.hash,window.origin),currentParams:((A=r[0])==null?void 0:A.params)||{},nextUrl:new URL(a,window.origin),nextParams:g.params,defaultShouldRevalidate:!0});if(typeof _=="boolean")return _}return!0}):[]}function xE(a,l,{includeHydrateFallback:r}={}){return EE(a.map(s=>{let c=l.routes[s.route.id];if(!c)return[];let f=[c.module];return c.clientActionModule&&(f=f.concat(c.clientActionModule)),c.clientLoaderModule&&(f=f.concat(c.clientLoaderModule)),r&&c.hydrateFallbackModule&&(f=f.concat(c.hydrateFallbackModule)),c.imports&&(f=f.concat(c.imports)),f}).flat(1))}function EE(a){return[...new Set(a)]}function _E(a){let l={},r=Object.keys(a).sort();for(let s of r)l[s]=a[s];return l}function wE(a,l){let r=new Set;return new Set(l),a.reduce((s,c)=>{let f=JSON.stringify(_E(c));return r.has(f)||(r.add(f),s.push({key:f,link:c})),s},[])}Object.getOwnPropertyNames(Object.prototype).sort().join("\0");var AE=new Set([100,101,204,205]);function TE(a,l){let r=typeof a=="string"?new URL(a,typeof window>"u"?"server://singlefetch/":window.location.origin):a;return r.pathname==="/"?r.pathname="_root.data":l&&qn(r.pathname,l)==="/"?r.pathname=`${l.replace(/\/$/,"")}/_root.data`:r.pathname=`${r.pathname.replace(/\/$/,"")}.data`,r}function fp(){let a=N.useContext(ql);return yf(a,"You must render this element inside a <DataRouterContext.Provider> element"),a}function RE(){let a=N.useContext(nu);return yf(a,"You must render this element inside a <DataRouterStateContext.Provider> element"),a}var pf=N.createContext(void 0);pf.displayName="FrameworkContext";function dp(){let a=N.useContext(pf);return yf(a,"You must render this element inside a <HydratedRouter> element"),a}function OE(a,l){let r=N.useContext(pf),[s,c]=N.useState(!1),[f,d]=N.useState(!1),{onFocus:m,onBlur:g,onMouseEnter:y,onMouseLeave:v,onTouchStart:A}=l,_=N.useRef(null);N.useEffect(()=>{if(a==="render"&&d(!0),a==="viewport"){let B=X=>{X.forEach(V=>{d(V.isIntersecting)})},L=new IntersectionObserver(B,{threshold:.5});return _.current&&L.observe(_.current),()=>{L.disconnect()}}},[a]),N.useEffect(()=>{if(s){let B=setTimeout(()=>{d(!0)},100);return()=>{clearTimeout(B)}}},[s]);let Q=()=>{c(!0)},R=()=>{c(!1),d(!1)};return r?a!=="intent"?[f,_,{}]:[f,_,{onFocus:Xi(m,Q),onBlur:Xi(g,R),onMouseEnter:Xi(y,Q),onMouseLeave:Xi(v,R),onTouchStart:Xi(A,Q)}]:[!1,_,{}]}function Xi(a,l){return r=>{a&&a(r),r.defaultPrevented||l(r)}}function NE({page:a,...l}){let{router:r}=fp(),s=N.useMemo(()=>np(r.routes,a,r.basename),[r.routes,a,r.basename]);return s?N.createElement(DE,{page:a,matches:s,...l}):null}function CE(a){let{manifest:l,routeModules:r}=dp(),[s,c]=N.useState([]);return N.useEffect(()=>{let f=!1;return SE(a,l,r).then(d=>{f||c(d)}),()=>{f=!0}},[a,l,r]),s}function DE({page:a,matches:l,...r}){let s=pa(),{manifest:c,routeModules:f}=dp(),{basename:d}=fp(),{loaderData:m,matches:g}=RE(),y=N.useMemo(()=>Jy(a,l,g,c,s,"data"),[a,l,g,c,s]),v=N.useMemo(()=>Jy(a,l,g,c,s,"assets"),[a,l,g,c,s]),A=N.useMemo(()=>{if(a===s.pathname+s.search+s.hash)return[];let R=new Set,B=!1;if(l.forEach(X=>{var $;let V=c.routes[X.route.id];!V||!V.hasLoader||(!y.some(ie=>ie.route.id===X.route.id)&&X.route.id in m&&(($=f[X.route.id])!=null&&$.shouldRevalidate)||V.hasClientLoader?B=!0:R.add(X.route.id))}),R.size===0)return[];let L=TE(a,d);return B&&R.size>0&&L.searchParams.set("_routes",l.filter(X=>R.has(X.route.id)).map(X=>X.route.id).join(",")),[L.pathname+L.search]},[d,m,s,c,y,l,a,f]),_=N.useMemo(()=>xE(v,c),[v,c]),Q=CE(v);return N.createElement(N.Fragment,null,A.map(R=>N.createElement("link",{key:R,rel:"prefetch",as:"fetch",href:R,...r})),_.map(R=>N.createElement("link",{key:R,rel:"modulepreload",href:R,...r})),Q.map(({key:R,link:B})=>N.createElement("link",{key:R,...B})))}function ME(...a){return l=>{a.forEach(r=>{typeof r=="function"?r(l):r!=null&&(r.current=l)})}}var hp=typeof window<"u"&&typeof window.document<"u"&&typeof window.document.createElement<"u";try{hp&&(window.__reactRouterVersion="7.6.3")}catch{}function UE({basename:a,children:l,window:r}){let s=N.useRef();s.current==null&&(s.current=Sx({window:r,v5Compat:!0}));let c=s.current,[f,d]=N.useState({action:c.action,location:c.location}),m=N.useCallback(g=>{N.startTransition(()=>d(g))},[d]);return N.useLayoutEffect(()=>c.listen(m),[c,m]),N.createElement(uE,{basename:a,children:l,location:f.location,navigationType:f.action,navigator:c})}var mp=/^(?:[a-z][a-z0-9+.-]*:|\/\/)/i,lu=N.forwardRef(function({onClick:l,discover:r="render",prefetch:s="none",relative:c,reloadDocument:f,replace:d,state:m,target:g,to:y,preventScrollReset:v,viewTransition:A,..._},Q){let{basename:R}=N.useContext(rn),B=typeof y=="string"&&mp.test(y),L,X=!1;if(typeof y=="string"&&B&&(L=y,hp))try{let pe=new URL(window.location.href),Qe=y.startsWith("//")?new URL(pe.protocol+y):new URL(y),Ze=qn(Qe.pathname,R);Qe.origin===pe.origin&&Ze!=null?y=Ze+Qe.search+Qe.hash:X=!0}catch{an(!1,`<Link to="${y}"> contains an invalid URL which will probably break when clicked - please update to a valid URL path.`)}let V=Kx(y,{relative:c}),[$,ie,K]=OE(s,_),Ae=LE(y,{replace:d,state:m,target:g,preventScrollReset:v,relative:c,viewTransition:A});function ye(pe){l&&l(pe),pe.defaultPrevented||Ae(pe)}let De=N.createElement("a",{..._,...K,href:L||V,onClick:X||f?l:ye,ref:ME(Q,ie),target:g,"data-discover":!B&&r==="render"?"true":void 0});return $&&!B?N.createElement(N.Fragment,null,De,N.createElement(NE,{page:V})):De});lu.displayName="Link";var zE=N.forwardRef(function({"aria-current":l="page",caseSensitive:r=!1,className:s="",end:c=!1,style:f,to:d,viewTransition:m,children:g,...y},v){let A=rr(d,{relative:y.relative}),_=pa(),Q=N.useContext(nu),{navigator:R,basename:B}=N.useContext(rn),L=Q!=null&&YE(A)&&m===!0,X=R.encodeLocation?R.encodeLocation(A).pathname:A.pathname,V=_.pathname,$=Q&&Q.navigation&&Q.navigation.location?Q.navigation.location.pathname:null;r||(V=V.toLowerCase(),$=$?$.toLowerCase():null,X=X.toLowerCase()),$&&B&&($=qn($,B)||$);const ie=X!=="/"&&X.endsWith("/")?X.length-1:X.length;let K=V===X||!c&&V.startsWith(X)&&V.charAt(ie)==="/",Ae=$!=null&&($===X||!c&&$.startsWith(X)&&$.charAt(X.length)==="/"),ye={isActive:K,isPending:Ae,isTransitioning:L},De=K?l:void 0,pe;typeof s=="function"?pe=s(ye):pe=[s,K?"active":null,Ae?"pending":null,L?"transitioning":null].filter(Boolean).join(" ");let Qe=typeof f=="function"?f(ye):f;return N.createElement(lu,{...y,"aria-current":De,className:pe,ref:v,style:Qe,to:d,viewTransition:m},typeof g=="function"?g(ye):g)});zE.displayName="NavLink";var jE=N.forwardRef(({discover:a="render",fetcherKey:l,navigate:r,reloadDocument:s,replace:c,state:f,method:d=zs,action:m,onSubmit:g,relative:y,preventScrollReset:v,viewTransition:A,..._},Q)=>{let R=HE(),B=VE(m,{relative:y}),L=d.toLowerCase()==="get"?"get":"post",X=typeof m=="string"&&mp.test(m),V=$=>{if(g&&g($),$.defaultPrevented)return;$.preventDefault();let ie=$.nativeEvent.submitter,K=(ie==null?void 0:ie.getAttribute("formmethod"))||d;R(ie||$.currentTarget,{fetcherKey:l,method:K,navigate:r,replace:c,state:f,relative:y,preventScrollReset:v,viewTransition:A})};return N.createElement("form",{ref:Q,method:L,action:B,onSubmit:s?g:V,..._,"data-discover":!X&&a==="render"?"true":void 0})});jE.displayName="Form";function BE(a){return`${a} must be used within a data router.  See https://reactrouter.com/en/main/routers/picking-a-router.`}function yp(a){let l=N.useContext(ql);return Ge(l,BE(a)),l}function LE(a,{target:l,replace:r,state:s,preventScrollReset:c,relative:f,viewTransition:d}={}){let m=ir(),g=pa(),y=rr(a,{relative:f});return N.useCallback(v=>{if(mE(v,l)){v.preventDefault();let A=r!==void 0?r:Ii(g)===Ii(y);m(a,{replace:A,state:s,preventScrollReset:c,relative:f,viewTransition:d})}},[g,m,y,r,s,l,a,c,f,d])}var kE=0,qE=()=>`__${String(++kE)}__`;function HE(){let{router:a}=yp("useSubmit"),{basename:l}=N.useContext(rn),r=lE();return N.useCallback(async(s,c={})=>{let{action:f,method:d,encType:m,formData:g,body:y}=gE(s,l);if(c.navigate===!1){let v=c.fetcherKey||qE();await a.fetch(v,r,c.action||f,{preventScrollReset:c.preventScrollReset,formData:g,body:y,formMethod:c.method||d,formEncType:c.encType||m,flushSync:c.flushSync})}else await a.navigate(c.action||f,{preventScrollReset:c.preventScrollReset,formData:g,body:y,formMethod:c.method||d,formEncType:c.encType||m,replace:c.replace,state:c.state,fromRouteId:r,flushSync:c.flushSync,viewTransition:c.viewTransition})},[a,l,r])}function VE(a,{relative:l}={}){let{basename:r}=N.useContext(rn),s=N.useContext(gn);Ge(s,"useFormAction must be used inside a RouteContext");let[c]=s.matches.slice(-1),f={...rr(a||".",{relative:l})},d=pa();if(a==null){f.search=d.search;let m=new URLSearchParams(f.search),g=m.getAll("index");if(g.some(v=>v==="")){m.delete("index"),g.filter(A=>A).forEach(A=>m.append("index",A));let v=m.toString();f.search=v?`?${v}`:""}}return(!a||a===".")&&c.route.index&&(f.search=f.search?f.search.replace(/^\?/,"?index&"):"?index"),r!=="/"&&(f.pathname=f.pathname==="/"?r:Bn([r,f.pathname])),Ii(f)}function YE(a,l={}){let r=N.useContext(rp);Ge(r!=null,"`useViewTransitionState` must be used within `react-router-dom`'s `RouterProvider`.  Did you accidentally import `RouterProvider` from `react-router`?");let{basename:s}=yp("useViewTransitionState"),c=rr(a,{relative:l.relative});if(!r.isTransitioning)return!1;let f=qn(r.currentLocation.pathname,s)||r.currentLocation.pathname,d=qn(r.nextLocation.pathname,s)||r.nextLocation.pathname;return Hs(c.pathname,d)!=null||Hs(c.pathname,f)!=null}[...AE];const XE=()=>{const[a,l]=N.useState([]),[r,s]=N.useState(!1),c=ir();return N.useEffect(()=>{(async()=>{s(!0);try{const d=Pi.get("token");console.log("token",d);const m=await Ya.get("/api/getUserInfo",{withCredentials:!0,headers:{Authorization:`Bearer ${d}`}});console.log(m),l(m.data.allUsers),s(!1)}catch(d){console.log("Error in useGetAllUsers: "+d)}})()},[c]),[a,r]},GE=({onMobileSelect:a})=>{const[l,r]=XE();return r?S.jsx("p",{className:"text-center py-4",children:"Loading..."}):S.jsx(S.Fragment,{children:S.jsx("div",{className:"flex-1 overflow-y-auto scroll-container h-full",children:l.length===0?S.jsxs("div",{className:"flex flex-col items-center justify-center py-8 text-center h-full",children:[S.jsx("div",{className:"w-16 h-16 bg-gray-200 dark:bg-gray-600 rounded-full flex items-center justify-center mb-4",children:S.jsx("svg",{className:"w-8 h-8 text-gray-400",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:S.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z"})})}),S.jsx("p",{className:"text-gray-500 dark:text-gray-400 text-sm",children:"No users found"})]}):S.jsx("div",{className:"divide-y divide-gray-200 dark:divide-gray-700",children:l.map(s=>S.jsx(vx,{user:s,onMobileSelect:a},s._id))})})})};var pp={color:void 0,size:void 0,className:void 0,style:void 0,attr:void 0},$y=at.createContext&&at.createContext(pp),QE=["attr","size","title"];function ZE(a,l){if(a==null)return{};var r=KE(a,l),s,c;if(Object.getOwnPropertySymbols){var f=Object.getOwnPropertySymbols(a);for(c=0;c<f.length;c++)s=f[c],!(l.indexOf(s)>=0)&&Object.prototype.propertyIsEnumerable.call(a,s)&&(r[s]=a[s])}return r}function KE(a,l){if(a==null)return{};var r={};for(var s in a)if(Object.prototype.hasOwnProperty.call(a,s)){if(l.indexOf(s)>=0)continue;r[s]=a[s]}return r}function Vs(){return Vs=Object.assign?Object.assign.bind():function(a){for(var l=1;l<arguments.length;l++){var r=arguments[l];for(var s in r)Object.prototype.hasOwnProperty.call(r,s)&&(a[s]=r[s])}return a},Vs.apply(this,arguments)}function Wy(a,l){var r=Object.keys(a);if(Object.getOwnPropertySymbols){var s=Object.getOwnPropertySymbols(a);l&&(s=s.filter(function(c){return Object.getOwnPropertyDescriptor(a,c).enumerable})),r.push.apply(r,s)}return r}function Ys(a){for(var l=1;l<arguments.length;l++){var r=arguments[l]!=null?arguments[l]:{};l%2?Wy(Object(r),!0).forEach(function(s){FE(a,s,r[s])}):Object.getOwnPropertyDescriptors?Object.defineProperties(a,Object.getOwnPropertyDescriptors(r)):Wy(Object(r)).forEach(function(s){Object.defineProperty(a,s,Object.getOwnPropertyDescriptor(r,s))})}return a}function FE(a,l,r){return l=JE(l),l in a?Object.defineProperty(a,l,{value:r,enumerable:!0,configurable:!0,writable:!0}):a[l]=r,a}function JE(a){var l=$E(a,"string");return typeof l=="symbol"?l:l+""}function $E(a,l){if(typeof a!="object"||!a)return a;var r=a[Symbol.toPrimitive];if(r!==void 0){var s=r.call(a,l);if(typeof s!="object")return s;throw new TypeError("@@toPrimitive must return a primitive value.")}return(l==="string"?String:Number)(a)}function gp(a){return a&&a.map((l,r)=>at.createElement(l.tag,Ys({key:r},l.attr),gp(l.child)))}function vp(a){return l=>at.createElement(WE,Vs({attr:Ys({},a.attr)},l),gp(a.child))}function WE(a){var l=r=>{var{attr:s,size:c,title:f}=a,d=ZE(a,QE),m=c||r.size||"1em",g;return r.className&&(g=r.className),a.className&&(g=(g?g+" ":"")+a.className),at.createElement("svg",Vs({stroke:"currentColor",fill:"currentColor",strokeWidth:"0"},r.attr,s,d,{className:g,style:Ys(Ys({color:a.color||r.color},r.style),a.style),height:m,width:m,xmlns:"http://www.w3.org/2000/svg"}),f&&at.createElement("title",null,f),a.children)};return $y!==void 0?at.createElement($y.Consumer,null,r=>l(r)):l(pp)}function bp(a){return vp({attr:{viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round"},child:[{tag:"path",attr:{d:"M9 21H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h4"},child:[]},{tag:"polyline",attr:{points:"16 17 21 12 16 7"},child:[]},{tag:"line",attr:{x1:"21",x2:"9",y1:"12",y2:"12"},child:[]}]})(a)}const PE=()=>{const[a,l]=N.useState(!1),[r,s]=N.useState(!1),{selectedConversation:c}=ma(),f=async()=>{s(!0);try{const d=await Ya.post("/api/user/logout");console.log(d),localStorage.removeItem("message"),Pi.remove("token"),kn.success("Logout Successfully"),window.location.reload()}catch(d){console.error("Logout failed:",d),kn.error("Logout failed. Please try again.")}finally{s(!1)}};return S.jsxs(S.Fragment,{children:[S.jsxs("div",{className:"lg:hidden bg-gradient-to-r from-green-600 to-green-700 text-white shadow-lg fixed top-0 left-0 right-0 z-50",children:[S.jsxs("div",{className:"flex justify-between items-center px-4 py-3",children:[S.jsxs("div",{className:"flex items-center space-x-3",children:[S.jsx("div",{className:"w-8 h-8 bg-white bg-opacity-20 rounded-full flex items-center justify-center",children:S.jsx("svg",{className:"w-5 h-5 text-white",fill:"currentColor",viewBox:"0 0 20 20",children:S.jsx("path",{fillRule:"evenodd",d:"M18 10c0 3.866-3.582 7-8 7a8.841 8.841 0 01-4.083-.98L2 17l1.338-3.123C2.493 12.767 2 11.434 2 10c0-3.866 3.582-7 8-7s8 3.134 8 7zM7 9H5v2h2V9zm8 0h-2v2h2V9zM9 9h2v2H9V9z",clipRule:"evenodd"})})}),S.jsx("h1",{className:"font-semibold text-lg tracking-wide",children:"Chats for Fun"})]}),S.jsxs("div",{className:"flex items-center space-x-2",children:[S.jsx("button",{className:"p-2 hover:bg-white hover:bg-opacity-10 rounded-full transition-colors duration-200",title:"Search",children:S.jsx("svg",{className:"w-5 h-5",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:S.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"})})}),S.jsx("button",{onClick:f,disabled:r,className:"p-2 hover:bg-white hover:bg-opacity-10 rounded-full transition-colors duration-200 disabled:opacity-50",title:"Logout",children:r?S.jsxs("svg",{className:"w-5 h-5 animate-spin",fill:"none",viewBox:"0 0 24 24",children:[S.jsx("circle",{className:"opacity-25",cx:"12",cy:"12",r:"10",stroke:"currentColor",strokeWidth:"4"}),S.jsx("path",{className:"opacity-75",fill:"currentColor",d:"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"})]}):S.jsx(bp,{className:"w-5 h-5"})}),S.jsx("button",{onClick:()=>l(!a),className:"p-2 hover:bg-white hover:bg-opacity-10 rounded-full transition-all duration-200 ml-1",title:a?"Close menu":"Open menu",children:S.jsx("div",{className:"relative w-6 h-6 flex items-center justify-center",children:a?S.jsx("svg",{className:"w-6 h-6 transform rotate-180 transition-transform duration-200",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:S.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M6 18L18 6M6 6l12 12"})}):S.jsx("svg",{className:"w-6 h-6 transition-transform duration-200",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:S.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M4 6h16M4 12h16M4 18h16"})})})})]})]}),S.jsx("div",{className:"bg-green-700 bg-opacity-50 px-4 py-1",children:S.jsx("p",{className:"text-xs text-green-100 text-center",children:c?`Chatting with ${c.username}`:"Select a chat to start messaging"})})]}),S.jsxs("div",{className:`
        bg-white dark:bg-gray-800 border-r border-gray-200 dark:border-gray-700
        lg:w-full lg:flex lg:flex-col lg:max-w-md xl:max-w-lg
        ${c&&!a?"hidden lg:flex":"flex"}
        ${a?"fixed inset-0 z-40 pt-20":"lg:relative lg:pt-0"}
        ${a?"w-full h-full":"w-full lg:w-full"}
        ${a?"animate-slide-in-left":""}
      `,children:[S.jsxs("div",{className:"hidden lg:flex items-center justify-between p-4 bg-gray-50 dark:bg-gray-700 border-b border-gray-200 dark:border-gray-600",children:[S.jsx("h1",{className:"font-semibold text-xl text-gray-800 dark:text-white",children:"Chats"}),S.jsx("div",{className:"flex items-center gap-2",children:S.jsx("button",{className:"p-2 hover:bg-gray-200 dark:hover:bg-gray-600 rounded-full text-gray-600 dark:text-gray-300",children:S.jsx("svg",{className:"w-5 h-5",fill:"currentColor",viewBox:"0 0 20 20",children:S.jsx("path",{fillRule:"evenodd",d:"M10 3a1 1 0 011 1v5h5a1 1 0 110 2h-5v5a1 1 0 11-2 0v-5H4a1 1 0 110-2h5V4a1 1 0 011-1z",clipRule:"evenodd"})})})})]}),S.jsx("div",{className:"p-3 bg-gray-50 dark:bg-gray-700 border-b border-gray-200 dark:border-gray-600",children:S.jsx(xS,{onMobileSelect:()=>l(!1)})}),S.jsx("div",{className:"flex-1 overflow-hidden",children:S.jsx(GE,{onMobileSelect:()=>l(!1)})})]}),a&&S.jsx("div",{className:"lg:hidden fixed inset-0 bg-black bg-opacity-60 z-30 animate-fade-in",onClick:()=>l(!1),children:S.jsx("div",{className:"absolute inset-0 bg-gradient-to-r from-black to-transparent opacity-30"})})]})},IE=()=>{const{selectedConversation:a}=ma(),{onlineUsers:l}=of(),r=l.includes(a._id);return S.jsx(S.Fragment,{children:S.jsxs("div",{className:"flex items-center bg-white dark:bg-gray-800 border-b border-gray-200 dark:border-gray-700 px-4 py-3 h-16",children:[S.jsx("button",{className:"lg:hidden mr-3 p-2 hover:bg-gray-100 dark:hover:bg-gray-700 rounded-full",children:S.jsx("svg",{className:"w-5 h-5 text-gray-600 dark:text-gray-400",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:S.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M15 19l-7-7 7-7"})})}),S.jsxs("div",{className:"relative flex-shrink-0",children:[S.jsx("div",{className:"w-10 h-10 rounded-full overflow-hidden bg-gray-300 dark:bg-gray-600",children:S.jsx("img",{src:"https://img.daisyui.com/images/profile/demo/<EMAIL>",alt:a==null?void 0:a.username,className:"w-full h-full object-cover"})}),r&&S.jsx("div",{className:"absolute bottom-0 right-0 w-3 h-3 bg-green-500 border-2 border-white dark:border-gray-800 rounded-full"})]}),S.jsxs("div",{className:"flex-1 min-w-0 ml-3",children:[S.jsx("h1",{className:"font-medium text-gray-900 dark:text-white truncate",children:a==null?void 0:a.username}),S.jsx("p",{className:"text-sm text-gray-500 dark:text-gray-400",children:r?"Online":"Last seen recently"})]}),S.jsxs("div",{className:"flex items-center space-x-2",children:[S.jsx("button",{className:"p-2 hover:bg-gray-100 dark:hover:bg-gray-700 rounded-full",children:S.jsx("svg",{className:"w-5 h-5 text-gray-600 dark:text-gray-400",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:S.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"})})}),S.jsx("button",{className:"p-2 hover:bg-gray-100 dark:hover:bg-gray-700 rounded-full",children:S.jsx("svg",{className:"w-5 h-5 text-gray-600 dark:text-gray-400",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:S.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M12 5v.01M12 12v.01M12 19v.01M12 6a1 1 0 110-2 1 1 0 010 2zm0 7a1 1 0 110-2 1 1 0 010 2zm0 7a1 1 0 110-2 1 1 0 010 2z"})})})]})]})})},e2=({message:a})=>{const l=JSON.parse(localStorage.getItem("message"));console.log(l);const r=l.user.userId===a.senderId,s=r?"chat-end":"chat-start",c=r?"bg-zinc-700":"bg-blue-500";//! for date with message to send
const d=new Date(a.createdAt).toLocaleTimeString([],{hour:"2-digit",minute:"2-digit"});return S.jsx(S.Fragment,{children:S.jsx("div",{className:"p-3 ",children:S.jsxs("div",{className:`chat ${s}`,children:[S.jsx("div",{className:`chat-bubble ${c}`,children:S.jsx("p",{children:a.message})}),S.jsx("div",{className:"chat-footer",children:d})]})})})},t2=()=>S.jsx("div",{children:"Loading ....."}),n2=()=>{const{socket:a}=of(),{message:l,setMessage:r}=ma();N.useEffect(()=>(a.on("newMessage",s=>{r([...l,s])}),()=>{a.off("newMessage")}),[a,l,r])},a2=()=>{let{message:a,loading:l}=B0();n2();//! scroll to first message
const r=N.useRef();return N.useEffect(()=>{if(!r.current)return;requestAnimationFrame(()=>{r.current.scrollIntoView({behavior:"smooth",block:"end"})})},[a]),S.jsx(S.Fragment,{children:S.jsxs("div",{style:{maxHeight:"calc(91.5vh - 8vh)"},className:"scroll-container",children:[l?S.jsx(t2,{}):a.length>0&&a.map(s=>S.jsx("div",{ref:r,children:S.jsx(e2,{message:s})},s._id)),!l&&a.length===0&&S.jsx("div",{children:S.jsx("p",{className:"text-red-500  text-center mt-[25%]",children:"Say,Hi"})})]})})};function l2(a){return vp({attr:{viewBox:"0 0 512 512"},child:[{tag:"path",attr:{d:"m476.59 227.05-.16-.07L49.35 49.84A23.56 23.56 0 0 0 27.14 52 24.65 24.65 0 0 0 16 72.59v113.29a24 24 0 0 0 19.52 23.57l232.93 43.07a4 4 0 0 1 0 7.86L35.53 303.45A24 24 0 0 0 16 327v113.31A23.57 23.57 0 0 0 26.59 460a23.94 23.94 0 0 0 13.22 4 24.55 24.55 0 0 0 9.52-1.93L476.4 285.94l.19-.09a32 32 0 0 0 0-58.8z"},child:[]}]})(a)}const i2=()=>{const[a,l]=N.useState(!1),{message:r,setMessage:s,selectedConversation:c}=ma();return{loading:a,sendMessage:async d=>{if(l(!0),c&&c._id)try{const m=await Ya.post(`/api/message/send/${c._id}`,{message:d},{headers:{"Content-Type":"application/json"}});console.log(m),console.log(m.data),s([...r,m.data.newMessage]),l(!1)}catch(m){console.log("Error in sendMessage ",m)}}}},r2=()=>{const{loading:a,sendMessage:l}=i2(),[r,s]=N.useState(""),c=async f=>{f.preventDefault(),r.trim()!==""&&(await l(r),s(""))};return S.jsx(S.Fragment,{children:S.jsx("form",{onSubmit:c,children:S.jsxs("div",{className:"flex items-center gap-3 px-4 py-3 bg-white dark:bg-gray-800",children:[S.jsx("button",{type:"button",className:"p-2 hover:bg-gray-100 dark:hover:bg-gray-700 rounded-full text-gray-500 dark:text-gray-400",children:S.jsx("svg",{className:"w-6 h-6",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:S.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M14.828 14.828a4 4 0 01-5.656 0M9 10h1.01M15 10h1.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"})})}),S.jsx("div",{className:"flex-1 relative",children:S.jsx("input",{type:"text",className:"w-full px-4 py-2 bg-gray-100 dark:bg-gray-700 border border-gray-200 dark:border-gray-600 rounded-full outline-none focus:ring-2 focus:ring-green-500 focus:border-transparent text-gray-900 dark:text-white placeholder-gray-500 dark:placeholder-gray-400",placeholder:"Type a message",value:r,onChange:f=>s(f.target.value),disabled:a})}),r.trim()?S.jsx("button",{type:"submit",disabled:a,className:"p-2 bg-green-500 hover:bg-green-600 disabled:bg-gray-400 rounded-full text-white transition-colors",children:S.jsx(l2,{className:"w-5 h-5"})}):S.jsx("button",{type:"button",className:"p-2 hover:bg-gray-100 dark:hover:bg-gray-700 rounded-full text-gray-500 dark:text-gray-400",children:S.jsx("svg",{className:"w-6 h-6",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:S.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M19 11a7 7 0 01-7 7m0 0a7 7 0 01-7-7m7 7v4m0 0H8m4 0h4m-4-8a3 3 0 01-3-3V5a3 3 0 116 0v6a3 3 0 01-3 3z"})})})]})})})},s2=()=>{const{selectedConversation:a,setSelectedConversation:l}=ma();return N.useEffect(()=>l(null),[l]),S.jsx(S.Fragment,{children:S.jsx("div",{className:"flex-1 flex flex-col bg-gray-50 dark:bg-gray-900 lg:pt-0 pt-16 min-w-0",children:a?S.jsxs("div",{className:"flex flex-col h-full",children:[S.jsx(IE,{}),S.jsx("div",{className:"flex-1 overflow-y-auto scroll-container bg-gray-50 dark:bg-gray-900 px-4 py-2",children:S.jsx(a2,{})}),S.jsx("div",{className:"border-t border-gray-200 dark:border-gray-700 bg-white dark:bg-gray-800",children:S.jsx(r2,{})})]}):S.jsx(u2,{})})})},u2=()=>{const{authUser:a}=ar();return S.jsx(S.Fragment,{children:S.jsx("div",{className:"flex flex-col w-full h-full items-center justify-center px-4 text-center bg-gray-50 dark:bg-gray-900",children:S.jsxs("div",{className:"max-w-md",children:[S.jsx("div",{className:"mb-8",children:S.jsx("div",{className:"w-32 h-32 mx-auto mb-6 bg-green-100 dark:bg-green-900 rounded-full flex items-center justify-center",children:S.jsx("svg",{className:"w-16 h-16 text-green-600 dark:text-green-400",fill:"currentColor",viewBox:"0 0 20 20",children:S.jsx("path",{fillRule:"evenodd",d:"M18 10c0 3.866-3.582 7-8 7a8.841 8.841 0 01-4.083-.98L2 17l1.338-3.123C2.493 12.767 2 11.434 2 10c0-3.866 3.582-7 8-7s8 3.134 8 7zM7 9H5v2h2V9zm8 0h-2v2h2V9zM9 9h2v2H9V9z",clipRule:"evenodd"})})})}),S.jsx("h2",{className:"text-2xl lg:text-3xl font-light mb-4 text-gray-800 dark:text-gray-200",children:"Welcome to Chats for Fun"}),S.jsxs("p",{className:"text-base lg:text-lg mb-6 text-gray-600 dark:text-gray-400 leading-relaxed",children:["No conversation selected!",S.jsx("br",{}),"select to chat with User"]}),S.jsx("div",{className:"lg:hidden bg-blue-50 dark:bg-blue-900 p-4 rounded-lg",children:S.jsx("p",{className:"text-sm text-blue-800 dark:text-blue-200",children:"Tap the menu button (☰) at the top to see your chats"})})]})})})},o2=()=>{const[a,l]=N.useState(!1);ir();const r=async()=>{l(!0);try{const s=await Ya.post("/api/user/logout");console.log("Logout response:",s),localStorage.removeItem("message"),Pi.remove("token"),l(!1),kn.success("Logout Successfully"),window.location.reload()}catch(s){console.error("Logout failed:",s),kn.error("Logout failed. Please try again.")}finally{l(!1)}};return S.jsx("div",{className:"w-16 bg-gray-50 dark:bg-gray-700 border-r border-gray-200 dark:border-gray-600 flex flex-col justify-end",children:S.jsx("div",{className:"p-3",children:S.jsx("button",{disabled:a,onClick:r,className:"w-full flex justify-center p-2 hover:bg-gray-200 dark:hover:bg-gray-600 rounded-lg transition-colors group",title:"Logout",children:S.jsx(bp,{className:"text-xl text-gray-600 dark:text-gray-400 group-hover:text-red-500 transition-colors"})})})})};var sr=a=>a.type==="checkbox",qa=a=>a instanceof Date,Et=a=>a==null;const Sp=a=>typeof a=="object";var Pe=a=>!Et(a)&&!Array.isArray(a)&&Sp(a)&&!qa(a),c2=a=>Pe(a)&&a.target?sr(a.target)?a.target.checked:a.target.value:a,f2=a=>a.substring(0,a.search(/\.\d+(\.|$)/))||a,d2=(a,l)=>a.has(f2(l)),h2=a=>{const l=a.constructor&&a.constructor.prototype;return Pe(l)&&l.hasOwnProperty("isPrototypeOf")},gf=typeof window<"u"&&typeof window.HTMLElement<"u"&&typeof document<"u";function dt(a){let l;const r=Array.isArray(a),s=typeof FileList<"u"?a instanceof FileList:!1;if(a instanceof Date)l=new Date(a);else if(!(gf&&(a instanceof Blob||s))&&(r||Pe(a)))if(l=r?[]:{},!r&&!h2(a))l=a;else for(const c in a)a.hasOwnProperty(c)&&(l[c]=dt(a[c]));else return a;return l}var iu=a=>/^\w*$/.test(a),lt=a=>a===void 0,vf=a=>Array.isArray(a)?a.filter(Boolean):[],bf=a=>vf(a.replace(/["|']|\]/g,"").split(/\.|\[/)),te=(a,l,r)=>{if(!l||!Pe(a))return r;const s=(iu(l)?[l]:bf(l)).reduce((c,f)=>Et(c)?c:c[f],a);return lt(s)||s===a?lt(a[l])?r:a[l]:s},mn=a=>typeof a=="boolean",Xe=(a,l,r)=>{let s=-1;const c=iu(l)?[l]:bf(l),f=c.length,d=f-1;for(;++s<f;){const m=c[s];let g=r;if(s!==d){const y=a[m];g=Pe(y)||Array.isArray(y)?y:isNaN(+c[s+1])?{}:[]}if(m==="__proto__"||m==="constructor"||m==="prototype")return;a[m]=g,a=a[m]}};const Py={BLUR:"blur",FOCUS_OUT:"focusout"},tn={onBlur:"onBlur",onChange:"onChange",onSubmit:"onSubmit",onTouched:"onTouched",all:"all"},jn={max:"max",min:"min",maxLength:"maxLength",minLength:"minLength",pattern:"pattern",required:"required",validate:"validate"},m2=at.createContext(null);m2.displayName="HookFormContext";var y2=(a,l,r,s=!0)=>{const c={defaultValues:l._defaultValues};for(const f in a)Object.defineProperty(c,f,{get:()=>{const d=f;return l._proxyFormState[d]!==tn.all&&(l._proxyFormState[d]=!s||tn.all),a[d]}});return c};const p2=typeof window<"u"?N.useLayoutEffect:N.useEffect;var yn=a=>typeof a=="string",g2=(a,l,r,s,c)=>yn(a)?(s&&l.watch.add(a),te(r,a,c)):Array.isArray(a)?a.map(f=>(s&&l.watch.add(f),te(r,f))):(s&&(l.watchAll=!0),r),v2=(a,l,r,s,c)=>l?{...r[a],types:{...r[a]&&r[a].types?r[a].types:{},[s]:c||!0}}:{},Ji=a=>Array.isArray(a)?a:[a],Iy=()=>{let a=[];return{get observers(){return a},next:c=>{for(const f of a)f.next&&f.next(c)},subscribe:c=>(a.push(c),{unsubscribe:()=>{a=a.filter(f=>f!==c)}}),unsubscribe:()=>{a=[]}}},Pc=a=>Et(a)||!Sp(a);function da(a,l,r=new WeakSet){if(Pc(a)||Pc(l))return a===l;if(qa(a)&&qa(l))return a.getTime()===l.getTime();const s=Object.keys(a),c=Object.keys(l);if(s.length!==c.length)return!1;if(r.has(a)||r.has(l))return!0;r.add(a),r.add(l);for(const f of s){const d=a[f];if(!c.includes(f))return!1;if(f!=="ref"){const m=l[f];if(qa(d)&&qa(m)||Pe(d)&&Pe(m)||Array.isArray(d)&&Array.isArray(m)?!da(d,m,r):d!==m)return!1}}return!0}var Nt=a=>Pe(a)&&!Object.keys(a).length,Sf=a=>a.type==="file",nn=a=>typeof a=="function",Xs=a=>{if(!gf)return!1;const l=a?a.ownerDocument:0;return a instanceof(l&&l.defaultView?l.defaultView.HTMLElement:HTMLElement)},xp=a=>a.type==="select-multiple",xf=a=>a.type==="radio",b2=a=>xf(a)||sr(a),zc=a=>Xs(a)&&a.isConnected;function S2(a,l){const r=l.slice(0,-1).length;let s=0;for(;s<r;)a=lt(a)?s++:a[l[s++]];return a}function x2(a){for(const l in a)if(a.hasOwnProperty(l)&&!lt(a[l]))return!1;return!0}function nt(a,l){const r=Array.isArray(l)?l:iu(l)?[l]:bf(l),s=r.length===1?a:S2(a,r),c=r.length-1,f=r[c];return s&&delete s[f],c!==0&&(Pe(s)&&Nt(s)||Array.isArray(s)&&x2(s))&&nt(a,r.slice(0,-1)),a}var Ep=a=>{for(const l in a)if(nn(a[l]))return!0;return!1};function Gs(a,l={}){const r=Array.isArray(a);if(Pe(a)||r)for(const s in a)Array.isArray(a[s])||Pe(a[s])&&!Ep(a[s])?(l[s]=Array.isArray(a[s])?[]:{},Gs(a[s],l[s])):Et(a[s])||(l[s]=!0);return l}function _p(a,l,r){const s=Array.isArray(a);if(Pe(a)||s)for(const c in a)Array.isArray(a[c])||Pe(a[c])&&!Ep(a[c])?lt(l)||Pc(r[c])?r[c]=Array.isArray(a[c])?Gs(a[c],[]):{...Gs(a[c])}:_p(a[c],Et(l)?{}:l[c],r[c]):r[c]=!da(a[c],l[c]);return r}var Gi=(a,l)=>_p(a,l,Gs(l));const e0={value:!1,isValid:!1},t0={value:!0,isValid:!0};var wp=a=>{if(Array.isArray(a)){if(a.length>1){const l=a.filter(r=>r&&r.checked&&!r.disabled).map(r=>r.value);return{value:l,isValid:!!l.length}}return a[0].checked&&!a[0].disabled?a[0].attributes&&!lt(a[0].attributes.value)?lt(a[0].value)||a[0].value===""?t0:{value:a[0].value,isValid:!0}:t0:e0}return e0},Ap=(a,{valueAsNumber:l,valueAsDate:r,setValueAs:s})=>lt(a)?a:l?a===""?NaN:a&&+a:r&&yn(a)?new Date(a):s?s(a):a;const n0={isValid:!1,value:null};var Tp=a=>Array.isArray(a)?a.reduce((l,r)=>r&&r.checked&&!r.disabled?{isValid:!0,value:r.value}:l,n0):n0;function a0(a){const l=a.ref;return Sf(l)?l.files:xf(l)?Tp(a.refs).value:xp(l)?[...l.selectedOptions].map(({value:r})=>r):sr(l)?wp(a.refs).value:Ap(lt(l.value)?a.ref.value:l.value,a)}var E2=(a,l,r,s)=>{const c={};for(const f of a){const d=te(l,f);d&&Xe(c,f,d._f)}return{criteriaMode:r,names:[...a],fields:c,shouldUseNativeValidation:s}},Qs=a=>a instanceof RegExp,Qi=a=>lt(a)?a:Qs(a)?a.source:Pe(a)?Qs(a.value)?a.value.source:a.value:a,l0=a=>({isOnSubmit:!a||a===tn.onSubmit,isOnBlur:a===tn.onBlur,isOnChange:a===tn.onChange,isOnAll:a===tn.all,isOnTouch:a===tn.onTouched});const i0="AsyncFunction";var _2=a=>!!a&&!!a.validate&&!!(nn(a.validate)&&a.validate.constructor.name===i0||Pe(a.validate)&&Object.values(a.validate).find(l=>l.constructor.name===i0)),w2=a=>a.mount&&(a.required||a.min||a.max||a.maxLength||a.minLength||a.pattern||a.validate),r0=(a,l,r)=>!r&&(l.watchAll||l.watch.has(a)||[...l.watch].some(s=>a.startsWith(s)&&/^\.\w+/.test(a.slice(s.length))));const $i=(a,l,r,s)=>{for(const c of r||Object.keys(a)){const f=te(a,c);if(f){const{_f:d,...m}=f;if(d){if(d.refs&&d.refs[0]&&l(d.refs[0],c)&&!s)return!0;if(d.ref&&l(d.ref,d.name)&&!s)return!0;if($i(m,l))break}else if(Pe(m)&&$i(m,l))break}}};function s0(a,l,r){const s=te(a,r);if(s||iu(r))return{error:s,name:r};const c=r.split(".");for(;c.length;){const f=c.join("."),d=te(l,f),m=te(a,f);if(d&&!Array.isArray(d)&&r!==f)return{name:r};if(m&&m.type)return{name:f,error:m};if(m&&m.root&&m.root.type)return{name:`${f}.root`,error:m.root};c.pop()}return{name:r}}var A2=(a,l,r,s)=>{r(a);const{name:c,...f}=a;return Nt(f)||Object.keys(f).length>=Object.keys(l).length||Object.keys(f).find(d=>l[d]===(!s||tn.all))},T2=(a,l,r)=>!a||!l||a===l||Ji(a).some(s=>s&&(r?s===l:s.startsWith(l)||l.startsWith(s))),R2=(a,l,r,s,c)=>c.isOnAll?!1:!r&&c.isOnTouch?!(l||a):(r?s.isOnBlur:c.isOnBlur)?!a:(r?s.isOnChange:c.isOnChange)?a:!0,O2=(a,l)=>!vf(te(a,l)).length&&nt(a,l),N2=(a,l,r)=>{const s=Ji(te(a,r));return Xe(s,"root",l[r]),Xe(a,r,s),a},Bs=a=>yn(a);function u0(a,l,r="validate"){if(Bs(a)||Array.isArray(a)&&a.every(Bs)||mn(a)&&!a)return{type:r,message:Bs(a)?a:"",ref:l}}var Ul=a=>Pe(a)&&!Qs(a)?a:{value:a,message:""},o0=async(a,l,r,s,c,f)=>{const{ref:d,refs:m,required:g,maxLength:y,minLength:v,min:A,max:_,pattern:Q,validate:R,name:B,valueAsNumber:L,mount:X}=a._f,V=te(r,B);if(!X||l.has(B))return{};const $=m?m[0]:d,ie=oe=>{c&&$.reportValidity&&($.setCustomValidity(mn(oe)?"":oe||""),$.reportValidity())},K={},Ae=xf(d),ye=sr(d),De=Ae||ye,pe=(L||Sf(d))&&lt(d.value)&&lt(V)||Xs(d)&&d.value===""||V===""||Array.isArray(V)&&!V.length,Qe=v2.bind(null,B,s,K),Ze=(oe,xe,Me,Oe=jn.maxLength,j=jn.minLength)=>{const F=oe?xe:Me;K[B]={type:oe?Oe:j,message:F,ref:d,...Qe(oe?Oe:j,F)}};if(f?!Array.isArray(V)||!V.length:g&&(!De&&(pe||Et(V))||mn(V)&&!V||ye&&!wp(m).isValid||Ae&&!Tp(m).isValid)){const{value:oe,message:xe}=Bs(g)?{value:!!g,message:g}:Ul(g);if(oe&&(K[B]={type:jn.required,message:xe,ref:$,...Qe(jn.required,xe)},!s))return ie(xe),K}if(!pe&&(!Et(A)||!Et(_))){let oe,xe;const Me=Ul(_),Oe=Ul(A);if(!Et(V)&&!isNaN(V)){const j=d.valueAsNumber||V&&+V;Et(Me.value)||(oe=j>Me.value),Et(Oe.value)||(xe=j<Oe.value)}else{const j=d.valueAsDate||new Date(V),F=E=>new Date(new Date().toDateString()+" "+E),le=d.type=="time",Te=d.type=="week";yn(Me.value)&&V&&(oe=le?F(V)>F(Me.value):Te?V>Me.value:j>new Date(Me.value)),yn(Oe.value)&&V&&(xe=le?F(V)<F(Oe.value):Te?V<Oe.value:j<new Date(Oe.value))}if((oe||xe)&&(Ze(!!oe,Me.message,Oe.message,jn.max,jn.min),!s))return ie(K[B].message),K}if((y||v)&&!pe&&(yn(V)||f&&Array.isArray(V))){const oe=Ul(y),xe=Ul(v),Me=!Et(oe.value)&&V.length>+oe.value,Oe=!Et(xe.value)&&V.length<+xe.value;if((Me||Oe)&&(Ze(Me,oe.message,xe.message),!s))return ie(K[B].message),K}if(Q&&!pe&&yn(V)){const{value:oe,message:xe}=Ul(Q);if(Qs(oe)&&!V.match(oe)&&(K[B]={type:jn.pattern,message:xe,ref:d,...Qe(jn.pattern,xe)},!s))return ie(xe),K}if(R){if(nn(R)){const oe=await R(V,r),xe=u0(oe,$);if(xe&&(K[B]={...xe,...Qe(jn.validate,xe.message)},!s))return ie(xe.message),K}else if(Pe(R)){let oe={};for(const xe in R){if(!Nt(oe)&&!s)break;const Me=u0(await R[xe](V,r),$,xe);Me&&(oe={...Me,...Qe(xe,Me.message)},ie(Me.message),s&&(K[B]=oe))}if(!Nt(oe)&&(K[B]={ref:$,...oe},!s))return K}}return ie(!0),K};const C2={mode:tn.onSubmit,reValidateMode:tn.onChange,shouldFocusError:!0};function D2(a={}){let l={...C2,...a},r={submitCount:0,isDirty:!1,isReady:!1,isLoading:nn(l.defaultValues),isValidating:!1,isSubmitted:!1,isSubmitting:!1,isSubmitSuccessful:!1,isValid:!1,touchedFields:{},dirtyFields:{},validatingFields:{},errors:l.errors||{},disabled:l.disabled||!1},s={},c=Pe(l.defaultValues)||Pe(l.values)?dt(l.defaultValues||l.values)||{}:{},f=l.shouldUnregister?{}:dt(c),d={action:!1,mount:!1,watch:!1},m={mount:new Set,disabled:new Set,unMount:new Set,array:new Set,watch:new Set},g,y=0;const v={isDirty:!1,dirtyFields:!1,validatingFields:!1,touchedFields:!1,isValidating:!1,isValid:!1,errors:!1};let A={...v};const _={array:Iy(),state:Iy()},Q=l.criteriaMode===tn.all,R=x=>D=>{clearTimeout(y),y=setTimeout(x,D)},B=async x=>{if(!l.disabled&&(v.isValid||A.isValid||x)){const D=l.resolver?Nt((await ye()).errors):await pe(s,!0);D!==r.isValid&&_.state.next({isValid:D})}},L=(x,D)=>{!l.disabled&&(v.isValidating||v.validatingFields||A.isValidating||A.validatingFields)&&((x||Array.from(m.mount)).forEach(q=>{q&&(D?Xe(r.validatingFields,q,D):nt(r.validatingFields,q))}),_.state.next({validatingFields:r.validatingFields,isValidating:!Nt(r.validatingFields)}))},X=(x,D=[],q,P,J=!0,Z=!0)=>{if(P&&q&&!l.disabled){if(d.action=!0,Z&&Array.isArray(te(s,x))){const ne=q(te(s,x),P.argA,P.argB);J&&Xe(s,x,ne)}if(Z&&Array.isArray(te(r.errors,x))){const ne=q(te(r.errors,x),P.argA,P.argB);J&&Xe(r.errors,x,ne),O2(r.errors,x)}if((v.touchedFields||A.touchedFields)&&Z&&Array.isArray(te(r.touchedFields,x))){const ne=q(te(r.touchedFields,x),P.argA,P.argB);J&&Xe(r.touchedFields,x,ne)}(v.dirtyFields||A.dirtyFields)&&(r.dirtyFields=Gi(c,f)),_.state.next({name:x,isDirty:Ze(x,D),dirtyFields:r.dirtyFields,errors:r.errors,isValid:r.isValid})}else Xe(f,x,D)},V=(x,D)=>{Xe(r.errors,x,D),_.state.next({errors:r.errors})},$=x=>{r.errors=x,_.state.next({errors:r.errors,isValid:!1})},ie=(x,D,q,P)=>{const J=te(s,x);if(J){const Z=te(f,x,lt(q)?te(c,x):q);lt(Z)||P&&P.defaultChecked||D?Xe(f,x,D?Z:a0(J._f)):Me(x,Z),d.mount&&B()}},K=(x,D,q,P,J)=>{let Z=!1,ne=!1;const de={name:x};if(!l.disabled){if(!q||P){(v.isDirty||A.isDirty)&&(ne=r.isDirty,r.isDirty=de.isDirty=Ze(),Z=ne!==de.isDirty);const _e=da(te(c,x),D);ne=!!te(r.dirtyFields,x),_e?nt(r.dirtyFields,x):Xe(r.dirtyFields,x,!0),de.dirtyFields=r.dirtyFields,Z=Z||(v.dirtyFields||A.dirtyFields)&&ne!==!_e}if(q){const _e=te(r.touchedFields,x);_e||(Xe(r.touchedFields,x,q),de.touchedFields=r.touchedFields,Z=Z||(v.touchedFields||A.touchedFields)&&_e!==q)}Z&&J&&_.state.next(de)}return Z?de:{}},Ae=(x,D,q,P)=>{const J=te(r.errors,x),Z=(v.isValid||A.isValid)&&mn(D)&&r.isValid!==D;if(l.delayError&&q?(g=R(()=>V(x,q)),g(l.delayError)):(clearTimeout(y),g=null,q?Xe(r.errors,x,q):nt(r.errors,x)),(q?!da(J,q):J)||!Nt(P)||Z){const ne={...P,...Z&&mn(D)?{isValid:D}:{},errors:r.errors,name:x};r={...r,...ne},_.state.next(ne)}},ye=async x=>{L(x,!0);const D=await l.resolver(f,l.context,E2(x||m.mount,s,l.criteriaMode,l.shouldUseNativeValidation));return L(x),D},De=async x=>{const{errors:D}=await ye(x);if(x)for(const q of x){const P=te(D,q);P?Xe(r.errors,q,P):nt(r.errors,q)}else r.errors=D;return D},pe=async(x,D,q={valid:!0})=>{for(const P in x){const J=x[P];if(J){const{_f:Z,...ne}=J;if(Z){const de=m.array.has(Z.name),_e=J._f&&_2(J._f);_e&&v.validatingFields&&L([P],!0);const He=await o0(J,m.disabled,f,Q,l.shouldUseNativeValidation&&!D,de);if(_e&&v.validatingFields&&L([P]),He[Z.name]&&(q.valid=!1,D))break;!D&&(te(He,Z.name)?de?N2(r.errors,He,Z.name):Xe(r.errors,Z.name,He[Z.name]):nt(r.errors,Z.name))}!Nt(ne)&&await pe(ne,D,q)}}return q.valid},Qe=()=>{for(const x of m.unMount){const D=te(s,x);D&&(D._f.refs?D._f.refs.every(q=>!zc(q)):!zc(D._f.ref))&&ht(x)}m.unMount=new Set},Ze=(x,D)=>!l.disabled&&(x&&D&&Xe(f,x,D),!da(E(),c)),oe=(x,D,q)=>g2(x,m,{...d.mount?f:lt(D)?c:yn(x)?{[x]:D}:D},q,D),xe=x=>vf(te(d.mount?f:c,x,l.shouldUnregister?te(c,x,[]):[])),Me=(x,D,q={})=>{const P=te(s,x);let J=D;if(P){const Z=P._f;Z&&(!Z.disabled&&Xe(f,x,Ap(D,Z)),J=Xs(Z.ref)&&Et(D)?"":D,xp(Z.ref)?[...Z.ref.options].forEach(ne=>ne.selected=J.includes(ne.value)):Z.refs?sr(Z.ref)?Z.refs.forEach(ne=>{(!ne.defaultChecked||!ne.disabled)&&(Array.isArray(J)?ne.checked=!!J.find(de=>de===ne.value):ne.checked=J===ne.value||!!J)}):Z.refs.forEach(ne=>ne.checked=ne.value===J):Sf(Z.ref)?Z.ref.value="":(Z.ref.value=J,Z.ref.type||_.state.next({name:x,values:dt(f)})))}(q.shouldDirty||q.shouldTouch)&&K(x,J,q.shouldTouch,q.shouldDirty,!0),q.shouldValidate&&Te(x)},Oe=(x,D,q)=>{for(const P in D){if(!D.hasOwnProperty(P))return;const J=D[P],Z=x+"."+P,ne=te(s,Z);(m.array.has(x)||Pe(J)||ne&&!ne._f)&&!qa(J)?Oe(Z,J,q):Me(Z,J,q)}},j=(x,D,q={})=>{const P=te(s,x),J=m.array.has(x),Z=dt(D);Xe(f,x,Z),J?(_.array.next({name:x,values:dt(f)}),(v.isDirty||v.dirtyFields||A.isDirty||A.dirtyFields)&&q.shouldDirty&&_.state.next({name:x,dirtyFields:Gi(c,f),isDirty:Ze(x,Z)})):P&&!P._f&&!Et(Z)?Oe(x,Z,q):Me(x,Z,q),r0(x,m)&&_.state.next({...r}),_.state.next({name:d.mount?x:void 0,values:dt(f)})},F=async x=>{d.mount=!0;const D=x.target;let q=D.name,P=!0;const J=te(s,q),Z=_e=>{P=Number.isNaN(_e)||qa(_e)&&isNaN(_e.getTime())||da(_e,te(f,q,_e))},ne=l0(l.mode),de=l0(l.reValidateMode);if(J){let _e,He;const Ka=D.type?a0(J._f):c2(x),sn=x.type===Py.BLUR||x.type===Py.FOCUS_OUT,su=!w2(J._f)&&!l.resolver&&!te(r.errors,q)&&!J._f.deps||R2(sn,te(r.touchedFields,q),r.isSubmitted,de,ne),Hn=r0(q,m,sn);Xe(f,q,Ka),sn?(J._f.onBlur&&J._f.onBlur(x),g&&g(0)):J._f.onChange&&J._f.onChange(x);const Vn=K(q,Ka,sn),vn=!Nt(Vn)||Hn;if(!sn&&_.state.next({name:q,type:x.type,values:dt(f)}),su)return(v.isValid||A.isValid)&&(l.mode==="onBlur"?sn&&B():sn||B()),vn&&_.state.next({name:q,...Hn?{}:Vn});if(!sn&&Hn&&_.state.next({...r}),l.resolver){const{errors:va}=await ye([q]);if(Z(Ka),P){const ba=s0(r.errors,s,q),or=s0(va,s,ba.name||q);_e=or.error,q=or.name,He=Nt(va)}}else L([q],!0),_e=(await o0(J,m.disabled,f,Q,l.shouldUseNativeValidation))[q],L([q]),Z(Ka),P&&(_e?He=!1:(v.isValid||A.isValid)&&(He=await pe(s,!0)));P&&(J._f.deps&&Te(J._f.deps),Ae(q,He,_e,Vn))}},le=(x,D)=>{if(te(r.errors,D)&&x.focus)return x.focus(),1},Te=async(x,D={})=>{let q,P;const J=Ji(x);if(l.resolver){const Z=await De(lt(x)?x:J);q=Nt(Z),P=x?!J.some(ne=>te(Z,ne)):q}else x?(P=(await Promise.all(J.map(async Z=>{const ne=te(s,Z);return await pe(ne&&ne._f?{[Z]:ne}:ne)}))).every(Boolean),!(!P&&!r.isValid)&&B()):P=q=await pe(s);return _.state.next({...!yn(x)||(v.isValid||A.isValid)&&q!==r.isValid?{}:{name:x},...l.resolver||!x?{isValid:q}:{},errors:r.errors}),D.shouldFocus&&!P&&$i(s,le,x?J:m.mount),P},E=x=>{const D={...d.mount?f:c};return lt(x)?D:yn(x)?te(D,x):x.map(q=>te(D,q))},G=(x,D)=>({invalid:!!te((D||r).errors,x),isDirty:!!te((D||r).dirtyFields,x),error:te((D||r).errors,x),isValidating:!!te(r.validatingFields,x),isTouched:!!te((D||r).touchedFields,x)}),I=x=>{x&&Ji(x).forEach(D=>nt(r.errors,D)),_.state.next({errors:x?r.errors:{}})},W=(x,D,q)=>{const P=(te(s,x,{_f:{}})._f||{}).ref,J=te(r.errors,x)||{},{ref:Z,message:ne,type:de,..._e}=J;Xe(r.errors,x,{..._e,...D,ref:P}),_.state.next({name:x,errors:r.errors,isValid:!1}),q&&q.shouldFocus&&P&&P.focus&&P.focus()},re=(x,D)=>nn(x)?_.state.subscribe({next:q=>x(oe(void 0,D),q)}):oe(x,D,!0),Ee=x=>_.state.subscribe({next:D=>{T2(x.name,D.name,x.exact)&&A2(D,x.formState||v,Ht,x.reRenderRoot)&&x.callback({values:{...f},...r,...D})}}).unsubscribe,fe=x=>(d.mount=!0,A={...A,...x.formState},Ee({...x,formState:A})),ht=(x,D={})=>{for(const q of x?Ji(x):m.mount)m.mount.delete(q),m.array.delete(q),D.keepValue||(nt(s,q),nt(f,q)),!D.keepError&&nt(r.errors,q),!D.keepDirty&&nt(r.dirtyFields,q),!D.keepTouched&&nt(r.touchedFields,q),!D.keepIsValidating&&nt(r.validatingFields,q),!l.shouldUnregister&&!D.keepDefaultValue&&nt(c,q);_.state.next({values:dt(f)}),_.state.next({...r,...D.keepDirty?{isDirty:Ze()}:{}}),!D.keepIsValid&&B()},ke=({disabled:x,name:D})=>{(mn(x)&&d.mount||x||m.disabled.has(D))&&(x?m.disabled.add(D):m.disabled.delete(D))},qt=(x,D={})=>{let q=te(s,x);const P=mn(D.disabled)||mn(l.disabled);return Xe(s,x,{...q||{},_f:{...q&&q._f?q._f:{ref:{name:x}},name:x,mount:!0,...D}}),m.mount.add(x),q?ke({disabled:mn(D.disabled)?D.disabled:l.disabled,name:x}):ie(x,!0,D.value),{...P?{disabled:D.disabled||l.disabled}:{},...l.progressive?{required:!!D.required,min:Qi(D.min),max:Qi(D.max),minLength:Qi(D.minLength),maxLength:Qi(D.maxLength),pattern:Qi(D.pattern)}:{},name:x,onChange:F,onBlur:F,ref:J=>{if(J){qt(x,D),q=te(s,x);const Z=lt(J.value)&&J.querySelectorAll&&J.querySelectorAll("input,select,textarea")[0]||J,ne=b2(Z),de=q._f.refs||[];if(ne?de.find(_e=>_e===Z):Z===q._f.ref)return;Xe(s,x,{_f:{...q._f,...ne?{refs:[...de.filter(zc),Z,...Array.isArray(te(c,x))?[{}]:[]],ref:{type:Z.type,name:x}}:{ref:Z}}}),ie(x,!1,void 0,Z)}else q=te(s,x,{}),q._f&&(q._f.mount=!1),(l.shouldUnregister||D.shouldUnregister)&&!(d2(m.array,x)&&d.action)&&m.unMount.add(x)}}},ga=()=>l.shouldFocusError&&$i(s,le,m.mount),Ga=x=>{mn(x)&&(_.state.next({disabled:x}),$i(s,(D,q)=>{const P=te(s,q);P&&(D.disabled=P._f.disabled||x,Array.isArray(P._f.refs)&&P._f.refs.forEach(J=>{J.disabled=P._f.disabled||x}))},0,!1))},Qa=(x,D)=>async q=>{let P;q&&(q.preventDefault&&q.preventDefault(),q.persist&&q.persist());let J=dt(f);if(_.state.next({isSubmitting:!0}),l.resolver){const{errors:Z,values:ne}=await ye();r.errors=Z,J=dt(ne)}else await pe(s);if(m.disabled.size)for(const Z of m.disabled)nt(J,Z);if(nt(r.errors,"root"),Nt(r.errors)){_.state.next({errors:{}});try{await x(J,q)}catch(Z){P=Z}}else D&&await D({...r.errors},q),ga(),setTimeout(ga);if(_.state.next({isSubmitted:!0,isSubmitting:!1,isSubmitSuccessful:Nt(r.errors)&&!P,submitCount:r.submitCount+1,errors:r.errors}),P)throw P},Vl=(x,D={})=>{te(s,x)&&(lt(D.defaultValue)?j(x,dt(te(c,x))):(j(x,D.defaultValue),Xe(c,x,dt(D.defaultValue))),D.keepTouched||nt(r.touchedFields,x),D.keepDirty||(nt(r.dirtyFields,x),r.isDirty=D.defaultValue?Ze(x,dt(te(c,x))):Ze()),D.keepError||(nt(r.errors,x),v.isValid&&B()),_.state.next({...r}))},Za=(x,D={})=>{const q=x?dt(x):c,P=dt(q),J=Nt(x),Z=J?c:P;if(D.keepDefaultValues||(c=q),!D.keepValues){if(D.keepDirtyValues){const ne=new Set([...m.mount,...Object.keys(Gi(c,f))]);for(const de of Array.from(ne))te(r.dirtyFields,de)?Xe(Z,de,te(f,de)):j(de,te(Z,de))}else{if(gf&&lt(x))for(const ne of m.mount){const de=te(s,ne);if(de&&de._f){const _e=Array.isArray(de._f.refs)?de._f.refs[0]:de._f.ref;if(Xs(_e)){const He=_e.closest("form");if(He){He.reset();break}}}}if(D.keepFieldsRef)for(const ne of m.mount)j(ne,te(Z,ne));else s={}}f=l.shouldUnregister?D.keepDefaultValues?dt(c):{}:dt(Z),_.array.next({values:{...Z}}),_.state.next({values:{...Z}})}m={mount:D.keepDirtyValues?m.mount:new Set,unMount:new Set,array:new Set,disabled:new Set,watch:new Set,watchAll:!1,focus:""},d.mount=!v.isValid||!!D.keepIsValid||!!D.keepDirtyValues,d.watch=!!l.shouldUnregister,_.state.next({submitCount:D.keepSubmitCount?r.submitCount:0,isDirty:J?!1:D.keepDirty?r.isDirty:!!(D.keepDefaultValues&&!da(x,c)),isSubmitted:D.keepIsSubmitted?r.isSubmitted:!1,dirtyFields:J?{}:D.keepDirtyValues?D.keepDefaultValues&&f?Gi(c,f):r.dirtyFields:D.keepDefaultValues&&x?Gi(c,x):D.keepDirty?r.dirtyFields:{},touchedFields:D.keepTouched?r.touchedFields:{},errors:D.keepErrors?r.errors:{},isSubmitSuccessful:D.keepIsSubmitSuccessful?r.isSubmitSuccessful:!1,isSubmitting:!1})},ur=(x,D)=>Za(nn(x)?x(f):x,D),ru=(x,D={})=>{const q=te(s,x),P=q&&q._f;if(P){const J=P.refs?P.refs[0]:P.ref;J.focus&&(J.focus(),D.shouldSelect&&nn(J.select)&&J.select())}},Ht=x=>{r={...r,...x}},Yl={control:{register:qt,unregister:ht,getFieldState:G,handleSubmit:Qa,setError:W,_subscribe:Ee,_runSchema:ye,_focusError:ga,_getWatch:oe,_getDirty:Ze,_setValid:B,_setFieldArray:X,_setDisabledField:ke,_setErrors:$,_getFieldArray:xe,_reset:Za,_resetDefaultValues:()=>nn(l.defaultValues)&&l.defaultValues().then(x=>{ur(x,l.resetOptions),_.state.next({isLoading:!1})}),_removeUnmounted:Qe,_disableForm:Ga,_subjects:_,_proxyFormState:v,get _fields(){return s},get _formValues(){return f},get _state(){return d},set _state(x){d=x},get _defaultValues(){return c},get _names(){return m},set _names(x){m=x},get _formState(){return r},get _options(){return l},set _options(x){l={...l,...x}}},subscribe:fe,trigger:Te,register:qt,handleSubmit:Qa,watch:re,setValue:j,getValues:E,reset:ur,resetField:Vl,clearErrors:I,unregister:ht,setError:W,setFocus:ru,getFieldState:G};return{...Yl,formControl:Yl}}function Rp(a={}){const l=at.useRef(void 0),r=at.useRef(void 0),[s,c]=at.useState({isDirty:!1,isValidating:!1,isLoading:nn(a.defaultValues),isSubmitted:!1,isSubmitting:!1,isSubmitSuccessful:!1,isValid:!1,submitCount:0,dirtyFields:{},touchedFields:{},validatingFields:{},errors:a.errors||{},disabled:a.disabled||!1,isReady:!1,defaultValues:nn(a.defaultValues)?void 0:a.defaultValues});if(!l.current)if(a.formControl)l.current={...a.formControl,formState:s},a.defaultValues&&!nn(a.defaultValues)&&a.formControl.reset(a.defaultValues,a.resetOptions);else{const{formControl:d,...m}=D2(a);l.current={...m,formState:s}}const f=l.current.control;return f._options=a,p2(()=>{const d=f._subscribe({formState:f._proxyFormState,callback:()=>c({...f._formState}),reRenderRoot:!0});return c(m=>({...m,isReady:!0})),f._formState.isReady=!0,d},[f]),at.useEffect(()=>f._disableForm(a.disabled),[f,a.disabled]),at.useEffect(()=>{a.mode&&(f._options.mode=a.mode),a.reValidateMode&&(f._options.reValidateMode=a.reValidateMode)},[f,a.mode,a.reValidateMode]),at.useEffect(()=>{a.errors&&(f._setErrors(a.errors),f._focusError())},[f,a.errors]),at.useEffect(()=>{a.shouldUnregister&&f._subjects.state.next({values:f._getWatch()})},[f,a.shouldUnregister]),at.useEffect(()=>{if(f._proxyFormState.isDirty){const d=f._getDirty();d!==s.isDirty&&f._subjects.state.next({isDirty:d})}},[f,s.isDirty]),at.useEffect(()=>{a.values&&!da(a.values,r.current)?(f._reset(a.values,{keepFieldsRef:!0,...f._options.resetOptions}),r.current=a.values,c(d=>({...d}))):f._resetDefaultValues()},[f,a.values]),at.useEffect(()=>{f._state.mount||(f._setValid(),f._state.mount=!0),f._state.watch&&(f._state.watch=!1,f._subjects.state.next({...f._formState})),f._removeUnmounted()}),l.current.formState=y2(s,f),l.current}const M2=()=>{const a=ir(),{authUser:l,setAuthUser:r}=ar();console.log("hello"),console.log(l);const{register:s,handleSubmit:c,watch:f,formState:{errors:d}}=Rp(),m=f("password"),g=v=>v===m||"password and confirmpassword not match",y=async v=>{const A={username:v.username,email:v.email,password:v.password,confirmPassword:v.confirmPassword};try{const _=await Ya.post("/api/user/signup",A,{headers:{"Content-Type":"application/json"}});console.log(_.data),_.data&&(kn.success("SignUp successful ypu can login now"),localStorage.setItem("message",JSON.stringify(_.data))),r(_.data),a("/logi")}catch(_){_.response&&(console.log(_),kn.error("Error",_.response.data.error))}};return S.jsx(S.Fragment,{children:S.jsx("div",{className:"min-h-screen bg-gray-50 dark:bg-gray-900 flex items-center justify-center px-4",children:S.jsxs("div",{className:"max-w-md w-full space-y-8",children:[S.jsxs("div",{className:"text-center",children:[S.jsx("div",{className:"mx-auto h-16 w-16 bg-green-100 dark:bg-green-900 rounded-full flex items-center justify-center mb-4",children:S.jsx("svg",{className:"h-8 w-8 text-green-600 dark:text-green-400",fill:"currentColor",viewBox:"0 0 20 20",children:S.jsx("path",{fillRule:"evenodd",d:"M18 10c0 3.866-3.582 7-8 7a8.841 8.841 0 01-4.083-.98L2 17l1.338-3.123C2.493 12.767 2 11.434 2 10c0-3.866 3.582-7 8-7s8 3.134 8 7zM7 9H5v2h2V9zm8 0h-2v2h2V9zM9 9h2v2H9V9z",clipRule:"evenodd"})})}),S.jsx("h2",{className:"text-3xl font-bold text-gray-900 dark:text-white",children:"Create your account"}),S.jsx("p",{className:"mt-2 text-sm text-gray-600 dark:text-gray-400",children:"Join us and start chatting with friends"})]}),S.jsxs("form",{className:"mt-8 space-y-6",onSubmit:c(y),children:[S.jsxs("div",{className:"space-y-4",children:[S.jsxs("div",{children:[S.jsx("label",{htmlFor:"username",className:"sr-only",children:"Username"}),S.jsx("input",{id:"username",type:"text",className:"relative block w-full px-3 py-3 border border-gray-300 dark:border-gray-600 placeholder-gray-500 dark:placeholder-gray-400 text-gray-900 dark:text-white bg-white dark:bg-gray-700 rounded-lg focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-green-500 focus:z-10",placeholder:"Username",...s("username",{required:!0})}),d.username&&S.jsx("p",{className:"text-red-500 text-sm mt-1",children:"Username is required"})]}),S.jsxs("div",{children:[S.jsx("label",{htmlFor:"email",className:"sr-only",children:"Email address"}),S.jsx("input",{id:"email",type:"email",className:"relative block w-full px-3 py-3 border border-gray-300 dark:border-gray-600 placeholder-gray-500 dark:placeholder-gray-400 text-gray-900 dark:text-white bg-white dark:bg-gray-700 rounded-lg focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-green-500 focus:z-10",placeholder:"Email address",...s("email",{required:!0})}),d.email&&S.jsx("p",{className:"text-red-500 text-sm mt-1",children:"Email is required"})]}),S.jsxs("div",{children:[S.jsx("label",{htmlFor:"password",className:"sr-only",children:"Password"}),S.jsx("input",{id:"password",type:"password",className:"relative block w-full px-3 py-3 border border-gray-300 dark:border-gray-600 placeholder-gray-500 dark:placeholder-gray-400 text-gray-900 dark:text-white bg-white dark:bg-gray-700 rounded-lg focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-green-500 focus:z-10",placeholder:"Password",...s("password",{required:!0})}),d.password&&S.jsx("p",{className:"text-red-500 text-sm mt-1",children:"Password is required"})]}),S.jsxs("div",{children:[S.jsx("label",{htmlFor:"confirmPassword",className:"sr-only",children:"Confirm Password"}),S.jsx("input",{id:"confirmPassword",type:"password",className:"relative block w-full px-3 py-3 border border-gray-300 dark:border-gray-600 placeholder-gray-500 dark:placeholder-gray-400 text-gray-900 dark:text-white bg-white dark:bg-gray-700 rounded-lg focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-green-500 focus:z-10",placeholder:"Confirm Password",...s("confirmPassword",{required:!0,validate:g})}),d.confirmPassword&&S.jsx("p",{className:"text-red-500 text-sm mt-1",children:d.confirmPassword.message})]})]}),S.jsx("div",{children:S.jsx("button",{type:"submit",className:"group relative w-full flex justify-center py-3 px-4 border border-transparent text-sm font-medium rounded-lg text-white bg-green-600 hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500 transition-colors",children:"Create Account"})}),S.jsx("div",{className:"text-center",children:S.jsxs("p",{className:"text-sm text-gray-600 dark:text-gray-400",children:["Already have an account?"," ",S.jsx(lu,{to:"/login",className:"font-medium text-green-600 hover:text-green-500",children:"Sign in"})]})})]})]})})})},c0=()=>{const{authUser:a,setAuthUser:l}=ar();console.log("hello",a);const{register:r,handleSubmit:s,formState:{errors:c}}=Rp(),f=async m=>{var y;const g={email:m.email,password:m.password};try{const v=await Ya.post("/api/user/login",g);v.data&&(kn.success("login succesful"),localStorage.setItem("message",JSON.stringify(v.data)),console.log(v.data)),l(v.data)}catch(v){console.log(v.response),v.response&&kn.error("message : "+((y=v.response.data)==null?void 0:y.message))}},d=m=>{m.key=="enter"&&f()};return S.jsx(S.Fragment,{children:S.jsx("div",{className:"min-h-screen bg-gray-50 dark:bg-gray-900 flex items-center justify-center px-4",children:S.jsxs("div",{className:"max-w-md w-full space-y-8",children:[S.jsxs("div",{className:"text-center",children:[S.jsx("div",{className:"mx-auto h-16 w-16 bg-green-100 dark:bg-green-900 rounded-full flex items-center justify-center mb-4",children:S.jsx("svg",{className:"h-8 w-8 text-green-600 dark:text-green-400",fill:"currentColor",viewBox:"0 0 20 20",children:S.jsx("path",{fillRule:"evenodd",d:"M18 10c0 3.866-3.582 7-8 7a8.841 8.841 0 01-4.083-.98L2 17l1.338-3.123C2.493 12.767 2 11.434 2 10c0-3.866 3.582-7 8-7s8 3.134 8 7zM7 9H5v2h2V9zm8 0h-2v2h2V9zM9 9h2v2H9V9z",clipRule:"evenodd"})})}),S.jsx("h2",{className:"text-3xl font-bold text-gray-900 dark:text-white",children:"Welcome back"}),S.jsx("p",{className:"mt-2 text-sm text-gray-600 dark:text-gray-400",children:"Sign in to your account to continue chatting"})]}),S.jsxs("form",{className:"mt-8 space-y-6",onSubmit:s(f),children:[S.jsxs("div",{className:"space-y-4",children:[S.jsxs("div",{children:[S.jsx("label",{htmlFor:"email",className:"sr-only",children:"Email address"}),S.jsx("input",{id:"email",type:"email",className:"relative block w-full px-3 py-3 border border-gray-300 dark:border-gray-600 placeholder-gray-500 dark:placeholder-gray-400 text-gray-900 dark:text-white bg-white dark:bg-gray-700 rounded-lg focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-green-500 focus:z-10",placeholder:"Email address",...r("email",{required:!0})}),c.email&&S.jsx("p",{className:"text-red-500 text-sm mt-1",children:"Email is required"})]}),S.jsxs("div",{children:[S.jsx("label",{htmlFor:"password",className:"sr-only",children:"Password"}),S.jsx("input",{id:"password",type:"password",className:"relative block w-full px-3 py-3 border border-gray-300 dark:border-gray-600 placeholder-gray-500 dark:placeholder-gray-400 text-gray-900 dark:text-white bg-white dark:bg-gray-700 rounded-lg focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-green-500 focus:z-10",placeholder:"Password",...r("password",{required:!0})}),c.password&&S.jsx("p",{className:"text-red-500 text-sm mt-1",children:"Password is required"})]})]}),S.jsx("div",{children:S.jsx("button",{type:"submit",className:"group relative w-full flex justify-center py-3 px-4 border border-transparent text-sm font-medium rounded-lg text-white bg-green-600 hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500 transition-colors",onKeyDown:d,children:"Sign in"})}),S.jsx("div",{className:"text-center",children:S.jsxs("p",{className:"text-sm text-gray-600 dark:text-gray-400",children:["Don't have an account?"," ",S.jsx(lu,{to:"/signup",className:"font-medium text-green-600 hover:text-green-500",children:"Sign up"})]})})]})]})})})},U2=()=>{const{authUser:a}=ar();return console.log(a),S.jsx(S.Fragment,{children:S.jsxs("div",{className:"h-screen w-full bg-gray-100 dark:bg-gray-900",children:[S.jsxs(oE,{children:[S.jsx(Ki,{path:"/",element:a?S.jsxs("div",{className:"h-screen w-full flex max-w-full mx-auto bg-white dark:bg-gray-800 shadow-xl",children:[S.jsx("div",{className:"hidden lg:flex",children:S.jsx(o2,{})}),S.jsx(PE,{}),S.jsx(s2,{})]}):S.jsx(Fy,{to:"/login"})}),S.jsx(Ki,{path:"/signup",element:S.jsx(M2,{})}),S.jsx(Ki,{path:"/logi",element:S.jsx(c0,{})}),S.jsx(Ki,{path:"/login",element:a?S.jsx(Fy,{to:"/"}):S.jsx(c0,{})})]}),S.jsx(SS,{position:"top-center",reverseOrder:!1})]})})};$v.createRoot(document.getElementById("root")).render(S.jsx(UE,{children:S.jsx(_S,{children:S.jsx(gx,{children:S.jsx(U2,{})})})}));
