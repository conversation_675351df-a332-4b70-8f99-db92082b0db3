// THIS FILE IS AUTO GENERATED
var GenIcon = require('../lib').GenIcon
module.exports.GiAbstract120 = function GiAbstract120 (props) {
  return GenIcon({"tag":"svg","attr":{"viewBox":"0 0 512 512"},"child":[{"tag":"path","attr":{"d":"m256,21c-16.6132,0-30.1094,13.1471-30.1094,29.3305 0,3.1923.4999,6.2802 1.4688,9.1568-.6868.2767-1.3717.5636-2.0562.8585-28.3599,12.2172-48.3219,40.8549-48.3219,74.2561 0,9.0999 1.5327,17.752 4.2594,25.8967 .3088.7596.553,1.541.8812,2.2892 .2059.5751.3704,1.1465.5875,1.7168 .0892.1924.2032.3805.2937.5722 12.2495,25.9702 36.2931,44.4267 64.625,47.5011-32.6195-2.6949-60.1246-20.8654-73.2906-46.2134-13.581-5.1278-23.2063-18.3614-23.2063-34.0519v-66.6732c0-14.3008 8.0026-26.484 19.6812-32.4781h-35.8375c-10.0149,0-19.0054,4.2179-25.5562,10.8738-6.5991,6.7049-10.7219,15.8854-10.7219,26.1827v167.9704c0,1.2403.0303,2.5153.1469,3.7204h314.3125c.1168-1.205.147-2.4801.147-3.7204v-167.9704c0-10.2973-4.1227-19.4778-10.7219-26.1827-.7955-.8128-1.6301-1.6978-2.4969-2.4322-.0288-.0216-.1182.0216-.1463,0-.866-.7298-1.7125-1.5-2.6438-2.1462-.036-.0216-.1131.0216-.147,0-.9307-.6414-1.8005-1.3073-2.7906-1.86-.036-.0216-.1095.0216-.1463,0-.9908-.5484-2.04-1.1193-3.0844-1.5738-.0432-.0216-.1031.0216-.147,0-1.0422-.449-1.9944-.7947-3.0843-1.1446-.0432-.0144-.1031.0144-.147,0-1.0938-.3459-2.2421-.7605-3.3781-1.0015-.0432-.007-.1009.007-.147,0-1.137-.2371-2.3513-.4468-3.525-.5722-.0504-.005-.098.005-.147,0-1.2228-.1261-2.4155-.1427-3.6719-.1427h-35.6906c11.6786,5.9941 19.6813,18.1773 19.6813,32.4781v66.6732c0,15.6906-9.6253,28.9242-23.2063,34.0519-13.166,25.348-40.6711,43.5185-73.2906,46.2133 28.3319-3.0744 52.3755-21.5308 64.625-47.501 .6077-1.5081 1.2431-3.0272 1.7625-4.5784 2.7262-8.1443 4.2589-16.7971 4.2589-25.8969 0-34.2074-20.9225-63.4198-50.3781-75.1146 .9688-2.8766 1.4688-5.9645 1.4688-9.1568 0-16.1834-13.4962-29.3305-30.1094-29.3305zm-153.7781,232.7838c1.8209,3.9351 4.2024,7.5452 7.1969,10.5875 .7977.8104 1.628,1.7005 2.4969,2.432h.1469c.8684.728 1.7104,1.5031 2.6437,2.1464h.1469c.933.6385 1.7985,1.3107 2.7906,1.8598h.1469c.993.5455 2.038,1.123 3.0844,1.574h.1469c1.0444.4439 1.9926.7989 3.0844,1.1446h.1469c1.0956.3438 2.2404.765 3.3781,1.0017h.1469c1.1386.2335 2.3499.4533 3.525.5722h.1469c1.1753.1146 2.3187.142 3.525.142h.1469 88.2719v134.6956l-97.0844-72.2698c-5.919-7.3919-15.1879-12.1617-25.5563-12.1617-17.7998,0-32.1656,13.9944-32.1656,31.3338s14.3658,31.4766 32.1656,31.4766c7.253,0 13.9855-2.3405 19.3875-6.2953l103.2531,56.0856v22.9149l-168.7594-38.5553h-.1469c-15.1013-1.7187-27.8531,9.6074-32.0187,24.18-.293,1.0252-.5289,2.0942-.7344,3.1477-.0094.0505.0094.1009 0,.142-.4292,2.248-.7343,4.5445-.7343,6.8676 0,4.7473.9573,9.2104 2.6438,13.3063 .8432,2.0479 1.885,4.0459 3.0844,5.8663 2.3987,3.6348 5.3983,6.7046 8.9594,9.1569h.1469c.859.5845 1.7302,1.2101 2.6437,1.7169h.1469c.9135.498 1.8279,1.0182 2.7906,1.4303h.1469c.9631.4072 1.9303.8286 2.9375,1.1446h.1469c1.0083.3156 2.0371.6407 3.0844.859h.1469c1.049.2104 2.1483.4605 3.2313.5722h.1469c1.0831.1052 2.1193.142 3.2312.142h.1469 402.7313c11.6241,0 21.8666-5.9322 27.9062-15.0227 1.1993-1.8203 2.2411-3.8184 3.0844-5.8663 1.7002-4.0958 2.6438-8.5584 2.6438-13.3062 0-3.557-.4872-6.9312-1.4688-10.1582-4.1657-14.5727-16.9174-25.8983-32.0187-24.18h-.1463l-168.7594,38.5552v-22.9149l103.2532-56.2284c5.3941,4.0102 12.0784,6.4382 19.3875,6.4382 17.7998,0 32.1656-14.1372 32.1656-31.4766s-14.3658-31.3337-32.1656-31.3337c-10.358,0-19.5312,4.7826-25.4094,12.1616l-97.2313,72.2698v-134.6956h88.2719c10.0518,0 19.1466-4.1741 25.7032-10.8737 2.9944-3.0424 5.376-6.6525 7.1969-10.5876z"}}]})(props);
};
