// THIS FILE IS AUTO GENERATED
var GenIcon = require('../lib').GenIcon
module.exports.VscRootFolderOpened = function VscRootFolderOpened (props) {
  return GenIcon({"tag":"svg","attr":{"viewBox":"0 0 16 16","fill":"currentColor"},"child":[{"tag":"g","attr":{"clipPath":"url(#clip0)"},"child":[{"tag":"path","attr":{"fillRule":"evenodd","clipRule":"evenodd","d":"M0 7.337V2.5L.5 2h5l.35.15.86.85h5.79l.5.5V6h2.13l.48.63-2.63 7-.48.37H8.743a5.48 5.48 0 0 0 .657-1h2.73l2.37-6H8.743a5.534 5.534 0 0 0-.72-.724l.127-.126L8.5 6H12V4H6.5l-.35-.15L5.29 3H1l.013 3.246A5.531 5.531 0 0 0 0 7.336z"}},{"tag":"path","attr":{"d":"M6 10.5a1.5 1.5 0 1 1-3 0 1.5 1.5 0 0 1 3 0z"}},{"tag":"path","attr":{"fillRule":"evenodd","clipRule":"evenodd","d":"M8 10.5a3.5 3.5 0 1 1-7 0 3.5 3.5 0 0 1 7 0zM4.5 13a2.5 2.5 0 1 0 0-5 2.5 2.5 0 0 0 0 5z"}}]},{"tag":"defs","attr":{},"child":[{"tag":"clipPath","attr":{"id":"clip0"},"child":[{"tag":"path","attr":{"d":"M0 0h16v16H0V0z"}}]}]}]})(props);
};
