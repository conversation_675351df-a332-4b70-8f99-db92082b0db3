// THIS FILE IS AUTO GENERATED
var GenIcon = require('../lib').GenIcon
module.exports.BiCake = function BiCake (props) {
  return GenIcon({"tag":"svg","attr":{"viewBox":"0 0 24 24"},"child":[{"tag":"path","attr":{"d":"M19,5h-6V2h-2v3H5C3.346,5,2,6.346,2,8v10c0,1.103,0.897,2,2,2h16c1.103,0,2-0.897,2-2V8C22,6.346,20.654,5,19,5z M5,7h14 c0.552,0,1,0.448,1,1l0.001,3.12c-0.896,0.228-1.469,0.734-1.916,1.132C17.578,12.702,17.243,13,16.497,13 c-0.745,0-1.08-0.298-1.587-0.747C14.315,11.724,13.501,11,11.995,11c-1.505,0-2.319,0.724-2.914,1.253 C8.574,12.703,8.24,13,7.495,13c-0.743,0-1.077-0.297-1.582-0.747C5.466,11.855,4.895,11.348,4,11.12V8C4,7.448,4.448,7,5,7z M4,18v-4.714c0.191,0.123,0.374,0.274,0.583,0.461C5.178,14.276,5.991,15,7.495,15c1.505,0,2.319-0.724,2.914-1.253 C10.916,13.297,11.25,13,11.995,13s1.08,0.298,1.587,0.747C14.177,14.276,14.991,15,16.497,15s2.321-0.724,2.916-1.253 c0.211-0.188,0.395-0.34,0.588-0.464L20.002,18H4z"}}]})(props);
};
