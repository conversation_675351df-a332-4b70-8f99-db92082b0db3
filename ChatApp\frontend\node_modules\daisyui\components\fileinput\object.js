export default {".file-input":{"cursor":["pointer","pointer"],"border":"var(--border) solid #0000","display":"inline-flex","appearance":"none","align-items":"center","background-color":"var(--color-base-100)","vertical-align":"middle","webkit-user-select":"none","user-select":"none","width":"clamp(3rem, 20rem, 100%)","height":"var(--size)","padding-inline-end":"0.75rem","font-size":"0.875rem","line-height":2,"border-start-start-radius":"var(--join-ss, var(--radius-field))","border-start-end-radius":"var(--join-se, var(--radius-field))","border-end-start-radius":"var(--join-es, var(--radius-field))","border-end-end-radius":"var(--join-ee, var(--radius-field))","border-color":"var(--input-color)","box-shadow":"0 1px color-mix(in oklab, var(--input-color) calc(var(--depth) * 10%), #0000) inset, 0 -1px oklch(100% 0 0 / calc(var(--depth) * 0.1)) inset","--size":"calc(var(--size-field, 0.25rem) * 10)","--input-color":"color-mix(in oklab, var(--color-base-content) 20%, #0000)","&::file-selector-button":{"margin-inline-end":"calc(0.25rem * 4)","cursor":"pointer","padding-inline":"calc(0.25rem * 4)","webkit-user-select":"none","user-select":"none","height":"calc(100% + var(--border) * 2)","margin-block":"calc(var(--border) * -1)","margin-inline-start":"calc(var(--border) * -1)","font-size":"0.875rem","color":"var(--btn-fg)","border-width":"var(--border)","border-style":"solid","border-color":"var(--btn-border)","border-start-start-radius":"calc(var(--join-ss, var(--radius-field) - var(--border)))","border-end-start-radius":"calc(var(--join-es, var(--radius-field) - var(--border)))","font-weight":600,"background-color":"var(--btn-bg)","background-size":"calc(var(--noise) * 100%)","background-image":"var(--btn-noise)","text-shadow":"0 0.5px oklch(1 0 0 / calc(var(--depth) * 0.15))","box-shadow":"0 0.5px 0 0.5px color-mix( in oklab, color-mix(in oklab, white 30%, var(--btn-bg)) calc(var(--depth) * 20%), #0000 ) inset, var(--btn-shadow)","--size":"calc(var(--size-field, 0.25rem) * 10)","--btn-bg":"var(--btn-color, var(--color-base-200))","--btn-fg":"var(--color-base-content)","--btn-border":"color-mix(in oklab, var(--btn-bg), #000 5%)","--btn-shadow":"0 3px 2px -2px color-mix(in oklab, var(--btn-bg) 30%, #0000),\n      0 4px 3px -2px color-mix(in oklab, var(--btn-bg) 30%, #0000)","--btn-noise":"var(--fx-noise)"},"&:focus":{"--input-color":"var(--color-base-content)","box-shadow":"0 1px color-mix(in oklab, var(--input-color) 10%, #0000)","outline":"2px solid var(--input-color)","outline-offset":"2px","isolation":"isolate"},"&:has(> input[disabled]), &:is(:disabled, [disabled])":{"cursor":"not-allowed","border-color":"var(--color-base-200)","background-color":"var(--color-base-200)","&::placeholder":{"color":"color-mix(in oklab, var(--color-base-content) 20%, transparent)"},"box-shadow":"none","color":"color-mix(in oklch, var(--color-base-content) 20%, #0000)","&::file-selector-button":{"cursor":"not-allowed","border-color":"var(--color-base-200)","background-color":"var(--color-base-200)","--btn-border":"#0000","--btn-noise":"none","--btn-fg":"color-mix(in oklch, var(--color-base-content) 20%, #0000)"}}},".file-input-ghost":{"background-color":"transparent","transition":"background-color 0.2s","box-shadow":"none","border-color":"#0000","&::file-selector-button":{"margin-inline-start":"calc(0.25rem * 0)","margin-inline-end":"calc(0.25rem * 4)","height":"100%","cursor":"pointer","padding-inline":"calc(0.25rem * 4)","webkit-user-select":"none","user-select":"none","margin-block":"0","border-start-end-radius":"calc(var(--join-ss, var(--radius-field) - var(--border)))","border-end-end-radius":"calc(var(--join-es, var(--radius-field) - var(--border)))"},"&:focus, &:focus-within":{"background-color":"var(--color-base-100)","color":"var(--color-base-content)","border-color":"#0000","box-shadow":"none"}},".file-input-neutral":{"--btn-color":"var(--color-neutral)","&::file-selector-button":{"color":"var(--color-neutral-content)"},"&, &:focus, &:focus-within":{"--input-color":"var(--color-neutral)"}},".file-input-primary":{"--btn-color":"var(--color-primary)","&::file-selector-button":{"color":"var(--color-primary-content)"},"&, &:focus, &:focus-within":{"--input-color":"var(--color-primary)"}},".file-input-secondary":{"--btn-color":"var(--color-secondary)","&::file-selector-button":{"color":"var(--color-secondary-content)"},"&, &:focus, &:focus-within":{"--input-color":"var(--color-secondary)"}},".file-input-accent":{"--btn-color":"var(--color-accent)","&::file-selector-button":{"color":"var(--color-accent-content)"},"&, &:focus, &:focus-within":{"--input-color":"var(--color-accent)"}},".file-input-info":{"--btn-color":"var(--color-info)","&::file-selector-button":{"color":"var(--color-info-content)"},"&, &:focus, &:focus-within":{"--input-color":"var(--color-info)"}},".file-input-success":{"--btn-color":"var(--color-success)","&::file-selector-button":{"color":"var(--color-success-content)"},"&, &:focus, &:focus-within":{"--input-color":"var(--color-success)"}},".file-input-warning":{"--btn-color":"var(--color-warning)","&::file-selector-button":{"color":"var(--color-warning-content)"},"&, &:focus, &:focus-within":{"--input-color":"var(--color-warning)"}},".file-input-error":{"--btn-color":"var(--color-error)","&::file-selector-button":{"color":"var(--color-error-content)"},"&, &:focus, &:focus-within":{"--input-color":"var(--color-error)"}},".file-input-xs":{"--size":"calc(var(--size-field, 0.25rem) * 6)","font-size":"0.6875rem","line-height":"1rem","&::file-selector-button":{"font-size":"0.6875rem"}},".file-input-sm":{"--size":"calc(var(--size-field, 0.25rem) * 8)","font-size":"0.75rem","line-height":"1.5rem","&::file-selector-button":{"font-size":"0.75rem"}},".file-input-md":{"--size":"calc(var(--size-field, 0.25rem) * 10)","font-size":"0.875rem","line-height":2,"&::file-selector-button":{"font-size":"0.875rem"}},".file-input-lg":{"--size":"calc(var(--size-field, 0.25rem) * 12)","font-size":"1.125rem","line-height":"2.5rem","&::file-selector-button":{"font-size":"1.125rem"}},".file-input-xl":{"padding-inline-end":"calc(0.25rem * 6)","--size":"calc(var(--size-field, 0.25rem) * 14)","font-size":"1.125rem","line-height":"3rem","&::file-selector-button":{"font-size":"1.375rem"}}};