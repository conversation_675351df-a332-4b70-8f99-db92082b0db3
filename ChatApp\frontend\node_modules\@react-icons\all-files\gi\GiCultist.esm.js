// THIS FILE IS AUTO GENERATED
import { GenIcon } from '../lib';
export function Gi<PERSON>ultist (props) {
  return GenIcon({"tag":"svg","attr":{"viewBox":"0 0 512 512"},"child":[{"tag":"path","attr":{"d":"M386.168 460.394l17.08-9.8-13.25-25.36-115.91-377.33c-14.17-30.67-56.22-34.53-75.73-6.9l-85.77 121.16c-12.59 17.84 8.29 40.12 26.89 28.68l30.37-18.67 25.64-65.77-46.85 307.42-7 26.18 16.81 14.5 17.55-17.81q4.89 3.26 9.86 6l-3.18 26.31 20.23 10.38 16.31-22.87a157 157 0 0 0 16.36 3.63l2.61 26.52 22.09 2.93 5.21-27.16c6-.05 11.9-.38 17.69-1l11 27.09 23-4-2.77-28.5c4.85-1.17 9.54-2.46 14.07-3.83l15.76 24.24 21.52-7.88-8.53-26.81c4.67-2 8.92-3.93 12.71-5.77zm-65.23-185.7c8.48-2.39 19.08 8.89 23.67 25.19 4.59 16.3 1.44 31.45-7 33.84-8.44 2.39-19.08-8.89-23.67-25.19-4.59-16.3-1.48-31.45 7-33.84zm-63 12.41c15.51-2.1 30.06 10.84 32.52 28.9 2.46 18.06-8.14 34.44-23.65 36.55-15.51 2.11-30.08-10.85-32.53-28.92-2.45-18.07 8.13-34.43 23.65-36.53z"}}]})(props);
};
