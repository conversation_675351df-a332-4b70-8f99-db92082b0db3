/*! 🌼 daisyUI 5.0.42 - MIT License */ @layer utilities{.input{cursor:text;border:var(--border)solid #0000;appearance:none;background-color:var(--color-base-100);vertical-align:middle;white-space:nowrap;width:clamp(3rem,20rem,100%);height:var(--size);touch-action:manipulation;border-color:var(--input-color);box-shadow:0 1px color-mix(in oklab,var(--input-color)calc(var(--depth)*10%),#0000)inset,0 -1px oklch(100% 0 0/calc(var(--depth)*.1))inset;--size:calc(var(--size-field,.25rem)*10);--input-color:color-mix(in oklab,var(--color-base-content)20%,#0000);border-start-start-radius:var(--join-ss,var(--radius-field));border-start-end-radius:var(--join-se,var(--radius-field));border-end-end-radius:var(--join-ee,var(--radius-field));border-end-start-radius:var(--join-es,var(--radius-field));flex-shrink:1;align-items:center;gap:.5rem;padding-inline:.75rem;font-size:.875rem;display:inline-flex;position:relative;&:where(input){display:inline-flex}& :where(input){appearance:none;background-color:#0000;border:none;width:100%;height:100%;display:inline-flex;&:focus,&:focus-within{--tw-outline-style:none;outline-style:none;@media (forced-colors:active){&{outline-offset:2px;outline:2px solid #0000}}}}& :where(input[type=url]),& :where(input[type=email]){direction:ltr}& :where(input[type=date]){display:inline-block}&:focus,&:focus-within{--input-color:var(--color-base-content);box-shadow:0 1px color-mix(in oklab,var(--input-color)calc(var(--depth)*10%),#0000);outline:2px solid var(--input-color);outline-offset:2px;isolation:isolate;z-index:1}&:has(>input[disabled]),&:is(:disabled,[disabled]){cursor:not-allowed;border-color:var(--color-base-200);background-color:var(--color-base-200);color:color-mix(in oklab,var(--color-base-content)40%,transparent);box-shadow:none;&::placeholder{color:color-mix(in oklab,var(--color-base-content)20%,transparent)}}&:has(>input[disabled])>input[disabled]{cursor:not-allowed}&::-webkit-date-and-time-value{text-align:inherit}&[type=number]{&::-webkit-inner-spin-button{margin-block:-.75rem;margin-inline-end:-.75rem}}&::-webkit-calendar-picker-indicator{position:absolute;inset-inline-end:.75em}}.input-ghost{box-shadow:none;background-color:#0000;border-color:#0000;&:focus,&:focus-within{background-color:var(--color-base-100);color:var(--color-base-content);box-shadow:none;border-color:#0000}}.input-neutral{&,&:focus,&:focus-within{--input-color:var(--color-neutral)}}.input-primary{&,&:focus,&:focus-within{--input-color:var(--color-primary)}}.input-secondary{&,&:focus,&:focus-within{--input-color:var(--color-secondary)}}.input-accent{&,&:focus,&:focus-within{--input-color:var(--color-accent)}}.input-info{&,&:focus,&:focus-within{--input-color:var(--color-info)}}.input-success{&,&:focus,&:focus-within{--input-color:var(--color-success)}}.input-warning{&,&:focus,&:focus-within{--input-color:var(--color-warning)}}.input-error{&,&:focus,&:focus-within{--input-color:var(--color-error)}}.input-xs{--size:calc(var(--size-field,.25rem)*6);font-size:.6875rem;&[type=number]{&::-webkit-inner-spin-button{margin-block:-.25rem;margin-inline-end:-.75rem}}}.input-sm{--size:calc(var(--size-field,.25rem)*8);font-size:.75rem;&[type=number]{&::-webkit-inner-spin-button{margin-block:-.5rem;margin-inline-end:-.75rem}}}.input-md{--size:calc(var(--size-field,.25rem)*10);font-size:.875rem;&[type=number]{&::-webkit-inner-spin-button{margin-block:-.75rem;margin-inline-end:-.75rem}}}.input-lg{--size:calc(var(--size-field,.25rem)*12);font-size:1.125rem;&[type=number]{&::-webkit-inner-spin-button{margin-block:-.75rem;margin-inline-end:-.75rem}}}.input-xl{--size:calc(var(--size-field,.25rem)*14);font-size:1.375rem;&[type=number]{&::-webkit-inner-spin-button{margin-block:-1rem;margin-inline-end:-.75rem}}}@media (width>=640px){.sm\:input{cursor:text;border:var(--border)solid #0000;appearance:none;background-color:var(--color-base-100);vertical-align:middle;white-space:nowrap;width:clamp(3rem,20rem,100%);height:var(--size);touch-action:manipulation;border-color:var(--input-color);box-shadow:0 1px color-mix(in oklab,var(--input-color)calc(var(--depth)*10%),#0000)inset,0 -1px oklch(100% 0 0/calc(var(--depth)*.1))inset;--size:calc(var(--size-field,.25rem)*10);--input-color:color-mix(in oklab,var(--color-base-content)20%,#0000);border-start-start-radius:var(--join-ss,var(--radius-field));border-start-end-radius:var(--join-se,var(--radius-field));border-end-end-radius:var(--join-ee,var(--radius-field));border-end-start-radius:var(--join-es,var(--radius-field));flex-shrink:1;align-items:center;gap:.5rem;padding-inline:.75rem;font-size:.875rem;display:inline-flex;position:relative;&:where(input){display:inline-flex}& :where(input){appearance:none;background-color:#0000;border:none;width:100%;height:100%;display:inline-flex;&:focus,&:focus-within{--tw-outline-style:none;outline-style:none;@media (forced-colors:active){&{outline-offset:2px;outline:2px solid #0000}}}}& :where(input[type=url]),& :where(input[type=email]){direction:ltr}& :where(input[type=date]){display:inline-block}&:focus,&:focus-within{--input-color:var(--color-base-content);box-shadow:0 1px color-mix(in oklab,var(--input-color)calc(var(--depth)*10%),#0000);outline:2px solid var(--input-color);outline-offset:2px;isolation:isolate;z-index:1}&:has(>input[disabled]),&:is(:disabled,[disabled]){cursor:not-allowed;border-color:var(--color-base-200);background-color:var(--color-base-200);color:color-mix(in oklab,var(--color-base-content)40%,transparent);box-shadow:none;&::placeholder{color:color-mix(in oklab,var(--color-base-content)20%,transparent)}}&:has(>input[disabled])>input[disabled]{cursor:not-allowed}&::-webkit-date-and-time-value{text-align:inherit}&[type=number]{&::-webkit-inner-spin-button{margin-block:-.75rem;margin-inline-end:-.75rem}}&::-webkit-calendar-picker-indicator{position:absolute;inset-inline-end:.75em}}.sm\:input-ghost{box-shadow:none;background-color:#0000;border-color:#0000;&:focus,&:focus-within{background-color:var(--color-base-100);color:var(--color-base-content);box-shadow:none;border-color:#0000}}.sm\:input-neutral{&,&:focus,&:focus-within{--input-color:var(--color-neutral)}}.sm\:input-primary{&,&:focus,&:focus-within{--input-color:var(--color-primary)}}.sm\:input-secondary{&,&:focus,&:focus-within{--input-color:var(--color-secondary)}}.sm\:input-accent{&,&:focus,&:focus-within{--input-color:var(--color-accent)}}.sm\:input-info{&,&:focus,&:focus-within{--input-color:var(--color-info)}}.sm\:input-success{&,&:focus,&:focus-within{--input-color:var(--color-success)}}.sm\:input-warning{&,&:focus,&:focus-within{--input-color:var(--color-warning)}}.sm\:input-error{&,&:focus,&:focus-within{--input-color:var(--color-error)}}.sm\:input-xs{--size:calc(var(--size-field,.25rem)*6);font-size:.6875rem;&[type=number]{&::-webkit-inner-spin-button{margin-block:-.25rem;margin-inline-end:-.75rem}}}.sm\:input-sm{--size:calc(var(--size-field,.25rem)*8);font-size:.75rem;&[type=number]{&::-webkit-inner-spin-button{margin-block:-.5rem;margin-inline-end:-.75rem}}}.sm\:input-md{--size:calc(var(--size-field,.25rem)*10);font-size:.875rem;&[type=number]{&::-webkit-inner-spin-button{margin-block:-.75rem;margin-inline-end:-.75rem}}}.sm\:input-lg{--size:calc(var(--size-field,.25rem)*12);font-size:1.125rem;&[type=number]{&::-webkit-inner-spin-button{margin-block:-.75rem;margin-inline-end:-.75rem}}}.sm\:input-xl{--size:calc(var(--size-field,.25rem)*14);font-size:1.375rem;&[type=number]{&::-webkit-inner-spin-button{margin-block:-1rem;margin-inline-end:-.75rem}}}}@media (width>=768px){.md\:input{cursor:text;border:var(--border)solid #0000;appearance:none;background-color:var(--color-base-100);vertical-align:middle;white-space:nowrap;width:clamp(3rem,20rem,100%);height:var(--size);touch-action:manipulation;border-color:var(--input-color);box-shadow:0 1px color-mix(in oklab,var(--input-color)calc(var(--depth)*10%),#0000)inset,0 -1px oklch(100% 0 0/calc(var(--depth)*.1))inset;--size:calc(var(--size-field,.25rem)*10);--input-color:color-mix(in oklab,var(--color-base-content)20%,#0000);border-start-start-radius:var(--join-ss,var(--radius-field));border-start-end-radius:var(--join-se,var(--radius-field));border-end-end-radius:var(--join-ee,var(--radius-field));border-end-start-radius:var(--join-es,var(--radius-field));flex-shrink:1;align-items:center;gap:.5rem;padding-inline:.75rem;font-size:.875rem;display:inline-flex;position:relative;&:where(input){display:inline-flex}& :where(input){appearance:none;background-color:#0000;border:none;width:100%;height:100%;display:inline-flex;&:focus,&:focus-within{--tw-outline-style:none;outline-style:none;@media (forced-colors:active){&{outline-offset:2px;outline:2px solid #0000}}}}& :where(input[type=url]),& :where(input[type=email]){direction:ltr}& :where(input[type=date]){display:inline-block}&:focus,&:focus-within{--input-color:var(--color-base-content);box-shadow:0 1px color-mix(in oklab,var(--input-color)calc(var(--depth)*10%),#0000);outline:2px solid var(--input-color);outline-offset:2px;isolation:isolate;z-index:1}&:has(>input[disabled]),&:is(:disabled,[disabled]){cursor:not-allowed;border-color:var(--color-base-200);background-color:var(--color-base-200);color:color-mix(in oklab,var(--color-base-content)40%,transparent);box-shadow:none;&::placeholder{color:color-mix(in oklab,var(--color-base-content)20%,transparent)}}&:has(>input[disabled])>input[disabled]{cursor:not-allowed}&::-webkit-date-and-time-value{text-align:inherit}&[type=number]{&::-webkit-inner-spin-button{margin-block:-.75rem;margin-inline-end:-.75rem}}&::-webkit-calendar-picker-indicator{position:absolute;inset-inline-end:.75em}}.md\:input-ghost{box-shadow:none;background-color:#0000;border-color:#0000;&:focus,&:focus-within{background-color:var(--color-base-100);color:var(--color-base-content);box-shadow:none;border-color:#0000}}.md\:input-neutral{&,&:focus,&:focus-within{--input-color:var(--color-neutral)}}.md\:input-primary{&,&:focus,&:focus-within{--input-color:var(--color-primary)}}.md\:input-secondary{&,&:focus,&:focus-within{--input-color:var(--color-secondary)}}.md\:input-accent{&,&:focus,&:focus-within{--input-color:var(--color-accent)}}.md\:input-info{&,&:focus,&:focus-within{--input-color:var(--color-info)}}.md\:input-success{&,&:focus,&:focus-within{--input-color:var(--color-success)}}.md\:input-warning{&,&:focus,&:focus-within{--input-color:var(--color-warning)}}.md\:input-error{&,&:focus,&:focus-within{--input-color:var(--color-error)}}.md\:input-xs{--size:calc(var(--size-field,.25rem)*6);font-size:.6875rem;&[type=number]{&::-webkit-inner-spin-button{margin-block:-.25rem;margin-inline-end:-.75rem}}}.md\:input-sm{--size:calc(var(--size-field,.25rem)*8);font-size:.75rem;&[type=number]{&::-webkit-inner-spin-button{margin-block:-.5rem;margin-inline-end:-.75rem}}}.md\:input-md{--size:calc(var(--size-field,.25rem)*10);font-size:.875rem;&[type=number]{&::-webkit-inner-spin-button{margin-block:-.75rem;margin-inline-end:-.75rem}}}.md\:input-lg{--size:calc(var(--size-field,.25rem)*12);font-size:1.125rem;&[type=number]{&::-webkit-inner-spin-button{margin-block:-.75rem;margin-inline-end:-.75rem}}}.md\:input-xl{--size:calc(var(--size-field,.25rem)*14);font-size:1.375rem;&[type=number]{&::-webkit-inner-spin-button{margin-block:-1rem;margin-inline-end:-.75rem}}}}@media (width>=1024px){.lg\:input{cursor:text;border:var(--border)solid #0000;appearance:none;background-color:var(--color-base-100);vertical-align:middle;white-space:nowrap;width:clamp(3rem,20rem,100%);height:var(--size);touch-action:manipulation;border-color:var(--input-color);box-shadow:0 1px color-mix(in oklab,var(--input-color)calc(var(--depth)*10%),#0000)inset,0 -1px oklch(100% 0 0/calc(var(--depth)*.1))inset;--size:calc(var(--size-field,.25rem)*10);--input-color:color-mix(in oklab,var(--color-base-content)20%,#0000);border-start-start-radius:var(--join-ss,var(--radius-field));border-start-end-radius:var(--join-se,var(--radius-field));border-end-end-radius:var(--join-ee,var(--radius-field));border-end-start-radius:var(--join-es,var(--radius-field));flex-shrink:1;align-items:center;gap:.5rem;padding-inline:.75rem;font-size:.875rem;display:inline-flex;position:relative;&:where(input){display:inline-flex}& :where(input){appearance:none;background-color:#0000;border:none;width:100%;height:100%;display:inline-flex;&:focus,&:focus-within{--tw-outline-style:none;outline-style:none;@media (forced-colors:active){&{outline-offset:2px;outline:2px solid #0000}}}}& :where(input[type=url]),& :where(input[type=email]){direction:ltr}& :where(input[type=date]){display:inline-block}&:focus,&:focus-within{--input-color:var(--color-base-content);box-shadow:0 1px color-mix(in oklab,var(--input-color)calc(var(--depth)*10%),#0000);outline:2px solid var(--input-color);outline-offset:2px;isolation:isolate;z-index:1}&:has(>input[disabled]),&:is(:disabled,[disabled]){cursor:not-allowed;border-color:var(--color-base-200);background-color:var(--color-base-200);color:color-mix(in oklab,var(--color-base-content)40%,transparent);box-shadow:none;&::placeholder{color:color-mix(in oklab,var(--color-base-content)20%,transparent)}}&:has(>input[disabled])>input[disabled]{cursor:not-allowed}&::-webkit-date-and-time-value{text-align:inherit}&[type=number]{&::-webkit-inner-spin-button{margin-block:-.75rem;margin-inline-end:-.75rem}}&::-webkit-calendar-picker-indicator{position:absolute;inset-inline-end:.75em}}.lg\:input-ghost{box-shadow:none;background-color:#0000;border-color:#0000;&:focus,&:focus-within{background-color:var(--color-base-100);color:var(--color-base-content);box-shadow:none;border-color:#0000}}.lg\:input-neutral{&,&:focus,&:focus-within{--input-color:var(--color-neutral)}}.lg\:input-primary{&,&:focus,&:focus-within{--input-color:var(--color-primary)}}.lg\:input-secondary{&,&:focus,&:focus-within{--input-color:var(--color-secondary)}}.lg\:input-accent{&,&:focus,&:focus-within{--input-color:var(--color-accent)}}.lg\:input-info{&,&:focus,&:focus-within{--input-color:var(--color-info)}}.lg\:input-success{&,&:focus,&:focus-within{--input-color:var(--color-success)}}.lg\:input-warning{&,&:focus,&:focus-within{--input-color:var(--color-warning)}}.lg\:input-error{&,&:focus,&:focus-within{--input-color:var(--color-error)}}.lg\:input-xs{--size:calc(var(--size-field,.25rem)*6);font-size:.6875rem;&[type=number]{&::-webkit-inner-spin-button{margin-block:-.25rem;margin-inline-end:-.75rem}}}.lg\:input-sm{--size:calc(var(--size-field,.25rem)*8);font-size:.75rem;&[type=number]{&::-webkit-inner-spin-button{margin-block:-.5rem;margin-inline-end:-.75rem}}}.lg\:input-md{--size:calc(var(--size-field,.25rem)*10);font-size:.875rem;&[type=number]{&::-webkit-inner-spin-button{margin-block:-.75rem;margin-inline-end:-.75rem}}}.lg\:input-lg{--size:calc(var(--size-field,.25rem)*12);font-size:1.125rem;&[type=number]{&::-webkit-inner-spin-button{margin-block:-.75rem;margin-inline-end:-.75rem}}}.lg\:input-xl{--size:calc(var(--size-field,.25rem)*14);font-size:1.375rem;&[type=number]{&::-webkit-inner-spin-button{margin-block:-1rem;margin-inline-end:-.75rem}}}}@media (width>=1280px){.xl\:input{cursor:text;border:var(--border)solid #0000;appearance:none;background-color:var(--color-base-100);vertical-align:middle;white-space:nowrap;width:clamp(3rem,20rem,100%);height:var(--size);touch-action:manipulation;border-color:var(--input-color);box-shadow:0 1px color-mix(in oklab,var(--input-color)calc(var(--depth)*10%),#0000)inset,0 -1px oklch(100% 0 0/calc(var(--depth)*.1))inset;--size:calc(var(--size-field,.25rem)*10);--input-color:color-mix(in oklab,var(--color-base-content)20%,#0000);border-start-start-radius:var(--join-ss,var(--radius-field));border-start-end-radius:var(--join-se,var(--radius-field));border-end-end-radius:var(--join-ee,var(--radius-field));border-end-start-radius:var(--join-es,var(--radius-field));flex-shrink:1;align-items:center;gap:.5rem;padding-inline:.75rem;font-size:.875rem;display:inline-flex;position:relative;&:where(input){display:inline-flex}& :where(input){appearance:none;background-color:#0000;border:none;width:100%;height:100%;display:inline-flex;&:focus,&:focus-within{--tw-outline-style:none;outline-style:none;@media (forced-colors:active){&{outline-offset:2px;outline:2px solid #0000}}}}& :where(input[type=url]),& :where(input[type=email]){direction:ltr}& :where(input[type=date]){display:inline-block}&:focus,&:focus-within{--input-color:var(--color-base-content);box-shadow:0 1px color-mix(in oklab,var(--input-color)calc(var(--depth)*10%),#0000);outline:2px solid var(--input-color);outline-offset:2px;isolation:isolate;z-index:1}&:has(>input[disabled]),&:is(:disabled,[disabled]){cursor:not-allowed;border-color:var(--color-base-200);background-color:var(--color-base-200);color:color-mix(in oklab,var(--color-base-content)40%,transparent);box-shadow:none;&::placeholder{color:color-mix(in oklab,var(--color-base-content)20%,transparent)}}&:has(>input[disabled])>input[disabled]{cursor:not-allowed}&::-webkit-date-and-time-value{text-align:inherit}&[type=number]{&::-webkit-inner-spin-button{margin-block:-.75rem;margin-inline-end:-.75rem}}&::-webkit-calendar-picker-indicator{position:absolute;inset-inline-end:.75em}}.xl\:input-ghost{box-shadow:none;background-color:#0000;border-color:#0000;&:focus,&:focus-within{background-color:var(--color-base-100);color:var(--color-base-content);box-shadow:none;border-color:#0000}}.xl\:input-neutral{&,&:focus,&:focus-within{--input-color:var(--color-neutral)}}.xl\:input-primary{&,&:focus,&:focus-within{--input-color:var(--color-primary)}}.xl\:input-secondary{&,&:focus,&:focus-within{--input-color:var(--color-secondary)}}.xl\:input-accent{&,&:focus,&:focus-within{--input-color:var(--color-accent)}}.xl\:input-info{&,&:focus,&:focus-within{--input-color:var(--color-info)}}.xl\:input-success{&,&:focus,&:focus-within{--input-color:var(--color-success)}}.xl\:input-warning{&,&:focus,&:focus-within{--input-color:var(--color-warning)}}.xl\:input-error{&,&:focus,&:focus-within{--input-color:var(--color-error)}}.xl\:input-xs{--size:calc(var(--size-field,.25rem)*6);font-size:.6875rem;&[type=number]{&::-webkit-inner-spin-button{margin-block:-.25rem;margin-inline-end:-.75rem}}}.xl\:input-sm{--size:calc(var(--size-field,.25rem)*8);font-size:.75rem;&[type=number]{&::-webkit-inner-spin-button{margin-block:-.5rem;margin-inline-end:-.75rem}}}.xl\:input-md{--size:calc(var(--size-field,.25rem)*10);font-size:.875rem;&[type=number]{&::-webkit-inner-spin-button{margin-block:-.75rem;margin-inline-end:-.75rem}}}.xl\:input-lg{--size:calc(var(--size-field,.25rem)*12);font-size:1.125rem;&[type=number]{&::-webkit-inner-spin-button{margin-block:-.75rem;margin-inline-end:-.75rem}}}.xl\:input-xl{--size:calc(var(--size-field,.25rem)*14);font-size:1.375rem;&[type=number]{&::-webkit-inner-spin-button{margin-block:-1rem;margin-inline-end:-.75rem}}}}@media (width>=1536px){.\32 xl\:input{cursor:text;border:var(--border)solid #0000;appearance:none;background-color:var(--color-base-100);vertical-align:middle;white-space:nowrap;width:clamp(3rem,20rem,100%);height:var(--size);touch-action:manipulation;border-color:var(--input-color);box-shadow:0 1px color-mix(in oklab,var(--input-color)calc(var(--depth)*10%),#0000)inset,0 -1px oklch(100% 0 0/calc(var(--depth)*.1))inset;--size:calc(var(--size-field,.25rem)*10);--input-color:color-mix(in oklab,var(--color-base-content)20%,#0000);border-start-start-radius:var(--join-ss,var(--radius-field));border-start-end-radius:var(--join-se,var(--radius-field));border-end-end-radius:var(--join-ee,var(--radius-field));border-end-start-radius:var(--join-es,var(--radius-field));flex-shrink:1;align-items:center;gap:.5rem;padding-inline:.75rem;font-size:.875rem;display:inline-flex;position:relative;&:where(input){display:inline-flex}& :where(input){appearance:none;background-color:#0000;border:none;width:100%;height:100%;display:inline-flex;&:focus,&:focus-within{--tw-outline-style:none;outline-style:none;@media (forced-colors:active){&{outline-offset:2px;outline:2px solid #0000}}}}& :where(input[type=url]),& :where(input[type=email]){direction:ltr}& :where(input[type=date]){display:inline-block}&:focus,&:focus-within{--input-color:var(--color-base-content);box-shadow:0 1px color-mix(in oklab,var(--input-color)calc(var(--depth)*10%),#0000);outline:2px solid var(--input-color);outline-offset:2px;isolation:isolate;z-index:1}&:has(>input[disabled]),&:is(:disabled,[disabled]){cursor:not-allowed;border-color:var(--color-base-200);background-color:var(--color-base-200);color:color-mix(in oklab,var(--color-base-content)40%,transparent);box-shadow:none;&::placeholder{color:color-mix(in oklab,var(--color-base-content)20%,transparent)}}&:has(>input[disabled])>input[disabled]{cursor:not-allowed}&::-webkit-date-and-time-value{text-align:inherit}&[type=number]{&::-webkit-inner-spin-button{margin-block:-.75rem;margin-inline-end:-.75rem}}&::-webkit-calendar-picker-indicator{position:absolute;inset-inline-end:.75em}}.\32 xl\:input-ghost{box-shadow:none;background-color:#0000;border-color:#0000;&:focus,&:focus-within{background-color:var(--color-base-100);color:var(--color-base-content);box-shadow:none;border-color:#0000}}.\32 xl\:input-neutral{&,&:focus,&:focus-within{--input-color:var(--color-neutral)}}.\32 xl\:input-primary{&,&:focus,&:focus-within{--input-color:var(--color-primary)}}.\32 xl\:input-secondary{&,&:focus,&:focus-within{--input-color:var(--color-secondary)}}.\32 xl\:input-accent{&,&:focus,&:focus-within{--input-color:var(--color-accent)}}.\32 xl\:input-info{&,&:focus,&:focus-within{--input-color:var(--color-info)}}.\32 xl\:input-success{&,&:focus,&:focus-within{--input-color:var(--color-success)}}.\32 xl\:input-warning{&,&:focus,&:focus-within{--input-color:var(--color-warning)}}.\32 xl\:input-error{&,&:focus,&:focus-within{--input-color:var(--color-error)}}.\32 xl\:input-xs{--size:calc(var(--size-field,.25rem)*6);font-size:.6875rem;&[type=number]{&::-webkit-inner-spin-button{margin-block:-.25rem;margin-inline-end:-.75rem}}}.\32 xl\:input-sm{--size:calc(var(--size-field,.25rem)*8);font-size:.75rem;&[type=number]{&::-webkit-inner-spin-button{margin-block:-.5rem;margin-inline-end:-.75rem}}}.\32 xl\:input-md{--size:calc(var(--size-field,.25rem)*10);font-size:.875rem;&[type=number]{&::-webkit-inner-spin-button{margin-block:-.75rem;margin-inline-end:-.75rem}}}.\32 xl\:input-lg{--size:calc(var(--size-field,.25rem)*12);font-size:1.125rem;&[type=number]{&::-webkit-inner-spin-button{margin-block:-.75rem;margin-inline-end:-.75rem}}}.\32 xl\:input-xl{--size:calc(var(--size-field,.25rem)*14);font-size:1.375rem;&[type=number]{&::-webkit-inner-spin-button{margin-block:-1rem;margin-inline-end:-.75rem}}}}}