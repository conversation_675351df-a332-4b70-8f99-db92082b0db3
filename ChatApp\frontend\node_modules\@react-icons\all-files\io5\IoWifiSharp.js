// THIS FILE IS AUTO GENERATED
var GenIcon = require('../lib').GenIcon
module.exports.IoWifiSharp = function IoWifiSharp (props) {
  return GenIcon({"tag":"svg","attr":{"viewBox":"0 0 512 512"},"child":[{"tag":"path","attr":{"fill":"none","strokeLinecap":"square","strokeLinejoin":"round","strokeWidth":"42","d":"M332.69 320a115 115 0 00-152.8 0m213.85-61a201.26 201.26 0 00-274.92 0M448 191.52a288 288 0 00-383.44 0"}},{"tag":"path","attr":{"d":"M300.67 384L256 433l-44.34-49a56.73 56.73 0 0188.92 0z"}}]})(props);
};
