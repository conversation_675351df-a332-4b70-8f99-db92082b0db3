(()=>{var $;function O(B){return O=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(G){return typeof G}:function(G){return G&&typeof Symbol=="function"&&G.constructor===Symbol&&G!==Symbol.prototype?"symbol":typeof G},O(B)}function x(B,G){var H=Object.keys(B);if(Object.getOwnPropertySymbols){var J=Object.getOwnPropertySymbols(B);G&&(J=J.filter(function(X){return Object.getOwnPropertyDescriptor(B,X).enumerable})),H.push.apply(H,J)}return H}function q(B){for(var G=1;G<arguments.length;G++){var H=arguments[G]!=null?arguments[G]:{};G%2?x(Object(H),!0).forEach(function(J){E(B,J,H[J])}):Object.getOwnPropertyDescriptors?Object.defineProperties(B,Object.getOwnPropertyDescriptors(H)):x(Object(H)).forEach(function(J){Object.defineProperty(B,J,Object.getOwnPropertyDescriptor(H,J))})}return B}function E(B,G,H){if(G=N(G),G in B)Object.defineProperty(B,G,{value:H,enumerable:!0,configurable:!0,writable:!0});else B[G]=H;return B}function N(B){var G=z(B,"string");return O(G)=="symbol"?G:String(G)}function z(B,G){if(O(B)!="object"||!B)return B;var H=B[Symbol.toPrimitive];if(H!==void 0){var J=H.call(B,G||"default");if(O(J)!="object")return J;throw new TypeError("@@toPrimitive must return a primitive value.")}return(G==="string"?String:Number)(B)}var W=Object.defineProperty,HB=function B(G,H){for(var J in H)W(G,J,{get:H[J],enumerable:!0,configurable:!0,set:function X(Y){return H[J]=function(){return Y}}})},S={lessThanXSeconds:{one:"\u5C11\u65BC 1 \u79D2",other:"\u5C11\u65BC {{count}} \u79D2"},xSeconds:{one:"1 \u79D2",other:"{{count}} \u79D2"},halfAMinute:"\u534A\u5206\u9418",lessThanXMinutes:{one:"\u5C11\u65BC 1 \u5206\u9418",other:"\u5C11\u65BC {{count}} \u5206\u9418"},xMinutes:{one:"1 \u5206\u9418",other:"{{count}} \u5206\u9418"},xHours:{one:"1 \u5C0F\u6642",other:"{{count}} \u5C0F\u6642"},aboutXHours:{one:"\u5927\u7D04 1 \u5C0F\u6642",other:"\u5927\u7D04 {{count}} \u5C0F\u6642"},xDays:{one:"1 \u5929",other:"{{count}} \u5929"},aboutXWeeks:{one:"\u5927\u7D04 1 \u500B\u661F\u671F",other:"\u5927\u7D04 {{count}} \u500B\u661F\u671F"},xWeeks:{one:"1 \u500B\u661F\u671F",other:"{{count}} \u500B\u661F\u671F"},aboutXMonths:{one:"\u5927\u7D04 1 \u500B\u6708",other:"\u5927\u7D04 {{count}} \u500B\u6708"},xMonths:{one:"1 \u500B\u6708",other:"{{count}} \u500B\u6708"},aboutXYears:{one:"\u5927\u7D04 1 \u5E74",other:"\u5927\u7D04 {{count}} \u5E74"},xYears:{one:"1 \u5E74",other:"{{count}} \u5E74"},overXYears:{one:"\u8D85\u904E 1 \u5E74",other:"\u8D85\u904E {{count}} \u5E74"},almostXYears:{one:"\u5C07\u8FD1 1 \u5E74",other:"\u5C07\u8FD1 {{count}} \u5E74"}},D=function B(G,H,J){var X,Y=S[G];if(typeof Y==="string")X=Y;else if(H===1)X=Y.one;else X=Y.other.replace("{{count}}",String(H));if(J!==null&&J!==void 0&&J.addSuffix)if(J.comparison&&J.comparison>0)return X+"\u5167";else return X+"\u524D";return X};function A(B){return function(){var G=arguments.length>0&&arguments[0]!==void 0?arguments[0]:{},H=G.width?String(G.width):B.defaultWidth,J=B.formats[H]||B.formats[B.defaultWidth];return J}}var M={full:"y'\u5E74'M'\u6708'd'\u65E5' EEEE",long:"y'\u5E74'M'\u6708'd'\u65E5'",medium:"yyyy-MM-dd",short:"yy-MM-dd"},R={full:"zzzz a h:mm:ss",long:"z a h:mm:ss",medium:"a h:mm:ss",short:"a h:mm"},L={full:"{{date}} {{time}}",long:"{{date}} {{time}}",medium:"{{date}} {{time}}",short:"{{date}} {{time}}"},V={date:A({formats:M,defaultWidth:"full"}),time:A({formats:R,defaultWidth:"full"}),dateTime:A({formats:L,defaultWidth:"full"})},j={lastWeek:"'\u4E0A\u500B'eeee p",yesterday:"'\u6628\u5929' p",today:"'\u4ECA\u5929' p",tomorrow:"'\u660E\u5929' p",nextWeek:"'\u4E0B\u500B'eeee p",other:"P"},w=function B(G,H,J,X){return j[G]};function Q(B){return function(G,H){var J=H!==null&&H!==void 0&&H.context?String(H.context):"standalone",X;if(J==="formatting"&&B.formattingValues){var Y=B.defaultFormattingWidth||B.defaultWidth,Z=H!==null&&H!==void 0&&H.width?String(H.width):Y;X=B.formattingValues[Z]||B.formattingValues[Y]}else{var U=B.defaultWidth,C=H!==null&&H!==void 0&&H.width?String(H.width):B.defaultWidth;X=B.values[C]||B.values[U]}var I=B.argumentCallback?B.argumentCallback(G):G;return X[I]}}var _={narrow:["\u524D","\u516C\u5143"],abbreviated:["\u524D","\u516C\u5143"],wide:["\u516C\u5143\u524D","\u516C\u5143"]},f={narrow:["1","2","3","4"],abbreviated:["\u7B2C\u4E00\u523B","\u7B2C\u4E8C\u523B","\u7B2C\u4E09\u523B","\u7B2C\u56DB\u523B"],wide:["\u7B2C\u4E00\u523B\u9418","\u7B2C\u4E8C\u523B\u9418","\u7B2C\u4E09\u523B\u9418","\u7B2C\u56DB\u523B\u9418"]},F={narrow:["\u4E00","\u4E8C","\u4E09","\u56DB","\u4E94","\u516D","\u4E03","\u516B","\u4E5D","\u5341","\u5341\u4E00","\u5341\u4E8C"],abbreviated:["1\u6708","2\u6708","3\u6708","4\u6708","5\u6708","6\u6708","7\u6708","8\u6708","9\u6708","10\u6708","11\u6708","12\u6708"],wide:["\u4E00\u6708","\u4E8C\u6708","\u4E09\u6708","\u56DB\u6708","\u4E94\u6708","\u516D\u6708","\u4E03\u6708","\u516B\u6708","\u4E5D\u6708","\u5341\u6708","\u5341\u4E00\u6708","\u5341\u4E8C\u6708"]},v={narrow:["\u65E5","\u4E00","\u4E8C","\u4E09","\u56DB","\u4E94","\u516D"],short:["\u65E5","\u4E00","\u4E8C","\u4E09","\u56DB","\u4E94","\u516D"],abbreviated:["\u9031\u65E5","\u9031\u4E00","\u9031\u4E8C","\u9031\u4E09","\u9031\u56DB","\u9031\u4E94","\u9031\u516D"],wide:["\u661F\u671F\u65E5","\u661F\u671F\u4E00","\u661F\u671F\u4E8C","\u661F\u671F\u4E09","\u661F\u671F\u56DB","\u661F\u671F\u4E94","\u661F\u671F\u516D"]},P={narrow:{am:"\u4E0A",pm:"\u4E0B",midnight:"\u51CC\u6668",noon:"\u5348",morning:"\u65E9",afternoon:"\u4E0B\u5348",evening:"\u665A",night:"\u591C"},abbreviated:{am:"\u4E0A\u5348",pm:"\u4E0B\u5348",midnight:"\u51CC\u6668",noon:"\u4E2D\u5348",morning:"\u65E9\u6668",afternoon:"\u4E2D\u5348",evening:"\u665A\u4E0A",night:"\u591C\u9593"},wide:{am:"\u4E0A\u5348",pm:"\u4E0B\u5348",midnight:"\u51CC\u6668",noon:"\u4E2D\u5348",morning:"\u65E9\u6668",afternoon:"\u4E2D\u5348",evening:"\u665A\u4E0A",night:"\u591C\u9593"}},k={narrow:{am:"\u4E0A",pm:"\u4E0B",midnight:"\u51CC\u6668",noon:"\u5348",morning:"\u65E9",afternoon:"\u4E0B\u5348",evening:"\u665A",night:"\u591C"},abbreviated:{am:"\u4E0A\u5348",pm:"\u4E0B\u5348",midnight:"\u51CC\u6668",noon:"\u4E2D\u5348",morning:"\u65E9\u6668",afternoon:"\u4E2D\u5348",evening:"\u665A\u4E0A",night:"\u591C\u9593"},wide:{am:"\u4E0A\u5348",pm:"\u4E0B\u5348",midnight:"\u51CC\u6668",noon:"\u4E2D\u5348",morning:"\u65E9\u6668",afternoon:"\u4E2D\u5348",evening:"\u665A\u4E0A",night:"\u591C\u9593"}},h=function B(G,H){var J=Number(G);switch(H===null||H===void 0?void 0:H.unit){case"date":return J+"\u65E5";case"hour":return J+"\u6642";case"minute":return J+"\u5206";case"second":return J+"\u79D2";default:return"\u7B2C "+J}},b={ordinalNumber:h,era:Q({values:_,defaultWidth:"wide"}),quarter:Q({values:f,defaultWidth:"wide",argumentCallback:function B(G){return G-1}}),month:Q({values:F,defaultWidth:"wide"}),day:Q({values:v,defaultWidth:"wide"}),dayPeriod:Q({values:P,defaultWidth:"wide",formattingValues:k,defaultFormattingWidth:"wide"})};function T(B){return function(G){var H=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{},J=H.width,X=J&&B.matchPatterns[J]||B.matchPatterns[B.defaultMatchWidth],Y=G.match(X);if(!Y)return null;var Z=Y[0],U=J&&B.parsePatterns[J]||B.parsePatterns[B.defaultParseWidth],C=Array.isArray(U)?c(U,function(K){return K.test(Z)}):m(U,function(K){return K.test(Z)}),I;I=B.valueCallback?B.valueCallback(C):C,I=H.valueCallback?H.valueCallback(I):I;var GB=G.slice(Z.length);return{value:I,rest:GB}}}function m(B,G){for(var H in B)if(Object.prototype.hasOwnProperty.call(B,H)&&G(B[H]))return H;return}function c(B,G){for(var H=0;H<B.length;H++)if(G(B[H]))return H;return}function y(B){return function(G){var H=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{},J=G.match(B.matchPattern);if(!J)return null;var X=J[0],Y=G.match(B.parsePattern);if(!Y)return null;var Z=B.valueCallback?B.valueCallback(Y[0]):Y[0];Z=H.valueCallback?H.valueCallback(Z):Z;var U=G.slice(X.length);return{value:Z,rest:U}}}var d=/^(第\s*)?\d+(日|時|分|秒)?/i,g=/\d+/i,p={narrow:/^(前)/i,abbreviated:/^(前)/i,wide:/^(公元前|公元)/i},u={any:[/^(前)/i,/^(公元)/i]},l={narrow:/^[1234]/i,abbreviated:/^第[一二三四]刻/i,wide:/^第[一二三四]刻鐘/i},i={any:[/(1|一)/i,/(2|二)/i,/(3|三)/i,/(4|四)/i]},n={narrow:/^(一|二|三|四|五|六|七|八|九|十[二一])/i,abbreviated:/^(一|二|三|四|五|六|七|八|九|十[二一]|\d|1[12])月/i,wide:/^(一|二|三|四|五|六|七|八|九|十[二一])月/i},s={narrow:[/^一/i,/^二/i,/^三/i,/^四/i,/^五/i,/^六/i,/^七/i,/^八/i,/^九/i,/^十(?!(一|二))/i,/^十一/i,/^十二/i],any:[/^一|1/i,/^二|2/i,/^三|3/i,/^四|4/i,/^五|5/i,/^六|6/i,/^七|7/i,/^八|8/i,/^九|9/i,/^十(?!(一|二))|10/i,/^十一|11/i,/^十二|12/i]},o={narrow:/^[一二三四五六日]/i,short:/^[一二三四五六日]/i,abbreviated:/^週[一二三四五六日]/i,wide:/^星期[一二三四五六日]/i},r={any:[/日/i,/一/i,/二/i,/三/i,/四/i,/五/i,/六/i]},a={any:/^(上午?|下午?|午夜|[中正]午|早上?|下午|晚上?|凌晨)/i},e={any:{am:/^上午?/i,pm:/^下午?/i,midnight:/^午夜/i,noon:/^[中正]午/i,morning:/^早上/i,afternoon:/^下午/i,evening:/^晚上?/i,night:/^凌晨/i}},t={ordinalNumber:y({matchPattern:d,parsePattern:g,valueCallback:function B(G){return parseInt(G,10)}}),era:T({matchPatterns:p,defaultMatchWidth:"wide",parsePatterns:u,defaultParseWidth:"any"}),quarter:T({matchPatterns:l,defaultMatchWidth:"wide",parsePatterns:i,defaultParseWidth:"any",valueCallback:function B(G){return G+1}}),month:T({matchPatterns:n,defaultMatchWidth:"wide",parsePatterns:s,defaultParseWidth:"any"}),day:T({matchPatterns:o,defaultMatchWidth:"wide",parsePatterns:r,defaultParseWidth:"any"}),dayPeriod:T({matchPatterns:a,defaultMatchWidth:"any",parsePatterns:e,defaultParseWidth:"any"})},BB={code:"zh-TW",formatDistance:D,formatLong:V,formatRelative:w,localize:b,match:t,options:{weekStartsOn:1,firstWeekContainsDate:4}};window.dateFns=q(q({},window.dateFns),{},{locale:q(q({},($=window.dateFns)===null||$===void 0?void 0:$.locale),{},{zhTW:BB})})})();

//# debugId=BF1BC0D553C106B864756E2164756E21
