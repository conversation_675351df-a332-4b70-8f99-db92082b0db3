// THIS FILE IS AUTO GENERATED
var GenIcon = require('../lib').GenIcon
module.exports.GiBlindfold = function GiBlindfold (props) {
  return GenIcon({"tag":"svg","attr":{"viewBox":"0 0 512 512"},"child":[{"tag":"path","attr":{"d":"M295.568 31.755c-88.873 1.013-164.237 83.15-146.14 154.222 3.112 1.68 6.114 3.713 8.976 6.012 94.364-20.635 186.207-37.25 274.717-69.38-4.396-11.362-8.926-26.62-15.104-32.857-38.564-42.043-81.91-58.46-122.448-57.998zm162.787 100.527c-92.984 36.365-188.555 54.132-285.513 75.08 3 4.306 5.436 8.95 6.91 13.865 16.698.56 33.29.95 49.81 1.188 2.315-11.524 9.915-22.267 22.616-27.496l.338-.14.347-.11c4.037-1.292 8.075-1.804 11.944-1.66 3.87.14 7.57.94 10.93 2.268 6.725 2.66 12.12 7.126 16.605 12.01 4.4 4.79 8.038 10.1 11.054 15.06 56.644-.994 112.656-4.228 168.79-10.304-.018-3.805-.042-7.543-.096-11.22-16.977-1.565-36.94-.35-64.217 7.667 22.82-11.948 39.826-19.518 60.78-19.31 1.03.01 2.07.038 3.122.086-.45-10.747-1.432-20.984-3.654-30.824-33.557 19.84-62.436 23.53-105.98 26.362 50.238-10.525 79.007-24.07 102.546-38.356-1.695-4.802-3.77-9.52-6.33-14.166zM132.56 199.17c-.682-.004-1.15.09-1.45.194-4.756 2.414-9.748 9.214-12.018 17.453-2.215 8.037-1.57 16.664.984 21.662 4.615 4.572 14.302 6.43 24.166 4.493 9.68-1.9 17.22-7.725 18.862-10.728.035-5.966-4.99-16.103-12.74-23.406-4.08-3.848-8.656-6.877-12.417-8.417-1.88-.77-3.444-1.11-4.63-1.217-.277-.025-.53-.036-.756-.037zm131.753 11.76c-1.675-.076-3.475.16-5.56.786-8.19 3.47-11.016 8.43-11.85 16.082-.843 7.75 1.63 18.15 6.663 27.836 5.034 9.685 12.528 18.6 20.133 23.953 7.604 5.353 14.49 6.963 20.238 5.017l5.77 17.05c-12.697 4.3-25.788.1-36.37-7.348-10.582-7.45-19.485-18.33-25.744-30.372-3.893-7.49-6.8-15.45-8.108-23.474-16.447-.24-32.96-.625-49.57-1.178-2.164 5.224-5.78 9.34-10.246 12.565 5.82 11.84 12.81 22.992 21.11 33.396l2.597 3.252-.795 4.084c-6.046 31.008-13.87 62.623-36.97 82.58 31.778 52.62 70.812 94.726 150.777 102.636 7.516-26.908 14.15-57.853 60.483-89.71l2.422-1.663 2.937.084c40.79 1.18 61.765-5.75 71.61-18.506 4.322-5.6 7.014-13.152 8.17-22.847l-39.04-.797.366-17.996 39.19.8c-.368-8.815-1.513-18.807-3.42-30.08l-1.745-10.327 36.203-.586c-1.14-6.856-3.99-16.375-8.29-25.238-6.218-12.83-15.555-24.903-19.124-27.382l-2.123-1.477c-50.237 4.848-100.406 7.483-151.02 8.347-7.65 3.924-5.706 2.888-7.813 4.068-4.162-7.43-9.574-17.904-16.11-25.02-3.27-3.56-6.693-6.154-9.968-7.45-1.584-.625-3.133-1.01-4.807-1.086zm-157.125 40.21c-6.954 14.03-14.456 30.194-22.5 46.296-9.06 18.146-18.786 36.2-29.49 51.268-8.14 11.457-16.796 21.348-26.764 27.975 9.864 13.877 17.987 25.48 24.654 35.674 4.344-12.038 9.388-24.587 14.734-37.382 11.19-26.778 23.637-54.487 33.354-79.553 5.43-14.012 9.954-27.268 12.98-38.853-2.502-1.455-4.845-3.25-6.97-5.428zm38.093 9.92c-4.485.71-9.156.97-13.766.61-3.28 12.524-8.04 26.025-13.555 40.255-9.972 25.724-22.472 53.52-33.53 79.986-11.06 26.467-20.645 51.69-24.836 71.397-2.096 9.855-2.788 18.303-2.033 24.456.114.927.3 1.68.463 2.492 3.097-2.28 6.465-4.24 10.29-5.897 10.15-4.394 22.763-7.508 35.332-9.756 12.568-2.247 24.964-3.555 34.462-3.857.97-.03 1.77-.006 2.674-.018-10.392-58.63-2.174-142.745 4.5-199.666z"}}]})(props);
};
