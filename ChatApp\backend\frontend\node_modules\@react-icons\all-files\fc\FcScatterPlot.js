// THIS FILE IS AUTO GENERATED
var GenIcon = require('../lib').GenIcon
module.exports.FcScatterPlot = function FcScatterPlot (props) {
  return GenIcon({"tag":"svg","attr":{"version":"1","viewBox":"0 0 48 48","enableBackground":"new 0 0 48 48"},"child":[{"tag":"polygon","attr":{"fill":"#CFD8DC","points":"9,39 9,6 7,6 7,41 42,41 42,39"}},{"tag":"g","attr":{"fill":"#00BCD4"},"child":[{"tag":"circle","attr":{"cx":"39","cy":"11","r":"3"}},{"tag":"circle","attr":{"cx":"31","cy":"13","r":"3"}},{"tag":"circle","attr":{"cx":"37","cy":"19","r":"3"}},{"tag":"circle","attr":{"cx":"34","cy":"26","r":"3"}},{"tag":"circle","attr":{"cx":"28","cy":"20","r":"3"}},{"tag":"circle","attr":{"cx":"26","cy":"28","r":"3"}},{"tag":"circle","attr":{"cx":"20","cy":"23","r":"3"}},{"tag":"circle","attr":{"cx":"21","cy":"33","r":"3"}},{"tag":"circle","attr":{"cx":"14","cy":"30","r":"3"}}]}]})(props);
};
