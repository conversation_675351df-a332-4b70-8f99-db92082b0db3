import { formatDistance } from "./eu/_lib/formatDistance.js";
import { formatLong } from "./eu/_lib/formatLong.js";
import { formatRelative } from "./eu/_lib/formatRelative.js";
import { localize } from "./eu/_lib/localize.js";
import { match } from "./eu/_lib/match.js";

/**
 * @category Locales
 * @summary Basque locale.
 * @language Basque
 * @iso-639-2 eus
 * <AUTHOR> [@JacobSoderblom](https://github.com/JacobSoderblom)
 */
export const eu = {
  code: "eu",
  formatDistance: formatDistance,
  formatLong: formatLong,
  formatRelative: formatRelative,
  localize: localize,
  match: match,
  options: {
    weekStartsOn: 1 /* Monday */,
    firstWeekContainsDate: 1,
  },
};

// Fallback for modularized imports:
export default eu;
