// THIS FILE IS AUTO GENERATED
var GenIcon = require('../lib').GenIcon
module.exports.BiChalkboard = function BiChalkboard (props) {
  return GenIcon({"tag":"svg","attr":{"viewBox":"0 0 24 24"},"child":[{"tag":"path","attr":{"d":"M20,3H4C2.897,3,2,3.897,2,5v11c0,1.103,0.897,2,2,2h4l-1.8,2.4l1.6,1.2l2.7-3.6h3l2.7,3.6l1.6-1.2L16,18h4 c1.103,0,2-0.897,2-2V5C22,3.897,21.103,3,20,3z M4,16V5h16l0.001,11H4z"}},{"tag":"path","attr":{"d":"M6 12H10V14H6z"}}]})(props);
};
