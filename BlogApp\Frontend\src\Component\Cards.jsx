// import * as React from 'react';
// import Card from '@mui/material/Card';
// import CardContent from '@mui/material/CardContent';
// import CardMedia from '@mui/material/CardMedia';
// import Typography from '@mui/material/Typography';
// import CardActionArea from '@mui/material/CardActionArea';
// // import { useAuth } from '../Context/AuthProvider';
// import API from '../axios';
// import{usePost } from '../Context/PostProvider';
// import { useEffect } from 'react';
// // import { useEffect } from "react";




//  function ActionAreaCard() {
// //  const {authUser}= useAuth()

//        const   {userPost,setUserPost} = usePost()



//   useEffect( () => {

//    const userPost= async ()=>{
//      const res= await API.get('/api/user/userPosts', {
//         headers:{
//             'Content-Type':'application/json',
            
           
//         }
//     })

//     // console.log(res)

//     setUserPost(res.data)
//    }

// userPost()
  
// }, [userPost])



//   return (


//     <div className=' grid grid-cols-4'>
//      {    userPost.map((post ,id)=>(

//       <Card sx={{ maxWidth: 345 }} key={id}   className='m-2 '>
//       <CardActionArea >
//        <div className=''> 
//         <CardMedia
//           component="img"
//           height="140"
//           image="https://images.unsplash.com/photo-1532614338840-ab30cf10ed36?auto=format&fit=crop&w=318"
//           alt="green iguana"
//         /></div>
//         <CardContent>
//           <Typography gutterBottom variant="h5" component="div">
//             {post.title}  
//           </Typography>
//           <Typography variant="body2" sx={{ color: 'text.secondary' }}>
//            {post.content}
//           </Typography>
//         </CardContent>
//       </CardActionArea>
//     </Card>
//      ))}
//     </div>

    
//   );
// }

// export default ActionAreaCard;


import * as React from 'react';
import { styled } from '@mui/material/styles';
import Card from '@mui/material/Card';
import CardHeader from '@mui/material/CardHeader';
import CardMedia from '@mui/material/CardMedia';
import CardContent from '@mui/material/CardContent';
import CardActions from '@mui/material/CardActions';
import Collapse from '@mui/material/Collapse';
import Avatar from '@mui/material/Avatar';
import IconButton from '@mui/material/IconButton';
import Typography from '@mui/material/Typography';
import { red } from '@mui/material/colors';
import FavoriteIcon from '@mui/icons-material/Favorite';
import ShareIcon from '@mui/icons-material/Share';
import ExpandMoreIcon from '@mui/icons-material/ExpandMore';
import MoreVertIcon from '@mui/icons-material/MoreVert';

import API from '../axios';
import{usePost } from '../Context/PostProvider';
import { useEffect } from 'react';
const ExpandMore = styled((props) => {
  const { expand, ...other } = props;
  return <IconButton {...other} />;
})(({ theme }) => ({
  marginLeft: 'auto',
  transition: theme.transitions.create('transform', {
    duration: theme.transitions.duration.shortest,
  }),
  variants: [
    {
      props: ({ expand }) => !expand,
      style: {
        transform: 'rotate(0deg)',
      },
    },
    {
      props: ({ expand }) => !!expand,
      style: {
        transform: 'rotate(180deg)',
      },
    },
  ],
}));

 function RecipeReviewCard() {
  const [expanded, setExpanded] = React.useState(false);

  const handleExpandClick = () => {
    setExpanded(!expanded);
  };


    const   {userPost,setUserPost} = usePost()



  useEffect( () => {

   const userPost= async ()=>{
     const res= await API.get('/api/user/userPosts', {
        headers:{
            'Content-Type':'application/json',
            
           
        }
    })

    // console.log(res)

    setUserPost(res.data)
   }

userPost()
  
}, [userPost])

  return (
    <Card sx={{ maxWidth: 345 }}>
      {    userPost.map((post ,id)=>(
      <CardHeader
        avatar={
          <Avatar sx={{ bgcolor: red[500] }} aria-label="recipe">
            R
          </Avatar>
        }
        action={
          <IconButton aria-label="settings">
            <MoreVertIcon />
          </IconButton>
        }
        title="Shrimp and Chorizo Paella"
        subheader="September 14, 2016"
      />
      <CardMedia
        component="img"
        height="194"
        image="/static/images/cards/paella.jpg"
        alt="Paella dish"
      />
      <CardContent>
        <Typography variant="body2" sx={{ color: 'text.secondary' }}>
          This impressive paella is a perfect party dish and a fun meal to cook
          together with your guests. Add 1 cup of frozen peas along with the mussels,
          if you like.
        </Typography>
      </CardContent>
      <CardActions disableSpacing>
        <IconButton aria-label="add to favorites">
          <FavoriteIcon />
        </IconButton>
        <IconButton aria-label="share">
          <ShareIcon />
        </IconButton>
        <ExpandMore
          expand={expanded}
          onClick={handleExpandClick}
          aria-expanded={expanded}
          aria-label="show more"
        >
          <ExpandMoreIcon />
        </ExpandMore>
      </CardActions>
    
  ))}
      
    </Card>
  );
}

export default RecipeReviewCard;