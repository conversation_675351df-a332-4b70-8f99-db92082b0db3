// THIS FILE IS AUTO GENERATED
var GenIcon = require('../lib').GenIcon
module.exports.GiSpikedTrunk = function GiSpikedTrunk (props) {
  return GenIcon({"tag":"svg","attr":{"viewBox":"0 0 512 512"},"child":[{"tag":"path","attr":{"d":"M64 5c-7.364 0-12.91 4.272-16.564 8.512s-6.05 8.94-7.75 13.045c-5.095 12.299-5.095 26.587 0 38.886 1.636 3.95 3.927 8.446 7.351 12.557-3.424 4.111-5.715 8.607-7.351 12.557-5.095 12.299-5.095 26.587 0 38.886 1.636 3.95 3.927 8.446 7.351 12.557-3.424 4.111-5.715 8.607-7.351 12.557-5.095 12.299-5.095 26.587 0 38.886 1.636 3.95 3.927 8.446 7.351 12.557-3.424 4.111-5.715 8.607-7.351 12.557-5.095 12.299-5.095 26.587 0 38.886.279.675.584 1.369.904 2.07l20.137-1.204c-1.633-2.036-3.305-5.078-4.413-7.752-3.07-7.41-3.07-17.703 0-25.114 2.044-3.784 3.577-8.6 7.686-10.443 3.549 1.891 5.993 6.417 7.686 10.443 3.07 7.41 3.07 17.703 0 25.114-1.034 2.494-2.559 5.318-4.084 7.34l21.021-1.258c4.77-12.116 4.676-26.049-.309-38.082-1.636-3.95-3.927-8.446-7.351-12.557 3.424-4.111 5.715-8.607 7.351-12.557 5.095-12.299 5.095-26.587 0-38.886-1.636-3.95-3.927-8.446-7.351-12.557 3.424-4.111 5.715-8.607 7.351-12.557 5.095-12.299 5.095-26.587 0-38.886-1.636-3.95-3.927-8.446-7.351-12.557 3.424-4.111 5.715-8.607 7.351-12.557 5.095-12.299 5.095-26.587 0-38.886-1.7-4.106-4.096-8.805-7.75-13.045C76.911 9.272 71.364 5 64 5zm384 0c-7.364 0-12.91 4.272-16.564 8.512s-6.05 8.94-7.75 13.045c-5.095 12.299-5.095 26.587 0 38.886 1.636 3.95 3.927 8.446 7.351 12.557-3.424 4.111-5.715 8.607-7.351 12.557-5.095 12.299-5.095 26.587 0 38.886 1.636 3.95 3.927 8.446 7.351 12.557-3.424 4.111-5.715 8.607-7.351 12.557-5.095 12.299-5.095 26.587 0 38.886 1.636 3.95 3.927 8.446 7.351 12.557-3.424 4.111-5.715 8.607-7.351 12.557-4.96 11.971-5.076 25.822-.381 37.894l19.06.58c1.128-2.194-1.264-4.593-2.05-6.474-3.07-7.41-3.07-17.703 0-25.114 2.043-3.784 3.576-8.6 7.685-10.443 3.549 1.891 5.993 6.417 7.686 10.443l.052.141 20.227-47.559c.619-7.966-.588-16.076-3.65-23.468-1.637-3.95-3.928-8.446-7.352-12.557 3.424-4.111 5.715-8.607 7.351-12.557 5.095-12.299 5.095-26.587 0-38.886-1.636-3.95-3.927-8.446-7.351-12.557 3.424-4.111 5.715-8.607 7.351-12.557 5.095-12.299 5.095-26.587 0-38.886-1.7-4.106-4.096-8.805-7.75-13.045C460.911 9.272 455.364 5 448 5zM64 23c3.549 1.891 5.993 6.417 7.686 10.443 3.07 7.41 3.07 17.703 0 25.114C69.642 62.34 68.109 67.157 64 69c-3.549-1.891-5.993-6.417-7.686-10.443-3.07-7.41-3.07-17.703 0-25.114C58.358 29.66 59.891 24.843 64 23zm384 0c3.549 1.891 5.993 6.417 7.686 10.443 3.07 7.41 3.07 17.703 0 25.114-2.044 3.784-3.577 8.6-7.686 10.443-3.549-1.891-5.993-6.417-7.686-10.443-3.07-7.41-3.07-17.703 0-25.114 2.044-3.784 3.577-8.6 7.686-10.443zM64 87c3.549 1.891 5.993 6.417 7.686 10.443 3.07 7.41 3.07 17.703 0 25.114-2.044 3.784-3.577 8.6-7.686 10.443-3.549-1.891-5.993-6.417-7.686-10.443-3.07-7.41-3.07-17.703 0-25.114C58.358 93.66 59.891 88.843 64 87zm384 0c3.549 1.891 5.993 6.417 7.686 10.443 3.07 7.41 3.07 17.703 0 25.114-2.044 3.784-3.577 8.6-7.686 10.443-3.549-1.891-5.993-6.417-7.686-10.443-3.07-7.41-3.07-17.703 0-25.114 2.044-3.784 3.577-8.6 7.686-10.443zM64 151c3.549 1.891 5.993 6.417 7.686 10.443 3.07 7.41 3.07 17.703 0 25.114-2.044 3.784-3.577 8.6-7.686 10.443-3.549-1.891-5.993-6.417-7.686-10.443-3.07-7.41-3.07-17.703 0-25.114 2.044-3.784 3.577-8.6 7.686-10.443zm384 0c3.549 1.891 5.993 6.417 7.686 10.443 3.07 7.41 3.07 17.703 0 25.114-2.044 3.784-3.577 8.6-7.686 10.443-3.549-1.891-5.993-6.417-7.686-10.443-3.07-7.41-3.07-17.703 0-25.114 2.044-3.784 3.577-8.6 7.686-10.443zm-119.9 65.027l-12.506 58.514c1 12.537 31.82 10.714 31.771-1.168zm-117.323 3.157l-4.564 30.658 19.01.578zm265.069 5.113l-27.727 65.197c5.03 5.346 21.027 7.626 22.666 2.133zm-92.358 4.07l-2.742 26.787 17.367.53zM113.34 247.365l-12.865 40.598c5.605 5.822 19.259 3.149 23.845-.902zm90.469 20.41l-65.07 3.895c.815 4.403 2.992 8.609 3.5 13.064.94 8.292-4.441 15.445-10.259 19.022-5.817 3.577-12.538 5.168-19.324 5.474-6.785.307-13.683-.62-19.828-4.168-6.144-3.548-11.202-11.5-10.183-19.9.697-3.502 2.024-6.909 3.105-10.32l-72.236 4.324 2.187 62.879 24.926-1.205c-6.656-17.909 7.218-35.962 22.922-38.774 14.963.366 23.105 4.552 31.84 13.918l-2.916 22.35 51.115-2.47c-.988-2.855-2.817-5.929-2.938-8.753-.402-9.553 4.387-17.886 10.784-23.39 6.396-5.504 14.422-8.809 22.73-10 8.428-1.145 18.243.476 24.717 4.16 7.612 4.45 13.393 13.01 14.353 23.568.118 3.717-1.04 7.473-1.716 11.131l26.156-1.266c-6.193-5.641-10.112-17.753-9.604-24.171l17.698-38.09zm79.326 2.416c2.411 19.252 13.366 43.67-1.635 59.004 32.798 3.04 65.627 7.18 98.392 10.912l-1.863-18.638c.298-19.186 23.504-26.377 34.229-26.055 14.381.737 28.106 9.39 30.011 24.754l.291 2.344-9.707 23.629 62.975 7.173-1.129-76.681-4.726-.145c-.796 13.624-1.156 28.466-14.825 34.393-18.535 4.397-44.877-2.379-45.46-22.14.21-4.865 3.221-9.476 5.117-13.932l-69.372-2.112c1.35 35.875-67.767 38.633-67.931 1.362-.086-1.165.276-2.286.517-3.414zm-18.182.29l-19.125 41.156c.316 6.082 7.491 8.084 12.098 8.242 6.558.03 13.176-3.363 13.459-9.207zm-101.78 46.882c-2.9 2.496-4.256 4.963-4.452 7.848l24.49 61.55 11.93-64.622c-.65-4.873-2.465-7.036-5.344-8.72-8.999-3.648-19.459-2.046-26.623 3.944zm232.97 4.348l5.341 53.416 22.286-54.24c-6.582-10.394-23.33-9.702-27.627.824zm-340.034 6.103l12.477 54.49 7.777-59.628c-8.89-4.658-16.771-2.661-20.254 5.138zm256.436 22.737l-4.377 57.61 30.67-54.614z"}}]})(props);
};
