// THIS FILE IS AUTO GENERATED
import { GenIcon } from '../lib';
export function <PERSON>i<PERSON><PERSON>Horn (props) {
  return GenIcon({"tag":"svg","attr":{"viewBox":"0 0 512 512"},"child":[{"tag":"path","attr":{"d":"M282.6 37.13c-16.6 0-35.2 7.24-49.3 16.77l-4.8 3.3-5-3.14c-8.9-5.63-18.5-8.59-28-8.81-14.5-.24-23.6 5.99-25.5 12.71-.5 1.72-.6 3.65-.2 5.9l1.4 8.31-8.2 1.99c-13.3 3.24-23.8 8.18-30.5 13.42-6.6 5.24-9.1 10.17-9.1 14.42 0 5 3.5 11 12.6 16.9 9 5.9 23 10.9 39.8 13.4l7.5 1.1.2 7.6c.1 2.8.7 5.3 1.8 7.7h.1c6 12.2 27.4 19.1 47.7 11.4 2.1-.8 4.1-1.8 6.3-3l10.7-5.8 2.4 11.9c3.9 19.1 15.3 29.9 25.3 29.9 10.2 0 21.5-10.8 25.4-30l2.1-10.5 10 3.9c2.6 1 5.5 1.6 8 1.6 6.6 0 13.1-3 18.3-9.1l3.6-4.2 5.4 1.4c14.4 3.7 28.1 3.7 37.9 1.1 9.8-2.5 14.7-7 15.9-11.4.9-3.7-.1-8.4-4.7-14.2-4.6-5.9-12.6-12-23.1-16.8l-20.3-9.2 21-7.49c10-3.58 17.8-8.91 22.4-14.22 4.6-5.29 5.9-9.95 5.1-13.75-1-4.49-5.2-8.89-13.6-11.79-8.4-2.91-20.3-3.68-32.8-.99-6.5 1.41-12.8 3.67-18.2 6.53l-5.4 2.84-4.6-3.93c-13.4-11.36-30.4-15.77-47.6-15.77zm73.6 128.47l.4 26.9a17.62 26.44 0 0 1 9.2 23.2 17.62 26.44 0 0 1-17.6 26.4 17.62 26.44 0 0 1-17.6-26.4 17.62 26.44 0 0 1 8-22.1l-.3-20.6c-4.7 2-9.8 3.1-15 3.1-1.6 0-3.2-.3-4.8-.5-2.3 6.9-5.6 13.3-9.6 18.6l1.1 45.3a17.62 31.49 0 0 1 9.5 28 17.62 31.49 0 0 1-17.6 31.5 17.62 31.49 0 0 1-17.6-31.5 17.62 31.49 0 0 1 7.7-26l-.8-33.1c-4.2 1.8-8.7 2.7-13.4 2.7-18.4 0-32.9-14.3-39.9-33.7-4.7 1.7-9.5 2.8-14.4 3.5 25 38.6 39.9 71.9 46.7 101 9.3 40.2 2.5 73.1-14.4 99.3 15.2 1.4 29.3 7.6 40.1 16.4 11.3 9.3 19.6 21.7 21.4 35.5.3 1.9.4 3.8.4 5.7 11.3-9.3 21.1-19.7 29.5-31.2 40.9-56.2 47.7-140.1 21.7-240.7-4.1-.2-8.4-.6-12.7-1.3zm-107.8 233c-2.2.1-4.4.3-6.6.6-5.5 6-11.4 11.6-17.8 16.9 8.5 2.4 16.2 6.2 22.7 11.1 11 8.4 18.9 20.1 20.3 33.3.3 2.5.3 5.1.1 7.6 8.7-3.5 17-7.4 24.8-11.8 6.9-6.7 8.6-13.5 7.6-20.7-1.1-8.1-6.5-17.1-15-24.1-9.6-7.9-22.4-13.2-36.1-12.9zm-45.1 32.8c-32.1 21.3-70.7 37.5-106.64 54.1 59.84 5.7 109.54 1.7 149.84-10.3 2.5-4.3 3.1-8.5 2.6-12.7-.8-7.2-5.5-15-13.3-20.9-7.9-6-18.4-10.1-30.1-10.2h-2.4z"}}]})(props);
};
