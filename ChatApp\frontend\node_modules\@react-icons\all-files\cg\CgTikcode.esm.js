// THIS FILE IS AUTO GENERATED
import { GenIcon } from '../lib';
export function CgTikcode (props) {
  return GenIcon({"tag":"svg","attr":{"viewBox":"0 0 24 24","fill":"none"},"child":[{"tag":"path","attr":{"fillRule":"evenodd","clipRule":"evenodd","d":"M9 5H5V9H9V5ZM3 3V11H11V3H3Z","fill":"currentColor"}},{"tag":"path","attr":{"fillRule":"evenodd","clipRule":"evenodd","d":"M19 5H15V9H19V5ZM13 3V11H21V3H13Z","fill":"currentColor"}},{"tag":"path","attr":{"fillRule":"evenodd","clipRule":"evenodd","d":"M9 15H5V19H9V15ZM3 13V21H11V13H3Z","fill":"currentColor"}},{"tag":"path","attr":{"d":"M13 13H15V21H13V13Z","fill":"currentColor"}},{"tag":"path","attr":{"d":"M16 13H18V21H16V13Z","fill":"currentColor"}},{"tag":"path","attr":{"d":"M19 13H21V21H19V13Z","fill":"currentColor"}}]})(props);
};
