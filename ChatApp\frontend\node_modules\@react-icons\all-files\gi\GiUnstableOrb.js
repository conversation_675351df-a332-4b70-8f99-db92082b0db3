// THIS FILE IS AUTO GENERATED
var GenIcon = require('../lib').GenIcon
module.exports.GiUnstableOrb = function GiUnstableOrb (props) {
  return GenIcon({"tag":"svg","attr":{"viewBox":"0 0 512 512"},"child":[{"tag":"path","attr":{"d":"M146.71 19.252l45.472 27.152-25.71 62.268c-30.323 18.782-54.614 46.39-69.26 79.223l-40.644-18 25.51-39.926-62.824-50.74v24.02L57.324 134l-28.22 44.172 61.402 27.193c-5.617 17.308-8.666 35.766-8.666 54.928 0 3.234.093 6.448.264 9.64l-25.875 7.22-36.976-25.596v22.728l33.55 23.227 31.32-8.74c2.476 15.37 6.92 30.085 13.07 43.88l-67.098 20.18 22.142 44.28-32.984 5.82v18.978l60.902-10.748-23.414-46.828 49.102-14.766c15.915 27.395 38.92 50.176 66.49 65.828l4.852 33.96-49.13 10.345 21.38 33.046h22.26l-13.175-20.36 39.64-8.347-5.54-38.774c20.844 8.55 43.652 13.28 67.552 13.28 15.134 0 29.83-1.9 43.867-5.458l80.265 28.506 23.365 31.152h23.36l-30.716-40.957 36.39-32.927-45.626-37.62c19.485-21.056 33.957-46.807 41.512-75.352l11.892 17.38 48.264-64.85v-31.303L445.12 291.11l-7.692-11.245c.705-6.43 1.078-12.957 1.078-19.572 0-32.163-8.557-62.352-23.51-88.422l21.447-27.653-16.44-47.274H450.1l-14.852-77.69H416.22l11.28 59h-70.287l-12.97 24.858c-25.058-13.436-53.684-21.067-84.07-21.067-5.948 0-11.826.3-17.624.87l-33.105-29.35 6.166-14.934-32.448-19.378h-36.45zm55.186 52.592l16.956 15.033c-9.208 2.19-18.14 5.095-26.735 8.652l9.78-23.686zm166.647 25.1h31.672l15.246 43.833-11.25 14.51c-12.103-16.544-26.977-30.935-43.948-42.475l8.28-15.87zm-108.26 3.66c88.114 0 159.746 71.6 159.746 159.677 0 88.08-71.633 159.675-159.747 159.675-19.067 0-37.36-3.36-54.324-9.506-1.37-.497-2.735-1.007-4.087-1.54l-.12-.045c-1.208-.477-2.408-.967-3.603-1.472-2.926-1.233-5.812-2.542-8.646-3.94l-.006-.047c-7.198-3.57-14.09-7.662-20.625-12.234l.016.103c-18.408-12.827-33.977-29.426-45.583-48.695l-.12-.396c-2.897-4.83-5.544-9.824-7.927-14.965l-.078.022c-6.276-13.58-10.702-28.193-12.952-43.523l.028-.01c-.916-6.205-1.48-12.524-1.656-18.94l-.016.005c-.04-1.49-.066-2.982-.066-4.482 0-16.49 2.494-32.387 7.123-47.34l.043.02c1.863-5.998 4.08-11.84 6.61-17.514l-.066-.028c10.893-24.494 27.77-45.716 48.788-61.83l.074.03c5.807-4.46 11.925-8.54 18.322-12.183l.02-.048c4.14-2.346 8.394-4.512 12.754-6.488.033-.015.067-.03.1-.047 20.126-9.156 42.472-14.262 65.995-14.262zm0 18.697c-41.134 0-78.1 17.522-103.863 45.513l13.39 27.236 64.983-56.282 57.822 50.388-60.396-25.818-39.42 40.066 34.62 11.89 8.637 52.87 99.566 77.99-112.168-54.314-67.578 7.66 73.85 59.19-62.98 10.183c24.878 22.025 57.617 35.39 93.536 35.39 13.404 0 26.367-1.86 38.643-5.337l-103.742-22.73h-.002l101.707 6.583-39.187-50.102 74.276 52.082c19.23-11.346 35.52-27.12 47.47-45.934l-121.837-98.54 100.55-19.942-67.42-79.45 101.325 71.97c-20.26-53-71.554-90.563-131.785-90.563zm-116.855 61.91c-15.28 22.538-24.2 49.745-24.2 79.07 0 5.495.323 10.91.932 16.24l85.18-13.444-3.83-36.215-51.905-13.23-6.177-32.42zm254.832 49.64l-88.53 14.94 74.44 82.034c10.946-20.047 17.166-43.056 17.166-67.543 0-10.096-1.066-19.94-3.076-29.43zm-273.447 68.88c5.875 20.247 16.163 38.6 29.757 53.963l26.385-9.84-56.143-44.124zm252.79 94.625l30.52 25.165-24.367 22.046-51.432-18.265c16.572-7.35 31.82-17.15 45.28-28.945z"}}]})(props);
};
