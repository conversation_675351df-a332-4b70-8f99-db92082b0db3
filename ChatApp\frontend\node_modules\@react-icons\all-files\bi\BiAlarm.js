// THIS FILE IS AUTO GENERATED
var GenIcon = require('../lib').GenIcon
module.exports.BiAlarm = function BiAlarm (props) {
  return GenIcon({"tag":"svg","attr":{"viewBox":"0 0 24 24"},"child":[{"tag":"path","attr":{"d":"M12,4c-4.879,0-9,4.121-9,9s4.121,9,9,9s9-4.121,9-9S16.879,4,12,4z M12,20c-3.794,0-7-3.206-7-7s3.206-7,7-7s7,3.206,7,7 S15.794,20,12,20z"}},{"tag":"path","attr":{"d":"M13 12L13 8 11 8 11 12 11 14 13 14 17 14 17 12z"}},{"tag":"path","attr":{"transform":"rotate(-45.082 19.494 4.5)","d":"M18.495 2.375H20.495V6.625H18.495z"}},{"tag":"path","attr":{"transform":"rotate(134.918 4.495 4.5)","d":"M2.378 3.5H6.613V5.5H2.378z"}}]})(props);
};
