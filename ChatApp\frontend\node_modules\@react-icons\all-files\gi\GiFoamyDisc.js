// THIS FILE IS AUTO GENERATED
var GenIcon = require('../lib').GenIcon
module.exports.GiFoamyDisc = function GiFoamyDisc (props) {
  return GenIcon({"tag":"svg","attr":{"viewBox":"0 0 512 512"},"child":[{"tag":"path","attr":{"d":"M18.36 18.36V61.93C34.627 68.63 57.354 57.75 56.34 37.66c-.428-8.467-2.997-14.816-6.8-19.3H18.362zm70.935 0c-8.762 6.123-14.738 15.86-14.117 28.154 2.822 55.843 74.69 43.358 72.61 2.193-.73-14.484-6.11-24.363-13.638-30.346H89.295zm160.44 0c-.77 2.073-1.154 4.366-1.028 6.86 1.518 30.043 40.184 23.326 39.064 1.176-.153-3.05-.69-5.72-1.53-8.035h-36.504zm-39.6 14.732c-13.868-.192-28.148 9.758-27.35 25.554 2.04 40.344 53.96 31.326 52.457 1.59-.956-18.91-12.87-26.975-25.107-27.144zm72.136 30.902c-18.84-.26-38.24 13.26-37.157 34.727.16 3.18.553 6.13 1.145 8.862-13.22-.435-27.03 8.986-26.27 24.068 1.228 24.295 21.65 29.56 35.858 22.89-3.048 5.303-7.85 10.39-14.81 14.89-30.926 20-71.9-25.052-41.349-54.955-17.843-15.657-45.15-14.958-64.37-2.758-11.738-25.242-52.11-14.796-50.764 11.836.955 18.904 12.577 27.248 24.785 27.836 14.04-3.622 30.87 3.156 31.998 25.49 1.277 25.31-37.17 35.205-48.2 11.073-14.592 6.695-25.635 20.36-24.71 38.668 1.84 36.44 27.72 49.087 51.076 45.316-1.726 10.21-2.637 20.695-2.637 31.396 0 103.293 83.735 187.025 187.028 187.025 103.293 0 187.025-83.734 187.025-187.025 0-86.036-58.096-158.493-137.197-180.305-5.765-19.033-22.106-25.988-37.472-24.066-1.99-24.32-17.772-34.743-33.977-34.966zM41.09 120.23c-8.12-.113-16.354 2.547-22.73 7.426v51.614c19.334 16.46 55.685 3.082 54.27-24.944-1.2-23.755-16.17-33.886-31.54-34.097zm305.99 23.813c4.212-.068 8.445.638 12.496 1.986-12.756 1.485-24.682 11.16-23.945 25.75 1.74 34.452 39.317 33.438 50.296 14.275-1.235 7.157-4.886 14.728-11.713 22.28-28.037 31.018-88.545-11.576-50.51-53.66 6.57-7.27 14.926-10.495 23.377-10.632zm-74.404 41.64c11.91.163 23.51 8.013 24.44 26.42 1.463 28.96-49.077 37.74-51.06-1.53-.78-15.385 13.12-25.074 26.62-24.89zm124.322 42.99c9.134.124 18.197 1.884 26.598 5.42-25.967-3.65-55.135 12.415-58.756 40.22-19.837-11.83-51.45 1.51-50.13 27.628 2.172 43.01 49.663 41.154 62.476 16.634 25.55 30.17 80.996 15.122 87.332-23.23.246 2.257.45 4.556.572 6.94 3.127 61.913-79.096 90.707-119.992 51.277-30.644 12.28-71.25 1.878-79.932-42.828-18.6 10.04-47.025 3.74-48.682-29.058-1.528-30.262 41.848-43.098 58.39-18.63 16.493-20.24 47.792-28.115 72.108-17.59 13.89-11.007 32.075-17.03 50.016-16.782zm-215.773 62.143c17.442-.282 35.54 20.266 17.142 40.622-17.71 19.596-55.942-7.316-31.912-33.903 4.15-4.593 9.43-6.632 14.77-6.72zm51.447 44.147c.478.006.957.024 1.433.05-6.825 4.058-11.688 11.082-11.228 20.186 1.36 26.906 26.273 30.494 40.2 20.347-6.124 30.702-61.747 37.17-64.085-9.106-.983-19.457 16.602-31.712 33.68-31.477zm66.8 48.166c-2.646 4.508-4.096 9.84-3.792 15.856 2.55 50.456 67.483 39.175 65.605 1.977-.033-.658-.082-1.297-.137-1.934.522-.663 1.07-1.325 1.668-1.987 17.356-19.203 54.816 7.17 31.27 33.22-7.726 8.55-19.426 8.052-27.965 2.587-8.86 41.688-84.556 50.255-87.75-12.948-.845-16.75 8.347-29.605 21.1-36.773z"}}]})(props);
};
