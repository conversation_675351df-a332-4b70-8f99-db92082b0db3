// THIS FILE IS AUTO GENERATED
import { GenIcon } from '../lib';
export function BiGitBranch (props) {
  return GenIcon({"tag":"svg","attr":{"viewBox":"0 0 24 24"},"child":[{"tag":"path","attr":{"d":"M17.5,4C15.57,4,14,5.57,14,7.5c0,1.554,1.025,2.859,2.43,3.315c-0.146,0.932-0.547,1.7-1.23,2.323 C13.254,14.911,9.673,15.073,8,15.045V8.837C9.44,8.403,10.5,7.08,10.5,5.5C10.5,3.57,8.93,2,7,2S3.5,3.57,3.5,5.5 c0,1.58,1.06,2.903,2.5,3.337v6.326C4.56,15.597,3.5,16.92,3.5,18.5C3.5,20.43,5.07,22,7,22s3.5-1.57,3.5-3.5 c0-0.551-0.14-1.065-0.367-1.529c2.06-0.186,4.657-0.757,6.409-2.35c1.097-0.997,1.731-2.264,1.904-3.768 C19.915,10.438,21,9.1,21,7.5C21,5.57,19.43,4,17.5,4z M5.5,5.5C5.5,4.673,6.173,4,7,4s1.5,0.673,1.5,1.5S7.827,7,7,7 S5.5,6.327,5.5,5.5z M7,20c-0.827,0-1.5-0.673-1.5-1.5c0-0.821,0.664-1.488,1.482-1.498c0.034,0.003,0.072,0.005,0.13,0.01 C7.887,17.07,8.5,17.711,8.5,18.5C8.5,19.327,7.827,20,7,20z M17.5,9C16.673,9,16,8.327,16,7.5S16.673,6,17.5,6S19,6.673,19,7.5 S18.327,9,17.5,9z"}}]})(props);
};
