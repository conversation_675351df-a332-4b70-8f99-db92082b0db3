// THIS FILE IS AUTO GENERATED
import { GenIcon } from '../lib';
export function BiBriefcase (props) {
  return GenIcon({"tag":"svg","attr":{"viewBox":"0 0 24 24"},"child":[{"tag":"path","attr":{"d":"M20,6h-3V4c0-1.103-0.897-2-2-2H9C7.897,2,7,2.897,7,4v2H4C2.897,6,2,6.897,2,8v11c0,1.103,0.897,2,2,2h16\tc1.103,0,2-0.897,2-2V8C22,6.897,21.103,6,20,6z M15,4v2H9V4H15z M8,8h8h4v3H4V8H8z M4,19v-6h6v2h4v-2h6l0.001,6H4z"}}]})(props);
};
