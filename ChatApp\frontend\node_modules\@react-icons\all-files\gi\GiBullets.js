// THIS FILE IS AUTO GENERATED
var GenIcon = require('../lib').GenIcon
module.exports.GiBullets = function GiBullets (props) {
  return GenIcon({"tag":"svg","attr":{"viewBox":"0 0 512 512"},"child":[{"tag":"path","attr":{"d":"M162.322 17.943l-28.316 105.682 18.053 4.838 4.194-15.654 169.764 45.487 11.28-42.088-169.872-45.105 4.838-18.052 169.87 45.103 4.038-15.066L176.404 37.6l3.97-14.82-18.052-4.837zM361.336 98.7l-14.453 53.943c53.99 13.97 102.986 20.59 150.95 10.964-37.1-32.648-82.453-50.034-136.497-64.906zM128.676 145l-41.82 101.104 17.27 7.142 6.216-15.023L272.75 305.4l16.36-39.556-162.43-67.12 7.142-17.27 162.432 67.118 6.254-15.12-162.406-67.18 5.843-14.13-17.27-7.142zm186.818 105.95l-21.344 51.605c51.717 20.872 99.435 33.81 148.24 30.502-32.538-37.196-75.244-60.332-126.896-82.106zM77.107 266.067l-54.705 94.752 16.186 9.346 8.133-14.086 152.23 87.893 20.472-35.452L66.988 320.97l9.344-16.183 152.434 87.545 9.117-15.79L85.65 288.647l7.643-13.238-16.186-9.344zM248.46 395.594l-27.92 48.363c48.526 27.46 94.13 46.532 142.942 49.647-27.38-41.132-66.678-69.662-115.02-98.01z","fillRule":"evenodd"}}]})(props);
};
