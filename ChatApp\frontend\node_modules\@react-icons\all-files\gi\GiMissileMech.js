// THIS FILE IS AUTO GENERATED
var GenIcon = require('../lib').GenIcon
module.exports.GiMissileMech = function GiMissileMech (props) {
  return GenIcon({"tag":"svg","attr":{"viewBox":"0 0 512 512"},"child":[{"tag":"path","attr":{"d":"M110.35 18.398l-80.952 61.74v47.173h162.524c-15.238 16.172-24.608 38.184-24.608 62.538 0 49.97 39.442 90.097 88.11 90.097s88.107-40.126 88.107-90.097c0-24.353-9.368-46.366-24.604-62.537h163.396V80.138l-80.95-61.74h-88.6v102.978c-5.696-4.987-12.02-9.24-18.84-12.623V52.494h-75.778v55.66c-6.942 3.308-13.392 7.51-19.205 12.47V18.397h-88.6zm10.67 18.688c7.38 0 13.156 5.776 13.156 13.152 0 7.38-5.777 13.158-13.156 13.158-7.38 0-13.16-5.778-13.16-13.158s5.78-13.152 13.16-13.152zm45.662 0c7.38 0 13.156 5.776 13.156 13.152 0 7.38-5.777 13.158-13.156 13.158-7.38 0-13.16-5.778-13.16-13.158s5.78-13.152 13.16-13.152zm177.86 0c7.38 0 13.155 5.773 13.155 13.152 0 7.38-5.776 13.158-13.156 13.158-7.38 0-13.155-5.778-13.155-13.158 0-7.376 5.776-13.152 13.156-13.152zm46.16 0c7.38 0 13.155 5.773 13.155 13.152 0 7.38-5.776 13.158-13.156 13.158-7.378 0-13.155-5.778-13.155-13.158 0-7.376 5.777-13.152 13.156-13.152zM61.25 82.088c7.38 0 13.156 5.776 13.156 13.152 0 7.38-5.776 13.158-13.156 13.158-7.38 0-13.156-5.778-13.156-13.158S53.87 82.088 61.25 82.088zm45.705 0c7.38 0 13.156 5.776 13.156 13.152 0 7.38-5.775 13.158-13.155 13.158-7.38 0-13.156-5.778-13.156-13.158s5.774-13.152 13.155-13.152zm45.662 0c7.38 0 13.158 5.776 13.158 13.152 0 7.38-5.778 13.158-13.158 13.158S139.46 102.62 139.46 95.24s5.776-13.152 13.157-13.152zm205.738 0c7.38 0 13.157 5.773 13.157 13.152 0 7.38-5.777 13.158-13.157 13.158S345.2 102.62 345.2 95.24c0-7.376 5.776-13.152 13.155-13.152zm46.41 0c7.38 0 13.155 5.773 13.155 13.152 0 7.38-5.777 13.158-13.156 13.158-7.38 0-13.157-5.778-13.157-13.158 0-7.376 5.777-13.152 13.157-13.152zm45.704 0c7.38 0 13.157 5.773 13.157 13.152 0 7.38-5.78 13.158-13.158 13.158-7.38 0-13.158-5.778-13.158-13.158 0-7.376 5.777-13.152 13.157-13.152zm-195.046 39.2c9.8 0 19.12 2.093 27.57 5.843l2.045 32.16c-8.006-7.307-18.012-11.77-29.257-11.77-10.234 0-19.45 3.69-27.064 9.855l2-31.434c7.673-2.994 15.997-4.65 24.704-4.65zm-44.172 16.267l-3.867 60.785c-7.05-1.798-14.102-3.95-21.153-6.445 0-21.858 9.737-41.39 25.02-54.34zm91.303 2.666c13.567 12.89 22.062 31.266 22.062 51.675-6.127 2.41-12.255 4.46-18.383 6.177l-3.68-57.85zm-205.22 22.87v54.44h54.806c-2.288-8.845-3.513-18.123-3.513-27.682 0-9.226 1.14-18.19 3.277-26.758h-54.57zm261.608 0c2.137 8.567 3.278 17.532 3.278 26.758 0 9.56-1.224 18.837-3.513 27.683h54.084v-54.44h-53.847zm-103.16 3.12c13.253 0 25.927 12.614 29.205 31.124h-58.41c3.278-18.51 15.952-31.125 29.205-31.125zm-143.887 70.01v62.032c5.21-2.606 10.862-4.078 16.874-4.078 6.013 0 11.666 1.472 16.876 4.08V236.22h-33.75zm252.58 0v62.034c5.21-2.607 10.863-4.08 16.876-4.08 6.012 0 11.665 1.473 16.875 4.08V236.22h-33.75zM128.77 312.862c-8.27 0-17.002 5.945-24.237 18.412-7.235 12.467-12.123 30.715-12.123 50.996 0 20.283 4.888 38.53 12.123 50.998 7.235 12.467 15.966 18.414 24.237 18.414 8.27 0 17.003-5.946 24.238-18.412 7.235-12.467 12.123-30.716 12.123-51 0-20.28-4.887-38.528-12.122-50.995-7.235-12.467-15.968-18.412-24.238-18.412zm252.582 0c-8.27 0-17.002 5.945-24.237 18.412-7.235 12.467-12.125 30.715-12.125 50.996 0 20.283 4.89 38.53 12.125 50.998 7.235 12.467 15.966 18.414 24.237 18.414 8.27 0 17-5.946 24.236-18.412 7.235-12.467 12.123-30.716 12.123-51 0-20.28-4.887-38.528-12.122-50.995-7.235-12.467-15.966-18.412-24.236-18.412zM98.664 456.4L65.21 489.855h127.12L158.875 456.4c-8.298 8.535-18.537 13.97-30.105 13.97-11.568 0-21.81-5.435-30.106-13.97zm252.582 0l-33.455 33.455h127.122L411.457 456.4c-8.298 8.535-18.537 13.97-30.105 13.97-11.568 0-21.808-5.435-30.106-13.97z"}}]})(props);
};
