// THIS FILE IS AUTO GENERATED
var GenIcon = require('../lib').GenIcon
module.exports.GiFrozenArrow = function GiFrozenArrow (props) {
  return GenIcon({"tag":"svg","attr":{"viewBox":"0 0 512 512"},"child":[{"tag":"path","attr":{"d":"M16.375 29.063V52l176.938 102.28 51.718-37.874 7.19-5.25 5.593 6.906 161.75 199.75 15.343 18.907-24.062-3.814-159.906-25.375.156-.842h-5.47l-88.81-14.094-8.845-1.375 1-8.908 7.5-65.156-140.095-81.094v63.563l24.5 141.03L66.938 189.97l22.187 106 17.344-83.408 25.53 14.75L122.062 312l.282.03 22.844 124.283 10.687-58.47 19.97 117 28.405-167.468 21.594 117.438 20.312-111.157 17.72 36 16-32.656 18.843 3 22.06 132.938 20.814-126.157 12.812 2.033 24.53 62.968 14.752-38.092 18.093 98.375 17.595-96.313 16.78 23.5 22.97-32.28 25.53 4.03-15.155-18.656.22-.28-.44-.002L256.97 78.406l-68.345 50.22L16.375 29.06zm0 44.53V114.5l154.75 89.53 5.313 3.095-.72 6.063-7.124 62.375 221.125 35.062-44.876-55.406-9.625 31.31-20.408-66.124-13.28 46.53-19.282-72.28-16.28 79.094-27.94-110.22-15.53 51.876-13.906-46.53-12.406 67.655-12.125-66L16.375 73.595z"}}]})(props);
};
