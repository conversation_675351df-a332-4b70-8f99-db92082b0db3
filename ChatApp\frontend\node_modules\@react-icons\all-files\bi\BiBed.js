// THIS FILE IS AUTO GENERATED
var GenIcon = require('../lib').GenIcon
module.exports.BiBed = function BiBed (props) {
  return GenIcon({"tag":"svg","attr":{"viewBox":"0 0 24 24"},"child":[{"tag":"path","attr":{"d":"M20,9.557V7V6V3h-2v2h-5h-2H6V3H4v3v1v2.557C2.81,10.25,2,11.525,2,13v4c0,0.553,0.448,1,1,1h1v4h2v-4h12v4h2v-4h1 c0.553,0,1-0.447,1-1v-4C22,11.525,21.189,10.25,20,9.557z M18,7v2h-5V7H18z M6,7h5v2H6V7z M20,16h-2H4v-3c0-1.103,0.897-2,2-2h12 c1.103,0,2,0.897,2,2V16z"}}]})(props);
};
