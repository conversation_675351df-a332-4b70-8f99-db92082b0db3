// THIS FILE IS AUTO GENERATED
var GenIcon = require('../lib').GenIcon
module.exports.BiImages = function BiImages (props) {
  return GenIcon({"tag":"svg","attr":{"viewBox":"0 0 24 24"},"child":[{"tag":"path","attr":{"d":"M20,2H8C6.897,2,6,2.897,6,4v12c0,1.103,0.897,2,2,2h12c1.103,0,2-0.897,2-2V4C22,2.897,21.103,2,20,2z M8,16V4h12 l0.002,12H8z"}},{"tag":"path","attr":{"d":"M4,8H2v12c0,1.103,0.897,2,2,2h12v-2H4V8z"}},{"tag":"path","attr":{"d":"M12 12L11 11 9 14 19 14 15 8z"}}]})(props);
};
