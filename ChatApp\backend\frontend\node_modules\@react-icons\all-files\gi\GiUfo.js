// THIS FILE IS AUTO GENERATED
var GenIcon = require('../lib').GenIcon
module.exports.GiUfo = function GiUfo (props) {
  return GenIcon({"tag":"svg","attr":{"viewBox":"0 0 512 512"},"child":[{"tag":"path","attr":{"d":"M256 27c-28.334 0-54.153 8.54-73.283 22.89C163.587 64.236 151 84.874 151 108c0 8.204 1.796 15.548 4.975 21.975 2.398 5.19 5.692 9.893 9.95 13.757 1.386 1.213 2.82 2.35 4.294 3.43 4.322 3.357 9.202 6.14 14.473 8.31 19.476 8.01 44.305 10 71.307 10 3.78 0 7.51-.045 11.197-.134 8.767-.154 17.47-.64 25.965-1.713 12.52-1.48 24.056-4.003 34.145-8.154 3.58-1.473 6.982-3.23 10.152-5.257 1.413-.782 2.815-1.59 4.192-2.45 5.74-4.175 10.267-9.775 13.512-16.132 3.15-5.776 5.153-12.34 5.688-19.644.216-2.173.32-4.358.285-6.54-.065-.015-.13-.03-.196-.048-.903-22.068-13.247-41.702-31.657-55.51C310.153 35.542 284.333 27 256 27zm0 18c24.686 0 46.868 7.578 62.482 19.29C334.097 76 343 91.36 343 108s-7.273 24.542-22.543 30.824c-15.27 6.283-38.44 8.65-64.457 8.65-26.017 0-49.187-2.367-64.457-8.65C176.273 132.542 169 124.64 169 108s8.903-32 24.518-43.71C209.132 52.577 231.314 45 256 45zm-37.775 17.748c-6.138.054-12.69 2.517-18.168 6.828-11.194 8.808-14.907 22.76-8.295 31.162 6.612 8.402 21.046 8.07 32.238-.738 11.193-8.81 14.906-22.76 8.293-31.162-3.115-3.957-8.16-6.142-14.068-6.09zm-85.29 47.78c-11.378 3.587-21.944 7.64-31.537 12.095C62.448 140.707 41 164.52 41 188c0 23.48 21.448 47.293 60.398 65.377C140.348 271.46 195.273 283 256 283c60.727 0 115.65-11.54 154.602-29.623C449.552 235.293 471 211.48 471 188c0-23.48-21.448-47.293-60.398-65.377-9.533-4.426-20.028-8.457-31.325-12.025-.997 20.097-10.243 39.685-27.293 51.935l-.222.16-.233.147c-33.465 21.076-73.328 21.37-108.768 20.252-29.29-.734-63.383-3.588-88.776-25.88l-.05-.046-.05-.045c-13.61-12.312-20.22-29.268-20.95-46.59zm290.116 47.23c7.672.046 15.3 2.61 20.97 8.28 6.98 6.978 9.254 16.924 7.92 26.265-1.335 9.34-6.04 18.522-13.577 26.06-7.538 7.538-16.72 12.242-26.06 13.576-9.34 1.334-19.287-.94-26.266-7.92-6.98-6.98-9.252-16.923-7.918-26.264 1.334-9.34 6.037-18.523 13.575-26.06 7.538-7.54 16.72-12.242 26.06-13.577 1.752-.25 3.525-.373 5.296-.362zm-336.042 1.94c1.77-.01 3.543.114 5.295.364 9.34 1.335 18.524 6.037 26.062 13.575 7.538 7.538 12.24 16.72 13.574 26.062 1.334 9.34-.94 19.284-7.92 26.263-6.978 6.98-16.92 9.25-26.262 7.916-9.34-1.336-18.525-6.037-26.063-13.575-7.538-7.538-12.24-16.722-13.574-26.063-1.333-9.34.94-19.284 7.92-26.263 5.67-5.672 13.297-8.235 20.968-8.28zm336.78 16.046c-1.078-.044-2.238.017-3.485.195-4.99.712-10.922 3.523-15.88 8.482-4.96 4.958-7.77 10.89-8.484 15.88-.713 4.99.432 8.598 2.826 10.99 2.393 2.394 6 3.54 10.992 2.827 4.99-.714 10.918-3.527 15.877-8.485 4.958-4.96 7.77-10.887 8.484-15.877.712-4.99-.434-8.6-2.827-10.992-1.795-1.795-4.274-2.888-7.506-3.022zM86.27 177.686c-3.232.133-5.71 1.226-7.504 3.02-2.394 2.394-3.54 6-2.828 10.99.712 4.992 3.527 10.923 8.486 15.882 4.958 4.96 10.886 7.77 15.877 8.483 4.99.713 8.6-.432 10.993-2.826 2.393-2.393 3.54-6 2.826-10.99s-3.525-10.922-8.483-15.88c-4.96-4.96-10.89-7.77-15.88-8.483-1.25-.177-2.41-.24-3.487-.194zM256 211c10.66 0 20.48 3.17 28.027 8.83C291.577 225.492 297 234.13 297 244c0 9.87-5.424 18.508-12.973 24.17C276.48 273.83 266.66 277 256 277c-10.66 0-20.48-3.17-28.027-8.83C220.423 262.508 215 253.87 215 244c0-9.87 5.424-18.508 12.973-24.17C235.52 214.17 245.34 211 256 211zm0 18c-7.013 0-13.194 2.204-17.227 5.23-4.033 3.023-5.773 6.385-5.773 9.77s1.74 6.747 5.773 9.77c4.033 3.026 10.214 5.23 17.227 5.23s13.194-2.204 17.227-5.23c4.033-3.023 5.773-6.385 5.773-9.77s-1.74-6.747-5.773-9.77C269.194 231.203 263.013 229 256 229zm-55.1 68.898L112 480h288l-88.9-182.102C293.433 299.925 274.988 301 256 301s-37.433-1.075-55.1-3.102z"}}]})(props);
};
