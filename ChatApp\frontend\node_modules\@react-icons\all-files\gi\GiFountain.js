// THIS FILE IS AUTO GENERATED
var GenIcon = require('../lib').GenIcon
module.exports.GiFountain = function GiFountain (props) {
  return GenIcon({"tag":"svg","attr":{"viewBox":"0 0 512 512"},"child":[{"tag":"path","attr":{"d":"M288.875 16.594c-23.342 22.17-40.225 48.12-50.5 77.906-9.354-18.433-21.854-35.043-37.438-49.844 9.606 23.365 16.495 48.275 19.688 73.78-21.13-23.87-50.358-37.07-87.344-40 20.613 12.106 38.503 27.737 51.5 46.658-41.81-13.675-85.358-15.232-130.874-5.344 28.394 2.768 55.846 8.35 81.28 17.438-37.564 4.487-74.492 15.51-110.56 34.093 39.224-6.523 78.482-7.64 115.655-3.593-49.523 25.295-90.26 62.703-122.124 112.25 28.508-23.452 59.035-43.244 90.656-58.03-24.99 43.303-38.51 91.956-41.062 145.218 11.335-30.223 25.73-59.34 42.47-85.188-7.78 41.418-6.463 84.636 3.124 128.782 5.07-52.127 18.546-101.855 40.5-144.564.826 32.724 6.57 65.49 17.937 98.625-.928-47.574 9.546-96.23 29.69-136.405 51.69-62.853 50.093 218.18-49.814 265.25H371.47c-100.415-47.31-102.88-309.304-65-265.438 20.203 40.213 30.742 88.945 29.81 136.594 11.368-33.135 17.08-65.9 17.908-98.624 21.953 42.71 35.43 92.437 40.5 144.563 9.587-44.147 10.903-87.365 3.125-128.783 16.74 25.847 31.134 54.965 42.468 85.188-2.552-53.262-16.07-101.915-41.06-145.22 31.62 14.788 62.147 34.58 90.655 58.032-31.864-49.547-72.6-86.955-122.125-112.25 37.173-4.046 76.43-2.93 115.656 3.594-36.07-18.58-72.996-29.605-110.562-34.093 25.435-9.088 52.887-14.67 81.28-17.437-45.515-9.888-89.062-8.33-130.874 5.344 12.998-18.92 30.888-34.552 51.5-46.656-26.998 2.137-49.872 9.727-68.47 22.968 19.373-25.332 42.246-47.79 67.064-66.937-40.257 9.683-75.303 28.49-104.97 56.686 3.657-25.754 10.774-50.903 20.5-74.562z"}}]})(props);
};
