// THIS FILE IS AUTO GENERATED
var GenIcon = require('../lib').GenIcon
module.exports.CgEditFlipH = function CgEditFlipH (props) {
  return GenIcon({"tag":"svg","attr":{"viewBox":"0 0 24 24","fill":"none"},"child":[{"tag":"path","attr":{"d":"M18 7C18.5523 7 19 7.44772 19 8V16C19 16.5523 18.5523 17 18 17H15V19H18C19.6569 19 21 17.6569 21 16V8C21 6.34315 19.6569 5 18 5H15V7H18Z","fill":"currentColor","fillOpacity":"0.5"}},{"tag":"path","attr":{"d":"M13 3H11V21H13V3Z","fill":"currentColor"}},{"tag":"path","attr":{"d":"M5 8C5 7.44772 5.44772 7 6 7H9V5H6C4.34315 5 3 6.34315 3 8V16C3 17.6569 4.34315 19 6 19H9V17H6C5.44772 17 5 16.5523 5 16V8Z","fill":"currentColor"}}]})(props);
};
