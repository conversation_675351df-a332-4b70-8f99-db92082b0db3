/*! 🌼 daisyUI 5.0.42 - MIT License */ @layer utilities{.indicator{width:max-content;display:inline-flex;position:relative;& :where(.indicator-item){z-index:1;white-space:nowrap;top:var(--indicator-t,0);bottom:var(--indicator-b,auto);left:var(--indicator-s,auto);right:var(--indicator-e,0);translate:var(--indicator-x,50%)var(--indicator-y,-50%);position:absolute}}.indicator-start{--indicator-s:0;--indicator-e:auto;--indicator-x:-50%;[dir=rtl] &{--indicator-s:auto;--indicator-e:0;--indicator-x:50%}}.indicator-center{--indicator-s:50%;--indicator-e:50%;--indicator-x:-50%;[dir=rtl] &{--indicator-x:50%}}.indicator-end{--indicator-s:auto;--indicator-e:0;--indicator-x:50%;[dir=rtl] &{--indicator-s:0;--indicator-e:auto;--indicator-x:-50%}}.indicator-bottom{--indicator-t:auto;--indicator-b:0;--indicator-y:50%}.indicator-middle{--indicator-t:50%;--indicator-b:50%;--indicator-y:-50%}.indicator-top{--indicator-t:0;--indicator-b:auto;--indicator-y:-50%}@media (width>=640px){.sm\:indicator{width:max-content;display:inline-flex;position:relative;& :where(.indicator-item){z-index:1;white-space:nowrap;top:var(--indicator-t,0);bottom:var(--indicator-b,auto);left:var(--indicator-s,auto);right:var(--indicator-e,0);translate:var(--indicator-x,50%)var(--indicator-y,-50%);position:absolute}}.sm\:indicator-start{--indicator-s:0;--indicator-e:auto;--indicator-x:-50%;[dir=rtl] &{--indicator-s:auto;--indicator-e:0;--indicator-x:50%}}.sm\:indicator-center{--indicator-s:50%;--indicator-e:50%;--indicator-x:-50%;[dir=rtl] &{--indicator-x:50%}}.sm\:indicator-end{--indicator-s:auto;--indicator-e:0;--indicator-x:50%;[dir=rtl] &{--indicator-s:0;--indicator-e:auto;--indicator-x:-50%}}.sm\:indicator-bottom{--indicator-t:auto;--indicator-b:0;--indicator-y:50%}.sm\:indicator-middle{--indicator-t:50%;--indicator-b:50%;--indicator-y:-50%}.sm\:indicator-top{--indicator-t:0;--indicator-b:auto;--indicator-y:-50%}}@media (width>=768px){.md\:indicator{width:max-content;display:inline-flex;position:relative;& :where(.indicator-item){z-index:1;white-space:nowrap;top:var(--indicator-t,0);bottom:var(--indicator-b,auto);left:var(--indicator-s,auto);right:var(--indicator-e,0);translate:var(--indicator-x,50%)var(--indicator-y,-50%);position:absolute}}.md\:indicator-start{--indicator-s:0;--indicator-e:auto;--indicator-x:-50%;[dir=rtl] &{--indicator-s:auto;--indicator-e:0;--indicator-x:50%}}.md\:indicator-center{--indicator-s:50%;--indicator-e:50%;--indicator-x:-50%;[dir=rtl] &{--indicator-x:50%}}.md\:indicator-end{--indicator-s:auto;--indicator-e:0;--indicator-x:50%;[dir=rtl] &{--indicator-s:0;--indicator-e:auto;--indicator-x:-50%}}.md\:indicator-bottom{--indicator-t:auto;--indicator-b:0;--indicator-y:50%}.md\:indicator-middle{--indicator-t:50%;--indicator-b:50%;--indicator-y:-50%}.md\:indicator-top{--indicator-t:0;--indicator-b:auto;--indicator-y:-50%}}@media (width>=1024px){.lg\:indicator{width:max-content;display:inline-flex;position:relative;& :where(.indicator-item){z-index:1;white-space:nowrap;top:var(--indicator-t,0);bottom:var(--indicator-b,auto);left:var(--indicator-s,auto);right:var(--indicator-e,0);translate:var(--indicator-x,50%)var(--indicator-y,-50%);position:absolute}}.lg\:indicator-start{--indicator-s:0;--indicator-e:auto;--indicator-x:-50%;[dir=rtl] &{--indicator-s:auto;--indicator-e:0;--indicator-x:50%}}.lg\:indicator-center{--indicator-s:50%;--indicator-e:50%;--indicator-x:-50%;[dir=rtl] &{--indicator-x:50%}}.lg\:indicator-end{--indicator-s:auto;--indicator-e:0;--indicator-x:50%;[dir=rtl] &{--indicator-s:0;--indicator-e:auto;--indicator-x:-50%}}.lg\:indicator-bottom{--indicator-t:auto;--indicator-b:0;--indicator-y:50%}.lg\:indicator-middle{--indicator-t:50%;--indicator-b:50%;--indicator-y:-50%}.lg\:indicator-top{--indicator-t:0;--indicator-b:auto;--indicator-y:-50%}}@media (width>=1280px){.xl\:indicator{width:max-content;display:inline-flex;position:relative;& :where(.indicator-item){z-index:1;white-space:nowrap;top:var(--indicator-t,0);bottom:var(--indicator-b,auto);left:var(--indicator-s,auto);right:var(--indicator-e,0);translate:var(--indicator-x,50%)var(--indicator-y,-50%);position:absolute}}.xl\:indicator-start{--indicator-s:0;--indicator-e:auto;--indicator-x:-50%;[dir=rtl] &{--indicator-s:auto;--indicator-e:0;--indicator-x:50%}}.xl\:indicator-center{--indicator-s:50%;--indicator-e:50%;--indicator-x:-50%;[dir=rtl] &{--indicator-x:50%}}.xl\:indicator-end{--indicator-s:auto;--indicator-e:0;--indicator-x:50%;[dir=rtl] &{--indicator-s:0;--indicator-e:auto;--indicator-x:-50%}}.xl\:indicator-bottom{--indicator-t:auto;--indicator-b:0;--indicator-y:50%}.xl\:indicator-middle{--indicator-t:50%;--indicator-b:50%;--indicator-y:-50%}.xl\:indicator-top{--indicator-t:0;--indicator-b:auto;--indicator-y:-50%}}@media (width>=1536px){.\32 xl\:indicator{width:max-content;display:inline-flex;position:relative;& :where(.indicator-item){z-index:1;white-space:nowrap;top:var(--indicator-t,0);bottom:var(--indicator-b,auto);left:var(--indicator-s,auto);right:var(--indicator-e,0);translate:var(--indicator-x,50%)var(--indicator-y,-50%);position:absolute}}.\32 xl\:indicator-start{--indicator-s:0;--indicator-e:auto;--indicator-x:-50%;[dir=rtl] &{--indicator-s:auto;--indicator-e:0;--indicator-x:50%}}.\32 xl\:indicator-center{--indicator-s:50%;--indicator-e:50%;--indicator-x:-50%;[dir=rtl] &{--indicator-x:50%}}.\32 xl\:indicator-end{--indicator-s:auto;--indicator-e:0;--indicator-x:50%;[dir=rtl] &{--indicator-s:0;--indicator-e:auto;--indicator-x:-50%}}.\32 xl\:indicator-bottom{--indicator-t:auto;--indicator-b:0;--indicator-y:50%}.\32 xl\:indicator-middle{--indicator-t:50%;--indicator-b:50%;--indicator-y:-50%}.\32 xl\:indicator-top{--indicator-t:0;--indicator-b:auto;--indicator-y:-50%}}}