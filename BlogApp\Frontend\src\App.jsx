import React from 'react'
import Signup from './Component/Signup'
import Home from './Component/Home'
import Login from './Component/Login'
import Profile from './Component/Profile'

import {Routes,Route} from 'react-router-dom'
import { useAuth } from './Context/AuthProvider'
import { Navigate } from 'react-router-dom'
import UploadBlogPost from './Pages/UploadBlogPost'


const App = () => {

  const {authUser} = useAuth()
  return (
    <div>

   {/* <Home/> */}
    <Routes>
      <Route path='/' element={authUser?<Profile/>:<Navigate to={"/login"}/>}/> 
     <Route path='/home' element={<Home/>}/>
     <Route path='/profile' element={<Profile/>}/>
      <Route path='/login' element={<Login/>}/>
      <Route path='/signup' element={<Signup/>}/>
      <Route path='/upload' element={<UploadBlogPost/>}/>
    </Routes>

  

    </div>
  )
}

export default App