// THIS FILE IS AUTO GENERATED
var GenIcon = require('../lib').GenIcon
module.exports.BiGitPullRequest = function BiGitPullRequest (props) {
  return GenIcon({"tag":"svg","attr":{"viewBox":"0 0 24 24"},"child":[{"tag":"path","attr":{"d":"M19.01 15.163V7.997C19.005 6.391 17.933 4 15 4V2l-4 3 4 3V6c1.829 0 2.001 1.539 2.01 2v7.163c-1.44.434-2.5 1.757-2.5 3.337 0 1.93 1.57 3.5 3.5 3.5s3.5-1.57 3.5-3.5C21.51 16.92 20.45 15.597 19.01 15.163zM18.01 20c-.827 0-1.5-.673-1.5-1.5s.673-1.5 1.5-1.5 1.5.673 1.5 1.5S18.837 20 18.01 20zM9.5 5.5C9.5 3.57 7.93 2 6 2S2.5 3.57 2.5 5.5c0 1.58 1.06 2.903 2.5 3.337v6.326C3.56 15.597 2.5 16.92 2.5 18.5 2.5 20.43 4.07 22 6 22s3.5-1.57 3.5-3.5c0-1.58-1.06-2.903-2.5-3.337V8.837C8.44 8.403 9.5 7.08 9.5 5.5zM4.5 5.5C4.5 4.673 5.173 4 6 4s1.5.673 1.5 1.5S6.827 7 6 7 4.5 6.327 4.5 5.5zM7.5 18.5C7.5 19.327 6.827 20 6 20s-1.5-.673-1.5-1.5S5.173 17 6 17 7.5 17.673 7.5 18.5z"}}]})(props);
};
