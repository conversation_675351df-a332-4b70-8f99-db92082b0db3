// THIS FILE IS AUTO GENERATED
var GenIcon = require('../lib').GenIcon
module.exports.GiBracer = function GiBracer (props) {
  return GenIcon({"tag":"svg","attr":{"viewBox":"0 0 512 512"},"child":[{"tag":"path","attr":{"d":"M412.6 27.246c-62.483 12.864-108.973 44.93-148.62 82.2-8.492 8.785-16.66 17.994-24.543 27.56 4.898 1.954 9.432 4.115 13.304 7.09.313-.395.7-.9 1.005-1.286 6.282-7.98 10.457-13.283 13.182-16.123l12.988 12.46c-.528.552-5.826 6.92-12.025 14.796-6.2 7.875-13.92 17.864-21.528 27.89-7.608 10.023-15.113 20.094-20.86 28.094-2.872 4-5.31 7.49-7.062 10.13-1.75 2.64-2.987 5.184-2.582 4.134l-16.793-6.48c1.2-3.11 2.415-4.648 4.375-7.603 1.082-1.63 2.37-3.5 3.774-5.506-3.99-2.19-8.23-4.21-12.563-5.67-11.845 18.36-23.12 37.345-34.032 56.61 4.74.9 9.385 2.15 13.65 4.094.547.25 1.095.51 1.64.785 2.275-3.822 4.35-7.24 6.07-9.948 1.923-3.023 3.048-4.9 4.93-6.988l13.374 12.047c.304-.336-1.358 1.838-3.113 4.598-1.756 2.76-4.004 6.458-6.542 10.745-3.152 5.323-6.773 11.605-10.516 18.203-.92 1.636-1.87 3.322-2.918 5.16-1.333 2.37-2.672 4.758-4.006 7.156-6.112 10.993-12.096 22.043-16.634 30.797-2.27 4.376-4.18 8.187-5.537 11.06-1.358 2.872-2.233 5.552-1.993 4.53l-17.52-4.128c.747-3.167 1.722-4.885 3.237-8.092 1.01-2.133 2.29-4.694 3.677-7.428-.176-.093-.336-.185-.563-.29-1.398-.654-3.906-1.24-7.11-1.456-2.58-.174-5.55-.114-8.714.024-6.96 13.054-13.88 26.046-20.812 38.855 24.224-3.728 49.8.884 72.717 11.266 25.21 11.42 47.412 29.837 61.177 52.778 9.69-11.386 19.125-22.82 28.346-34.295-6.03-7.047-12.925-13.504-20.574-19.506-3.778 5.388-12.217 17.41-12.217 17.41l-14.744-10.328s15.805-22.57 31.834-45.36c8.014-11.396 16.084-22.845 22.27-31.57 3.092-4.363 5.712-8.043 7.632-10.71.96-1.336 1.74-2.415 2.34-3.228.3-.406.554-.742.788-1.047.234-.304.034-.217 1.11-1.293l12.726 12.73c.31-.31.315-.324.395-.41-.113.145-.295.383-.54.714-.517.702-1.273 1.743-2.21 3.044-1.872 2.603-4.478 6.263-7.558 10.608-.47.662-1.203 1.71-1.693 2.402 8.655 6.093 16.436 13.453 22.96 21.498 14.668-19.693 28.886-39.478 42.85-59.322-6.27-7.52-13.506-14.377-21.59-20.72-3.778 5.388-12.22 17.408-12.22 17.408l-14.742-10.327s15.806-22.568 31.834-45.36c8.015-11.395 16.084-22.846 22.27-31.57 3.093-4.364 5.713-8.044 7.633-10.712.96-1.335 1.74-2.412 2.34-3.225.302-.406.552-.742.787-1.047.234-.304.033-.217 1.11-1.293l12.727 12.73c.305-.307.31-.32.39-.407-.112.146-.292.382-.535.71-.52.703-1.275 1.744-2.21 3.045-1.874 2.603-4.48 6.263-7.56 10.608-.47.662-1.203 1.708-1.693 2.4 8.053 5.668 15.35 12.433 21.58 19.826 14.802-21.625 29.52-43.287 44.39-64.95-8.163-14.727-13.61-35.985-17.267-59.9-3.51-22.94-4.823-47.863-3.476-70.616zm-24.935 23.988l.033 12.432c.068 26.335.24 57.675 8.26 81.712l4.045 12.127-12.78-.28c-21.215-.467-41.945-6.482-58.818-14.827-16.872-8.345-30.143-18.334-35.918-30.986l-2.78-6.09 5.032-4.412c21.065-18.48 51.315-35.886 81.126-45.766zm-17.883 25.353c-20.675 8.326-41.308 20.34-56.947 32.725 4.763 5.217 13.01 11.738 23.55 16.95 11.334 5.607 25.01 9.99 38.96 11.904-4.7-20.446-5.428-42.064-5.563-61.58zm-155.35 72.47l-19.294 31.21c8.46 2.03 15.834 5.644 22.017 9.223 2.87-4.034 5.655-7.918 11.22-15.912 3.968-5.703 7.908-11.442 10.737-15.727.11-.165.144-.23.25-.39-2.897-1.764-6.678-3.825-10.644-5.292-5.795-2.144-11.265-2.857-14.287-3.112zm143.58 69.394L338.99 246.3c14.875 11.8 27.542 25.663 36.984 42.082l15.574-23.535c-4.002-15.06-17.05-34.1-33.535-46.394zm-214.28 53.458c-2.03-.036-2.87.123-4.33.203l-17.692 36.36c3.37-.127 6.782-.277 10.246-.043 4.495.304 9.07 1.028 13.52 3.108.237.11.463.28.7.4 2.856-4.895 4.654-7.948 9.272-15.957 3.72-6.452 7.428-12.932 10.15-17.8.505-.906.755-1.39 1.186-2.17-2.534-1.152-6.38-2.25-10.494-2.98-4.125-.734-8.703-1.053-12.56-1.12zm129.925 62.125l-19.023 27.847c14.875 11.8 27.542 25.663 36.984 42.082l15.576-23.537c-4.002-15.06-17.052-34.097-33.537-46.392zM96.634 384.427c-3.352.813-6.633 1.826-9.82 3.06l-8.155 3.153c1.374 45.12 28.474 76.875 60.952 88.8 30.687 11.27 66.89 4.883 89.315-21.724l-3.803-8.79c-1.42-3.28-3.08-6.498-4.943-9.645-16.81 27.018-47.216 33.235-74.363 23.266-27.302-10.025-50.384-36.346-49.183-78.12z"}}]})(props);
};
