// THIS FILE IS AUTO GENERATED
var GenIcon = require('../lib').GenIcon
module.exports.GiThwomp = function GiThwomp (props) {
  return GenIcon({"tag":"svg","attr":{"viewBox":"0 0 512 512"},"child":[{"tag":"path","attr":{"d":"M256 30l-33.7 44.87h67.3L256 30zm103.3 0l-33.7 44.87h67.3L359.3 30zm-206.5.01l-33.7 44.86h67.2l-33.5-44.86zM61.69 61.82l9.77 38.88 29.14-29.16-38.91-9.72zm388.51 0l-38.9 9.72 29.1 29.16 9.8-38.88zM104.8 92.84l-12 11.96v302.4l12 12h302.3l12.1-12.2V104.9l-12.1-12.06H104.8zM74.7 119.1l-44.72 33.7 44.72 33.6v-67.3zm362.4 0v67.3l44.9-33.6-44.9-33.7zm-268.6 7.4h1.5c5.2 0 8.8 2 12.6 4.3 3.8 2.3 7.5 5.2 11.5 8.6 7.9 6.9 16.6 15.6 25.2 24.2 8.6 8.6 17.1 17.1 24.2 23.2 3.6 3 6.8 5.5 9.2 6.9 1.6 1 2.8 1.4 3.3 1.6.4-.2 1.6-.6 3.2-1.5 2.5-1.5 5.7-4 9.2-7 7.1-6.1 15.7-14.6 24.3-23.2 8.6-8.6 17.3-17.3 25.3-24.2 3.9-3.4 7.7-6.3 11.5-8.6s7.4-4.3 12.6-4.3h1.5l53 17.8-5.8 17-49.2-16.5c-.6.3-1.6.6-2.8 1.3-2.4 1.5-5.6 4-9.1 7-7.1 6.1-15.7 14.6-24.3 23.2l-2.3 2.3c1.5-.3 3-.4 4.5-.4 10.4 0 19.5 5.7 25.5 13.7 6 7.9 9.3 18.4 9.3 29.7s-3.3 21.8-9.3 29.7c-6 8-15.1 13.7-25.5 13.7s-19.5-5.7-25.5-13.7c-5.9-7.9-9.3-18.4-9.3-29.7 0-6.1 1-12.1 2.9-17.4-2.4 1.8-4.8 3.5-7.1 4.9-3.8 2.3-7.4 4.3-12.6 4.3-5.2 0-8.9-2-12.6-4.3-2.4-1.4-4.8-3.2-7.2-5 1.9 5.4 2.9 11.3 2.9 17.5 0 11.3-3.4 21.8-9.3 29.7-6 8-15.1 13.7-25.5 13.7s-19.5-5.7-25.5-13.7c-5.9-7.9-9.3-18.4-9.3-29.7s3.4-21.8 9.3-29.7c6-8 15.1-13.7 25.5-13.7 1.6 0 3.1.1 4.6.4l-2.3-2.3c-8.6-8.6-17.2-17.1-24.3-23.2-3.5-3.1-6.7-5.5-9.1-7-1.2-.7-2.1-1-2.7-1.3l-49.4 16.5-5.8-17 53.2-17.8zm87.5 68.8c-.2.1-.3.1 0 .1s.2 0 0-.1zM74.7 222.4L29.99 256l44.71 33.6v-67.2zm362.4 0v67.2L482 256l-44.9-33.6zm-232.8 6.2c-7.9 0-12.4 4.7-12.4 8.2 0 3.5 4.5 8.2 12.4 8.2s12.4-4.7 12.4-8.2c0-3.5-4.5-8.2-12.4-8.2zm103.3 0c-7.9 0-12.4 4.7-12.4 8.2 0 3.5 4.5 8.2 12.4 8.2s12.4-4.7 12.4-8.2c0-3.5-4.5-8.2-12.4-8.2zm-154.8 35.7c7.3 0 14.5 2.6 23.9 5.9 9.4 3.3 20.3 7.6 31 11.9 10.7 4.3 21.4 8.5 30.3 11.6 8.8 3.2 16.6 4.9 18 4.9 1.3 0 9.1-1.7 18-4.9 8.9-3.1 19.6-7.3 30.3-11.6 10.8-4.3 21.6-8.6 31-11.9 9.5-3.3 16.7-5.9 24-5.9 6.6 0 12.7 2.7 17.2 6.7 4.6 3.9 7.8 9.1 10.4 14.8 5 11.3 7.2 25.2 7.2 39.1 0 13.9-2.2 27.8-7.2 39.1-2.6 5.7-5.8 10.9-10.4 14.8-4.5 4-10.6 6.7-17.2 6.7-20.2 0-38.4-9.5-55.6-18.1-17.2-8.6-33.4-16.3-47.7-16.3-14.2 0-30.4 7.7-47.6 16.3-17.2 8.6-35.4 18.1-55.6 18.1-13.1 0-22.6-10.1-27.7-21.5-5.1-11.3-7.3-25.2-7.3-39.1 0-13.9 2.2-27.8 7.3-39.1 5.1-11.4 14.6-21.5 27.7-21.5zm0 18c-4.2 0-7.7 2.8-11.2 10.8-2.7 6-4.5 14.1-5.3 22.8h41.8v-26c-2.5-1-5-1.9-7.3-2.7-8.9-3.2-16.7-4.9-18-4.9zm206.5 0c-1.3 0-9.1 1.7-18 4.9-2.3.8-4.8 1.7-7.4 2.7v26h41.8c-.8-8.7-2.6-16.8-5.3-22.8-1.7-4-3.8-6.9-5.7-8.6-1.9-1.6-3.3-2.2-5.4-2.2zm-163.2 14.5v19.1h50.8v-.5c-4.5-1.1-9.3-2.7-14.9-4.7-9.4-3.3-20.2-7.6-31-11.9-1.6-.7-3.3-1.3-4.9-2zm119.8 0c-1.6.7-3.3 1.3-4.9 2-10.8 4.3-21.6 8.6-31 11.9-5.7 2-10.5 3.7-15.1 4.8v.4h51v-19.1zM74.7 325.6l-44.72 33.5 44.72 33.7v-67.2zm362.4 0v67.3l44.9-33.8-44.9-33.5zm-300.8 8.3c.8 8.7 2.6 16.8 5.3 22.8 3.5 8 7 10.8 11.2 10.8 7.8 0 16.3-2.4 25.3-6v-27.6h-41.8zm59.8 0v19.4l4.2-2.1c14.3-7.1 29.3-14.9 45.5-17.3h-49.7zm70.1 0c16.2 2.4 31.2 10.2 45.5 17.3l4.2 2.1v-19.4h-49.7zm67.7 0v27.6c9 3.6 17.5 6 25.4 6 2.1 0 3.5-.6 5.4-2.2 1.9-1.7 4-4.6 5.7-8.6 2.7-6 4.5-14.1 5.3-22.8h-41.8zM71.46 411.3l-9.77 38.9 38.91-9.8-29.14-29.1zm368.94 0l-29.1 29.1 38.9 9.8-9.8-38.9zM119 437.2l33.8 44.8 33.5-44.8H119zm103.3 0L256 482l33.6-44.8h-67.3zm103.3 0l33.7 44.8 33.6-44.8h-67.3z"}}]})(props);
};
