// THIS FILE IS AUTO GENERATED
import { GenIcon } from '../lib';
export function GiBeanstalk (props) {
  return GenIcon({"tag":"svg","attr":{"viewBox":"0 0 512 512"},"child":[{"tag":"path","attr":{"d":"M277.625 18.28l-.03.19c43.815 5.928 65.818 46.853 56.405 83.093-2.896 4.01-5.93 7.953-9.125 11.812-3.104 3.75-6.334 7.45-9.688 11.094 6.68-.152 13.668.15 20.875.624 9.283.61 19.004 1.59 28.844 2.562 26.745-9.698 50.2-28.826 64.063-49.937 13.548-20.635 17.76-41.997 10-59.314h-72.44c-1.88 18.198-6.547 34.794-13.405 50.188-3.814-19.43-14.214-37.48-30-50.313h-45.5zm181.25.126c6.692 22.627-.026 47.854-14.28 69.563-10.697 16.286-25.69 31.1-43.47 42.5l.875.03c21.978.764 42.687-1.078 58.813-8.656 15.16-7.125 26.88-18.75 33.875-40.656V18.406h-35.813zM196.53 61.47c-1.478-.006-2.935.045-4.374.155-20.14 1.544-35.922 13.995-41.47 30.25-6.338 18.577 2.098 41.598 26.19 53.97l8.53-16.626c-17.98-9.234-20.494-21.165-17.03-31.314 3.462-10.15 14.346-18.986 31.155-17.625 22.357 1.813 37.36 13.07 45.064 32.407 7.483 18.79 7.26 46.055-5.156 78.75-2.05 1.637-4.052 3.255-6.125 4.907-6.142 4.893-12.406 9.9-18.72 14.97 5.213-.747 10.338-1.144 15.376-1.22 1.482-.023 2.968-.028 4.436 0 11.748.222 23.045 2.02 34 4.437 37.61 8.3 71.348 23.35 116.094 15.157 22.666-5.32 41.84-16.25 58.22-31.125 22.16-20.127 38.942-47.686 50.56-77.812-7.16 7.816-15.474 13.776-24.53 18.03-20.514 9.64-44.072 11.188-67.406 10.376-11.56-.402-23.12-1.424-34.25-2.5-.312.102-.625.213-.938.313l-.125-.407c-10.95-1.065-21.484-2.174-31.186-2.813-19.856-1.308-35.957-.013-44.063 4.875l-.03-.063c-8.127 7.4-16.694 14.732-25.594 22.094 5.29-24.71 4.016-46.79-3.187-64.875-10.062-25.258-32.55-41.824-60.94-44.124-1.518-.123-3.02-.183-4.5-.187zm33.845 167.28c-8.852.132-17.864 1.37-27.344 4.188-10.568 3.14-21.832 8.313-34.06 16.343-12.443 10.887-24.955 22.415-37.376 34.75.008-2.096-.117-4.168-.344-6.186-1.712-15.185-10.186-27.355-21.188-35.063-11-7.707-24.638-11.676-37.968-11.186-13.33.49-26.343 5.43-36.03 15.47-18.76 19.434-19.104 43.565-6.44 57.186 12.666 13.62 36.54 14.862 55.345-1.875l-12.407-13.97c-13.2 11.75-23.73 9.063-29.25 3.126-5.52-5.936-7.83-16.975 6.187-31.5 11.472-11.885 35.21-12.22 49.844-1.967 7.317 5.126 12.254 12.21 13.344 21.875 1.004 8.913-1.393 20.632-10.5 35.218-3.974 4.497-7.923 9.098-11.844 13.813 22.05-8.594 43.864-9.63 66.22-10.533 42.668-1.724 88.234-2.342 152.28-48.593 13.46-3.538 24.74-3.17 32.937-.313 10.874 3.792 16.97 11.038 18.533 22.783 1.088 8.183-2.835 12.686-7.282 14.312-4.445 1.626-9.816 1.293-15.217-7.875l-16.094 9.5c8.898 15.105 25.03 20.576 37.717 15.938 12.687-4.64 21.516-18.457 19.407-34.313-2.384-17.922-14.236-32.156-30.906-37.97-4.707-1.64-9.77-2.682-15.094-3.124-.02 0-.042.003-.063 0-25.17-3.576-47.403-11.364-68.405-16-11.5-2.537-22.618-4.2-34-4.03zm41.438 84.625c-23.824 11.1-45.46 16.75-65.25 19.78-8.886 26.954-37.427 47.482-70.47 61.626-33.138 14.187-71.51 21.632-103.437 17.19-4.55 7.932-9.002 16.1-13.343 24.56v57.25H90.28c53.69-26.358 106.106-70.88 143.69-124.374 11.174-20.2 23.99-38.768 37.843-56.03zm-87.157 22.28c-9.562.76-18.652 1.12-27.344 1.47-23.177.937-43.168 2.06-62.562 10.188-12.78 5.356-25.608 13.84-39.156 27.875-4.182 6.178-8.318 12.53-12.375 19.062 25.817 1.616 58.038-4.857 85.53-16.625 25.574-10.947 46.53-26.7 56.03-41.97-.04.005-.083-.002-.124 0zM333.75 364.5c-1.094-.003-2.182.024-3.28.063-17.584.616-35.97 6.193-54.126 15.093-24.357 11.94-48.364 29.917-68.906 51.188.05-.157.106-.313.156-.47-23.996 24.686-50.768 46.182-78.72 63.407h65.032c.874-7.292 1.98-14.4 3.25-21.374 22.468-31.78 55.39-60.272 87.406-75.97 16.41-8.044 32.53-12.694 46.563-13.186 14.034-.492 25.78 2.896 35.313 11 15.122 12.856 14.897 31.182 6.312 44.5-8.585 13.318-24.29 21.298-46.78 10l-8.376 16.72c29.54 14.837 57.8 3.69 70.875-16.595 13.075-20.284 12.206-50.05-9.94-68.875-12.62-10.73-28.374-15.46-44.78-15.5z"}}]})(props);
};
