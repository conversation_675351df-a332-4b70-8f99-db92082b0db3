// THIS FILE IS AUTO GENERATED
var GenIcon = require('../lib').GenIcon
module.exports.IoTvOutline = function IoTvOutline (props) {
  return GenIcon({"tag":"svg","attr":{"viewBox":"0 0 512 512"},"child":[{"tag":"rect","attr":{"width":"448","height":"272","x":"32","y":"96","fill":"none","strokeLinejoin":"round","strokeWidth":"32","rx":"32.14","ry":"32.14"}},{"tag":"path","attr":{"strokeLinecap":"round","strokeMiterlimit":"10","strokeWidth":"32","d":"M128 416h256"}}]})(props);
};
