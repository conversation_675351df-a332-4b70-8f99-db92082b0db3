// THIS FILE IS AUTO GENERATED
import { GenIcon } from '../lib';
export function BsTable (props) {
  return GenIcon({"tag":"svg","attr":{"viewBox":"0 0 16 16","fill":"currentColor"},"child":[{"tag":"path","attr":{"fillRule":"evenodd","d":"M14 1H2a1 1 0 00-1 1v12a1 1 0 001 1h12a1 1 0 001-1V2a1 1 0 00-1-1zM2 0a2 2 0 00-2 2v12a2 2 0 002 2h12a2 2 0 002-2V2a2 2 0 00-2-2H2z","clipRule":"evenodd"}},{"tag":"path","attr":{"fillRule":"evenodd","d":"M15 4H1V3h14v1z","clipRule":"evenodd"}},{"tag":"path","attr":{"fillRule":"evenodd","d":"M5 15.5v-14h1v14H5zm5 0v-14h1v14h-1z","clipRule":"evenodd"}},{"tag":"path","attr":{"fillRule":"evenodd","d":"M15 8H1V7h14v1zm0 4H1v-1h14v1z","clipRule":"evenodd"}},{"tag":"path","attr":{"d":"M0 2a2 2 0 012-2h12a2 2 0 012 2v2H0V2z"}}]})(props);
};
