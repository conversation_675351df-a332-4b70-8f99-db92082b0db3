// THIS FILE IS AUTO GENERATED
var GenIcon = require('../lib').GenIcon
module.exports.SiTor = function SiTor (props) {
  return GenIcon({"tag":"svg","attr":{"viewBox":"0 0 24 24","role":"img"},"child":[{"tag":"title","attr":{},"child":[]},{"tag":"path","attr":{"d":"M17.578 12.201c-.76-.692-1.721-1.251-2.704-1.81-.446-.246-1.81-1.318-1.34-2.838l-.851-.358c1.342-2.078 3.085-4.134 5.229-6.056-1.721.581-3.24 1.476-4.379 3.062.67-1.407 1.765-2.793 2.972-4.201-1.654 1.185-3.084 2.525-3.979 4.313l.627-2.503c-.894 1.608-1.52 3.24-1.766 4.871l-1.317-.535-.223.178c1.162 2.078.559 3.174-.022 3.553-1.162.783-2.838 1.788-3.688 2.659-1.609 1.654-2.078 3.218-1.921 5.296.157 2.66 2.101 4.873 4.67 5.744 1.14.38 2.19.424 3.352.424 1.877 0 3.799-.491 5.207-1.676a6.551 6.551 0 0 0 2.369-5.027 6.875 6.875 0 0 0-2.236-5.096zm-3.553 8.872c-.09.402-.38.894-.737 1.341.134-.246.246-.492.313-.76.559-1.989.805-2.904.537-5.095-.045-.224-.135-.938-.471-1.721-.468-1.185-1.184-2.303-1.272-2.548-.157-.38-.38-1.989-.403-3.084.023.938.089 2.659.335 3.329.067.225.715 1.229 1.185 2.459.312.849.38 1.632.446 1.854.224 1.007-.045 2.705-.401 4.313-.111.581-.426 1.252-.828 1.766.225-.313.402-.715.537-1.185.269-.938.38-2.145.356-2.905-.021-.446-.222-1.407-.558-2.278-.201-.47-.492-.961-.692-1.297-.224-.335-.224-1.072-.313-1.921.021.916-.068 1.385.156 2.033.134.379.625.916.759 1.43.201.693.402 1.453.381 1.922 0 .536-.022 1.52-.269 2.593-.157.804-.515 1.497-1.095 1.943.246-.312.38-.625.447-.938.089-.469.111-.916.156-1.475a5.96 5.96 0 0 0-.111-1.721c-.179-.805-.469-1.608-.604-2.168.022.626.269 1.408.381 2.235.089.604.044 1.206.021 1.742-.021.627-.223 1.722-.492 2.258-.268-.112-.357-.269-.537-.491-.223-.291-.357-.604-.491-.962a5.043 5.043 0 0 1-.291-.915 3.071 3.071 0 0 1 .559-2.213c.469-.671.559-.716.715-1.497-.223.692-.379.759-.871 1.341-.559.647-.648 1.586-.648 2.346 0 .313.134.671.246 1.007.134.356.268.714.447.982.134.223.313.379.469.491-.581-.156-1.184-.379-1.564-.692-.938-.805-1.765-2.167-1.877-3.375-.089-.982.804-2.413 2.078-3.128 1.073-.626 1.318-1.319 1.542-2.459-.313.983-.626 1.833-1.654 2.348-1.475.804-2.235 2.1-2.167 3.352.112 1.586.737 2.682 2.011 3.554.291.2.693.401 1.118.559-1.587-.381-1.788-.604-2.324-1.229 0-.045-.134-.135-.134-.156-.715-.805-1.609-2.19-1.922-3.464-.112-.447-.224-.916-.089-1.363.581-2.101 1.854-2.905 3.128-3.775.313-.225.626-.426.916-.649.715-.559.894-2.012 1.05-2.838-.29 1.006-.603 2.258-1.162 2.659-.29.224-.648.402-.938.604-1.318.894-2.637 1.743-3.24 3.91-.134.56-.044.962.089 1.498.335 1.317 1.229 2.748 1.989 3.597l.134.135c.335.381.76.67 1.274.871a5.945 5.945 0 0 1-1.296-.469c-2.078-1.005-3.463-3.173-3.553-4.939-.179-3.597 1.542-4.647 3.151-5.966.894-.737 2.145-1.095 2.86-2.413.134-.291.224-.916.045-1.587-.067-.224-.402-1.028-.537-1.207l1.989.872c-.044.938-.067 1.698.112 2.391.2.76 1.184 1.854 1.586 3.129.783 2.41.583 5.561.023 8.019z"}}]})(props);
};
