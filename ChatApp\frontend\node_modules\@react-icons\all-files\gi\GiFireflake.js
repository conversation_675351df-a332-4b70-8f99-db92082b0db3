// THIS FILE IS AUTO GENERATED
var GenIcon = require('../lib').GenIcon
module.exports.GiFireflake = function GiFireflake (props) {
  return GenIcon({"tag":"svg","attr":{"viewBox":"0 0 512 512"},"child":[{"tag":"path","attr":{"d":"M253.938 16s-.116 5.61 0 14.47c.116-8.86 0-14.47 0-14.47zm0 14.47c-.465 35.427-4.82 125.15-36.594 138.31-31.437 13.024-95.04-50.706-120.28-77.905 24.6 27.913 82.115 97.995 69.092 129.438-13.138 31.722-100.9 35.99-135.78 36.437 34.88.446 122.638 4.747 135.78 36.47 13.2 31.863-46.11 97.757-70.125 122.624 24.537-24.197 89.464-83.88 121.314-70.688 31.777 13.162 36.13 101.667 36.594 136.594.453-34.932 4.746-123.502 36.562-136.594 32.305-13.293 106.245 48.2 132.844 71.72-26.05-24.104-94.274-91.384-80.906-123.657 13.058-31.53 101.93-35.98 138.125-36.47-36.192-.488-125.06-4.9-138.125-36.438-13.227-31.924 53.26-103.695 79.937-130.718-27.322 26.44-99.95 92.406-131.875 79.187-31.777-13.156-36.1-102.882-36.563-138.31zm168.437 59.124c6.673-6.457 10.72-10.656 10.72-10.656s-4.193 4.044-10.72 10.656zm58.188 167.156c9.406.127 15.437 0 15.437 0s-6.03-.128-15.438 0zm-57.22 160.125c6.012 5.562 9.75 8.844 9.75 8.844s-3.61-3.417-9.75-8.845zM253.938 481.75c-.113 8.723 0 14.25 0 14.25s.116-5.518 0-14.25zM96.03 415.844c-6.063 5.98-9.78 9.875-9.78 9.875s3.86-3.744 9.78-9.876zM30.376 256.75c-8.796-.113-14.375 0-14.375 0s5.58.113 14.375 0zM97.063 90.875c-6.478-7.35-10.813-11.938-10.813-11.938s4.162 4.772 10.813 11.938zM333.719 68.28s-.176-.063-27.314 29.376c-8.554 9.28-13.223 35.225-1.47 40.094 11.756 4.868 27.006-16.758 27.377-29.375 1.173-40.035 1.406-40.094 1.406-40.094zm-155.5.032s-.196.077 1.436 40.094c.515 12.61 15.59 34.212 27.344 29.344 11.754-4.87 7.253-30.91-1.406-40.094-27.48-29.137-27.375-29.344-27.375-29.344zm265.5 109.938s-.032.263-40.064 1.438c-3.154.09-6.9 1.12-10.625 2.78-11.17 4.987-22.43 15.748-18.78 24.563 4.87 11.755 30.844 7.117 40.125-1.436 29.445-27.14 29.344-27.344 29.344-27.344zm-375.44.03s-.075.206 29.376 27.345c9.28 8.553 35.226 13.16 40.094 1.406 3.804-9.182-8.583-20.478-20.188-25.155-3.248-1.31-6.396-2.075-9.156-2.156-40.033-1.173-40.125-1.44-40.125-1.44zm187.22 3.22c9.68-.074 19.532 1.752 29.03 5.688 38 15.74 56.024 59.345 40.283 97.343-15.74 38-59.315 56.056-97.313 40.314-37.998-15.74-56.086-59.346-40.344-97.344 11.806-28.5 39.307-45.78 68.344-46zm.5 15.72c-32.468 0-58.78 26.314-58.78 58.78 0 32.467 26.314 58.78 58.78 58.78 32.467 0 58.78-26.314 58.78-58.78 0-32.468-26.314-58.78-58.78-58.78zM122.312 297.905c-5.516.075-11.556 1.42-16.593 3.563-3.225 1.37-6.024 3.073-8.032 4.967C68.55 333.918 68.31 333.75 68.31 333.75s.113.224 40.126-1.406c12.61-.515 34.18-15.59 29.312-27.344-2.14-5.165-8.345-7.19-15.438-7.094zm266.125 0c-3.208.077-6.177.622-8.625 1.72-2.554 1.144-4.535 2.895-5.562 5.374-4.868 11.754 16.764 26.83 29.375 27.344 40.01 1.63 40.094 1.406 40.094 1.406s-.237.166-29.376-27.313c-2.295-2.164-5.63-4.104-9.438-5.562-5.133-1.965-11.12-3.097-16.47-2.97zM203.03 373.562c-1.343.054-2.71.358-4.06.907-9.645 3.92-18.913 19.3-19.314 29.155-1.63 40.01-1.437 40.063-1.437 40.063s-.11-.205 27.374-29.344c8.66-9.183 13.16-35.226 1.406-40.094-1.286-.533-2.625-.74-3.97-.688zm105.314 0c-1.148.02-2.273.263-3.375.72-11.755 4.868-7.118 30.78 1.436 40.062 27.14 29.445 27.313 29.344 27.313 29.344s-.235-.034-1.408-40.063c-.335-11.434-12.873-30.26-23.968-30.063z"}}]})(props);
};
