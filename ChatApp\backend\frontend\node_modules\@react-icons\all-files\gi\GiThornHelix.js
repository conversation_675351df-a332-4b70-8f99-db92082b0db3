// THIS FILE IS AUTO GENERATED
var GenIcon = require('../lib').GenIcon
module.exports.GiThornHelix = function GiThornHelix (props) {
  return GenIcon({"tag":"svg","attr":{"viewBox":"0 0 512 512"},"child":[{"tag":"path","attr":{"d":"M71.875 18.688c3.203 7.786 3.926 16.95 4.03 27.78.117 11.915-.877 25.668-2.468 40.126-2.693 24.49-6.377 49.535-.656 71.78-6.075 11.6-19.268 17.043-35.218 20.095 16.236 11.488 36.372 16.193 55.75 11.155 4.172 2.988 8.886 5.517 14.063 7.406 12.283 4.483 26.604 5.65 45.03 3.845.483-7.57 1.287-14.966 2.126-22.125-18.17 2.18-30.275 1.23-39.155-2-8.93-3.248-14.99-9.736-18.03-16.563-3.664-8.236-4.52-18.246-4.657-30.375-.14-12.128.8-26.15 2.406-40.75 1.655-15.05 3.667-30.256 3.53-44.812l-.53-3.563c.147-.022.29-.04.437-.062-.267-7.548-1.168-14.902-3.093-21.938H71.876zm54.344 38.406c-2.93.008-5.966.116-9.095.312-.462 7.62-1.278 15.044-2.125 22.25 7.17-.687 13.54-.854 19.125-.594 8.052.377 14.53 1.59 19.688 3.313 9.983 3.337 15.33 10.67 18.125 16.313.004.007-.004.023 0 .03 4.084 8.26 4.915 18.162 5.03 30.063.116 11.915-.91 25.67-2.5 40.126-3.174 28.863-7.678 58.49 3.407 83.406 1.387 3.118 3.24 6.112 5.438 8.97 2.654 17.69-5.822 30.658-19.22 40.062 21.26.607 38.842-6.724 54.032-19.25 10.6 2.244 22.79 2.488 37.594.875.07-.633.148-1.313.218-1.94l.062-.467c.755-6.864 1.368-13.512 1.78-19.782-19.424 2.535-32.108 1.636-41.342-1.717-8.934-3.245-15.024-9.73-18.063-16.563-3.663-8.236-4.487-18.278-4.625-30.406-.138-12.13.77-26.12 2.375-40.72.205-1.863.414-3.727.625-5.593 17.935-6.422 33.858-16.394 43.594-37.155-14.194 6.125-27.66 5.143-40.688-2.063-.13-13.513-2.115-26.493-8-38.374-5.732-11.577-16.797-21.134-31.875-26.157-9.67-3.22-20.668-4.97-33.56-4.936zM56.405 67.687C40.13 69.94 28.428 69.9 19.53 68.03v22.064c9.908 1.497 21.237 1.45 34.69-.125.212-1.813.43-3.636.624-5.407.64-5.812 1.162-11.468 1.562-16.876zm261.625 98.407c-9.924 12.336-24.8 18.83-41.155 18.344-4.872-3.597-10.56-6.574-17-8.72-9.672-3.22-20.67-4.972-33.563-4.937-3.625.01-7.406.17-11.343.47-.082.71-.174 1.453-.25 2.156-.765 6.95-1.358 13.738-1.75 20.156 8.056-.874 15.108-1.1 21.25-.812 8.05.376 14.53 1.59 19.686 3.313 9.998 3.34 15.334 10.668 18.125 16.312 4.098 8.263 4.917 18.18 5.033 30.094.115 11.913-.88 25.666-2.47 40.124-1.157 10.53-2.49 21.157-3.187 31.625-14.21 8.57-28.415 6.47-42.625 2.967 12.176 19.17 26.11 34.532 45.47 38.313.976 3.577 2.2 7.086 3.72 10.5 5.11 11.485 16.065 21.71 30.53 27 10.1 3.675 21.575 5.122 35.563 4.53 28.41 16.137 58.544 27.008 89.906 34.595-6.23-29.518-17.78-58.992-31.75-88.53-.816.236-1.642.453-2.47.655 1.122-12.293 1.614-24.397.094-35.938 13.016-15.182 22.528-30.87 24.812-51.343-12.36 11.8-26.07 15.875-41.47 14.217-5.83-5.63-13.32-10.213-22.248-13.187-9.673-3.222-20.67-4.982-33.563-4.938-2.927.01-5.965.115-9.094.313-.462 7.617-1.28 15.083-2.124 22.28 7.162-.686 13.51-.854 19.094-.592 8.052.376 14.563 1.59 19.72 3.312 9.995 3.34 15.3 10.668 18.092 16.313 4.096 8.263 4.948 18.18 5.063 30.093.09 9.358-.556 19.863-1.594 30.94-.005.017.007.043 0 .06-3.253 10.205-12.32 16.49-22.374 19.126-17.327 1.948-28.993.983-37.625-2.156-8.93-3.248-14.99-9.734-18.03-16.563h-.03c-3.664-8.236-4.488-18.278-4.626-30.406-.14-12.127.77-26.12 2.375-40.717 1.658-15.09 3.673-30.27 3.53-44.844l-.53-3.564c.157-.023.31-.04.467-.062-.178-4.943-.63-9.805-1.468-14.563 14.082-11.002 20.396-32.75 19.81-55.936zm132.345 143.594c-10.293 10.04-20.307 19.424-31.156 26.125 16.522 34.94 30.197 70.528 35.78 107.375l2.03 13.28-13.155-2.718c-36.956-7.69-72.802-19.764-106.625-38.594-5.232 9.03-13.202 17.718-23.844 28.094 55.5 27.003 116.156 41.43 180.156 47.78-2.68-62.727-20.228-122.558-43.187-181.342z","fillRule":"evenodd"}}]})(props);
};
