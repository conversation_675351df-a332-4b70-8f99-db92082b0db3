// THIS FILE IS AUTO GENERATED
var GenIcon = require('../lib').GenIcon
module.exports.SiWriteDotAs = function SiWriteDotAs (props) {
  return GenIcon({"tag":"svg","attr":{"role":"img","viewBox":"0 0 24 24"},"child":[{"tag":"title","attr":{},"child":[]},{"tag":"path","attr":{"d":"M12.815,5.139l2.179,7.509c0.107,0.381,0.22,0.793,0.339,1.236c0.12,0.443,0.229,0.85,0.327,1.223h0.025 c0.054-0.372,0.145-0.772,0.273-1.204c0.129-0.429,0.246-0.821,0.353-1.176l1.05-3.442c0.079-0.248,0.15-0.472,0.213-0.671 c0.062-0.199,0.126-0.382,0.192-0.551c0.068-0.167,0.131-0.327,0.194-0.478c0.062-0.151,0.132-0.301,0.213-0.451V7.106 l-1.569,0.105V5.139h5.169V6.88c-0.364,0-0.682,0.119-0.956,0.358c-0.275,0.239-0.512,0.524-0.711,0.85 c-0.201,0.328-0.364,0.657-0.493,0.984c-0.129,0.328-0.224,0.581-0.286,0.758l-3.096,8.997h-2.884l-1.874-5.807 c-0.053-0.142-0.12-0.345-0.199-0.606c-0.079-0.261-0.161-0.545-0.247-0.85c-0.083-0.307-0.173-0.623-0.265-0.95 c-0.092-0.328-0.179-0.638-0.259-0.931h-0.026c-0.053,0.381-0.14,0.809-0.26,1.283c-0.119,0.474-0.243,0.937-0.372,1.388 c-0.128,0.451-0.248,0.859-0.358,1.223c-0.111,0.364-0.194,0.62-0.246,0.771l-1.501,4.479H5.04L1.745,9.392 C1.62,9.046,1.523,8.767,1.452,8.555C1.381,8.342,1.321,8.157,1.273,8.002C1.223,7.848,1.186,7.709,1.16,7.584 C1.133,7.459,1.101,7.319,1.066,7.159L0.003,7.212V5.139h6.526V6.88C6.147,6.907,5.879,7.021,5.723,7.225 C5.568,7.429,5.492,7.691,5.492,8.009C5.483,8.16,5.493,8.32,5.524,8.487c0.032,0.169,0.074,0.342,0.128,0.519l0.916,3.322 c0.107,0.399,0.21,0.818,0.312,1.256c0.101,0.438,0.184,0.884,0.247,1.336h0.026c0.044-0.195,0.088-0.394,0.134-0.598 c0.044-0.204,0.092-0.424,0.145-0.658c0.054-0.235,0.116-0.492,0.186-0.771c0.072-0.278,0.155-0.587,0.254-0.922l1.993-6.832 C9.864,5.139,12.815,5.139,12.815,5.139z M24.003,16.628c0,1.233-1,2.233-2.233,2.233c-1.233,0-2.231-1-2.231-2.233 c0-1.233,0.998-2.233,2.231-2.233S24.003,15.395,24.003,16.628"}}]})(props);
};
