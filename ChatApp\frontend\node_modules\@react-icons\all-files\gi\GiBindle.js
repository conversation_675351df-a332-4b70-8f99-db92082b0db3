// THIS FILE IS AUTO GENERATED
var GenIcon = require('../lib').GenIcon
module.exports.GiBindle = function GiBindle (props) {
  return GenIcon({"tag":"svg","attr":{"viewBox":"0 0 512 512"},"child":[{"tag":"path","attr":{"d":"M459.03 42.48c-2.55-.025-5.894.37-9.842 1.18-11.325 11.974-22.63 23.982-33.936 35.99 9.132-1.408 19.102-2.543 29.178-3.183 1.767-.112 3.53-.204 5.29-.28 6.668-7.36 13.322-14.714 19.958-22.068-.224-5.638-1.588-8.51-3.414-9.86-1.075-.794-3.04-1.545-6.188-1.742-.33-.02-.68-.034-1.045-.038zM300.396 62.234c-5.184-.05-9.115.76-11.852 2.032 1.86 10.065 11.133 23.568 26.262 33.168 9.702 6.156 21.45 10.673 34.35 12.328 1.83-10.858 7.36-20.503 15.236-27.108-9.046-4.71-19.862-9.646-30.77-13.404-11.056-3.81-22.124-6.455-30.927-6.94-.79-.043-1.558-.068-2.298-.076zm88.697 29.29c-12.05 0-22.606 11.1-22.606 26.117 0 2.084.21 4.088.592 6 7.943-3.524 15.51-9.308 22.336-15.247 4.76-4.14 9.056-8.22 13.36-11.55-3.855-3.367-8.636-5.32-13.682-5.32zm69.502 2.45c-4.225.02-8.608.177-13.024.458-7.024.446-14.114 1.217-20.838 2.154 3.172 6.314 4.965 13.51 4.965 21.055 0 .088-.005.175-.006.262 9.91.117 19.423-1.054 27.94-3.193 9.94-2.498 18.488-6.28 24.524-10.378 2.99-2.03 5.274-4.132 6.918-6.06-2.74-1.208-6.496-2.405-11.256-3.124-4.463-.674-9.58-1.04-15.054-1.144-1.37-.026-2.76-.035-4.168-.03zm-47.24 19.102c-2.818 2.398-6.25 5.526-10.124 8.895-6.77 5.89-14.976 12.635-24.794 17.317 3.663 2.844 8.05 4.47 12.656 4.47 12.05 0 22.605-11.1 22.605-26.116 0-1.567-.122-3.09-.342-4.564zm-65.08 39.9c-24.567 26.112-49.153 52.204-73.844 78.165-.207.578-.386 1.057-.592 1.633-3.1 8.662-6.002 16.934-6.002 16.934l-8.33-2.922c-19.385 20.305-38.846 40.505-58.393 60.588.314-4.198 1.113-8.584 2.54-13.29l-17.22-5.23c-3.96 13.04-4.087 25.034-2.287 35.905-39.558 40.36-79.537 80.127-120.208 118.904 4.97.714 9.293 2.395 13.003 4.975 5.108 3.55 8.363 8.17 10.45 13.04 17.98-18.18 35.817-36.342 53.516-54.488l-4.406-10.23s8.09-3.484 16.627-7.1c2.732-1.155 5.238-2.195 7.82-3.272 52.52-54.117 103.84-108.087 154.162-161.932 3.956-8.16 7.978-16.437 11.972-24.802 7.444-15.593 14.738-31.41 21.19-46.88zm19.255.565c-7.26 17.985-15.68 36.216-24.204 54.07-17.833 37.354-36.067 73.075-44.787 99.236-6.393 19.176 4.528 44.254 24.772 64.843C341.558 392.28 370.296 407 392 407c21.62 0 50.51-15.045 70.83-35.828s31.132-45.893 24.682-64.178c-13.708-38.856-51.462-87.162-76.266-152.404-1.886 1.344-3.88 2.53-5.975 3.52-3.163 31.98 9.366 64.71-13.27 65.89-23.425 1.22-21.09-35.15-21.027-66.877-1.91-1.04-3.726-2.243-5.444-3.582zm78.28 117.245c11.813 15.312 17.416 32.482 16.237 49.293-1.255 17.897-10.14 34.967-25.26 48.758l-11.293-12.38c2.583-3.106 4.936-6.212 7.053-9.327 4.492-5.66 7.63-11.578 9.512-17.618.723-1.79 1.4-3.586 1.993-5.4 5.532-16.914 5.167-34.305 1.756-53.327zm-129.246 41.688c.034.038.066.078.1.117 6.64 7.516 16.95 14.62 33.078 22.3 4.968 2.965 11.132 5.073 18.106 7.962l-6.89 16.628c-7.56-3.13-17.076-5.836-25.897-12.894-8.488-6.792-15.337-17.694-18.496-34.113zM55.784 461.215c-3.93.045-9.178 1-16.087 3.478-1.082 1.016-2.16 2.037-3.242 3.05 2.886 4.915 6.124 8.473 9.877 10.587 4.718 2.658 11.108 3.672 21.414 1.17.98-.988 1.952-1.974 2.932-2.96-.754-5.7-2.693-10.818-6.012-13.126-1.75-1.216-4.526-2.25-8.88-2.2z"}}]})(props);
};
