// THIS FILE IS AUTO GENERATED
var GenIcon = require('../lib').GenIcon
module.exports.GiDragonfly = function GiDragonfly (props) {
  return GenIcon({"tag":"svg","attr":{"viewBox":"0 0 512 512"},"child":[{"tag":"path","attr":{"d":"M131.613 21.576c-.667-.002-1.334.015-1.998.05-1.517.082-3.025.263-4.52.55-11.974 2.296-21.052 13.1-23.2 26.908-1.396 8.968-.708 19.472 2.136 31.824-2.445-1.106-4.86-2.102-7.227-2.967-7.13-2.6-14-4.21-20.438-4.467-6.437-.257-12.443.842-17.84 3.662-10.794 5.64-16.044 18.626-14.44 32.8 1.602 14.17 8.625 30.916 21.837 51.21C85.398 191.062 114.3 228.23 144.48 253.7c14.917 12.59 30.117 22.445 45.584 26.738-.845-6.264-.584-12.854.627-19.44-10.287-3.848-22.168-11.46-34.16-21.58-27.774-23.44-56.247-59.745-74.946-88.47-12.245-18.808-17.856-33.625-18.93-43.116-1.073-9.49.955-12.268 4.528-14.135 3.572-1.866 11.803-2.362 23.216 1.8 6.458 2.357 13.792 6.067 21.65 11.18 11.93 32.113 30.72 71.88 53.518 102.02 9.756 12.898 20.218 24.092 31.672 32.06 2.543-5.522 5.687-10.984 9.403-16.294-8.655-6.367-17.628-15.747-26.17-27.04-14.347-18.968-27.52-43.103-38.09-66.32 32.386 29.372 52.07 54.056 72.23 83.198 1.764-2.008 3.61-3.984 5.544-5.917 2.6-2.6 5.276-5.035 8.008-7.324-22.464-32.146-45.464-60.11-85.05-94.265-5.26-4.537-10.46-8.602-15.573-12.21-6.9-19.66-8.5-34.132-7.18-42.628 1.36-8.733 3.763-10.564 8.255-11.426 4.493-.86 13.276 1.01 23.797 7.91 10.52 6.9 22.59 18.372 34.285 34.187 30.384 41.094 45.308 71.17 59.217 106.19 5.643-3.123 11.385-5.618 17.12-7.444-14.292-35.772-30.187-67.765-61.31-109.857-12.76-17.26-26.134-30.224-39.062-38.704-10.605-6.955-21.034-11.202-31.05-11.236zM292.355 122.99c-5.678 0-11.355 2.166-15.687 6.498-8.665 8.665-8.665 22.715 0 31.38 4.917 4.918 11.566 7.04 17.982 6.378-4.65 11.852-2.165 25.768 7.47 35.404 9.343 9.343 22.71 11.97 34.316 7.885-.43 6.184 1.71 12.515 6.437 17.242 8.665 8.666 22.714 8.665 31.38 0 8.664-8.665 8.664-22.715 0-31.38-4.767-4.766-11.16-6.905-17.392-6.428 3.984-11.56 1.338-24.823-7.95-34.11-6.5-6.5-14.947-9.75-23.394-9.75-3.78 0-7.56.657-11.164 1.958.86-6.598-1.238-13.51-6.307-18.58-4.333-4.333-10.01-6.498-15.69-6.498zm-15.388 74.147c-.846.145-1.703.31-2.574.5-13.036 2.85-28.087 11.024-41.024 23.96-12.937 12.938-21.11 27.99-23.96 41.026-2.175 9.95-1.36 18.227 1.922 24.367-46.51 35.78-149.555 119.44-185.19 139.307-14.113 46.802-2.547 79.386 57.16 57.16 18.846-36.327 105.196-141.11 141.325-185.81 5.487 1.66 12.187 1.78 19.898.093 13.036-2.85 28.087-11.024 41.024-23.96 12.935-12.938 21.108-27.99 23.958-41.026.304-1.394.537-2.747.726-4.074-7.817-2.393-15.17-6.658-21.328-12.815-5.468-5.47-9.44-11.885-11.94-18.728zm48.976 46.455c-1.79 5.75-4.26 11.512-7.357 17.176 38.903 14.977 70.47 29.76 114.947 62.648 15.813 11.693 27.286 23.765 34.186 34.285 6.9 10.522 8.767 19.307 7.905 23.8-.862 4.493-2.693 6.895-11.426 8.254-8.733 1.36-23.76-.352-44.264-7.748-1.815-.655-3.667-1.343-5.54-2.05-2.504-3.28-5.182-6.587-8.058-9.92-36.56-42.378-66.137-65.798-101.258-89.868-1.997 2.32-4.094 4.603-6.316 6.826-2.326 2.326-4.716 4.514-7.15 6.592 29.87 20.288 54.574 39.032 83.163 69.332-19.92-9.646-39.86-21.038-56.037-33.275-14.147-10.703-25.3-22.084-31.267-32.666-5.08 3.958-10.33 7.383-15.67 10.246 7.99 13.694 20.677 25.987 35.664 37.324 28.014 21.192 64.35 38.923 95.127 50.902 7.14 9.994 12.13 19.315 15.04 27.298 4.164 11.414 3.668 19.646 1.802 23.22-1.867 3.572-4.645 5.6-14.135 4.526-9.49-1.073-24.304-6.685-43.113-18.93-28.722-18.7-65.025-47.176-88.464-74.953-11.72-13.887-20.09-27.632-23.127-38.92-.19-.702-.362-1.39-.518-2.067-.52.125-1.04.262-1.56.375-5.727 1.252-11.486 1.777-17.063 1.512.29 1.687.645 3.37 1.094 5.035 4.21 15.65 14.155 31.03 26.89 46.12 25.468 30.18 62.636 59.084 92.55 78.562 20.293 13.21 37.04 20.234 51.21 21.837 14.173 1.604 27.16-3.65 32.8-14.443 5.64-10.793 4.393-24.016-.81-38.276-1.363-3.74-3.038-7.587-5.008-11.516 14.58 3.888 26.72 4.976 36.89 3.393 13.807-2.148 24.612-11.228 26.908-23.2 2.297-11.973-2.156-24.64-10.634-37.567-8.48-12.928-21.443-26.302-38.7-39.064-45.53-33.667-79.24-49.524-118.703-64.798z"}}]})(props);
};
