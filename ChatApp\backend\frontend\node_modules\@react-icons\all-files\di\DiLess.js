// THIS FILE IS AUTO GENERATED
var GenIcon = require('../lib').GenIcon
module.exports.DiLess = function DiLess (props) {
  return GenIcon({"tag":"svg","attr":{"version":"1.1","viewBox":"0 0 32 32"},"child":[{"tag":"path","attr":{"d":"M8.972 10.374v7.262c0 0.874 0.097 1.003 0.965 1.252l-0.208 1.217c-0.463-0.105-0.909-0.152-1.312-0.311-0.769-0.302-0.89-0.999-0.893-1.715-0.010-2.053-0.004-4.106-0.004-6.158v-0.406c-0.353 0.027-0.678 0.024-0.99 0.084-0.359 0.068-0.564 0.363-0.576 0.795-0.017 0.602-0.006 1.204-0.016 1.806-0.005 0.276-0.017 0.553-0.050 0.826-0.062 0.515-0.299 0.934-0.733 1.215 0.004 0.039-0.002 0.068 0.008 0.075 0.651 0.455 0.763 1.137 0.775 1.857 0.009 0.572-0 1.145 0.003 1.717 0.005 0.863 0.229 1.096 1.095 1.151l0.14 0.021v1.126c-0.601-0.015-1.189-0.040-1.727-0.328-0.599-0.321-0.801-0.877-0.83-1.505-0.030-0.65-0.019-1.302-0.025-1.953-0.001-0.118 0.004-0.237-0.003-0.356-0.043-0.754-0.337-1.086-1.079-1.214-0.006-0.034-0.017-0.071-0.017-0.109-0.002-0.316-0.001-0.631-0.001-0.937l0.053-0.048c0.781-0.189 1.037-0.507 1.046-1.323 0.006-0.523-0.009-1.046 0.005-1.569 0.009-0.354 0.024-0.712 0.087-1.059 0.139-0.767 0.697-1.231 1.59-1.351 0.264-0.035 0.53-0.057 0.796-0.061 0.62-0.009 1.241-0.003 1.902-0.003v0zM11.489 17.175c-0.092 0.759 0.493 1.455 1.305 1.606 0.768 0.143 1.509 0.011 2.276-0.229 0.061 0.356 0.128 0.7 0.172 1.047 0.007 0.053-0.084 0.154-0.149 0.174-1.125 0.352-2.26 0.468-3.389 0.035-0.696-0.267-1.199-0.764-1.467-1.46-0.425-1.107-0.43-2.221 0.071-3.305 0.732-1.585 2.556-1.997 3.921-1.362 0.825 0.384 1.223 1.098 1.365 1.954 0.081 0.49 0.074 0.994 0.11 1.539-1.449-0.001-2.834-0.001-4.214-0.001v0zM14.217 16.105c0.017-0.924-0.507-1.502-1.328-1.497-0.785 0.005-1.423 0.677-1.403 1.497h2.731zM24.829 22.2v-1.144l0.328-0.023c0.617-0.050 0.892-0.324 0.908-0.942 0.015-0.592 0-1.184 0.011-1.776 0.005-0.285 0.022-0.573 0.064-0.855 0.073-0.488 0.322-0.88 0.78-1.177-0.81-0.514-0.837-1.299-0.845-2.089-0.006-0.543 0.006-1.086-0.005-1.628-0.015-0.737-0.287-1.006-1.026-1.039l-0.204-0.014v-1.137c0.642 0.014 1.278 0.034 1.841 0.398 0.524 0.339 0.688 0.876 0.713 1.454 0.031 0.7 0.015 1.401 0.028 2.101 0.004 0.206 0.014 0.418 0.067 0.615 0.135 0.507 0.486 0.769 1.019 0.787v1.109c-0.51 0.022-0.875 0.265-1.012 0.773-0.054 0.197-0.068 0.408-0.072 0.613-0.014 0.691 0 1.382-0.025 2.072-0.039 1.095-0.616 1.704-1.706 1.831-0.28 0.033-0.563 0.045-0.863 0.069v0zM25.096 13.701l-0.263 1.146c-0.61-0.202-1.211-0.341-1.841-0.24-0.397 0.064-0.631 0.238-0.68 0.516-0.054 0.301 0.093 0.568 0.457 0.748 0.308 0.152 0.635 0.266 0.952 0.4 0.19 0.081 0.381 0.16 0.566 0.253 0.89 0.444 1.198 1.013 1.096 2.015-0.071 0.702-0.577 1.219-1.467 1.415-1.061 0.233-2.111 0.168-3.168-0.23l0.232-1.154c0.455 0.104 0.888 0.239 1.331 0.292 0.366 0.044 0.747 0.009 1.117-0.028 0.244-0.024 0.449-0.149 0.509-0.423 0.062-0.282-0.018-0.527-0.256-0.672-0.292-0.178-0.612-0.312-0.922-0.461-0.372-0.178-0.76-0.328-1.118-0.531-1.054-0.595-1.044-2.437 0.128-2.997 0.347-0.166 0.741-0.266 1.124-0.32 0.743-0.106 1.466 0.047 2.204 0.271zM20.318 13.649l-0.275 1.2c-0.611-0.208-1.203-0.339-1.825-0.244-0.416 0.064-0.649 0.24-0.699 0.532-0.050 0.296 0.1 0.556 0.475 0.739 0.327 0.159 0.673 0.28 1.008 0.421 0.163 0.069 0.326 0.14 0.485 0.219 0.922 0.459 1.244 1.078 1.097 2.096-0.101 0.702-0.713 1.241-1.615 1.383-0.916 0.144-1.825 0.12-2.719-0.166-0.219-0.070-0.311-0.155-0.244-0.395 0.080-0.282 0.128-0.573 0.191-0.863 0.452 0.102 0.885 0.238 1.328 0.29 0.366 0.043 0.747 0.009 1.117-0.028 0.26-0.025 0.462-0.171 0.517-0.454 0.053-0.273-0.042-0.51-0.267-0.641-0.339-0.197-0.705-0.346-1.059-0.516-0.328-0.157-0.668-0.294-0.982-0.474-1.055-0.605-1.038-2.433 0.151-3.024 0.529-0.263 1.089-0.361 1.664-0.316 0.543 0.043 1.081 0.155 1.653 0.241v0z"}}]})(props);
};
