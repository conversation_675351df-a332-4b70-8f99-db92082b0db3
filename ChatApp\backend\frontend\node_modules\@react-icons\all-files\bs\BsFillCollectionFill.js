// THIS FILE IS AUTO GENERATED
var GenIcon = require('../lib').GenIcon
module.exports.BsFillCollectionFill = function BsFillCollectionFill (props) {
  return GenIcon({"tag":"svg","attr":{"viewBox":"0 0 16 16","fill":"currentColor"},"child":[{"tag":"rect","attr":{"width":"16","height":"10","rx":"1.5","transform":"matrix(1 0 0 -1 0 14.5)"}},{"tag":"path","attr":{"fillRule":"evenodd","d":"M2 3a.5.5 0 00.5.5h11a.5.5 0 000-1h-11A.5.5 0 002 3zm2-2a.5.5 0 00.5.5h7a.5.5 0 000-1h-7A.5.5 0 004 1z","clipRule":"evenodd"}}]})(props);
};
