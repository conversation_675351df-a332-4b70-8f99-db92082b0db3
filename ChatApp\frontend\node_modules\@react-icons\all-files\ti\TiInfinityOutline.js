// THIS FILE IS AUTO GENERATED
var GenIcon = require('../lib').GenIcon
module.exports.TiInfinityOutline = function TiInfinityOutline (props) {
  return GenIcon({"tag":"svg","attr":{"version":"1.2","baseProfile":"tiny","viewBox":"0 0 24 24"},"child":[{"tag":"path","attr":{"d":"M16.434 8.596c1.152 0 2.237.449 3.053 1.264.815.816 1.266 1.9 1.266 3.056s-.45 2.239-1.268 3.056c-.813.815-1.898 1.266-3.053 1.266s-2.238-.449-3.055-1.266l-1.376-1.32-1.395 1.338c-.797.799-1.882 1.248-3.036 1.248-1.154 0-2.239-.449-3.054-1.267-.815-.813-1.265-1.899-1.265-3.053s.45-2.237 1.267-3.056c.814-.813 1.898-1.266 3.053-1.266 1.154 0 2.239.449 3.055 1.266l1.375 1.32 1.396-1.34c.798-.797 1.882-1.246 3.037-1.246m0-2c-1.679 0-3.25.645-4.433 1.813-1.163-1.159-2.746-1.813-4.43-1.813-1.688 0-3.274.657-4.467 1.853-1.194 1.192-1.852 2.78-1.852 4.469s.658 3.274 1.852 4.468c1.191 1.192 2.779 1.852 4.468 1.852 1.679 0 3.251-.645 4.431-1.814 1.163 1.16 2.746 1.814 4.431 1.814 1.689 0 3.276-.658 4.469-1.854 1.193-1.188 1.852-2.776 1.852-4.467 0-1.688-.658-3.274-1.852-4.47-1.197-1.195-2.783-1.851-4.469-1.851zM7.571 12.096c.225 0 .426.088.612.271l.57.548-.603.579c-.141.142-.352.223-.578.223-.227 0-.438-.08-.58-.223-.155-.155-.24-.36-.24-.578 0-.221.084-.422.243-.581.156-.155.355-.239.576-.239m0-1c-.486 0-.942.189-1.285.533-.345.346-.535.801-.535 1.287 0 .484.189.941.533 1.285.344.344.815.516 1.287.516.471 0 .942-.172 1.285-.516l1.339-1.285-1.321-1.27c-.36-.361-.817-.55-1.303-.55zM16.434 12.113c.228 0 .438.08.576.219.158.159.242.359.242.582s-.083.422-.243.581c-.144.146-.352.228-.571.228-.23 0-.444-.088-.617-.261l-.571-.548.603-.578c.141-.143.353-.223.581-.223m0-1c-.472 0-.943.172-1.287.516l-1.34 1.285 1.322 1.27c.362.361.838.539 1.311.539.472 0 .937-.177 1.279-.521.346-.344.534-.801.534-1.287s-.188-.941-.532-1.287c-.346-.344-.817-.515-1.287-.515z"}}]})(props);
};
