// THIS FILE IS AUTO GENERATED
var GenIcon = require('../lib').GenIcon
module.exports.GiLargeWound = function GiLargeWound (props) {
  return GenIcon({"tag":"svg","attr":{"viewBox":"0 0 512 512"},"child":[{"tag":"path","attr":{"d":"M128.648 17.008c109.003 34.557 192.306 71.064 219.7 149.158-58.572-74.49-153.467-123.28-298.842-144.98 117.47 34.102 183.435 69.43 217.81 144.527C210.64 99.858 124.57 59.073 12.512 66.82 224.765 98.88 309.19 333.65 246.664 504.285c34.37-49.026 53.516-92.46 64.922-135.967 6.433 48.942 6.577 95.272-10.29 140.573 31.854-58.362 64.562-150.973 53.526-239.745 34.39 87.13 34.71 152.248-1.875 223.523 67.038-82.263 78.11-105.023 87.188-174.656 13.478 29.99 16.32 58.098 9.595 96.12 95.74-140.367 26.28-275.754-80.074-326.415 59.778 43.85 92.914 121.168 90.96 217.254-31.8-165.58-108.18-265.18-331.968-287.962zm-106.185 107.1c72.06 35.308 129.12 88.086 164.693 147.62-29.827-37.263-75.784-70.606-138.41-98.876 112.746 90.495 170.86 215.24 153.012 321.96 87.7-207.514 3.01-348.097-179.295-370.705zm193.834 32.91c64.25 34.715 95.31 113.943 72.336 195.584 2.308-71.497-33.978-148.48-72.336-195.584z"}}]})(props);
};
