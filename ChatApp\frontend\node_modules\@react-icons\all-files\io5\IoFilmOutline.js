// THIS FILE IS AUTO GENERATED
var GenIcon = require('../lib').GenIcon
module.exports.IoFilmOutline = function IoFilmOutline (props) {
  return GenIcon({"tag":"svg","attr":{"viewBox":"0 0 512 512"},"child":[{"tag":"rect","attr":{"width":"416","height":"320","x":"48","y":"96","fill":"none","strokeLinejoin":"round","strokeWidth":"32","rx":"28","ry":"28"}},{"tag":"rect","attr":{"width":"80","height":"80","x":"384","y":"336","fill":"none","strokeLinejoin":"round","strokeWidth":"32","rx":"28","ry":"28"}},{"tag":"rect","attr":{"width":"80","height":"80","x":"384","y":"256","fill":"none","strokeLinejoin":"round","strokeWidth":"32","rx":"28","ry":"28"}},{"tag":"rect","attr":{"width":"80","height":"80","x":"384","y":"176","fill":"none","strokeLinejoin":"round","strokeWidth":"32","rx":"28","ry":"28"}},{"tag":"rect","attr":{"width":"80","height":"80","x":"384","y":"96","fill":"none","strokeLinejoin":"round","strokeWidth":"32","rx":"28","ry":"28"}},{"tag":"rect","attr":{"width":"80","height":"80","x":"48","y":"336","fill":"none","strokeLinejoin":"round","strokeWidth":"32","rx":"28","ry":"28"}},{"tag":"rect","attr":{"width":"80","height":"80","x":"48","y":"256","fill":"none","strokeLinejoin":"round","strokeWidth":"32","rx":"28","ry":"28"}},{"tag":"rect","attr":{"width":"80","height":"80","x":"48","y":"176","fill":"none","strokeLinejoin":"round","strokeWidth":"32","rx":"28","ry":"28"}},{"tag":"rect","attr":{"width":"80","height":"80","x":"48","y":"96","fill":"none","strokeLinejoin":"round","strokeWidth":"32","rx":"28","ry":"28"}},{"tag":"rect","attr":{"width":"256","height":"160","x":"128","y":"96","fill":"none","strokeLinejoin":"round","strokeWidth":"32","rx":"28","ry":"28"}},{"tag":"rect","attr":{"width":"256","height":"160","x":"128","y":"256","fill":"none","strokeLinejoin":"round","strokeWidth":"32","rx":"28","ry":"28"}}]})(props);
};
