// THIS FILE IS AUTO GENERATED
var GenIcon = require('../lib').GenIcon
module.exports.GiHexagonalNut = function GiHexagonalNut (props) {
  return GenIcon({"tag":"svg","attr":{"viewBox":"0 0 512 512"},"child":[{"tag":"path","attr":{"d":"M256 31.365L29.44 123.697v179.9L256 395.926l226.56-92.33v-179.9L256 31.365zm0 73.93c38.765 0 74.22 10.93 100.89 29.756 26.72 18.864 45.11 46.74 45.11 78.596s-18.39 59.733-45.11 78.596C330.22 311.067 294.764 322 256 322s-74.22-10.933-100.89-29.758C128.39 273.38 110 245.502 110 213.646c0-31.855 18.39-59.732 45.11-78.595 26.67-18.824 62.125-29.755 100.89-29.755zm0 18c-35.36 0-67.328 10.1-90.51 26.463-21.357 15.076-35.23 35.528-37.23 58.207 7.18-14.2 18.19-26.816 31.83-37.047C185.01 152.228 218.83 141 256 141c37.17 0 70.99 11.227 95.91 29.918 13.64 10.23 24.65 22.846 31.83 37.047-2-22.68-15.873-43.13-37.23-58.207-23.182-16.364-55.15-26.463-90.51-26.463zM256 159c-33.522 0-63.704 10.264-85.11 26.318-16.03 12.024-27.058 27.025-31.585 43.573 5.897-7.35 12.9-14.06 20.785-19.972C185.01 190.228 218.83 179 256 179c37.17 0 70.99 11.227 95.91 29.918 7.884 5.913 14.888 12.622 20.785 19.973-4.527-16.547-15.554-31.548-31.586-43.572C319.703 169.264 289.52 159 256 159zm0 38c-33.522 0-63.704 10.264-85.11 26.318-12.542 9.407-22.014 20.638-27.714 33.004.83 1.093 1.7 2.168 2.59 3.233 4.355-4.525 9.147-8.754 14.324-12.637C185.01 228.228 218.83 217 256 217c37.17 0 70.99 11.227 95.91 29.918 5.177 3.883 9.97 8.112 14.324 12.637.89-1.065 1.76-2.14 2.59-3.233-5.7-12.366-15.172-23.597-27.715-33.004C319.703 207.264 289.52 197 256 197zM29.44 323.033v70.916L247 482.612v-70.916L29.44 323.033zm453.12 0L265 411.7v70.913l217.56-88.664v-70.917z"}}]})(props);
};
