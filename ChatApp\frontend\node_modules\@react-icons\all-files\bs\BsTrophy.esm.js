// THIS FILE IS AUTO GENERATED
import { GenIcon } from '../lib';
export function BsTrophy (props) {
  return GenIcon({"tag":"svg","attr":{"viewBox":"0 0 16 16","fill":"currentColor"},"child":[{"tag":"path","attr":{"d":"M3 1h10c-.495 3.467-.5 10-5 10S3.495 4.467 3 1zm0 15a1 1 0 011-1h8a1 1 0 011 1H3zm2-1a1 1 0 011-1h4a1 1 0 011 1H5z"}},{"tag":"path","attr":{"fillRule":"evenodd","d":"M12.5 3a2 2 0 100 4 2 2 0 000-4zm-3 2a3 3 0 116 0 3 3 0 01-6 0zm-6-2a2 2 0 100 4 2 2 0 000-4zm-3 2a3 3 0 116 0 3 3 0 01-6 0z","clipRule":"evenodd"}},{"tag":"path","attr":{"d":"M7 10h2v4H7v-4z"}},{"tag":"path","attr":{"d":"M10 11c0 .552-.895 1-2 1s-2-.448-2-1 .895-1 2-1 2 .448 2 1z"}}]})(props);
};
