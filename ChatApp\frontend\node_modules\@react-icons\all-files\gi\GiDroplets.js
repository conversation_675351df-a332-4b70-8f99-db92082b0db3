// THIS FILE IS AUTO GENERATED
var GenIcon = require('../lib').GenIcon
module.exports.GiDroplets = function GiDroplets (props) {
  return GenIcon({"tag":"svg","attr":{"viewBox":"0 0 512 512"},"child":[{"tag":"path","attr":{"d":"M107.156 17.72c-22.515 130.852-89.5 157.227-89.5 248.968 0 46.03 45.485 79.843 89.5 79.843 1.7 0 3.402-.066 5.094-.155.82-6.47 1.875-12.74 3.125-18.844-2.962.39-5.995.595-9.063.595-38.51 0-69.656-31.488-69.656-70.72 0-39.23 31.145-70.717 69.656-70.717.576 0 1.147.017 1.72.03-17.205 9.417-28.876 28.2-28.876 49.5 0 27.927 20.063 51.5 46.344 55.345 10.542-29.276 25.437-55.543 41.125-83.125 3.053-5.368 6.148-10.822 9.25-16.344-20.93-44.947-53.572-89.35-68.72-174.375zm296.531 0c-14.905 86.624-49.318 127.455-70.875 171.78 4.337 8.4 8.666 16.6 12.907 24.625.27.514.54 1.018.81 1.53 12.664-17.583 33.143-28.968 56.283-28.968.585 0 1.167.017 1.75.032-17.214 9.413-28.907 28.192-28.907 49.5 0 30.555 24.045 55.905 53.938 55.905 16.704 0 31.57-7.9 41.437-20.22-6.557 32.216-34.585 56.22-68.217 56.22-3.16 0-6.266-.245-9.313-.656 1.425 6.11 2.654 12.378 3.656 18.78 2.18.163 4.354.28 6.53.28 45.005 0 87.752-31.642 87.752-75.342 0-88.064-64.484-122.863-87.75-253.47zM256.72 52.47c-18.814 73.33-47.047 123.11-71.69 166.436-29.68 52.186-53.686 94.326-53.686 161.313 0 31.412 15.432 58.706 39.094 78.624 23.66 19.917 55.448 31.906 86.125 31.906 31.66 0 62.733-11.278 85.468-29.906 22.736-18.628 37.095-44.098 37.095-73.78 0-64.192-23.26-108.428-52.125-163.064-23.955-45.34-51.36-97.692-70.28-171.53zm-1.5 212.56c6.448 0 12.75.62 18.874 1.75-31.1 8.977-53.844 38.675-53.844 73.5 0 41.822 32.78 76.314 73.47 76.314 28.857 0 53.72-17.368 65.75-42.375-1.763 57.015-47.717 102.374-104.25 102.374-57.654 0-104.314-47.18-104.314-105.78 0-58.603 46.66-105.783 104.313-105.783z","fillRule":"evenodd"}}]})(props);
};
