// THIS FILE IS AUTO GENERATED
var GenIcon = require('../lib').GenIcon
module.exports.CgDialpad = function CgDialpad (props) {
  return GenIcon({"tag":"svg","attr":{"viewBox":"0 0 24 24","fill":"none"},"child":[{"tag":"path","attr":{"d":"M5.5 3H8.5V6H5.5V3Z","fill":"currentColor"}},{"tag":"path","attr":{"d":"M5.5 8H8.5V11H5.5V8Z","fill":"currentColor"}},{"tag":"path","attr":{"d":"M5.5 13V16H8.5V13H5.5Z","fill":"currentColor"}},{"tag":"path","attr":{"d":"M10.5 3H13.5V6H10.5V3Z","fill":"currentColor"}},{"tag":"path","attr":{"d":"M10.5 8V11H13.5V8H10.5Z","fill":"currentColor"}},{"tag":"path","attr":{"d":"M10.5 13H13.5V16H10.5V13Z","fill":"currentColor"}},{"tag":"path","attr":{"d":"M10.5 18V21H13.5V18H10.5Z","fill":"currentColor"}},{"tag":"path","attr":{"d":"M15.5 3H18.5V6H15.5V3Z","fill":"currentColor"}},{"tag":"path","attr":{"d":"M15.5 8V11H18.5V8H15.5Z","fill":"currentColor"}},{"tag":"path","attr":{"d":"M15.5 13H18.5V16H15.5V13Z","fill":"currentColor"}}]})(props);
};
