// THIS FILE IS AUTO GENERATED
import { GenIcon } from '../lib';
export function GiInnerSelf (props) {
  return GenIcon({"tag":"svg","attr":{"viewBox":"0 0 512 512"},"child":[{"tag":"path","attr":{"d":"M263.28 18.47c-1.164.028-2.346.124-3.5.124-32.842 0-59.592 33.925-59.592 75.937 0 21.274 7.08 40.466 18.093 54.282-68.132 10.567-82.852 105.422-66.655 191.563h23.563l-1.032-105.344 18.688-.186 1.25 126.687 8.75 132.876h46.875V318.812h18.686v175.594h44.313l11.5-154.03h.03l1.063-105.532 18.687.187-1.063 105.345h24.532c18.362-88.46-4.724-178.95-67.095-190.688 11.507-13.88 18.97-33.344 18.97-55.156 0-39.387-23.416-72.038-53.408-75.936-.5-.075-.987-.11-1.5-.125-.384-.012-.767-.01-1.156 0zm-4.717 137.686c11.48 0 20.78 10.683 20.78 23.875 0 8.73-4.072 16.365-10.156 20.532h38.907v18.688h-40.22v23.344l24.563 49.437-16.75 8.314-17.156-34.53-17.155 34.53-16.75-8.313 24.563-49.436V219.25h-40.22v-18.688h38.97c-6.084-4.167-10.157-11.802-10.157-20.53 0-13.192 9.303-23.876 20.782-23.876z","fillRule":"evenodd"}}]})(props);
};
