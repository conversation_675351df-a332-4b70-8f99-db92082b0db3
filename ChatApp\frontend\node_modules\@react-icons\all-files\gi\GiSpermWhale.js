// THIS FILE IS AUTO GENERATED
var GenIcon = require('../lib').GenIcon
module.exports.GiSpermWhale = function GiSpermWhale (props) {
  return GenIcon({"tag":"svg","attr":{"viewBox":"0 0 512 512"},"child":[{"tag":"path","attr":{"d":"M388.81 85.863c-10.106.254-17.64 6.364-22.757 14.442-6.165-4.3-13.264-7.895-21.207-10.39-6.427-2.02-26.645 4.732-45.768 18.536 16.392 6.376 11.844 6.948 16.82 19.672-19.66-1.988-28.31-2.94-30.048-17.138-.314-1.5-.81-2.916-1.467-4.254-15.234 3.722-22.6 8.77-41.026 11.262-4.858 1.226 9.583-18.77 28.99-22.52-17.26-9.138-46.225-9.985-60.945-6.962-38.524 7.91-42.33 52.6-23.88 65.316 3.717-9.353 14.566-12.168 29.5-11.238-6.366 11.284-9.915 22.665-2.81 34.414 12.574-8.952 22.625-20.764 41.436-21.068 9.623 2.09 11.337 1.27 17.832-4.88l15.178 108 14.834-3.673-17.642-88.91s26.496 16.27 32.306 11.938c5.81-4.33 4.52-17.324 2.108-25.986 8.803-10.564 32.432 10.386 50.568 20.367 4.478 2.463 8.11 2.078 11.05.108 5.887 9.216 28.117 16.154 29.333 8.32-7.037-6.56-5.26-19.9-21.938-19.044 3.03-7.668 4.03-16.074 4.03-16.074.157-5.07-.863-10.5-2.957-15.952 4.736 4.108 12.537 8.33 17.173 11.89 13.703-7.19 31.292.545 35.612-7.997-12.8-13.524-33.406-21.83-50.864-15.343-3.113 1.157-4.855 2.447-5.567 3.826-2.825-4.944-6.51-9.768-10.996-14.203 3.144.37 7.633-.016 14.79-1.902 9.453-4.466 11.325-11 10.534-18.26-4.425-1.688-8.496-2.388-12.22-2.295zm-222.017 27.145c-12.067.374-20 12.273-24.922 24.664 8.988 1.977 24.257 1.042 30.202-7.024 4.057-5.503 4.395-16.866-2.81-17.558a19.48 19.48 0 0 0-2.47-.082zm5.135 30.133c-1.324-.01-3.125.28-5.475.854-9.398 2.297-10.538 15.336-11.94 24.58 5.744-4.33 14.544-9.105 18.26-16.855 2.788-5.814 3.125-8.546-.845-8.58zm-22.887 73.38c-11.094.18-46.64 9.46-55.977 17.11-2.352 1.925-6.47 11.213-8.688 11.042-3.068-.237-5.27-10.055-5.27-10.055-22.123-12.38-60.006-10.56-59.652-8.71.858 4.478 4.636 49.63 63.166 65.92 9.884 67.545 64.372 95.08 134.497 98.36-19.497 13.367-75.48 18.697-67.775 30.55 12.604 10.522 83.862 4.593 124.504-8.34 14.09-4.484 28.865-8.31 44.047-11.637 43.23 10.56 111.83 23.66 151.36 10.984 13.348-4.28 7.064-20.362 1.054-18.262-14.39 5.03-63.573 1.735-91.79-3.49 34.826-5.05 69.914-8.652 102.325-12.45 9.162-20.386 12.522-54.712 11.545-79.774-1.878-48.145-32-52.028-101.092-44.247-104.013 11.715-145.852 43.226-218.777 61.837-11.118 2.838-47.684-4.157-63.373-29.783 36.728-17.253 50.916-61.758 43.005-68.545-.44-.378-1.524-.536-3.11-.51zm220.81 141.173c-1.616 4.194-4.184 8.546-11.72 9.526-7.538.978-11.46-3.26-13.696-8.824 8.47 1.463 16.944 1.146 25.416-.703z"}}]})(props);
};
