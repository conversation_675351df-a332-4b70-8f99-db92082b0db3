// THIS FILE IS AUTO GENERATED
var GenIcon = require('../lib').GenIcon
module.exports.GiRibcage = function GiRibcage (props) {
  return GenIcon({"tag":"svg","attr":{"viewBox":"0 0 512 512"},"child":[{"tag":"path","attr":{"d":"M119.28 19.938c-29.117 11.646-50.61 28.4-60.468 49.093-4.718 9.903-6.43 20.086-5.437 30.282-25.137 12.786-38.693 39.24-38.188 65.626-.036.693-.062 1.397-.062 2.093 0 3.464.455 6.883 1.344 10.25.28 1.475.592 2.95.968 4.407l.437-.125c2.83 7.498 7.817 14.706 14.688 21.532-11.103 10.945-16.3 25.256-13.47 39.437 1.473 7.375 4.836 14.43 9.938 21.032.4.725.806 1.442 1.25 2.157 1.926 3.087 4.235 6.008 6.907 8.75-10.928 13.738-13.85 31.155-8.124 46.717 3.435 9.34 9.718 17.922 18.468 25.22 2.547 2.71 5.41 5.146 8.532 7.374-3.073 7.786-3.99 16.288-2.437 24.75 2.523 13.754 11.09 27.07 25.063 38.408l.124-.125c3.38 2.656 7.127 4.824 11.188 6.53-2.32 12.464.09 23.86 7.063 32.22 9.88 11.844 26.615 16.188 44.937 13.812 29.986-3.89 66.278-25.483 96.375-70.03 5.44 3.526 11.503 5.155 17.906 5.155 6.34 0 12.35-1.608 17.75-5.063 30.086 44.486 66.348 66.052 96.314 69.938 18.32 2.376 35.057-1.968 44.937-13.813 6.95-8.33 9.375-19.68 7.095-32.093 4.166-1.727 7.985-3.935 11.438-6.658l.093.125c13.973-11.338 22.57-24.653 25.094-38.406 1.55-8.453.606-16.94-2.47-24.717 3.05-2.18 5.845-4.61 8.345-7.25.02-.022.042-.042.063-.063 8.83-7.32 15.17-15.92 18.625-25.313 5.725-15.562 2.803-32.98-8.125-46.718 2.673-2.742 4.98-5.663 6.906-8.75.457-.735.874-1.476 1.28-2.22 5.08-6.586 8.44-13.615 9.907-20.97 2.832-14.178-2.362-28.49-13.467-39.436 6.86-6.82 11.823-14.042 14.656-21.53l.436.124c.357-1.384.634-2.79.906-4.188.017-.06.016-.126.032-.188.895-3.376 1.375-6.81 1.375-10.28 0-.697-.028-1.4-.064-2.095.51-26.385-13.024-52.84-38.156-65.625.994-10.196-.72-20.38-5.438-30.28-9.856-20.693-31.35-37.448-60.468-49.094 17.375 9.772 30.292 22.603 37.125 36.937 25.16 52.8-41.854 113.403-149.69 136.156-2.454.523-4.905.95-7.342 1.345-5.236-9.99-11.065-16.094-17.157-16.094-6.09 0-11.888 6.104-17.124 16.095-2.443-.394-4.89-.822-7.344-1.344C123.978 170.28 56.996 109.675 82.156 56.876c6.833-14.334 19.75-27.165 37.125-36.938zm-61.093 98.03c17.415 41.942 78.38 81.302 162.25 99 3.03.64 6.09 1.186 9.157 1.72-2.002 6.498-3.856 13.538-5.53 20.875-102.14-5.383-182.168-38.334-190.19-80.188 1.086-16.487 9.413-32.31 24.313-41.406zm396.25.032c14.878 9.1 23.197 24.902 24.282 41.375-8.02 41.858-88.035 74.81-190.19 80.188-1.674-7.337-3.528-14.377-5.53-20.875 3.064-.534 6.123-1.08 9.156-1.72C376.01 199.275 437 159.932 454.436 118zM47.063 215.063c35.894 24.998 98.886 42.877 172.594 47.093-.886 5.284-1.677 10.614-2.375 15.938-82.59 11.893-152.356 1.95-175.843-29.5-.022-.03-.04-.064-.062-.094-2.06-3.344-3.333-6.6-3.938-9.625-1.696-8.498.853-16.387 9.626-23.813zm418.5 0c8.773 7.425 11.322 15.314 9.625 23.812-.623 3.122-2.007 6.48-4.187 9.938-23.655 31.27-93.295 41.145-175.688 29.28-.697-5.323-1.488-10.653-2.375-15.937 73.73-4.213 136.737-22.09 172.625-47.094zm-413.53 70.812C85.6 305.7 145.534 311.38 214.78 302.47c-.55 7.628-.85 15.03-.876 21.967-63.133 24.992-122.27 28.97-153.562 8.282-7.008-5.65-11.51-11.912-13.75-18-3.632-9.873-2.157-19.526 5.437-28.845zm408.562 0c7.593 9.32 9.038 18.972 5.406 28.844-2.24 6.088-6.72 12.35-13.72 18-31.298 20.688-90.46 16.71-153.592-8.283-.025-6.938-.325-14.34-.875-21.968 69.248 8.912 129.204 3.23 162.78-16.595zM215.28 349.813c.55 4.528 1.25 8.748 2.095 12.656-52.482 36.246-103.022 50.802-132.22 35.217-7.81-7.708-11.874-15.515-13.155-22.5-.822-4.48-.604-8.697.656-12.687 34.113 12.894 86.456 8.03 142.625-12.688zm82.064 0c56.182 20.72 108.52 25.56 142.625 12.656 1.26 3.997 1.448 8.226.624 12.717-1.28 6.985-5.346 14.792-13.156 22.5-29.193 15.58-79.713 1.022-132.188-35.218.852-3.91 1.55-8.122 2.094-12.658zm-10.188 35.593c43.873 29.743 86.124 45.232 117.22 42.344 1.275 7.2-.42 12.227-3.438 15.844-4.498 5.39-14.17 9.068-28.188 7.25-23.577-3.058-57.274-22.018-85.594-65.438zm-62.22.344c-28.27 43.167-61.83 62.045-85.342 65.094-14.02 1.818-23.69-1.86-28.188-7.25-3.02-3.622-4.724-8.66-3.437-15.875 30.996 2.99 73.155-12.373 116.968-41.97z"}}]})(props);
};
