// THIS FILE IS AUTO GENERATED
var GenIcon = require('../lib').GenIcon
module.exports.BiLeftTopArrowCircle = function BiLeftTopArrowCircle (props) {
  return GenIcon({"tag":"svg","attr":{"viewBox":"0 0 24 24"},"child":[{"tag":"path","attr":{"d":"M11.993,2.007c-2.675,0-5.187,1.037-7.071,2.922c-3.899,3.899-3.899,10.243,0,14.143c1.885,1.885,4.396,2.923,7.071,2.923 c2.676,0,5.187-1.038,7.071-2.923c3.899-3.899,3.899-10.243,0-14.143C17.18,3.044,14.669,2.007,11.993,2.007z M17.65,17.657 c-1.507,1.507-3.516,2.337-5.657,2.337c-2.141,0-4.15-0.83-5.657-2.337c-3.119-3.119-3.119-8.195,0-11.314 c1.507-1.507,3.517-2.336,5.657-2.336c2.142,0,4.15,0.829,5.657,2.336C20.77,9.462,20.77,14.538,17.65,17.657z"}},{"tag":"path","attr":{"d":"M14.657 9L8.993 9 8.993 14.663 11.118 12.539 14.333 15.753 15.747 14.339 12.532 11.125z"}}]})(props);
};
