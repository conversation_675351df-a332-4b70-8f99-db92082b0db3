// THIS FILE IS AUTO GENERATED
var GenIcon = require('../lib').GenIcon
module.exports.GiScarWound = function GiScarWound (props) {
  return GenIcon({"tag":"svg","attr":{"viewBox":"0 0 512 512"},"child":[{"tag":"path","attr":{"d":"M17.736 19.96c16.94 32.065 35.82 63.162 56.496 93.315 8.384 21.977 3.6 46.57-37.845 76.315 150.19-40.237 106.49 56.516-11.266 126.22 199.958-74.2 249.65-57.033 121.08 93.788 101.084-73.473 180.62-53.61 147.825 37.632 38.067-29.477 69.664-35.763 93.87-25.097 34.812 24.628 71.08 48.27 108.668 70.935-20.512-40.28-42.167-79.152-65.374-116.306-18.072-38.867-22.457-79.24-7.493-113.918-56.766 70.273-91.592 5.3-4.924-85.084-122.303 75.43-177.787 37.864-16.875-106.178-123.854 65.763-219.54 83.267-157.507 18.285-44.75 12.79-70.03 6.265-91.474-2.93C111.59 61.1 66.732 38.62 17.736 19.958zm49.56 39.923c28.685 21.552 56.615 43.913 83.862 67.056l36.617-9.522-22.105 21.978c15.59 13.528 30.947 27.33 46.09 41.39l71.86-30.462-18.776 29.02.3-.24c-21.556 27.54 18.764 86.03 45.995 78.423l31.913-17.7-24.787 36.534 36.875 52.71c.262.3.526.598.788.9 13.642 12.504 31.23 1.71 43.05-14.525l-19.59 41.985c21.418 25.495 42.38 51.654 62.91 78.494-23.153-16.44-45.624-33.417-67.468-50.887-19.98-11.91-31.485.295-44.256 16.856 8.794-18.174 13.377-36.95 3.24-50.864-1.488-1.28-2.978-2.555-4.46-3.84l-44.856-35.346-47.738 26.48 17.187-25.336c7.31-12.403 3.43-27.175-5.648-41.144-10.91-10.88-21.647-21.896-32.216-33.043-1.66-1.227-3.312-2.38-4.957-3.493l-66.44 18.863 21.768-25.76c10.566-17.616 0-41.59-9.848-55.7-5.592-6.538-11.12-13.13-16.617-19.743l-43.666 7.283c18.428-7.3 31.37-22.376 16.844-40.04l-2.268-2.562c.83.86 1.57 1.713 2.268 2.563l2.062 2.33c-16.384-20.564-32.362-41.45-47.933-62.657z"}}]})(props);
};
