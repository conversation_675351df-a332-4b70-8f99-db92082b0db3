// THIS FILE IS AUTO GENERATED
import { GenIcon } from '../lib';
export function BiWinkSmile (props) {
  return GenIcon({"tag":"svg","attr":{"viewBox":"0 0 24 24"},"child":[{"tag":"path","attr":{"d":"M12,2C6.486,2,2,6.486,2,12c0,5.514,4.486,10,10,10s10-4.486,10-10C22,6.486,17.514,2,12,2z M12,20c-4.411,0-8-3.589-8-8 s3.589-8,8-8s8,3.589,8,8S16.411,20,12,20z"}},{"tag":"path","attr":{"d":"M14.828,14.828c-0.184,0.184-0.383,0.348-0.591,0.489c-0.215,0.145-0.444,0.269-0.679,0.368 c-0.245,0.103-0.497,0.182-0.75,0.233c-0.531,0.107-1.087,0.107-1.616,0c-0.254-0.052-0.506-0.131-0.749-0.232 c-0.236-0.101-0.466-0.225-0.679-0.368c-0.21-0.143-0.409-0.307-0.594-0.492c-0.182-0.18-0.346-0.379-0.488-0.59l-1.658,1.117 c0.215,0.319,0.462,0.619,0.733,0.889c0.272,0.273,0.571,0.52,0.888,0.733c0.32,0.217,0.663,0.402,1.021,0.554 c0.366,0.153,0.744,0.271,1.127,0.349C11.189,17.959,11.596,18,12,18s0.811-0.041,1.208-0.122c0.382-0.077,0.76-0.195,1.128-0.35 c0.355-0.15,0.698-0.336,1.021-0.554c0.314-0.213,0.613-0.459,0.885-0.73c0.272-0.271,0.52-0.571,0.734-0.891l-1.658-1.117 C15.175,14.447,15.011,14.646,14.828,14.828z"}},{"tag":"circle","attr":{"cx":"8.5","cy":"10.5","r":"1.5"}},{"tag":"path","attr":{"d":"M15.5,10c-2,0-2.5,2-2.5,2h5C18,12,17.499,10,15.5,10z"}}]})(props);
};
