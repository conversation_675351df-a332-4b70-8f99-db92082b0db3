// THIS FILE IS AUTO GENERATED
import { GenIcon } from '../lib';
export function GiPapers (props) {
  return GenIcon({"tag":"svg","attr":{"viewBox":"0 0 512 512"},"child":[{"tag":"path","attr":{"d":"M18.906 18.06v369.23C112.4 252.618 269.43 157.82 430.37 133.76L228.42 18.06H18.906zM325.72 179.327C200.38 223.948 86.405 311.052 18.157 422.568v33.602c113.074-111.488 277-176.38 434.373-175.25L325.72 179.326zm25.56 128.682c-125.218 21.642-246.974 83.6-333.124 174.812v10.297h58.916c113.9-65.58 251.166-95.325 379.492-80.814L351.28 308.008zm-2.253 120.96c-80.122 5.884-160.432 27.957-232.61 64.15h266.42l-33.81-64.15z"}}]})(props);
};
