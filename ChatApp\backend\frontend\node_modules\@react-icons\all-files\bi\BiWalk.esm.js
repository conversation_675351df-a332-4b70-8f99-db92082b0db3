// THIS FILE IS AUTO GENERATED
import { GenIcon } from '../lib';
export function BiWalk (props) {
  return GenIcon({"tag":"svg","attr":{"viewBox":"0 0 24 24"},"child":[{"tag":"circle","attr":{"cx":"13","cy":"4","r":"2"}},{"tag":"path","attr":{"d":"M13.978,12.27c0.245,0.368,0.611,0.647,1.031,0.787l2.675,0.892l0.633-1.896l-2.675-0.892l-1.663-2.495 c-0.192-0.288-0.457-0.521-0.769-0.679L11.776,7.27c-0.425-0.212-0.913-0.267-1.378-0.149L7.205,7.918 C6.639,8.059,6.163,8.439,5.899,8.964l-1.794,3.589l1.789,0.895l1.794-3.589l2.223-0.556l-1.804,8.346l-3.674,2.527l1.133,1.648 l3.675-2.528c0.421-0.29,0.713-0.725,0.82-1.225l0.517-2.388l2.517,1.888l0.925,4.625l1.961-0.393l-0.925-4.627 c-0.099-0.484-0.369-0.913-0.762-1.206l-2.171-1.628l0.647-3.885L13.978,12.27z"}}]})(props);
};
