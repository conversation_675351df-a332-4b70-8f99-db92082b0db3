"use strict";
exports.uk = void 0;
var _index = require("./uk/_lib/formatDistance.cjs");
var _index2 = require("./uk/_lib/formatLong.cjs");
var _index3 = require("./uk/_lib/formatRelative.cjs");
var _index4 = require("./uk/_lib/localize.cjs");
var _index5 = require("./uk/_lib/match.cjs");

/**
 * @category Locales
 * @summary Ukrainian locale.
 * @language Ukrainian
 * @iso-639-2 ukr
 * <AUTHOR> [@korzhyk](https://github.com/korzhyk)
 * <AUTHOR> [@shcherbyakdev](https://github.com/shcherbyakdev)
 */
const uk = (exports.uk = {
  code: "uk",
  formatDistance: _index.formatDistance,
  formatLong: _index2.formatLong,
  formatRelative: _index3.formatRelative,
  localize: _index4.localize,
  match: _index5.match,
  options: {
    weekStartsOn: 1 /* Monday */,
    firstWeekContainsDate: 1,
  },
});
