// THIS FILE IS AUTO GENERATED
import { GenIcon } from '../lib';
export function IoKeypadOutline (props) {
  return GenIcon({"tag":"svg","attr":{"viewBox":"0 0 512 512"},"child":[{"tag":"circle","attr":{"cx":"256","cy":"448","r":"32","fill":"none","strokeMiterlimit":"10","strokeWidth":"32"}},{"tag":"circle","attr":{"cx":"256","cy":"320","r":"32","fill":"none","strokeMiterlimit":"10","strokeWidth":"32"}},{"tag":"path","attr":{"fill":"none","strokeMiterlimit":"10","strokeWidth":"32","d":"M288 192a32 32 0 11-32-32 32 32 0 0132 32z"}},{"tag":"circle","attr":{"cx":"256","cy":"64","r":"32","fill":"none","strokeMiterlimit":"10","strokeWidth":"32"}},{"tag":"circle","attr":{"cx":"384","cy":"320","r":"32","fill":"none","strokeMiterlimit":"10","strokeWidth":"32"}},{"tag":"circle","attr":{"cx":"384","cy":"192","r":"32","fill":"none","strokeMiterlimit":"10","strokeWidth":"32"}},{"tag":"circle","attr":{"cx":"384","cy":"64","r":"32","fill":"none","strokeMiterlimit":"10","strokeWidth":"32"}},{"tag":"circle","attr":{"cx":"128","cy":"320","r":"32","fill":"none","strokeMiterlimit":"10","strokeWidth":"32"}},{"tag":"circle","attr":{"cx":"128","cy":"192","r":"32","fill":"none","strokeMiterlimit":"10","strokeWidth":"32"}},{"tag":"circle","attr":{"cx":"128","cy":"64","r":"32","fill":"none","strokeMiterlimit":"10","strokeWidth":"32"}}]})(props);
};
