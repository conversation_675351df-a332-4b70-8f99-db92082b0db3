// THIS FILE IS AUTO GENERATED
var GenIcon = require('../lib').GenIcon
module.exports.BiMessageAltEdit = function BiMessageAltEdit (props) {
  return GenIcon({"tag":"svg","attr":{"viewBox":"0 0 24 24"},"child":[{"tag":"path","attr":{"d":"M8.586,18L12,21.414L15.414,18H19c1.103,0,2-0.897,2-2V4c0-1.103-0.897-2-2-2H5C3.897,2,3,2.897,3,4v12 c0,1.103,0.897,2,2,2H8.586z M5,4h14v12h-4.414L12,18.586L9.414,16H5V4z"}},{"tag":"path","attr":{"d":"M12.479 7.219L7.502 12.188 7.502 13.987 9.302 13.987 14.277 9.018z"}},{"tag":"path","attr":{"transform":"rotate(44.984 14.913 6.585)","d":"M13.64 5.615H16.185000000000002V7.554H13.64z"}}]})(props);
};
