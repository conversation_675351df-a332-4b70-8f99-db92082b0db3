// THIS FILE IS AUTO GENERATED
var GenIcon = require('../lib').GenIcon
module.exports.GiTargeting = function GiTargeting (props) {
  return GenIcon({"tag":"svg","attr":{"viewBox":"0 0 512 512"},"child":[{"tag":"path","attr":{"d":"M310.537 18.758L295.6 74.506c-13.073-2.813-26.154-4.16-39.067-4.156v18.662c11.306.01 22.76 1.187 34.217 3.59l-14.488 54.066c2.972.543 5.946 1.206 8.916 2.002 3.11.834 6.152 1.798 9.127 2.873l14.502-54.123c11.127 3.643 21.627 8.365 31.425 14.006l9.338-16.174c-11.182-6.454-23.19-11.818-35.918-15.912l16.235-60.582h-19.35zM208.4 76.756c-31.462 8.497-60.265 25.152-83.328 48.181l13.15 13.15c8.15-8.16 17.1-15.426 26.684-21.704l27.936 48.387c5.127-3.557 10.544-6.68 16.19-9.334l-27.948-48.41c10.225-5.174 20.994-9.3 32.133-12.297L208.4 76.755zm179.668 48.105l-13.154 13.156c8.155 8.164 15.418 17.127 21.69 26.722l-48.692 28.114c3.55 5.132 6.664 10.554 9.31 16.205l48.725-28.13c5.168 10.23 9.292 21.003 12.278 32.147l17.97-4.816c-8.473-31.48-25.11-60.31-48.127-83.397zm-292.55 38.574c-6.442 11.175-11.8 23.17-15.885 35.888l-60.737-16.275v19.348L74.8 217.373c-2.805 13.06-4.13 26.126-4.122 39.025H89.33c.008-11.293 1.157-22.734 3.553-34.18l53.847 14.428c.554-3.077 1.233-6.157 2.057-9.232.805-3.005 1.732-5.944 2.762-8.822l-53.843-14.428c3.634-11.116 8.346-21.606 13.973-31.398l-16.162-9.332zm162.023 20.484c-.895.003-1.788.022-2.68.06-30.888 1.26-58.825 22.36-67.23 53.73-10.347 38.61 12.708 78.54 51.317 88.886 38.61 10.345 78.54-12.71 88.885-51.32 10.345-38.607-12.708-78.538-51.316-88.883-6.335-1.697-12.706-2.494-18.975-2.472zm-.01 18.658c1.287 0 2.582.045 3.88.137l-5.498 20.377c.9-.072 1.806-.12 2.725-.12 5.642 0 10.955 1.398 15.623 3.856l5.293-19.617c21.288 9.357 34.11 31.358 31.818 54.456l-19.15-5.168c0 .018.003.036.003.055 0 6.584-1.904 12.722-5.178 17.906l19.332 5.214c-9.793 20.695-31.702 32.98-54.574 30.39l5.385-19.96c-6.583-.28-12.67-2.457-17.74-5.995l-5.59 20.706c-19.83-9.728-31.712-30.68-29.83-52.753l21.054 5.68c-.014-.396-.03-.79-.03-1.19 0-6.18 1.675-11.964 4.587-16.938l-20.882-5.633c8.495-18.616 26.727-30.598 46.777-31.367.663-.025 1.328-.038 1.994-.04zm166.353 53.822c-.02 11.3-1.186 22.75-3.598 34.2l-54.342-14.56c-.547 3.007-1.214 6.02-2.02 9.024-.823 3.074-1.774 6.08-2.833 9.02l54.346 14.563c-3.65 11.117-8.38 21.608-14.024 31.398l16.172 9.338c6.46-11.173 11.833-23.17 15.936-35.89l60.654 16.252v-19.346l-55.805-14.953c2.822-13.065 4.162-26.137 4.17-39.045h-18.657zM95.027 299.672l-17.97 4.814c8.487 31.435 25.118 60.222 48.11 83.278l13.157-13.155c-8.142-8.15-15.396-17.094-21.664-26.67L165.057 320c-3.57-5.118-6.708-10.528-9.377-16.168l-48.37 27.928c-5.166-10.213-9.292-20.965-12.283-32.088zm224.965 47.95c-5.122 3.563-10.534 6.692-16.178 9.355l28.112 48.69c-10.22 5.157-20.98 9.274-32.112 12.255l4.815 17.965c31.454-8.46 60.26-25.07 83.34-48.053l-13.152-13.15c-8.158 8.14-17.112 15.39-26.7 21.652l-28.126-48.715zM218.81 361.08l-14.49 54.07c-11.104-3.64-21.583-8.36-31.363-13.992l-9.338 16.174c11.163 6.446 23.15 11.807 35.855 15.9l-16.293 60.8h19.35l14.993-55.96c13.053 2.815 26.115 4.172 39.01 4.178v-18.674c-11.288-.018-22.725-1.18-34.164-3.584l14.5-54.11c-3.113-.558-6.227-1.244-9.34-2.077-2.97-.796-5.875-1.71-8.72-2.725z"}}]})(props);
};
