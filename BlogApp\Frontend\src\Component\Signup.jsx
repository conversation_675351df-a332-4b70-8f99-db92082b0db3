import React from "react";
import styled from "styled-components";

import {  useForm } from "react-hook-form"
import API from '../axios.js'
import { useAuth } from "../Context/AuthProvider.jsx";
import { Link } from "react-router-dom";
import {useNavigate} from "react-router-dom"
import { useEffect } from "react";

// useEffect(() => {
 
// }, [])


const Signup = () => {


   const {authUser, setAuthUser} = useAuth()
   const navigate = useNavigate()

   console.log(authUser)


    const {
        register,
        handleSubmit,
        watch,
        formState: { errors },
    } = useForm()

    const password=watch("password")

    const validatePassword=(value)=>{
        return( value === password || "password  and ConfirmPassword do not match")
    }
    
    const onSubmit =  async (data)=> {
        const user = {
            name: data.name,
            email:data.email,
            password:data.password,
            confirmPassword:data.confirmPassword
        }

        try {
            const res = await API.post('/api/user/signup', user,{

            headers:{
                'Content-Type':'application/json'
            }

        })
        alert("Signup successful")
        console.log(res)
        navigate("/login")

        setAuthUser(res.data)
        // localStorage.setItem('userData',JSON.stringify(res.data))

        } catch (error) {
            console.log( "Signup " ,error)
            
            
        }

       
  
    }
 // github

        const handlelogin =  () => {
          window.location.assign(`https://github.com/login/oauth/authorize?client_id=Ov23liy3BsN6P3trYEpe&scope=user:email`)
        }
        useEffect(() => {
          
        }, [])
        

     useEffect(() => {

      const parmas= new URLSearchParams(window.location.search)
        const code = parmas.get('code')
        console.log(code)


        
          
          // setAuthUser(res.data)
          if(code && localStorage.getItem("access_token")==null){

          const getAccessToken = async ()=>{

            const res = await API.get(`/api/user/github/${code}`)

            const data = res.data
            console.log(data)
            localStorage.setItem("user", JSON.stringify(data.user));
            localStorage.setItem("access_token", JSON.stringify(data.access_token));
            console.log(data)
            setAuthUser(res.data)
            navigate("/profile")

         
          }

          getAccessToken()

      //     async function getUserDetail(access_token){
      //       const res = await API.get(`/api/user/github`,{
      //         headers:{
      //           'Authorization':`Bearer ${access_token}`
      //         }
      //       })

      //      const  data = res.data
      //       console.log(data)

            
         

          

      //   }
      // }
        }
       
        },[])
        



         
        
        

  return (
    <div  className="w-full h-screen flex justify-center items-center bg-amber-50">
      <StyledWrapper>
        <div className="form-box " onSubmit ={handleSubmit(onSubmit)}>
          <form className="form">
            <span className="title">Sign up</span>
            <span className="subtitle">
              Create a free account with your email.
            </span>
            <div className="form-container">
              <input type="text" className="input" placeholder="Full Name" {...register ("name",{require:true})} />
              {errors.name && <span>This field is required</span>}
              <input type="email" className="input" placeholder="Email"  {...register ("email",{require:true})}/>
              {errors.email && <span>This field is required</span>}
              <input type="password" className="input" placeholder="Password" {...register ("password",{require:true})} />
              {errors.password && <span>This field is required</span>}
              <input type="password" className="input" placeholder="Confirm Password" {...register ("confirmPassword",{require:true ,validate:validatePassword})} />
              {errors.confirmPassword && <span>This field is required</span>}
            </div>
            <button>Sign up</button>
          </form>

          {/* github */}
            <button className="bg-emerald-200 px-3 py-2 rounded-lg " onClick={handlelogin}>Github</button>
          <div className="form-section">
            <p>
              Have an account? <Link to="/login">Log in</Link>{" "}
            </p>
          </div>
        </div>
      </StyledWrapper>
    </div>
  );
};

const StyledWrapper = styled.div`
  .form-box {
    max-width: 300px;
    background: #f1f7fe;
    overflow: hidden;
    border-radius: 16px;
    color: #010101;
  }

  .form {
    position: relative;
    display: flex;
    flex-direction: column;
    padding: 32px 24px 24px;
    gap: 16px;
    text-align: center;
  }

  /*Form text*/
  .title {
    font-weight: bold;
    font-size: 1.6rem;
  }

  .subtitle {
    font-size: 1rem;
    color: #666;
  }

  /*Inputs box*/
  .form-container {
    overflow: hidden;
    border-radius: 8px;
    background-color: #fff;
    margin: 1rem 0 0.5rem;
    width: 100%;
  }

  .input {
    background: none;
    border: 0;
    outline: 0;
    height: 40px;
    width: 100%;
    border-bottom: 1px solid #eee;
    font-size: 0.9rem;
    padding: 8px 15px;
  }

  .form-section {
    padding: 16px;
    font-size: 0.85rem;
    background-color: #e0ecfb;
    box-shadow: rgb(0 0 0 / 8%) 0 -1px;
  }

  .form-section a {
    font-weight: bold;
    color: #0066ff;
    transition: color 0.3s ease;
  }

  .form-section a:hover {
    color: #005ce6;
    text-decoration: underline;
  }

  /*Button*/
  .form button {
    background-color: #0066ff;
    color: #fff;
    border: 0;
    border-radius: 24px;
    padding: 10px 16px;
    font-size: 1rem;
    font-weight: 600;
    cursor: pointer;
    transition: background-color 0.3s ease;
  }

  .form button:hover {
    background-color: #005ce6;
  }
`;

export default Signup;
