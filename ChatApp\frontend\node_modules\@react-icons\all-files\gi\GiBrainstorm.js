// THIS FILE IS AUTO GENERATED
var GenIcon = require('../lib').GenIcon
module.exports.GiBrainstorm = function GiBrainstorm (props) {
  return GenIcon({"tag":"svg","attr":{"viewBox":"0 0 512 512"},"child":[{"tag":"path","attr":{"d":"M209.5 21.094c-.55.008-1.115.033-1.688.062H207.345c-10.422 0-19.238 3.402-23.125 7.938-3.646 4.252-5.472 10.1-.095 22.156 5.748 8.803 10.352 20.848 14.344 36.375l-18.126 4.656c-6.755-26.27-15.17-36.83-20.875-39.343-2.854-1.255-5.655-1.367-9.845-.093s-9.438 4.185-15.156 8.406c-8.817 6.506-14.327 14.915-14 22.03.25 5.477 3.61 12.565 15.25 20.345 1.096.61 2.187 1.205 3.31 1.844l-.093.186c2.797 1.684 5.95 3.396 9.594 5.125l-8.03 16.876c-5.075-2.408-9.628-4.944-13.688-7.594-14.715-8.047-25.54-11.577-31.312-11.093-3.078.257-4.93 1.067-7.25 3.374-2.32 2.306-4.914 6.49-7.28 13.094-3.94 10.99-4.602 19.98-1.595 24.874 2.392 3.895 8.464 8.087 22.844 9.532 3.59.074 7.412.22 11.593.406v.125c.4 0 .776.004 1.187 0l.156 18.688c-4.292.04-8.302-.11-12.094-.407-11.966-.073-19.223.894-22.218 2.406-1.876.947-2.586 1.665-3.625 3.782-1.04 2.116-2.022 5.81-2.69 11.25-.615 5.033-.204 8.733.845 11.28 1.05 2.55 2.562 4.42 6.28 6.407 7.44 3.975 24.322 6.5 52.564 4.095l1.593 18.625c-21.627 1.84-37.814 1.534-50.313-1.563-.853 3.737-.672 6.658.03 9.125.12.412.257.82.407 1.22.012.03.02.062.032.093 1.71 3.198 3.952 5.995 6.874 8.438.018.013.044.017.062.03 7.05 5.22 18.622 8.188 25.47 8.188h103.81c12.11 0 22.62-8.916 26.69-25.092l8.06-32.063 9.94 31.563c5.216 16.602 16.087 25.593 26.843 25.593h103.81c6.26 0 18.37-3.946 25.595-10.25.903-.787 1.747-1.608 2.5-2.436.142-.157.27-.31.406-.47.02-.022.046-.038.064-.06 1.5-1.915 2.716-4 3.75-6.25.126-.346.274-.686.375-1.033.612-2.107.84-4.314.436-6.874-12.646 3.365-29.16 3.74-51.437 1.844l1.592-18.625c28.243 2.404 45.093-.12 52.532-4.094 3.72-1.986 5.232-3.857 6.28-6.405 1.05-2.548 1.492-6.248.876-11.28-.667-5.442-1.68-9.135-2.72-11.25-1.038-2.118-1.748-2.835-3.624-3.782-2.995-1.513-10.252-2.48-22.22-2.407-3.79.296-7.8.447-12.092.406l.187-18.687c.412.004.787 0 1.19 0v-.125c4.18-.185 8-.332 11.592-.406 14.38-1.445 20.452-5.637 22.844-9.53 3.007-4.897 2.345-13.885-1.594-24.876-2.366-6.605-4.992-10.788-7.312-13.094-2.32-2.307-4.172-3.117-7.25-3.375-5.772-.485-16.57 3.045-31.28 11.093-4.066 2.654-8.636 5.18-13.72 7.593l-8-16.875c3.644-1.728 6.797-3.44 9.594-5.124l-.125-.187c1.185-.676 2.343-1.297 3.5-1.94 11.52-7.74 14.843-14.796 15.092-20.25.326-7.115-5.185-15.524-14-22.03-5.718-4.22-10.966-7.132-15.156-8.406-4.19-1.274-6.99-1.162-9.844.093-1.98.872-4.294 2.745-6.75 5.876-.453 1.054-.944 2.124-1.468 3.187l-.594-.28c-4.042 6.21-8.336 15.944-12.094 30.56l-18.094-4.655c4.456-17.33 9.653-30.324 16.375-39.313 3.834-10.083 2.083-15.33-1.25-19.218-3.887-4.536-12.703-7.938-23.125-7.938H308.094c-9.16-.46-15.315 1.746-20.406 5.563-5.092 3.816-9.184 9.628-12.282 16.905-6.195 14.554-7.937 34.38-7.937 48.906v.064l-.19 34.03v.095h-18.686v-.095l-.188-34.03v-.064c0-14.525-1.71-34.35-7.906-48.905-3.098-7.277-7.19-13.09-12.28-16.906-4.775-3.58-10.477-5.75-18.72-5.626zm48.78 239.47c-8.486 12.207-21.288 20.186-36.092 20.186H181.53l98.126 80.625-82.22 1.28 217.408 133.44L342.28 387.78l55.126 4.75-76.03-111.78h-27.657c-14.393 0-26.788-8.02-35.44-20.188z","fillRule":"evenodd"}}]})(props);
};
