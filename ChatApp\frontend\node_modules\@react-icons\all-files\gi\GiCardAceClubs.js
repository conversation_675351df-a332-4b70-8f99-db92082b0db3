// THIS FILE IS AUTO GENERATED
var GenIcon = require('../lib').GenIcon
module.exports.GiCardAceClubs = function GiCardAceClubs (props) {
  return GenIcon({"tag":"svg","attr":{"viewBox":"0 0 512 512"},"child":[{"tag":"path","attr":{"d":"M119.436 36c-16.126 0-29.2 17.237-29.2 38.5v363c0 21.263 13.074 38.5 29.2 38.5h275.298c16.126 0 29.198-17.237 29.198-38.5v-363c0-21.263-13.072-38.5-29.198-38.5H119.436zm26.832 8.408v.002h.015c13.587.01 24.442 10.853 24.442 24.442 0 5.71-2.003 10.997-5.266 15.173 1.12-.158 2.232-.31 3.396-.31 13.595 0 24.458 11.157 24.458 24.752 0 13.594-10.863 24.752-24.458 24.752-5.76 0-11.08-2.048-15.294-5.422l12.609 30.193h-40.117l12.42-29.744c-4.12 3.115-9.233 4.973-14.776 4.973-13.594 0-24.752-11.16-24.752-24.754 0-13.595 11.158-24.752 24.752-24.752 1.059 0 2.058.184 3.082.312-3.215-4.16-5.248-9.509-5.248-15.173 0-13.589 11.15-24.434 24.737-24.444zm95.466 120.596h7.965l63.121 160.834c2.536 6.498 7.727 9.748 15.573 9.748h5.468v8.916h-70.134v-8.916h5.586c7.29 0 12.443-.792 15.455-2.377 2.06-1.11 3.09-2.813 3.09-5.111 0-1.348-.278-2.774-.833-4.28l-14.62-37.326h-69.423l-8.2 21.397c-2.14 5.705-3.212 10.222-3.212 13.55 0 3.884 1.784 7.213 5.35 9.987 3.645 2.774 8.916 4.16 15.81 4.16h5.944v8.916h-63.715v-8.916c6.815 0 12.204-1.466 16.166-4.399 3.962-3.01 7.607-8.676 10.936-16.998l59.673-149.185zm-3.447 33.879l-31.5 78.336h62.17l-30.67-78.336zm107.508 155.129h40.117l-12.611 30.193c4.215-3.374 9.535-5.422 15.295-5.422 13.594 0 24.459 11.157 24.459 24.752 0 13.595-10.865 24.752-24.46 24.752-1.163 0-2.275-.152-3.396-.31 3.263 4.176 5.266 9.462 5.266 15.173 0 13.589-10.853 24.433-24.44 24.442h-.017c-13.588-.01-24.735-10.853-24.735-24.442 0-5.665 2.033-11.013 5.248-15.173-1.023.128-2.025.312-3.084.312-13.594 0-24.751-11.159-24.751-24.754 0-13.594 11.157-24.752 24.752-24.752 5.542 0 10.655 1.858 14.775 4.973l-12.418-29.744z"}}]})(props);
};
