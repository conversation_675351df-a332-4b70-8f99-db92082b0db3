// THIS FILE IS AUTO GENERATED
var GenIcon = require('../lib').GenIcon
module.exports.BiStopwatch = function BiStopwatch (props) {
  return GenIcon({"tag":"svg","attr":{"viewBox":"0 0 24 24"},"child":[{"tag":"path","attr":{"d":"M12,5c-4.411,0-8,3.589-8,8s3.589,8,8,8s8-3.589,8-8S16.411,5,12,5z M12,19c-3.309,0-6-2.691-6-6s2.691-6,6-6s6,2.691,6,6 S15.309,19,12,19z"}},{"tag":"path","attr":{"d":"M11 9H13V14H11zM9 2H15V4H9z"}},{"tag":"path","attr":{"transform":"rotate(-134.999 19 6)","d":"M17.586 5H20.413999999999998V7H17.586z"}}]})(props);
};
