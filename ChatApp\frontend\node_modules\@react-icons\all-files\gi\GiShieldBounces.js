// THIS FILE IS AUTO GENERATED
var GenIcon = require('../lib').GenIcon
module.exports.GiShieldBounces = function GiShieldBounces (props) {
  return GenIcon({"tag":"svg","attr":{"viewBox":"0 0 512 512"},"child":[{"tag":"path","attr":{"d":"M142.37 21.035c-1.544-.007-3.092-.004-4.64.01-41.1.368-83.607 8.237-120.058 21.103.386 80.173 7.825 165.28 27.805 235.348 20.04 70.286 52.434 124.282 100.748 145.553 53.917-24.07 85.753-72.574 104.593-136.5 18.927-64.216 24.042-143.3 24.215-224.29-28.374-25.59-71.56-38.62-118.838-40.867-4.578-.218-9.19-.336-13.824-.358zm2.136 27.844c4.387.018 8.76.13 13.105.345 34.76 1.712 67.982 9.664 90.966 29.44l3.25 2.796v4.288c0 62.11-3.212 125.186-16.738 178.236-13.526 53.05-37.82 97.014-80.918 117.147l-3.87 1.81-3.902-1.745c-40.838-18.267-64.292-66.432-78.556-124.086C53.578 199.455 48.707 131.105 48.707 69v-7.28l7.06-1.78c26.68-6.732 58.032-11.2 88.74-11.063zM347.18 60.376c-.007 11.243-.104 22.458-.315 33.615l-53.578 5.467c-.145 6.3-.32 12.584-.547 18.84l53.647-5.474c-.39 12.47-.93 24.85-1.676 37.084l.52 4.436L500.1 87.36 347.18 60.376zm-202.75 7.195c-26.233-.098-53.274 3.532-76.955 8.953.39 59.084 5.394 123.083 18.51 176.096 13.11 52.988 34.476 93.765 64.204 109.554 33.705-17.703 54.448-54.405 66.79-102.807C229.475 210.35 232.9 150.27 233.065 90.34c-18.126-13.75-45.83-20.945-76.375-22.45-4.048-.2-8.142-.302-12.26-.318zm198.648 105.07c-.778 9.35-1.702 18.59-2.758 27.715l-52.654-8.59c-.674 6.24-1.412 12.436-2.227 18.57l52.5 8.567c-1.68 11.92-3.626 23.612-5.905 34.994l141.606-30.613L343.08 172.64zm-12.865 99.573l-8.844 24.767c-.607 2.012-1.23 4.007-1.864 5.993l-48.633-18.643c-.69 2.516-1.398 5.02-2.13 7.502-1.037 3.52-2.128 7-3.253 10.45l47.885 18.355c-1.41 3.737-2.873 7.425-4.398 11.048l-.004.01-7.89 22.095 150.517 7.754-121.387-89.332zm-95.79 97.205c-3.44 5.338-7.074 10.486-10.907 15.434l29.248 28.025c-8.436 7.337-17.59 13.893-27.545 19.567l140.58 62.37-78.357-122.184c-6.4 9.8-13.464 18.9-21.238 27.24l-31.783-30.453z"}}]})(props);
};
