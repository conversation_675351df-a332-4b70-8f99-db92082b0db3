:root:has(input.theme-controller[value=sunset]:checked),[data-theme="sunset"] {
color-scheme: dark;
--color-base-100: oklch(22% 0.019 237.69);
--color-base-200: oklch(20% 0.019 237.69);
--color-base-300: oklch(18% 0.019 237.69);
--color-base-content: oklch(77.383% 0.043 245.096);
--color-primary: oklch(74.703% 0.158 39.947);
--color-primary-content: oklch(14.94% 0.031 39.947);
--color-secondary: oklch(72.537% 0.177 2.72);
--color-secondary-content: oklch(14.507% 0.035 2.72);
--color-accent: oklch(71.294% 0.166 299.844);
--color-accent-content: oklch(14.258% 0.033 299.844);
--color-neutral: oklch(26% 0.019 237.69);
--color-neutral-content: oklch(70% 0.019 237.69);
--color-info: oklch(85.559% 0.085 206.015);
--color-info-content: oklch(17.111% 0.017 206.015);
--color-success: oklch(85.56% 0.085 144.778);
--color-success-content: oklch(17.112% 0.017 144.778);
--color-warning: oklch(85.569% 0.084 74.427);
--color-warning-content: oklch(17.113% 0.016 74.427);
--color-error: oklch(85.511% 0.078 16.886);
--color-error-content: oklch(17.102% 0.015 16.886);
--radius-selector: 1rem;
--radius-field: 0.5rem;
--radius-box: 1rem;
--size-selector: 0.25rem;
--size-field: 0.25rem;
--border: 1px;
--depth: 0;
--noise: 0;
}
