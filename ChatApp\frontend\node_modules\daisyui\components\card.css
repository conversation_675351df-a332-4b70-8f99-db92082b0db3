/*! 🌼 daisyUI 5.0.42 - MIT License */ @layer utilities{.card{border-radius:var(--radius-box);outline-offset:2px;outline:0 solid #0000;flex-direction:column;transition:outline .2s ease-in-out;display:flex;position:relative;&:focus{--tw-outline-style:none;outline-style:none;@media (forced-colors:active){&{outline-offset:2px;outline:2px solid #0000}}}&:focus-visible{outline-color:currentColor}& :where(figure:first-child){border-start-start-radius:inherit;border-start-end-radius:inherit;border-end-end-radius:unset;border-end-start-radius:unset;overflow:hidden}& :where(figure:last-child){border-start-start-radius:unset;border-start-end-radius:unset;border-end-end-radius:inherit;border-end-start-radius:inherit;overflow:hidden}&:where(.card-border){border:var(--border)solid var(--color-base-200)}&:where(.card-dash){border:var(--border)dashed var(--color-base-200)}&.image-full{display:grid;&>*{grid-row-start:1;grid-column-start:1}&>.card-body{color:var(--color-neutral-content);position:relative}& :where(figure){border-radius:inherit;overflow:hidden}&>figure img{object-fit:cover;filter:brightness(28%);height:100%}}& figure{justify-content:center;align-items:center;display:flex}&:has(>input:is(input[type=checkbox],input[type=radio])){cursor:pointer;user-select:none}&:has(>:checked){outline:2px solid}}.card-title{font-size:var(--cardtitle-fs,1.125rem);align-items:center;gap:.5rem;font-weight:600;display:flex}.card-body{padding:var(--card-p,1.5rem);font-size:var(--card-fs,.875rem);flex-direction:column;flex:auto;gap:.5rem;display:flex;& :where(p){flex-grow:1}}.card-actions{flex-wrap:wrap;align-items:flex-start;gap:.5rem;display:flex}.card-xs{& .card-body{--card-p:.5rem;--card-fs:.6875rem}& .card-title{--cardtitle-fs:.875rem}}.card-sm{& .card-body{--card-p:1rem;--card-fs:.75rem}& .card-title{--cardtitle-fs:1rem}}.card-md{& .card-body{--card-p:1.5rem;--card-fs:.875rem}& .card-title{--cardtitle-fs:1.125rem}}.card-lg{& .card-body{--card-p:2rem;--card-fs:1rem}& .card-title{--cardtitle-fs:1.25rem}}.card-xl{& .card-body{--card-p:2.5rem;--card-fs:1.125rem}& .card-title{--cardtitle-fs:1.375rem}}.card-side{flex-direction:row;align-items:stretch;& :where(figure:first-child){border-start-start-radius:inherit;border-start-end-radius:unset;border-end-end-radius:unset;border-end-start-radius:inherit;overflow:hidden}& :where(figure:last-child){border-start-start-radius:unset;border-start-end-radius:inherit;border-end-end-radius:inherit;border-end-start-radius:unset;overflow:hidden}& figure>*{max-width:unset}& :where(figure>*){object-fit:cover;width:100%;height:100%}}@media (width>=640px){.sm\:card{border-radius:var(--radius-box);outline-offset:2px;outline:0 solid #0000;flex-direction:column;transition:outline .2s ease-in-out;display:flex;position:relative;&:focus{--tw-outline-style:none;outline-style:none;@media (forced-colors:active){&{outline-offset:2px;outline:2px solid #0000}}}&:focus-visible{outline-color:currentColor}& :where(figure:first-child){border-start-start-radius:inherit;border-start-end-radius:inherit;border-end-end-radius:unset;border-end-start-radius:unset;overflow:hidden}& :where(figure:last-child){border-start-start-radius:unset;border-start-end-radius:unset;border-end-end-radius:inherit;border-end-start-radius:inherit;overflow:hidden}&:where(.card-border){border:var(--border)solid var(--color-base-200)}&:where(.card-dash){border:var(--border)dashed var(--color-base-200)}&.image-full{display:grid;&>*{grid-row-start:1;grid-column-start:1}&>.card-body{color:var(--color-neutral-content);position:relative}& :where(figure){border-radius:inherit;overflow:hidden}&>figure img{object-fit:cover;filter:brightness(28%);height:100%}}& figure{justify-content:center;align-items:center;display:flex}&:has(>input:is(input[type=checkbox],input[type=radio])){cursor:pointer;user-select:none}&:has(>:checked){outline:2px solid}}.sm\:card-title{font-size:var(--cardtitle-fs,1.125rem);align-items:center;gap:.5rem;font-weight:600;display:flex}.sm\:card-body{padding:var(--card-p,1.5rem);font-size:var(--card-fs,.875rem);flex-direction:column;flex:auto;gap:.5rem;display:flex;& :where(p){flex-grow:1}}.sm\:card-actions{flex-wrap:wrap;align-items:flex-start;gap:.5rem;display:flex}.sm\:card-xs{& .card-body{--card-p:.5rem;--card-fs:.6875rem}& .card-title{--cardtitle-fs:.875rem}}.sm\:card-sm{& .card-body{--card-p:1rem;--card-fs:.75rem}& .card-title{--cardtitle-fs:1rem}}.sm\:card-md{& .card-body{--card-p:1.5rem;--card-fs:.875rem}& .card-title{--cardtitle-fs:1.125rem}}.sm\:card-lg{& .card-body{--card-p:2rem;--card-fs:1rem}& .card-title{--cardtitle-fs:1.25rem}}.sm\:card-xl{& .card-body{--card-p:2.5rem;--card-fs:1.125rem}& .card-title{--cardtitle-fs:1.375rem}}.sm\:card-side{flex-direction:row;align-items:stretch;& :where(figure:first-child){border-start-start-radius:inherit;border-start-end-radius:unset;border-end-end-radius:unset;border-end-start-radius:inherit;overflow:hidden}& :where(figure:last-child){border-start-start-radius:unset;border-start-end-radius:inherit;border-end-end-radius:inherit;border-end-start-radius:unset;overflow:hidden}& figure>*{max-width:unset}& :where(figure>*){object-fit:cover;width:100%;height:100%}}}@media (width>=768px){.md\:card{border-radius:var(--radius-box);outline-offset:2px;outline:0 solid #0000;flex-direction:column;transition:outline .2s ease-in-out;display:flex;position:relative;&:focus{--tw-outline-style:none;outline-style:none;@media (forced-colors:active){&{outline-offset:2px;outline:2px solid #0000}}}&:focus-visible{outline-color:currentColor}& :where(figure:first-child){border-start-start-radius:inherit;border-start-end-radius:inherit;border-end-end-radius:unset;border-end-start-radius:unset;overflow:hidden}& :where(figure:last-child){border-start-start-radius:unset;border-start-end-radius:unset;border-end-end-radius:inherit;border-end-start-radius:inherit;overflow:hidden}&:where(.card-border){border:var(--border)solid var(--color-base-200)}&:where(.card-dash){border:var(--border)dashed var(--color-base-200)}&.image-full{display:grid;&>*{grid-row-start:1;grid-column-start:1}&>.card-body{color:var(--color-neutral-content);position:relative}& :where(figure){border-radius:inherit;overflow:hidden}&>figure img{object-fit:cover;filter:brightness(28%);height:100%}}& figure{justify-content:center;align-items:center;display:flex}&:has(>input:is(input[type=checkbox],input[type=radio])){cursor:pointer;user-select:none}&:has(>:checked){outline:2px solid}}.md\:card-title{font-size:var(--cardtitle-fs,1.125rem);align-items:center;gap:.5rem;font-weight:600;display:flex}.md\:card-body{padding:var(--card-p,1.5rem);font-size:var(--card-fs,.875rem);flex-direction:column;flex:auto;gap:.5rem;display:flex;& :where(p){flex-grow:1}}.md\:card-actions{flex-wrap:wrap;align-items:flex-start;gap:.5rem;display:flex}.md\:card-xs{& .card-body{--card-p:.5rem;--card-fs:.6875rem}& .card-title{--cardtitle-fs:.875rem}}.md\:card-sm{& .card-body{--card-p:1rem;--card-fs:.75rem}& .card-title{--cardtitle-fs:1rem}}.md\:card-md{& .card-body{--card-p:1.5rem;--card-fs:.875rem}& .card-title{--cardtitle-fs:1.125rem}}.md\:card-lg{& .card-body{--card-p:2rem;--card-fs:1rem}& .card-title{--cardtitle-fs:1.25rem}}.md\:card-xl{& .card-body{--card-p:2.5rem;--card-fs:1.125rem}& .card-title{--cardtitle-fs:1.375rem}}.md\:card-side{flex-direction:row;align-items:stretch;& :where(figure:first-child){border-start-start-radius:inherit;border-start-end-radius:unset;border-end-end-radius:unset;border-end-start-radius:inherit;overflow:hidden}& :where(figure:last-child){border-start-start-radius:unset;border-start-end-radius:inherit;border-end-end-radius:inherit;border-end-start-radius:unset;overflow:hidden}& figure>*{max-width:unset}& :where(figure>*){object-fit:cover;width:100%;height:100%}}}@media (width>=1024px){.lg\:card{border-radius:var(--radius-box);outline-offset:2px;outline:0 solid #0000;flex-direction:column;transition:outline .2s ease-in-out;display:flex;position:relative;&:focus{--tw-outline-style:none;outline-style:none;@media (forced-colors:active){&{outline-offset:2px;outline:2px solid #0000}}}&:focus-visible{outline-color:currentColor}& :where(figure:first-child){border-start-start-radius:inherit;border-start-end-radius:inherit;border-end-end-radius:unset;border-end-start-radius:unset;overflow:hidden}& :where(figure:last-child){border-start-start-radius:unset;border-start-end-radius:unset;border-end-end-radius:inherit;border-end-start-radius:inherit;overflow:hidden}&:where(.card-border){border:var(--border)solid var(--color-base-200)}&:where(.card-dash){border:var(--border)dashed var(--color-base-200)}&.image-full{display:grid;&>*{grid-row-start:1;grid-column-start:1}&>.card-body{color:var(--color-neutral-content);position:relative}& :where(figure){border-radius:inherit;overflow:hidden}&>figure img{object-fit:cover;filter:brightness(28%);height:100%}}& figure{justify-content:center;align-items:center;display:flex}&:has(>input:is(input[type=checkbox],input[type=radio])){cursor:pointer;user-select:none}&:has(>:checked){outline:2px solid}}.lg\:card-title{font-size:var(--cardtitle-fs,1.125rem);align-items:center;gap:.5rem;font-weight:600;display:flex}.lg\:card-body{padding:var(--card-p,1.5rem);font-size:var(--card-fs,.875rem);flex-direction:column;flex:auto;gap:.5rem;display:flex;& :where(p){flex-grow:1}}.lg\:card-actions{flex-wrap:wrap;align-items:flex-start;gap:.5rem;display:flex}.lg\:card-xs{& .card-body{--card-p:.5rem;--card-fs:.6875rem}& .card-title{--cardtitle-fs:.875rem}}.lg\:card-sm{& .card-body{--card-p:1rem;--card-fs:.75rem}& .card-title{--cardtitle-fs:1rem}}.lg\:card-md{& .card-body{--card-p:1.5rem;--card-fs:.875rem}& .card-title{--cardtitle-fs:1.125rem}}.lg\:card-lg{& .card-body{--card-p:2rem;--card-fs:1rem}& .card-title{--cardtitle-fs:1.25rem}}.lg\:card-xl{& .card-body{--card-p:2.5rem;--card-fs:1.125rem}& .card-title{--cardtitle-fs:1.375rem}}.lg\:card-side{flex-direction:row;align-items:stretch;& :where(figure:first-child){border-start-start-radius:inherit;border-start-end-radius:unset;border-end-end-radius:unset;border-end-start-radius:inherit;overflow:hidden}& :where(figure:last-child){border-start-start-radius:unset;border-start-end-radius:inherit;border-end-end-radius:inherit;border-end-start-radius:unset;overflow:hidden}& figure>*{max-width:unset}& :where(figure>*){object-fit:cover;width:100%;height:100%}}}@media (width>=1280px){.xl\:card{border-radius:var(--radius-box);outline-offset:2px;outline:0 solid #0000;flex-direction:column;transition:outline .2s ease-in-out;display:flex;position:relative;&:focus{--tw-outline-style:none;outline-style:none;@media (forced-colors:active){&{outline-offset:2px;outline:2px solid #0000}}}&:focus-visible{outline-color:currentColor}& :where(figure:first-child){border-start-start-radius:inherit;border-start-end-radius:inherit;border-end-end-radius:unset;border-end-start-radius:unset;overflow:hidden}& :where(figure:last-child){border-start-start-radius:unset;border-start-end-radius:unset;border-end-end-radius:inherit;border-end-start-radius:inherit;overflow:hidden}&:where(.card-border){border:var(--border)solid var(--color-base-200)}&:where(.card-dash){border:var(--border)dashed var(--color-base-200)}&.image-full{display:grid;&>*{grid-row-start:1;grid-column-start:1}&>.card-body{color:var(--color-neutral-content);position:relative}& :where(figure){border-radius:inherit;overflow:hidden}&>figure img{object-fit:cover;filter:brightness(28%);height:100%}}& figure{justify-content:center;align-items:center;display:flex}&:has(>input:is(input[type=checkbox],input[type=radio])){cursor:pointer;user-select:none}&:has(>:checked){outline:2px solid}}.xl\:card-title{font-size:var(--cardtitle-fs,1.125rem);align-items:center;gap:.5rem;font-weight:600;display:flex}.xl\:card-body{padding:var(--card-p,1.5rem);font-size:var(--card-fs,.875rem);flex-direction:column;flex:auto;gap:.5rem;display:flex;& :where(p){flex-grow:1}}.xl\:card-actions{flex-wrap:wrap;align-items:flex-start;gap:.5rem;display:flex}.xl\:card-xs{& .card-body{--card-p:.5rem;--card-fs:.6875rem}& .card-title{--cardtitle-fs:.875rem}}.xl\:card-sm{& .card-body{--card-p:1rem;--card-fs:.75rem}& .card-title{--cardtitle-fs:1rem}}.xl\:card-md{& .card-body{--card-p:1.5rem;--card-fs:.875rem}& .card-title{--cardtitle-fs:1.125rem}}.xl\:card-lg{& .card-body{--card-p:2rem;--card-fs:1rem}& .card-title{--cardtitle-fs:1.25rem}}.xl\:card-xl{& .card-body{--card-p:2.5rem;--card-fs:1.125rem}& .card-title{--cardtitle-fs:1.375rem}}.xl\:card-side{flex-direction:row;align-items:stretch;& :where(figure:first-child){border-start-start-radius:inherit;border-start-end-radius:unset;border-end-end-radius:unset;border-end-start-radius:inherit;overflow:hidden}& :where(figure:last-child){border-start-start-radius:unset;border-start-end-radius:inherit;border-end-end-radius:inherit;border-end-start-radius:unset;overflow:hidden}& figure>*{max-width:unset}& :where(figure>*){object-fit:cover;width:100%;height:100%}}}@media (width>=1536px){.\32 xl\:card{border-radius:var(--radius-box);outline-offset:2px;outline:0 solid #0000;flex-direction:column;transition:outline .2s ease-in-out;display:flex;position:relative;&:focus{--tw-outline-style:none;outline-style:none;@media (forced-colors:active){&{outline-offset:2px;outline:2px solid #0000}}}&:focus-visible{outline-color:currentColor}& :where(figure:first-child){border-start-start-radius:inherit;border-start-end-radius:inherit;border-end-end-radius:unset;border-end-start-radius:unset;overflow:hidden}& :where(figure:last-child){border-start-start-radius:unset;border-start-end-radius:unset;border-end-end-radius:inherit;border-end-start-radius:inherit;overflow:hidden}&:where(.card-border){border:var(--border)solid var(--color-base-200)}&:where(.card-dash){border:var(--border)dashed var(--color-base-200)}&.image-full{display:grid;&>*{grid-row-start:1;grid-column-start:1}&>.card-body{color:var(--color-neutral-content);position:relative}& :where(figure){border-radius:inherit;overflow:hidden}&>figure img{object-fit:cover;filter:brightness(28%);height:100%}}& figure{justify-content:center;align-items:center;display:flex}&:has(>input:is(input[type=checkbox],input[type=radio])){cursor:pointer;user-select:none}&:has(>:checked){outline:2px solid}}.\32 xl\:card-title{font-size:var(--cardtitle-fs,1.125rem);align-items:center;gap:.5rem;font-weight:600;display:flex}.\32 xl\:card-body{padding:var(--card-p,1.5rem);font-size:var(--card-fs,.875rem);flex-direction:column;flex:auto;gap:.5rem;display:flex;& :where(p){flex-grow:1}}.\32 xl\:card-actions{flex-wrap:wrap;align-items:flex-start;gap:.5rem;display:flex}.\32 xl\:card-xs{& .card-body{--card-p:.5rem;--card-fs:.6875rem}& .card-title{--cardtitle-fs:.875rem}}.\32 xl\:card-sm{& .card-body{--card-p:1rem;--card-fs:.75rem}& .card-title{--cardtitle-fs:1rem}}.\32 xl\:card-md{& .card-body{--card-p:1.5rem;--card-fs:.875rem}& .card-title{--cardtitle-fs:1.125rem}}.\32 xl\:card-lg{& .card-body{--card-p:2rem;--card-fs:1rem}& .card-title{--cardtitle-fs:1.25rem}}.\32 xl\:card-xl{& .card-body{--card-p:2.5rem;--card-fs:1.125rem}& .card-title{--cardtitle-fs:1.375rem}}.\32 xl\:card-side{flex-direction:row;align-items:stretch;& :where(figure:first-child){border-start-start-radius:inherit;border-start-end-radius:unset;border-end-end-radius:unset;border-end-start-radius:inherit;overflow:hidden}& :where(figure:last-child){border-start-start-radius:unset;border-start-end-radius:inherit;border-end-end-radius:inherit;border-end-start-radius:unset;overflow:hidden}& figure>*{max-width:unset}& :where(figure>*){object-fit:cover;width:100%;height:100%}}}}