// THIS FILE IS AUTO GENERATED
import { GenIcon } from '../lib';
export function IoBarbellOutline (props) {
  return GenIcon({"tag":"svg","attr":{"viewBox":"0 0 512 512"},"child":[{"tag":"path","attr":{"fill":"none","strokeLinecap":"round","strokeLinejoin":"round","strokeWidth":"32","d":"M48 256h416"}},{"tag":"rect","attr":{"width":"32","height":"256","x":"384","y":"128","fill":"none","strokeLinecap":"round","strokeLinejoin":"round","strokeWidth":"32","rx":"16","ry":"16"}},{"tag":"rect","attr":{"width":"32","height":"256","x":"96","y":"128","fill":"none","strokeLinecap":"round","strokeLinejoin":"round","strokeWidth":"32","rx":"16","ry":"16"}},{"tag":"rect","attr":{"width":"16","height":"128","x":"32","y":"192","fill":"none","strokeLinecap":"round","strokeLinejoin":"round","strokeWidth":"32","rx":"8","ry":"8"}},{"tag":"rect","attr":{"width":"16","height":"128","x":"464","y":"192","fill":"none","strokeLinecap":"round","strokeLinejoin":"round","strokeWidth":"32","rx":"8","ry":"8"}}]})(props);
};
