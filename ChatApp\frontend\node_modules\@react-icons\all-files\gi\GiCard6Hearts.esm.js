// THIS FILE IS AUTO GENERATED
import { GenIcon } from '../lib';
export function GiCard6Hearts (props) {
  return GenIcon({"tag":"svg","attr":{"viewBox":"0 0 512 512"},"child":[{"tag":"path","attr":{"d":"M119.436 36c-16.126 0-29.2 17.237-29.2 38.5v363c0 21.263 13.074 38.5 29.2 38.5h275.298c16.126 0 29.198-17.237 29.198-38.5v-363c0-21.263-13.072-38.5-29.198-38.5H119.436zm57.216 16.174c13.613-.319 26.504 9.854 27.075 28.043.976 31.09-47.74 52.945-52.313 70.015-4.997-18.649-51.413-37.573-52.45-70.015-.994-31.155 37.404-37.907 52.452-11.846 6.262-10.846 15.923-15.978 25.236-16.195v-.002zm83.166 124.361c11.57 0 21.398 3.17 29.48 9.51 6.737 5.151 10.104 11.333 10.104 18.545 0 4.834-1.504 8.796-4.515 11.887-2.932 3.09-6.54 4.636-10.819 4.636-3.962 0-7.37-1.308-10.222-3.924-2.774-2.615-4.16-5.705-4.16-9.271 0-2.377.95-5.073 2.851-8.084 1.744-2.774 2.615-4.952 2.615-6.537 0-2.22-1.069-4.042-3.209-5.469-2.853-1.823-6.775-2.734-11.767-2.734-7.687 0-14.701 2.418-21.041 7.252s-11.728 13.155-16.166 24.963c-4.359 11.728-6.54 24.923-6.54 39.584 0 2.14.12 5.35.358 9.628 6.181-8.241 12.679-14.146 19.494-17.712 6.816-3.567 14.305-5.348 22.467-5.348 13.155 0 24.171 4.477 33.047 13.432 8.955 8.875 13.434 20.129 13.434 33.76 0 16.404-5.271 30.035-15.811 40.892-10.54 10.857-23.577 16.285-39.11 16.285-10.936 0-20.683-2.893-29.242-8.678-8.558-5.864-15.573-14.977-21.04-27.34-5.39-12.362-8.083-26.785-8.083-43.269 0-17.83 3.328-33.998 9.985-48.5 6.657-14.502 15.137-25.36 25.44-32.572 10.38-7.291 21.197-10.936 32.45-10.936zm-7.607 78.574c-9.906 0-18.03 3.607-24.37 10.819-6.26 7.132-9.39 17.196-9.39 30.193 0 14.106 3.05 24.883 9.153 32.332 6.102 7.45 13.87 11.174 23.3 11.174 9.351 0 16.878-3.525 22.584-10.578 5.706-7.053 8.559-17.594 8.559-31.621 0-14.899-2.653-25.675-7.963-32.332-5.23-6.657-12.522-9.987-21.873-9.987zm108.518 106.66c4.572 17.071 53.289 38.924 52.312 70.014-.57 18.189-13.462 28.364-27.074 28.045v-.002c-9.314-.217-18.975-5.349-25.237-16.195-15.048 26.061-53.445 19.308-52.45-11.848 1.036-32.442 47.451-51.364 52.449-70.013z"}}]})(props);
};
