// THIS FILE IS AUTO GENERATED
var GenIcon = require('../lib').GenIcon
module.exports.BiRupee = function BiRupee (props) {
  return GenIcon({"tag":"svg","attr":{"viewBox":"0 0 24 24"},"child":[{"tag":"path","attr":{"d":"M17,6V4H9.5H7H6v2h1h2.5c1.302,0,2.401,0.838,2.815,2H6v2h6.315c-0.414,1.162-1.514,2-2.815,2H6v2.414L11.586,20h2.828 l-6-6H9.5c2.414,0,4.435-1.721,4.898-4H17V8h-2.602c-0.151-0.744-0.481-1.416-0.924-2H17z"}}]})(props);
};
