// THIS FILE IS AUTO GENERATED
import { GenIcon } from '../lib';
export function GiSinusoidalBeam (props) {
  return GenIcon({"tag":"svg","attr":{"viewBox":"0 0 512 512"},"child":[{"tag":"path","attr":{"d":"M18.7 18.7v31.474l7.804 9.853c-3.24 5.71-5.774 11.464-7.512 17.12-.104.34-.192.684-.29 1.025v28.437c1.392 3.52 3.446 6.762 6.275 9.585 9.892 9.873 24.882 10.316 39.107 5.93 2.954-.91 5.934-2.048 8.922-3.377l38.894 49.115c-1.928 4.116-3.545 8.218-4.81 12.266-5.35 17.106-4.78 34.656 6.61 46.07 11.392 11.41 28.924 11.99 45.96 6.67.977-.306 1.96-.637 2.942-.98l65.402 82.587c-.53 1.544-1.036 3.084-1.49 4.61-6.15 20.656-5.49 41.09 7.65 54.255 11.795 11.818 29.506 13.57 47.713 9.166l87.736 110.79h23.84l-2.316-2.923c3.324-9.926 8.54-20.652 15.472-31.45l31.365 34.374H493.3v-75.472l-31.062-27.096c5.482-8.462 10.068-16.987 13.664-25.396l17.397 13.36v-23.56l-10.98-8.435c5.676-22.206 3.702-43.492-10.226-57.39-15.163-15.132-39.034-16.085-63.23-8.472-2.783.875-5.59 1.88-8.41 2.984l-89.493-68.736c.83-2.283 1.583-4.55 2.24-6.8 5.66-19.4 4.954-38.725-7.53-51.235-12.484-12.51-31.86-13.23-50.99-7.25-6.114 1.91-12.345 4.524-18.582 7.786l-59.504-45.703c2.706-4.89 4.92-9.77 6.59-14.557 5.17-14.83 5.566-30.396-4.614-40.597-10.178-10.202-25.744-9.823-40.57-4.664-7.716 2.685-15.67 6.774-23.54 12.092L55.274 18.7H18.7zm137.84 48.556c4.273-.033 7.21 1.077 8.802 2.672 2.83 2.835 4.132 9.955.195 21.244-1.012 2.903-2.35 5.977-3.99 9.152l-31.27-24.017c4.855-2.87 9.55-5.088 13.866-6.59 4.938-1.72 9.072-2.436 12.396-2.46zM39.296 76.182l21.615 27.297c-.788.282-1.57.55-2.336.786-10.46 3.225-17.255 1.84-20.398-1.297-3.144-3.138-4.525-9.9-1.32-20.333.636-2.07 1.456-4.233 2.44-6.455zm78.72 14.273l33.383 25.64c-.527.686-1.086 1.37-1.636 2.053l-31.746-27.693zm-17.11 33.426l22.44 24.595c-.47.658-.94 1.317-1.396 1.978L100.91 123.88zm65.34 3.622l52.868 40.605c-2.168 1.557-4.323 3.197-6.463 4.903l-48.773-42.547c.803-.984 1.602-1.97 2.368-2.96zm-29.936 35.18l38.627 42.334c-2.238 1.42-4.468 2.74-6.67 3.923l-34.048-42.995c.667-1.085 1.368-2.174 2.09-3.263zm140.77 2.164c6.828.095 12.03 2.068 15.36 5.406 5.685 5.696 7.436 16.95 2.824 32.777L252.78 170.4c2.548-1.07 5.05-2.006 7.474-2.763 5.763-1.8 10.93-2.685 15.44-2.785.47-.01.93-.013 1.386-.006zm-42.338 15.26l53.252 40.904c-1.968 3.888-4.236 7.85-6.748 11.836l-54.215-47.293c2.567-1.938 5.142-3.765 7.712-5.446zm-109.488 4.62l24.86 31.395c-11.155 2.685-19.058 1.01-23.184-3.122l-.002-.002c-4.6-4.61-6.178-13.935-2.002-27.29.1-.322.22-.653.328-.98zm62.484 34.32l56.766 62.212c-2.957 4.59-5.663 9.19-8.06 13.775l-56.39-71.21c2.57-1.47 5.134-3.067 7.684-4.778zm115.342 13.55l78.408 60.222c-6.084 3.394-12.166 7.278-18.177 11.616l-67.794-59.14c2.768-4.223 5.302-8.465 7.564-12.698zm134.16 62.273c9.404.114 16.794 2.82 21.653 7.667 6.444 6.43 9.11 17.333 6.96 31.518l-47.537-36.512c6.178-1.672 11.877-2.538 17.016-2.662.645-.015 1.28-.018 1.908-.01zm-179.63.753l50.32 55.15c-6.605 4.173-13.157 7.547-19.403 10.03l-39.18-49.477c2.283-5.103 5.053-10.373 8.262-15.703zm140.245 9.768l62.665 48.13c-3.06 7.96-7.285 16.32-12.563 24.755l-70.105-61.156c6.714-4.59 13.44-8.52 20.003-11.73zM242.43 332.694l26.26 33.162c-9.63.86-16.912-1.318-21.3-5.714-5.222-5.232-7.314-14.614-4.96-27.447zm78.302 32.112l72.788 79.77c-6.504 9.502-11.876 19.133-15.967 28.646l-76.987-97.216c6.712-3.015 13.476-6.772 20.166-11.2z"}}]})(props);
};
