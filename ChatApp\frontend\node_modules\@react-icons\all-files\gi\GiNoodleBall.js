// THIS FILE IS AUTO GENERATED
var GenIcon = require('../lib').GenIcon
module.exports.GiNoodleBall = function GiNoodleBall (props) {
  return GenIcon({"tag":"svg","attr":{"viewBox":"0 0 512 512"},"child":[{"tag":"path","attr":{"d":"M304.04 95.943c-79.228 0-143.253 64.03-143.253 143.252 0 9.654.96 19.078 2.772 28.192-22.445 23.575-46.822 37.388-73.132 42.35-1.933-10.607-2.27-19.24-1.414-25.98 1.144-8.995 4.098-14.5 8.43-18.54 8.072-7.523 23.912-10.467 45.583-8.592-.615-5.728-.94-11.54-.94-17.43 0-.47.014-.938.018-1.408-3.816-.298-7.567-.49-11.216-.52-18.457-.16-34.558 3.44-46.187 14.28-7.753 7.226-12.66 17.52-14.228 29.853-1.132 8.898-.682 18.93 1.322 30.373-16.354.544-33.408-2.04-51.16-7.54v19.412c18.878 5.15 37.466 7.192 55.474 5.986 8.494 26.69-.19 41.56-14.157 51.196-11.892 8.205-28.824 10.96-41.316 8.95v19.1c17.094 2.303 36.343-1.914 51.93-12.667 18.842-13 30.736-37.682 22.26-69.046 26.722-5.317 51.87-18.246 74.444-39.228 2.447 6.778 5.384 13.32 8.78 19.576-30.85 25.655-43.58 56.202-43.204 85.517-9.9 3.555-20.404 6.9-31.147 10.747-29.692 10.635-61.255 26.32-83.063 62.43v27.207h7.082C46.43 447.77 76.77 433.274 110 421.374c8.885-3.183 17.95-6.072 26.947-9.208.882 4.257 2.017 8.46 3.42 12.58 4.645 13.653 11.786 26.503 20.722 38.1-22.457 6.016-45.297 14.744-64.725 30.568h35.256c13.68-6.47 28.634-10.904 43.734-14.576 5.403 5.27 11.19 10.146 17.268 14.576h37.518c-11.303-4.82-22.265-11.566-32.198-19.693 8.438-1.83 16.776-3.636 24.73-5.638 7.32-1.842 14.407-3.86 21.05-6.37 5.908 5.28 12.54 9.822 19.646 13.546 36.47 19.108 86.82 17.54 120.886-15.737 2.85.773 5.727 1.45 8.633 2.027 12.053 2.387 24.517 3.173 37.235 2.586 4.218 9.506 7.59 19.355 10.018 29.28h19.175c-2.296-10.575-5.534-21.05-9.658-31.212 15.25-2.28 30.752-6.333 46.254-11.81V430.48c-18.596 7.04-36.965 11.834-54.527 13.926-4.735-8.945-10.17-17.518-16.248-25.55 7.778-6.334 15.64-11.77 23.362-15.774 17.26-8.95 32.38-11.436 47.412-3.11v-20.126c-18.558-6.217-38.35-2.512-56.014 6.646-9.272 4.808-18.247 10.998-26.863 18.018-14.04-14.935-30.767-27.17-49.465-34.955 30.785-14.052 55.687-38.678 70.1-69.258 20.06 10.944 41.627 11.273 62.242 4.268v-19.943c-19.617 8.81-37.257 9.607-55.383-1.78 4.386-13.758 6.76-28.42 6.76-43.65 0-79.227-64.024-143.252-143.25-143.252zm191.874 225.63c-18.32 16.822-37.05 23.787-54.63 23.413-4.47-.095-8.896-.67-13.25-1.713-4.47 5.317-9.273 10.345-14.38 15.047 8.737 3.314 17.885 5.153 27.235 5.352 18.42.39 37.312-5.617 55.024-18.11v-23.988zm-307.805 1.86c10.72 14.74 24.183 27.35 39.65 37.08-12.352 24.623-13.65 48.263-6.67 68.03 2.277 6.45 5.386 12.45 9.174 17.96-3.806 1.23-7.86 2.375-12.15 3.454-11.104 2.795-23.568 5.155-36.573 7.99-10.448-11.807-18.713-25.218-23.477-39.22-1.487-4.37-2.662-8.788-3.502-13.24 3.416-1.437 6.795-2.965 10.124-4.62 15.186-7.55 29.37-18.018 39.957-33.887-4.973-3.876-9.714-8.037-14.2-12.457-8.587 13.99-20.055 22.64-34.075 29.61-1.027.51-2.07 1.01-3.123 1.5 1.272-21 11.417-42.236 34.867-62.2zm56.1 45.985c18.2 8.358 38.46 13.023 59.83 13.023 9.266 0 18.322-.885 27.095-2.56 25.992 3.503 49.128 17.207 67.89 37.016-7.132 6.784-13.942 13.978-20.42 21.24-18.798-7.092-35.432-19.51-49.26-38.974-6.888 1.085-13.918 1.74-21.064 1.926 15.024 24.198 34.243 40.98 55.94 51.115-27.487 21.187-64.714 20.888-92.175 6.5-3.558-1.864-6.92-3.964-10.074-6.262 3.602-2.48 6.925-5.302 9.903-8.563 9.614-10.527 14.227-25.235 12.77-43.898-6.5-.78-12.88-1.945-19.12-3.472 2.726 17.596-.733 27.412-7.45 34.765-2.627 2.88-6.04 5.44-10.106 7.756-3.976-5.084-7.128-10.68-9.256-16.708-5.19-14.705-4.758-32.27 5.5-52.904zm166.876 61.89c3.382 4.568 6.534 9.348 9.463 14.28-7.44-.07-14.675-.7-21.657-1.936 3.98-4.235 8.048-8.383 12.193-12.343z"}}]})(props);
};
