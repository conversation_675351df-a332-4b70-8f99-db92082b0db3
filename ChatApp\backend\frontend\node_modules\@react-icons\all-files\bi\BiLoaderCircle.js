// THIS FILE IS AUTO GENERATED
var GenIcon = require('../lib').GenIcon
module.exports.BiLoaderCircle = function BiLoaderCircle (props) {
  return GenIcon({"tag":"svg","attr":{"viewBox":"0 0 24 24"},"child":[{"tag":"circle","attr":{"cx":"12","cy":"20","r":"2"}},{"tag":"circle","attr":{"cx":"12","cy":"4","r":"2"}},{"tag":"circle","attr":{"cx":"6.343","cy":"17.657","r":"2"}},{"tag":"circle","attr":{"cx":"17.657","cy":"6.343","r":"2"}},{"tag":"circle","attr":{"cx":"4","cy":"12","r":"2.001"}},{"tag":"circle","attr":{"cx":"20","cy":"12","r":"2"}},{"tag":"circle","attr":{"cx":"6.343","cy":"6.344","r":"2"}},{"tag":"circle","attr":{"cx":"17.657","cy":"17.658","r":"2"}}]})(props);
};
