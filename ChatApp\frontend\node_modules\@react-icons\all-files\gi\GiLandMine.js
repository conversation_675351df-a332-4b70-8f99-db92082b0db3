// THIS FILE IS AUTO GENERATED
var GenIcon = require('../lib').GenIcon
module.exports.GiLandMine = function GiLandMine (props) {
  return GenIcon({"tag":"svg","attr":{"viewBox":"0 0 512 512"},"child":[{"tag":"path","attr":{"d":"M223.688 25.72l21.218 166.78h30.75l21.22-166.78h-73.188zm19.468 184.093v34.25h34.25v-34.25h-34.25zm17.156 79.968c-1.442 0-2.884.04-4.312.064-1.226.02-2.44.057-3.656.094l-1.063.03c-1.3.045-2.587.093-3.874.157-1.203.06-2.403.11-3.594.188-.217.012-.438.017-.656.03-39.48 2.43-74.968 12.954-101.22 25.938-15 7.42-26.97 15.677-34.78 23.25-7.808 7.576-10.78 14.353-10.78 17.314 0 2.96 2.228 7.307 9.686 12.594 7.46 5.286 19.353 10.675 34.407 15.187 30.106 9.025 72.746 14.844 119.843 14.844 47.096 0 89.767-5.82 119.875-14.845 15.053-4.512 26.947-9.9 34.406-15.188 7.46-5.286 9.687-9.633 9.687-12.593 0-2.96-2.972-9.738-10.78-17.313-7.808-7.573-19.78-15.83-34.78-23.25-26.25-12.983-61.764-23.507-101.25-25.936-.22-.015-.437-.017-.658-.03-1.19-.078-2.39-.13-3.593-.19-1.288-.063-2.576-.11-3.876-.155-.136-.006-.27-.028-.406-.033-.23-.007-.458.007-.688 0-1.195-.035-2.388-.074-3.594-.093-1.438-.024-2.89-.063-4.344-.063zm0 18.69c26.39 0 50.255 4.41 66.688 11.03 8.216 3.31 14.52 7.202 18.313 10.813 3.79 3.61 4.906 6.395 4.906 8.78 0 2.386-1.116 5.172-4.908 8.782-1.58 1.506-3.62 3.053-6.03 4.594-4.953-15.806-38.422-28.033-79-28.033-40.555 0-73.986 12.21-78.97 28-2.396-1.533-4.426-3.064-6-4.562-3.79-3.61-4.906-6.396-4.906-8.78 0-2.387 1.115-5.172 4.906-8.783 3.792-3.61 10.097-7.502 18.313-10.812 16.433-6.62 40.297-11.03 66.688-11.03zM77.75 358.25c-22.095 13.71-34.938 30.062-34.938 47.625 0 48.377 97.367 87.594 217.47 87.594 120.1 0 217.468-39.218 217.468-87.595 0-17.54-12.807-33.864-34.844-47.563-.61 10.722-7.856 19.54-17.5 26.375-10.08 7.144-23.57 12.956-39.875 17.844-32.61 9.777-76.607 15.626-125.217 15.626s-92.607-5.85-125.22-15.625c-16.305-4.887-29.764-10.7-39.843-17.842-9.662-6.848-16.915-15.69-17.5-26.438z"}}]})(props);
};
