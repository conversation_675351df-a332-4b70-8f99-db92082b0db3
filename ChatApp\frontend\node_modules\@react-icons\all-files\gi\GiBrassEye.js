// THIS FILE IS AUTO GENERATED
var GenIcon = require('../lib').GenIcon
module.exports.GiBrassEye = function GiBrassEye (props) {
  return GenIcon({"tag":"svg","attr":{"viewBox":"0 0 512 512"},"child":[{"tag":"path","attr":{"d":"M255.295 19.137C174.005 18.97 94.94 61.107 51.33 136.643c-64.91 112.426-26.51 255.934 85.918 320.843 112.427 64.91 255.91 26.41 320.818-86.015 64.91-112.426 26.474-255.873-85.953-320.783-36.89-21.298-77.12-31.47-116.818-31.55zm72.264 104.44c23.888.1 47.577 6.047 69.118 18.476 72.557 41.867 93.585 141.627 46.838 222.55C396.77 445.52 299.768 477.276 227.21 435.41c-72.556-41.867-93.54-141.7-46.794-222.62 32.87-56.9 90.563-89.453 147.143-89.214zm69.854 42.398c13.708 22.326 19.042 51.598 15.473 82.795-6.7-12.15-16.443-22.473-28.955-29.676-40.07-23.07-93.725-5.624-119.54 38.965-25.818 44.586-14.2 99.74 25.872 122.807 10.52 6.057 21.984 9.31 33.634 10.014-36.447 22.57-77.037 27.46-108.996 9.016-1.976-1.14-3.884-2.363-5.738-3.646 8.023 8.542 17.338 16.016 27.9 22.11 64.273 37.087 149.69 9.063 191.098-62.618 39.038-67.578 24.853-149.527-30.748-189.767zm-53.11 62.04c10.274.123 20.466 2.733 29.776 8.092 31.778 18.295 40.878 61.486 20.404 96.846-20.473 35.36-62.59 49.197-94.37 30.902-27.558-15.865-38.003-50.53-26.94-82.52 4.262 16.973 19.722 29.677 37.957 29.677 21.485 0 39.085-17.632 39.085-39.11 0-19.34-14.273-35.523-32.803-38.552 8.006-3.33 16.43-5.157 24.838-5.327.685-.013 1.37-.016 2.055-.007z"}}]})(props);
};
