// THIS FILE IS AUTO GENERATED
var GenIcon = require('../lib').GenIcon
module.exports.GiMazeSaw = function GiMazeSaw (props) {
  return GenIcon({"tag":"svg","attr":{"viewBox":"0 0 512 512"},"child":[{"tag":"path","attr":{"d":"M196.256 21.322l-28.205 85.29c-22.57 13.108-41.614 30.854-56.2 51.63l-66.834-23.65 40.375 80.273c-6.504 24.305-7.92 50.25-3.372 76.29L18.166 321.63l85.09 28.14c13.108 22.692 30.9 41.838 51.748 56.492l-23.57 66.61 79.908-40.194c24.45 6.6 50.57 8.05 76.783 3.455l30.348 63.588 28.02-84.732c22.83-13.163 42.075-31.066 56.777-52.05l66.443 23.513-40.135-79.802c6.58-24.48 7.998-50.63 3.356-76.87l63.628-30.364-84.92-28.084c-13.162-22.71-31.02-41.86-51.937-56.494l23.59-66.664-80.166 40.322c-12.48-3.326-25.39-5.316-38.523-5.844-12.48-.5-25.16.323-37.86 2.56l-30.49-63.888zm61.78 79.842c1.972.004 3.938.044 5.898.12 17.16.675 33.88 4.133 49.593 10.034l-31.627 35.71 33.116 26.36-14.9 18.65c5.292 3.287 10.21 7.18 14.64 11.616l26.547-33.228-32.352-25.752 22.454-25.356c30.827 16.123 56.32 42.248 71.307 75.405l-47.745 2.898 4.777 42.056-23.547 2.633c1.45 6.208 2.123 12.43 2.104 18.567l42.124-4.71-4.668-41.085 33.918-2.06c.656 2.102 1.27 4.227 1.846 6.373 8.98 33.516 6.615 67.36-4.74 97.474l-35.925-31.81-26.36 33.112-18.475-14.76c-3.293 5.29-7.194 10.2-11.633 14.625l33.072 26.42 25.75-32.352 25.55 22.625c-16.174 30.833-42.366 56.303-75.59 71.247l-2.904-47.856-42.057 4.776-2.618-23.406c-6.213 1.435-12.437 2.09-18.574 2.055l4.7 42.035 41.087-4.666 2.062 33.982c-2.01.624-4.037 1.224-6.09 1.774-33.593 9-67.515 6.603-97.683-4.82l31.734-35.836-33.115-26.36 14.78-18.504c-5.28-3.307-10.177-7.224-14.59-11.677l-26.475 33.142 32.35 25.754-22.53 25.44c-30.75-16.183-56.15-42.34-71.06-75.5l47.576-2.888-4.778-42.057 23.6-2.638c-1.426-6.215-2.073-12.44-2.03-18.58l-42.253 4.726 4.668 41.088-33.7 2.048c-.623-2.008-1.22-4.03-1.77-6.078-8.983-33.523-6.614-67.374 4.748-97.494l35.625 31.548 26.36-33.113 18.683 14.927c3.304-5.283 7.218-10.184 11.668-14.602l-33.314-26.61-25.752 32.35-25.248-22.36c16.13-30.74 42.22-56.153 75.312-71.11l2.88 47.453 42.058-4.775 2.654 23.738c6.21-1.44 12.434-2.122 18.57-2.095l-4.732-42.324-41.088 4.667-2.04-33.62c2.098-.654 4.214-1.28 6.358-1.855 13.972-3.744 28.002-5.516 41.795-5.488zm7.796 94.92v27.672c16.05 3.987 28.07 18.578 28.07 35.803 0 17.224-12.02 31.815-28.07 35.8v29.863c2.81-.372 5.63-.91 8.443-1.664 34.82-9.332 55.364-44.915 46.034-79.736-7.052-26.314-29.1-44.465-54.478-47.74zm-18.687.31c-2.19.353-4.38.81-6.57 1.397-34.82 9.33-55.362 44.914-46.032 79.733 6.877 25.665 28.02 43.555 52.602 47.463v-29.908c-15.518-4.354-27.012-18.676-27.012-35.52 0-16.843 11.494-31.165 27.012-35.52v-27.645zm9.873 44.97c-10.16 0-18.196 8.035-18.196 18.195 0 10.158 8.036 18.194 18.196 18.194 10.16 0 18.195-8.036 18.195-18.195 0-10.16-8.036-18.197-18.195-18.197z"}}]})(props);
};
