import { formatDistance } from "./ja/_lib/formatDistance.js";
import { formatLong } from "./ja/_lib/formatLong.js";
import { formatRelative } from "./ja/_lib/formatRelative.js";
import { localize } from "./ja/_lib/localize.js";
import { match } from "./ja/_lib/match.js";

/**
 * @category Locales
 * @summary Japanese locale.
 * @language Japanese
 * @iso-639-2 jpn
 * <AUTHOR> [@DeMuu](https://github.com/DeMuu)
 * <AUTHOR> [@ykzts](https://github.com/ykzts)
 * <AUTHOR> [@mesqueeb](https://github.com/mesqueeb)
 * <AUTHOR> [@skyuplam](https://github.com/skyuplam)
 * <AUTHOR> [@so99ynoodles](https://github.com/so99ynoodles)
 */
export const ja = {
  code: "ja",
  formatDistance: formatDistance,
  formatLong: formatLong,
  formatRelative: formatRelative,
  localize: localize,
  match: match,
  options: {
    weekStartsOn: 0 /* Sunday */,
    firstWeekContainsDate: 1,
  },
};

// Fallback for modularized imports:
export default ja;
