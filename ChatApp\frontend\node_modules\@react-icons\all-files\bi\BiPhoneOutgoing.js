// THIS FILE IS AUTO GENERATED
var GenIcon = require('../lib').GenIcon
module.exports.BiPhoneOutgoing = function BiPhoneOutgoing (props) {
  return GenIcon({"tag":"svg","attr":{"viewBox":"0 0 24 24"},"child":[{"tag":"path","attr":{"d":"M16.712,13.288c-0.391-0.391-1.023-0.391-1.414,0l-1.594,1.594c-0.739-0.22-2.118-0.72-2.992-1.594 s-1.374-2.253-1.594-2.992l1.594-1.594c0.391-0.391,0.391-1.023,0-1.414l-4-4c-0.391-0.391-1.023-0.391-1.414,0L2.586,6 C2.206,6.38,1.992,6.902,2,7.435c0.023,1.424,0.4,6.37,4.298,10.268S15.142,21.977,16.566,22c0.005,0,0.023,0,0.028,0 c0.528,0,1.027-0.208,1.405-0.586l2.712-2.712c0.391-0.391,0.391-1.023,0-1.414L16.712,13.288z M16.585,20 c-1.248-0.021-5.518-0.356-8.873-3.712C4.346,12.922,4.02,8.637,4,7.414l2.005-2.005l2.586,2.586L7.298,9.288 C7.059,9.526,6.957,9.87,7.026,10.2c0.024,0.115,0.611,2.842,2.271,4.502s4.387,2.247,4.502,2.271 c0.331,0.071,0.674-0.032,0.912-0.271l1.293-1.293l2.586,2.586L16.585,20z"}},{"tag":"path","attr":{"d":"M16.795 5.791L12.298 10.288 13.712 11.702 18.209 7.205 21.005 10 21.005 2.995 14 2.995z"}}]})(props);
};
