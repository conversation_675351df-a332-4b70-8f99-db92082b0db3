// THIS FILE IS AUTO GENERATED
var GenIcon = require('../lib').GenIcon
module.exports.BiMessageEdit = function BiMessageEdit (props) {
  return GenIcon({"tag":"svg","attr":{"viewBox":"0 0 24 24"},"child":[{"tag":"path","attr":{"d":"M20,2H4C2.897,2,2,2.897,2,4v12c0,1.103,0.897,2,2,2h3v3.767L13.277,18H20c1.103,0,2-0.897,2-2V4C22,2.897,21.103,2,20,2z M20,16h-7.277L9,18.233V16H4V4h16V16z"}},{"tag":"path","attr":{"d":"M13.803 9.189L12.404 7.791 8.535 11.655 8.535 13.054 9.934 13.054z"}},{"tag":"path","attr":{"transform":"rotate(45.001 14.295 7.298)","d":"M13.307 6.544H15.285V8.052H13.307z"}}]})(props);
};
