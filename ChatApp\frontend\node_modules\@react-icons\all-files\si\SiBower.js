// THIS FILE IS AUTO GENERATED
var GenIcon = require('../lib').GenIcon
module.exports.SiBower = function SiBower (props) {
  return GenIcon({"tag":"svg","attr":{"role":"img","viewBox":"0 0 24 24"},"child":[{"tag":"title","attr":{},"child":[]},{"tag":"path","attr":{"d":"M23.54157 11.3053c-1.2341-1.18676-7.40717-1.92715-9.35444-2.14222.0943-.22248.1748-.45344.24155-.69076.26593-.11654.55198-.2246.84863-.31466.03603.107.2066.5149.303.70878 3.9168.10806 4.1181-2.91032 4.27702-3.73775.15574-.80837.14832-1.59025 1.4917-3.0184-2.0013-.58375-4.87983.90372-5.84393 3.11798-.36234-.1356-.72574-.23626-1.0849-.2977C14.16277 3.8923 12.82257 1 9.30624 1c-2.27782 0-4.5747.9408-6.30162 2.58083-.9302.88358-1.6612 1.93244-2.17293 3.11903C.2797 7.98073 0 9.39086 0 10.8921c0 5.22735 3.56824 9.80736 5.58437 9.80736.8804 0 1.63792-.65898 1.8159-1.25015.1494.40578.606 1.66547.75645 1.98542.2225.47464 1.2491.88465 1.6983.392.5774.32102 1.63792.51384 2.21532-.3422 1.11243.2352 2.09666-.42802 2.11785-1.2205.5456-.0286.81365-.79564.695-1.40588-.08794-.4492-1.02662-2.06276-1.3932-2.61897.72468.5901 2.56177.75645 2.78425 0 1.16858.91748 2.99084.4365 3.13492-.31042 1.42073.3687 3.0491-.4418 2.78213-1.42285 2.28206-.15785 1.99026-2.58515 1.35027-3.2006zM17.5991 7.2815c-.6007-.23626-1.363-.38565-1.89696-.38565-.7575 0-1.21943.42908-1.93244.42908-.14938 0-.50642.00106-.79353-.1017.1886.1981.42273.30512.87723.30512.27122 0 .80942-.1388 1.24486-.2691.00635.09217.0159.18222.0286.27334-.81578.19493-1.6718.71406-1.91973.8486-.5509-1.2173-.07734-2.36787.36022-2.89865 1.9621.00423 3.54757 1.35186 4.03174 1.79895zm.84915-.09112l-.29983-.28075c-.3083-.28923-.6293-.54986-.95986-.78188.4916-.9747 1.10925-2.03945 1.889-2.69842-.85815.34644-1.70678 1.38046-2.2079 2.48547-.25532-.1621-.5149-.30724-.77658-.43332.69924-1.49276 2.32338-2.73868 4.11386-2.83615-1.1993 1.08806-.74903 3.25782-1.75975 4.544l.00105.00105zM15.9845 8.20693c-.13244-.2871-.2654-.76228-.2495-1.04197.22247-.0053.6505.0784.7183.0943-.0265.13136-.04026.41953-.04026.4566.04238-.0731.15997-.32418.20765-.42377.428.08157.99058.21824 1.32007.37186-.38776.2511-1.04515.5239-1.95628.54297zm-7.07214-1.9516c-.48483-.1739-.48483-.61095 0-.78484.48484-.1739 1.09417.04464 1.09417.3924 0 .34777-.60933.5663-1.09417.39242zm1.61647.1622c0-.96003-1.04643-1.5633-1.87905-1.0833-.83262.48002-.83262 1.68657 0 2.16658.83262.48003 1.87905-.12325 1.87905-1.08328zm2.7832-1.5749c-1.56482 1.586-.94716 3.5926-.37718 4.49844-.81048 1.3487-2.4039 2.2704-4.25476 2.68995 2.07758 0 3.3002-.53502 4.01108-1.0584.45345-.33478.69924-.66427.8253-.84755 3.0809.19917 7.95862 1.19188 8.43432 1.5129.1907.12925.38776.41424.41742.68758-2.3149-.3242-6.4881-.66534-7.58038-.72255.77552.1102 6.433 1.1813 7.41405 1.43238-.29878.4863-.97895.8306-2.00344.59118.5541.75433-.52125 1.6591-2.01932 1.16116.3295.74056-1.0033 1.40695-2.51938.63567.01907.74055-1.88052.82636-2.63062.0074.01484.09748.10383.28394.14197.36658-.24155 2.1634-2.014 3.50573-3.82885 3.50573-4.44335 0-8.3146-3.60955-8.3146-8.411 0-5.07582 3.75154-8.86443 8.27858-8.86443 2.59354 0 3.7653 2.04157 4.0058 2.81497z"}}]})(props);
};
