// THIS FILE IS AUTO GENERATED
import { GenIcon } from '../lib';
export function SiCoronaengine (props) {
  return GenIcon({"tag":"svg","attr":{"role":"img","viewBox":"0 0 24 24"},"child":[{"tag":"title","attr":{},"child":[]},{"tag":"path","attr":{"d":"M11.986,0C11.986,0,11.986,0,11.986,0C5.358,0.008-0.008,5.387,0,12.015 C0.008,18.642,5.387,24.008,12.015,24C18.636,23.992,24,18.622,24,12C23.996,5.369,18.617-0.004,11.986,0z M12.007,22.455 c-5.774,0.006-10.461-4.67-10.467-10.445C1.535,6.236,6.211,1.55,11.985,1.544C17.763,1.541,22.449,6.223,22.452,12 C22.452,17.77,17.777,22.449,12.007,22.455z M13.689,7.5c1.008,1.059,2.329,1.903,3.472,2.133c0.362-0.118,0.688-0.326,0.948-0.605 c0.277-0.324,0.46-0.718,0.529-1.139c-0.012-0.038-0.027-0.076-0.041-0.116l0,0c-0.49-1.318-1.804-2.265-3.158-2.831 c-1.333-0.557-2.695-0.746-3.35-0.558C12.112,5.395,12.769,6.535,13.689,7.5L13.689,7.5L13.689,7.5z M16.798,12.226 c-0.697,1.284-1.093,2.799-0.96,3.957c0.225,0.307,0.523,0.553,0.869,0.713c0.394,0.163,0.826,0.215,1.247,0.151 c0.038-0.027,0.07-0.053,0.1-0.075c1.103-0.872,1.6-2.413,1.72-3.875c0.119-1.438-0.122-2.79-0.503-3.354 c-0.957,0.334-1.839,1.311-2.473,2.482L16.798,12.226z M8.227,8.993c1.32-0.631,2.532-1.625,3.105-2.639 c0-0.381-0.097-0.755-0.283-1.088c-0.223-0.363-0.541-0.658-0.92-0.853l-0.123,0.002C8.6,4.473,7.292,5.428,6.334,6.541l0,0 C5.392,7.633,4.791,8.87,4.767,9.55c0.969,0.288,2.258,0.017,3.459-0.559L8.227,8.993z M7.96,14.641 c-0.193-1.449-0.765-2.907-1.553-3.767c-0.363-0.117-0.749-0.14-1.123-0.067l0,0c-0.415,0.099-0.794,0.31-1.097,0.61l-0.036,0.118 c-0.38,1.355,0.126,2.893,0.887,4.146c0.749,1.233,1.741,2.185,2.382,2.418c0.577-0.832,0.718-2.139,0.54-3.459L7.96,14.641z M13.257,16.641c-1.439-0.264-3.004-0.172-4.065,0.311c-0.224,0.308-0.366,0.668-0.411,1.046c-0.034,0.426,0.05,0.852,0.243,1.233 c0.033,0.025,0.067,0.047,0.1,0.068c1.172,0.779,2.793,0.774,4.223,0.438c1.405-0.332,2.619-0.979,3.039-1.515 c-0.614-0.807-1.817-1.344-3.128-1.584L13.257,16.641z"}}]})(props);
};
