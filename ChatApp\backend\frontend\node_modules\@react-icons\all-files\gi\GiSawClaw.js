// THIS FILE IS AUTO GENERATED
var GenIcon = require('../lib').GenIcon
module.exports.GiSawClaw = function GiSawClaw (props) {
  return GenIcon({"tag":"svg","attr":{"viewBox":"0 0 512 512"},"child":[{"tag":"path","attr":{"d":"M25.313 16.78V235.5c13.37 27.573 58.125 15.47 57.187-5.094l-.72.344c-12.918-25.814-3.11-55.896 16.595-83.156 19.705-27.26 49.982-53.282 83.313-73.844 33.33-20.562 69.625-35.658 102.218-39.938 4.074-.534 8.108-.915 12.063-1.093 1.482-.068 2.942-.113 4.405-.126 10.244-.09 20 1.18 29 4.125 10.333 3.382 19.63 9.2 26.906 17.436 16.08 11.61 43.625-11.567 14.126-37.375H25.312zM301.75 51.313c-4.876-.02-10.032.326-15.406 1.032-17.115 2.247-36.34 8.196-55.78 16.75 102.868 97.69 149.218 205.882 159.78 326.062C357.356 289.77 301.584 165.66 185.28 93.594c-29.122 18.857-55.263 42.13-71.75 64.937-16.258 22.494-22.568 43.2-16.78 59.69l.03-.032c.294.666.578 1.31.907 1.968 22.068 6.418 43.226 14.086 63.438 22.875l-37.875 20.626 91.188 11.313-41.313 31.467 93.688 11.5-39.438 25.094 88.72 21.72-40.47 16.5 84.22 37.563-39.876 8.968 95.967 65.095c.09.2.19.395.282.594.03-.116.06-.23.092-.345l.125.094-.093-.22c40.1-144.19 17.276-273.748-70.5-421.47-.847-1.423-1.734-2.776-2.688-4.06-.172-.234-.355-.46-.53-.69-.16-.185-.31-.38-.47-.56-4.88-5.542-11.032-9.276-18.594-11.75-6.333-2.075-13.686-3.123-21.812-3.157z"}}]})(props);
};
