// THIS FILE IS AUTO GENERATED
var GenIcon = require('../lib').GenIcon
module.exports.GiFeline = function GiFeline (props) {
  return GenIcon({"tag":"svg","attr":{"viewBox":"0 0 512 512"},"child":[{"tag":"path","attr":{"d":"M110.056 64.815c-4.234.027-8.355.587-12.337 1.799C83.13 71.054 72.93 77.03 65.24 87.333c-7.691 10.303-13.122 25.717-15.516 49.713-.669 6.708 2.012 18.384 7.75 30.986 5.738 12.602 14.248 26.276 23.829 38.387l2.49 3.146-.678 3.955c-3.097 18.091-3.644 50.706-.252 68.778.836 4.454 5.766 14.497 13.611 26.296 7.846 11.8 18.412 25.829 30.032 40.944 23.239 30.23 50.678 64.773 69.81 96.547.024.038.347.525 2.139.886 1.791.362 4.605.28 7.127-.35 2.441-.609 4.524-1.766 5.338-2.458-9.946-42.286-16.14-84.185-37.51-125.14l-10.102-19.358 20.809 6.617c21.64 6.882 48.718 8.897 73.396 7.719 24.679-1.179 47.26-5.681 58.868-10.362l5.648-2.277 4.215 4.397c43.225 45.089 73.427 98.048 112.644 140.935.12.132-.139.16.91.088 1.05-.072 3-.714 4.706-1.914 1.705-1.2 3.106-2.882 3.722-4.234.617-1.353.659-2.086.268-3.149-20.396-55.487-30.565-109.894-61.84-162.258l-2.103-3.521 1.279-3.9c6.869-20.923 17.852-42.768 42.902-60.303l4.535-3.176 4.881 2.617c11.454 6.145 19.123 5.873 25.69 1.936 13.132-7.875 24.15-23.88 28.625-37.784l-14.768-8.31-.943-3.746c-4.025-15.96-8.255-25.48-14.625-32.719-6.37-7.24-15.576-12.896-30.977-19.543l-6.426-2.773 1.104-6.91c.89-5.57.052-11.481-1.184-17.768-36.228 29.915-52.892 64.4-86.533 100.086l-3.197 3.39-4.613-.652c-57.84-8.187-142.926-5.257-198.455 1.436-6.045.728-11.262-1.14-16.34-3.733-5.078-2.593-9.996-6.2-14.475-10.492-8.957-8.584-16.938-19.87-16.01-33.258.654-9.426 2.683-17.048 7.006-23.01 4.323-5.961 11.13-9.434 17.815-10.27 13.368-1.669 26.916 3.833 42.058 10.04 30.285 12.415 65.987 29.01 93.022 21.717 10.554-2.848 30.674-20.089 44.4-37.834 6.863-8.873 12.442-17.861 15.377-24.528 1.339-3.041 2.013-5.487 2.24-6.923-.507-.106-1.153-.163-2.355-.08-2.665.182-7.069 1.375-12.56 3.814-10.985 4.877-26.237 14.423-44.675 26.896-11.984 8.108-26.017 8.386-38.992 5.153-12.975-3.233-25.743-9.626-38.58-16.04-19.255-9.619-38.213-18.81-55.45-20.077a53.657 53.657 0 0 0-4.27-.147zm343.096 73.006l9.832 22.123-22.474-3.512 12.642-18.611zM75.28 308.858c-14.235 49.028-35.548 97.072-55.633 132.947-.014.026-.02.015-.03.028a.651.651 0 0 1 .122.207c.258.592 1.295 1.961 2.856 3.035 1.56 1.074 3.553 1.828 4.945 2.008 1.391.18 1.836-.11 1.84-.114 33.669-24.914 58.717-55.4 83.47-85.668l-.613-.794c-11.682-15.197-22.427-29.428-30.75-41.946-2.25-3.384-4.321-6.6-6.207-9.703zM19.615 441.833c-.108-.114-.099.11 0 0zm297.377-119.485c-7.129 2.366-15.533 4.408-24.863 6.022-12.418 40.091-33.327 77.867-52.203 112.605l-.141.26-.158.25c-.31.491-.252.186-.049.783s1.012 1.81 2.27 2.707c2.514 1.796 4.82 2.374 7.208.23l.047-.04.047-.041c32.285-28.17 59.254-61.458 85.242-102.867-5.621-6.799-11.396-13.455-17.4-19.909z"}}]})(props);
};
