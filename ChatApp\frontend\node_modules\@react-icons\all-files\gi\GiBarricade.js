// THIS FILE IS AUTO GENERATED
var GenIcon = require('../lib').GenIcon
module.exports.GiBarricade = function GiBarricade (props) {
  return GenIcon({"tag":"svg","attr":{"viewBox":"0 0 512 512"},"child":[{"tag":"path","attr":{"d":"M197.492 19.762l9.692 43.058h-64.647l54.955 11.694-55.892 74.422 77.238-43.059 7.553 58.887 20.033-69.545-16.217-32.266-.068-.133zm139.203 38.162c-26.949 0-48.677 21.869-48.677 49.178 0 27.308 21.728 49.18 48.677 49.18 26.95 0 48.68-21.872 48.68-49.18 0-27.31-21.73-49.178-48.68-49.178zm-69.033 1.435c-6.037-.12-11.96.993-17.568 3.082l8.107 16.131c6.257-2.083 12.465-1.907 17.947.479a67.525 67.525 0 0 1 9.77-15.381c-6.068-2.838-12.219-4.19-18.256-4.31zm-43.205 139.053c-8.19.162-16.587 2.51-22.791 8.658-8.732 8.652-12.31 20.356-12.17 31.985.141 11.629 3.637 23.506 9.012 34.87 1.852 3.919 3.99 7.743 6.248 11.507-17.595 2.88-32.761 3.337-48.861 1.576 2.212-3.788 4.138-7.686 5.48-11.764 3.23-9.81 3.336-20.817-1.604-30.805-7.19-14.538-23.07-23.922-40.39-25.718-17.32-1.797-37.05 5.536-48.096 25.32l-.43.771-.273.842c-4.044 12.485-2.03 24.454 4.38 32.848 6.209 8.126 15.297 12.995 25.026 16.498h17.576l2.659 5.318c.118.024.235.05.353.073l-.222.185 8.279 16.565c8.666-5.656 11.814-12.884 16.443-22.141h67.608l10.959-6.047 12.808 5.92a34.624 34.624 0 0 0 1.606-.62c12.233-5.157 23.172-11.977 31.172-21.083 7.999-9.106 12.85-20.95 11.74-33.938-1.486-17.368-16.443-29.298-32.121-35.818-8.266-2.786-16.934-5.078-24.391-5.002zm1.86 18.004c5.47.074 11.067 1.763 15.617 3.617 11.712 4.871 20.551 14.338 21.1 20.735.676 7.923-1.84 14.27-7.33 20.523-5.494 6.252-14.17 11.959-24.645 16.377a113.568 113.568 0 0 1-7.97 2.996c-3.161-4.668-5.994-9.536-8.31-14.434-4.56-9.645-7.187-19.336-7.285-27.392-.097-8.057 1.968-14.156 6.84-18.983 3.548-2.578 7.727-3.497 11.982-3.439zm-113.11 19.988c10.382.084 25.994 7.69 30.43 16.014 2.69 5.439 2.757 10.77.64 17.2-1.562 4.745-4.488 9.9-8.406 15.038-10.05-1.39-20.343-3.272-28.986-6.293-14.593-5.753-23.412-12.353-19.38-26.326 6.746-11.5 15.932-15.577 25.702-15.633zM375.924 249l28.756 57.514 13.937-27.875L403.801 249zm77.639 0l-48.596 97.191 33.451-11.15L481.438 249zm-132.35 3.99c-16.417 1.038-28.12 9.49-29.205 25.674-.071 15.51 8.313 29.383 16.414 40.613-5.552.816-11.352 1.107-17.104.95 26.16 11.914 51.667 25.203 77.373 38.056l1.307-.435c-6.007-5.247-18.502-16.5-30.662-29.641 11.35-5.98 20.494-14.8 24.414-27.338 2.963-9.477 1.57-18.947-2.596-26.238-9.02-13.823-24.812-21.435-39.941-21.64zm-.28 18.123c5.03-.2 10.634 1.597 14.41 3.463 8.69 4.386 13.611 12.962 11.227 20.922-2.51 8.026-9.61 14.362-19.5 18.652-6.389-8.15-11.73-16.343-14.59-23.273-1.885-4.57-2.645-8.489-2.498-11.256 1.47-6.114 5.923-8.308 10.952-8.508zm-96.574 38.002l-25.156 13.88-33.82 67.644L213.563 487h40.705l40.09-16.037-21.651-15.332-1.96-1.568-7.677-6.141 56.764-59.385 83.709 36.041 36.258 46.356L403.738 487H487V386.725l-26.676-40.012-93.015 31.004-67.21-33.606zM78.562 313l28.758 57.514 13.938-27.875L106.438 313zm77.64 0l-87 174h27.874l87-174zM24.294 323.922l-.147 20.387c15.414 6.845 33.319 5.708 50.362.837l-8.239-16.478c-14.695 4.325-31.397 2.725-41.976-4.746zm.705 42.64V487h24.076l48.18-96.361-10.596-21.192-39.924 7.985zm299.203 43.454l-34.217 35.796 62.55 44.293 58.888-26.234-19.225-24.58zm-168.883.748l-13.937 27.875L165.563 487h27.875zm-24.002 48L117.201 487h28.237z"}}]})(props);
};
