// THIS FILE IS AUTO GENERATED
var GenIcon = require('../lib').GenIcon
module.exports.BiHotel = function BiHotel (props) {
  return GenIcon({"tag":"svg","attr":{"viewBox":"0 0 24 24"},"child":[{"tag":"circle","attr":{"cx":"7.5","cy":"11.5","r":"2.5"}},{"tag":"path","attr":{"d":"M17.205,7H12c-0.553,0-1,0.447-1,1v7H4V6H2v14h2v-3h8h8v3h2v-4v-0.024V15v-3.205C22,9.151,19.849,7,17.205,7z M13,15V9 h4.205C18.746,9,20,10.254,20,11.795V15H13z"}}]})(props);
};
