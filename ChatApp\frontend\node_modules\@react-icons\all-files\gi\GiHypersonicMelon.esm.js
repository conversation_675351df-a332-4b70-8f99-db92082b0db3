// THIS FILE IS AUTO GENERATED
import { GenIcon } from '../lib';
export function GiHypersonicMelon (props) {
  return GenIcon({"tag":"svg","attr":{"viewBox":"0 0 512 512"},"child":[{"tag":"path","attr":{"d":"M244.393 21.71c70.753 227.078 24.39 255.256-145.288 70.667 156.26 211.317 138.91 236.826-81.835 168.78 214.48 98.327 221.15 141.145 7.117 174.622 153.197-4.492 206.012 2.427 252.03 13.958-11.106-18.2-24.713-62.328-31.007-84.205-26.17-90.946 26.588-148.03 123.346-121.476 25.575 7.02 64.28 25.216 84.375 39.295-8.897-41.512-14.905-88.61-17.196-260.473-32.59 227.458-93.39 223.505-191.54-1.17zM359.535 287.83c-19.69.214-38.302 7.122-52.213 21.033-31.798 31.8-27.006 88.142 10.698 125.845 14.87 14.87 32.64 24.598 50.658 29.004-1.573.255-3.155.485-4.752.675-25.398 3.016-49.814-2.738-70.028-15.207 21.832 34.076 61.802 54.41 104.664 49.318 59.76-7.098 102.735-60.99 95.635-120.752-4.098-34.5-23.785-63.52-51.304-80.36 8.688 13.488 14.65 29.253 16.67 46.25.777 6.546.952 13.02.572 19.374-5.05-15.512-14.085-30.57-26.967-43.45-21.21-21.21-48.315-32.004-73.633-31.73z"}}]})(props);
};
