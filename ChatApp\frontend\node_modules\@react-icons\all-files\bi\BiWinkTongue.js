// THIS FILE IS AUTO GENERATED
var GenIcon = require('../lib').GenIcon
module.exports.BiWinkTongue = function BiWinkTongue (props) {
  return GenIcon({"tag":"svg","attr":{"viewBox":"0 0 24 24"},"child":[{"tag":"path","attr":{"d":"M15.5,9c-2,0-2.5,2-2.5,2h5C18,11,17.499,9,15.5,9z"}},{"tag":"path","attr":{"d":"M12,2C6.486,2,2,6.486,2,12s4.486,10,10,10c5.514,0,10-4.486,10-10S17.514,2,12,2z M10,18v-3h4v3c0,1.103-0.897,2-2,2 C10.897,20,10,19.103,10,18z M15.856,19.005C15.941,18.682,16,18.349,16,18v-1.499C17.589,15.028,18,13,18,13H6 c0,0,0.412,2.028,2,3.501V18c0,0.349,0.059,0.682,0.144,1.005C5.676,17.641,4,15.013,4,12c0-4.411,3.589-8,8-8s8,3.589,8,8 C20,15.013,18.324,17.641,15.856,19.005z"}},{"tag":"circle","attr":{"cx":"8.5","cy":"9.5","r":"1.5"}}]})(props);
};
