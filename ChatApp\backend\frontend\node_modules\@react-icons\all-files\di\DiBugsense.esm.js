// THIS FILE IS AUTO GENERATED
import { GenIcon } from '../lib';
export function DiBugsense (props) {
  return GenIcon({"tag":"svg","attr":{"version":"1.1","viewBox":"0 0 32 32"},"child":[{"tag":"path","attr":{"d":"M11.737 19.977c-0.133 0-0.269-0.022-0.404-0.065-0.142 0.85-0.176 1.88 0.494 1.898 0.858 0.023 2.648 0.011 3.486 0.005 0.030-0.154 0.030-0.184 0.006-0.168-0.677-0.286-1.824-0.944-1.234-1.95-0.198 0.028-0.391 0.062-0.589 0.097-0.528 0.089-1.074 0.183-1.759 0.183zM21.118 21.646c-0.626-0.264-1.657-0.849-1.337-1.732-0.386 0.221-0.825 0.333-1.313 0.333-0.454 0-0.893-0.112-1.351-0.242-0.128 0.83-0.136 1.788 0.509 1.805 0.858 0.023 2.648 0.011 3.486 0.005 0.030-0.156 0.030-0.186 0.006-0.169zM9.595 18.469c-0.249-0.351-0.452-0.7-0.592-0.959-0.17 0.775-1.003 3.745-3.869 4.501l-0.12 0.27c0 0 3.667-0.333 5.813-2.587-0.403-0.228-0.815-0.637-1.231-1.226zM21.413 13.922c0 0-0.204-0.706-0.871-1.497-1.308 0.8-2.216 2.189-2.355 3.798-0.071-0.335-0.109-0.682-0.109-1.039 0-1.423 0.6-2.706 1.561-3.61-0.336-0.252-0.731-0.488-1.194-0.688-1.579 1.078-2.434 3.010-2.085 4.961-0.156-0.305-0.283-0.63-0.376-0.975-0.433-1.607-0.024-3.242 0.957-4.438-0.428-0.079-0.895-0.13-1.405-0.145-1.501 1.451-1.951 3.732-1.039 5.669-0.23-0.254-0.438-0.535-0.616-0.842-0.849-1.46-0.868-3.173-0.203-4.595-0.689 0.218-1.276 0.575-1.777 1.011-0.653 1.66-0.376 3.6 0.828 5.020-0.288-0.185-0.561-0.403-0.815-0.653-0.759-0.753-1.224-1.69-1.393-2.669-1.058 1.787-1.338 3.726-1.338 3.726s1.232 2.675 2.554 2.675 2.103-0.36 3.395-0.36c1.292 0 2.314 0.631 3.336 0.631s3.095-0.512 2.945-5.981zM22.754 14.233c0.275-0.256 0.663-0.657 0.938-1.139 0.539-0.947 0.453-2.012 0.453-2.012s-1.227 0.47-1.766 1.418c-0.346 0.608-0.558 1.168-0.587 1.556 0.149 0.052 0.249-0.121-0-0 0.031 1.508-0.106 2.746-0.411 3.703 0.016 0.003 0.033 0.006 0.048 0.008 0 0 0.991 0.391 2.073-0.030s2.643 1.834 2.885 2.375l0.601 0.061c0-0.001-0.353-4.886-4.233-5.939zM23.746 16.191c-0.257 0-0.465-0.209-0.465-0.466s0.209-0.466 0.465-0.466c0.256 0 0.465 0.209 0.465 0.466s-0.209 0.466-0.465 0.466z"}}]})(props);
};
