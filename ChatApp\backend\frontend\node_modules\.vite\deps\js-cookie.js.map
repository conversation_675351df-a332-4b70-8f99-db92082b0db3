{"version": 3, "sources": ["../../js-cookie/dist/js.cookie.mjs"], "sourcesContent": ["/*! js-cookie v3.0.5 | MIT */\n/* eslint-disable no-var */\nfunction assign (target) {\n  for (var i = 1; i < arguments.length; i++) {\n    var source = arguments[i];\n    for (var key in source) {\n      target[key] = source[key];\n    }\n  }\n  return target\n}\n/* eslint-enable no-var */\n\n/* eslint-disable no-var */\nvar defaultConverter = {\n  read: function (value) {\n    if (value[0] === '\"') {\n      value = value.slice(1, -1);\n    }\n    return value.replace(/(%[\\dA-F]{2})+/gi, decodeURIComponent)\n  },\n  write: function (value) {\n    return encodeURIComponent(value).replace(\n      /%(2[346BF]|3[AC-F]|40|5[BDE]|60|7[BCD])/g,\n      decodeURIComponent\n    )\n  }\n};\n/* eslint-enable no-var */\n\n/* eslint-disable no-var */\n\nfunction init (converter, defaultAttributes) {\n  function set (name, value, attributes) {\n    if (typeof document === 'undefined') {\n      return\n    }\n\n    attributes = assign({}, defaultAttributes, attributes);\n\n    if (typeof attributes.expires === 'number') {\n      attributes.expires = new Date(Date.now() + attributes.expires * 864e5);\n    }\n    if (attributes.expires) {\n      attributes.expires = attributes.expires.toUTCString();\n    }\n\n    name = encodeURIComponent(name)\n      .replace(/%(2[346B]|5E|60|7C)/g, decodeURIComponent)\n      .replace(/[()]/g, escape);\n\n    var stringifiedAttributes = '';\n    for (var attributeName in attributes) {\n      if (!attributes[attributeName]) {\n        continue\n      }\n\n      stringifiedAttributes += '; ' + attributeName;\n\n      if (attributes[attributeName] === true) {\n        continue\n      }\n\n      // Considers RFC 6265 section 5.2:\n      // ...\n      // 3.  If the remaining unparsed-attributes contains a %x3B (\";\")\n      //     character:\n      // Consume the characters of the unparsed-attributes up to,\n      // not including, the first %x3B (\";\") character.\n      // ...\n      stringifiedAttributes += '=' + attributes[attributeName].split(';')[0];\n    }\n\n    return (document.cookie =\n      name + '=' + converter.write(value, name) + stringifiedAttributes)\n  }\n\n  function get (name) {\n    if (typeof document === 'undefined' || (arguments.length && !name)) {\n      return\n    }\n\n    // To prevent the for loop in the first place assign an empty array\n    // in case there are no cookies at all.\n    var cookies = document.cookie ? document.cookie.split('; ') : [];\n    var jar = {};\n    for (var i = 0; i < cookies.length; i++) {\n      var parts = cookies[i].split('=');\n      var value = parts.slice(1).join('=');\n\n      try {\n        var found = decodeURIComponent(parts[0]);\n        jar[found] = converter.read(value, found);\n\n        if (name === found) {\n          break\n        }\n      } catch (e) {}\n    }\n\n    return name ? jar[name] : jar\n  }\n\n  return Object.create(\n    {\n      set,\n      get,\n      remove: function (name, attributes) {\n        set(\n          name,\n          '',\n          assign({}, attributes, {\n            expires: -1\n          })\n        );\n      },\n      withAttributes: function (attributes) {\n        return init(this.converter, assign({}, this.attributes, attributes))\n      },\n      withConverter: function (converter) {\n        return init(assign({}, this.converter, converter), this.attributes)\n      }\n    },\n    {\n      attributes: { value: Object.freeze(defaultAttributes) },\n      converter: { value: Object.freeze(converter) }\n    }\n  )\n}\n\nvar api = init(defaultConverter, { path: '/' });\n/* eslint-enable no-var */\n\nexport { api as default };\n"], "mappings": ";;;AAEA,SAAS,OAAQ,QAAQ;AACvB,WAAS,IAAI,GAAG,IAAI,UAAU,QAAQ,KAAK;AACzC,QAAI,SAAS,UAAU,CAAC;AACxB,aAAS,OAAO,QAAQ;AACtB,aAAO,GAAG,IAAI,OAAO,GAAG;AAAA,IAC1B;AAAA,EACF;AACA,SAAO;AACT;AAIA,IAAI,mBAAmB;AAAA,EACrB,MAAM,SAAU,OAAO;AACrB,QAAI,MAAM,CAAC,MAAM,KAAK;AACpB,cAAQ,MAAM,MAAM,GAAG,EAAE;AAAA,IAC3B;AACA,WAAO,MAAM,QAAQ,oBAAoB,kBAAkB;AAAA,EAC7D;AAAA,EACA,OAAO,SAAU,OAAO;AACtB,WAAO,mBAAmB,KAAK,EAAE;AAAA,MAC/B;AAAA,MACA;AAAA,IACF;AAAA,EACF;AACF;AAKA,SAAS,KAAM,WAAW,mBAAmB;AAC3C,WAAS,IAAK,MAAM,OAAO,YAAY;AACrC,QAAI,OAAO,aAAa,aAAa;AACnC;AAAA,IACF;AAEA,iBAAa,OAAO,CAAC,GAAG,mBAAmB,UAAU;AAErD,QAAI,OAAO,WAAW,YAAY,UAAU;AAC1C,iBAAW,UAAU,IAAI,KAAK,KAAK,IAAI,IAAI,WAAW,UAAU,KAAK;AAAA,IACvE;AACA,QAAI,WAAW,SAAS;AACtB,iBAAW,UAAU,WAAW,QAAQ,YAAY;AAAA,IACtD;AAEA,WAAO,mBAAmB,IAAI,EAC3B,QAAQ,wBAAwB,kBAAkB,EAClD,QAAQ,SAAS,MAAM;AAE1B,QAAI,wBAAwB;AAC5B,aAAS,iBAAiB,YAAY;AACpC,UAAI,CAAC,WAAW,aAAa,GAAG;AAC9B;AAAA,MACF;AAEA,+BAAyB,OAAO;AAEhC,UAAI,WAAW,aAAa,MAAM,MAAM;AACtC;AAAA,MACF;AASA,+BAAyB,MAAM,WAAW,aAAa,EAAE,MAAM,GAAG,EAAE,CAAC;AAAA,IACvE;AAEA,WAAQ,SAAS,SACf,OAAO,MAAM,UAAU,MAAM,OAAO,IAAI,IAAI;AAAA,EAChD;AAEA,WAAS,IAAK,MAAM;AAClB,QAAI,OAAO,aAAa,eAAgB,UAAU,UAAU,CAAC,MAAO;AAClE;AAAA,IACF;AAIA,QAAI,UAAU,SAAS,SAAS,SAAS,OAAO,MAAM,IAAI,IAAI,CAAC;AAC/D,QAAI,MAAM,CAAC;AACX,aAAS,IAAI,GAAG,IAAI,QAAQ,QAAQ,KAAK;AACvC,UAAI,QAAQ,QAAQ,CAAC,EAAE,MAAM,GAAG;AAChC,UAAI,QAAQ,MAAM,MAAM,CAAC,EAAE,KAAK,GAAG;AAEnC,UAAI;AACF,YAAI,QAAQ,mBAAmB,MAAM,CAAC,CAAC;AACvC,YAAI,KAAK,IAAI,UAAU,KAAK,OAAO,KAAK;AAExC,YAAI,SAAS,OAAO;AAClB;AAAA,QACF;AAAA,MACF,SAAS,GAAG;AAAA,MAAC;AAAA,IACf;AAEA,WAAO,OAAO,IAAI,IAAI,IAAI;AAAA,EAC5B;AAEA,SAAO,OAAO;AAAA,IACZ;AAAA,MACE;AAAA,MACA;AAAA,MACA,QAAQ,SAAU,MAAM,YAAY;AAClC;AAAA,UACE;AAAA,UACA;AAAA,UACA,OAAO,CAAC,GAAG,YAAY;AAAA,YACrB,SAAS;AAAA,UACX,CAAC;AAAA,QACH;AAAA,MACF;AAAA,MACA,gBAAgB,SAAU,YAAY;AACpC,eAAO,KAAK,KAAK,WAAW,OAAO,CAAC,GAAG,KAAK,YAAY,UAAU,CAAC;AAAA,MACrE;AAAA,MACA,eAAe,SAAUA,YAAW;AAClC,eAAO,KAAK,OAAO,CAAC,GAAG,KAAK,WAAWA,UAAS,GAAG,KAAK,UAAU;AAAA,MACpE;AAAA,IACF;AAAA,IACA;AAAA,MACE,YAAY,EAAE,OAAO,OAAO,OAAO,iBAAiB,EAAE;AAAA,MACtD,WAAW,EAAE,OAAO,OAAO,OAAO,SAAS,EAAE;AAAA,IAC/C;AAAA,EACF;AACF;AAEA,IAAI,MAAM,KAAK,kBAAkB,EAAE,MAAM,IAAI,CAAC;", "names": ["converter"]}