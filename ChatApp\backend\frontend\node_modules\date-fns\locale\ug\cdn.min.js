(()=>{var $;function C(G){return C=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(H){return typeof H}:function(H){return H&&typeof Symbol=="function"&&H.constructor===Symbol&&H!==Symbol.prototype?"symbol":typeof H},C(G)}function x(G,H){var J=Object.keys(G);if(Object.getOwnPropertySymbols){var X=Object.getOwnPropertySymbols(G);H&&(X=X.filter(function(Y){return Object.getOwnPropertyDescriptor(G,Y).enumerable})),J.push.apply(J,X)}return J}function Q(G){for(var H=1;H<arguments.length;H++){var J=arguments[H]!=null?arguments[H]:{};H%2?x(Object(J),!0).forEach(function(X){N(G,X,J[X])}):Object.getOwnPropertyDescriptors?Object.defineProperties(G,Object.getOwnPropertyDescriptors(J)):x(Object(J)).forEach(function(X){Object.defineProperty(G,X,Object.getOwnPropertyDescriptor(J,X))})}return G}function N(G,H,J){if(H=z(H),H in G)Object.defineProperty(G,H,{value:J,enumerable:!0,configurable:!0,writable:!0});else G[H]=J;return G}function z(G){var H=E(G,"string");return C(H)=="symbol"?H:String(H)}function E(G,H){if(C(G)!="object"||!G)return G;var J=G[Symbol.toPrimitive];if(J!==void 0){var X=J.call(G,H||"default");if(C(X)!="object")return X;throw new TypeError("@@toPrimitive must return a primitive value.")}return(H==="string"?String:Number)(G)}var W=Object.defineProperty,JG=function G(H,J){for(var X in J)W(H,X,{get:J[X],enumerable:!0,configurable:!0,set:function Y(Z){return J[X]=function(){return Z}}})},D={lessThanXSeconds:{one:"\u0628\u0649\u0631 \u0633\u0649\u0643\u06C7\u0646\u062A \u0626\u0649\u0686\u0649\u062F\u06D5",other:"\u0633\u0649\u0643\u06C7\u0646\u062A \u0626\u0649\u0686\u0649\u062F\u06D5 {{count}}"},xSeconds:{one:"\u0628\u0649\u0631 \u0633\u0649\u0643\u06C7\u0646\u062A",other:"\u0633\u0649\u0643\u06C7\u0646\u062A {{count}}"},halfAMinute:"\u064A\u0649\u0631\u0649\u0645 \u0645\u0649\u0646\u06C7\u062A",lessThanXMinutes:{one:"\u0628\u0649\u0631 \u0645\u0649\u0646\u06C7\u062A \u0626\u0649\u0686\u0649\u062F\u06D5",other:"\u0645\u0649\u0646\u06C7\u062A \u0626\u0649\u0686\u0649\u062F\u06D5 {{count}}"},xMinutes:{one:"\u0628\u0649\u0631 \u0645\u0649\u0646\u06C7\u062A",other:"\u0645\u0649\u0646\u06C7\u062A {{count}}"},aboutXHours:{one:"\u062A\u06D5\u062E\u0645\u0649\u0646\u06D5\u0646 \u0628\u0649\u0631 \u0633\u0627\u0626\u06D5\u062A",other:"\u0633\u0627\u0626\u06D5\u062A {{count}} \u062A\u06D5\u062E\u0645\u0649\u0646\u06D5\u0646"},xHours:{one:"\u0628\u0649\u0631 \u0633\u0627\u0626\u06D5\u062A",other:"\u0633\u0627\u0626\u06D5\u062A {{count}}"},xDays:{one:"\u0628\u0649\u0631 \u0643\u06C8\u0646",other:"\u0643\u06C8\u0646 {{count}}"},aboutXWeeks:{one:"\u062A\u06D5\u062E\u0645\u0649\u0646\u06D5\u0646 \u0628\u0649\u0631\u06BE\u06D5\u067E\u062A\u06D5",other:"\u06BE\u06D5\u067E\u062A\u06D5 {{count}} \u062A\u06D5\u062E\u0645\u0649\u0646\u06D5\u0646"},xWeeks:{one:"\u0628\u0649\u0631\u06BE\u06D5\u067E\u062A\u06D5",other:"\u06BE\u06D5\u067E\u062A\u06D5 {{count}}"},aboutXMonths:{one:"\u062A\u06D5\u062E\u0645\u0649\u0646\u06D5\u0646 \u0628\u0649\u0631 \u0626\u0627\u064A",other:"\u0626\u0627\u064A {{count}} \u062A\u06D5\u062E\u0645\u0649\u0646\u06D5\u0646"},xMonths:{one:"\u0628\u0649\u0631 \u0626\u0627\u064A",other:"\u0626\u0627\u064A {{count}}"},aboutXYears:{one:"\u062A\u06D5\u062E\u0645\u0649\u0646\u06D5\u0646 \u0628\u0649\u0631 \u064A\u0649\u0644",other:"\u064A\u0649\u0644 {{count}} \u062A\u06D5\u062E\u0645\u0649\u0646\u06D5\u0646"},xYears:{one:"\u0628\u0649\u0631 \u064A\u0649\u0644",other:"\u064A\u0649\u0644 {{count}}"},overXYears:{one:"\u0628\u0649\u0631 \u064A\u0649\u0644\u062F\u0649\u0646 \u0626\u0627\u0631\u062A\u06C7\u0642",other:"\u064A\u0649\u0644\u062F\u0649\u0646 \u0626\u0627\u0631\u062A\u06C7\u0642 {{count}}"},almostXYears:{one:"\u0626\u0627\u0633\u0627\u0633\u06D5\u0646 \u0628\u0649\u0631 \u064A\u0649\u0644",other:"\u064A\u0649\u0644 {{count}} \u0626\u0627\u0633\u0627\u0633\u06D5\u0646"}},S=function G(H,J,X){var Y,Z=D[H];if(typeof Z==="string")Y=Z;else if(J===1)Y=Z.one;else Y=Z.other.replace("{{count}}",String(J));if(X!==null&&X!==void 0&&X.addSuffix)if(X.comparison&&X.comparison>0)return Y;else return Y+" \u0628\u0648\u0644\u062F\u0649";return Y};function A(G){return function(){var H=arguments.length>0&&arguments[0]!==void 0?arguments[0]:{},J=H.width?String(H.width):G.defaultWidth,X=G.formats[J]||G.formats[G.defaultWidth];return X}}var M={full:"EEEE, MMMM do, y",long:"MMMM do, y",medium:"MMM d, y",short:"MM/dd/yyyy"},R={full:"h:mm:ss a zzzz",long:"h:mm:ss a z",medium:"h:mm:ss a",short:"h:mm a"},L={full:"{{date}} '\u062F\u06D5' {{time}}",long:"{{date}} '\u062F\u06D5' {{time}}",medium:"{{date}}, {{time}}",short:"{{date}}, {{time}}"},V={date:A({formats:M,defaultWidth:"full"}),time:A({formats:R,defaultWidth:"full"}),dateTime:A({formats:L,defaultWidth:"full"})},j={lastWeek:"'\u0626\u200D\u06C6\u062A\u0643\u06D5\u0646' eeee '\u062F\u06D5' p",yesterday:"'\u062A\u06C8\u0646\u06C8\u06AF\u06C8\u0646 \u062F\u06D5' p",today:"'\u0628\u06C8\u06AF\u06C8\u0646 \u062F\u06D5' p",tomorrow:"'\u0626\u06D5\u062A\u06D5 \u062F\u06D5' p",nextWeek:"eeee '\u062F\u06D5' p",other:"P"},w=function G(H,J,X,Y){return j[H]};function I(G){return function(H,J){var X=J!==null&&J!==void 0&&J.context?String(J.context):"standalone",Y;if(X==="formatting"&&G.formattingValues){var Z=G.defaultFormattingWidth||G.defaultWidth,B=J!==null&&J!==void 0&&J.width?String(J.width):Z;Y=G.formattingValues[B]||G.formattingValues[Z]}else{var T=G.defaultWidth,q=J!==null&&J!==void 0&&J.width?String(J.width):G.defaultWidth;Y=G.values[q]||G.values[T]}var U=G.argumentCallback?G.argumentCallback(H):H;return Y[U]}}var _={narrow:["\u0628","\u0643"],abbreviated:["\u0628","\u0643"],wide:["\u0645\u0649\u064A\u0644\u0627\u062F\u0649\u062F\u0649\u0646 \u0628\u06C7\u0631\u06C7\u0646","\u0645\u0649\u064A\u0644\u0627\u062F\u0649\u062F\u0649\u0646 \u0643\u0649\u064A\u0649\u0646"]},f={narrow:["1","2","3","4"],abbreviated:["1","2","3","4"],wide:["\u0628\u0649\u0631\u0649\u0646\u062C\u0649 \u0686\u0627\u0631\u06D5\u0643","\u0626\u0649\u0643\u0643\u0649\u0646\u062C\u0649 \u0686\u0627\u0631\u06D5\u0643","\u0626\u06C8\u0686\u0649\u0646\u062C\u0649 \u0686\u0627\u0631\u06D5\u0643","\u062A\u06C6\u062A\u0649\u0646\u062C\u0649 \u0686\u0627\u0631\u06D5\u0643"]},F={narrow:["\u064A","\u0641","\u0645","\u0627","\u0645","\u0649","\u0649","\u0627","\u0633","\u06C6","\u0646","\u062F"],abbreviated:["\u064A\u0627\u0646\u06CB\u0627\u0631","\u0641\u06D0\u06CB\u0649\u0631\u0627\u0644","\u0645\u0627\u0631\u062A","\u0626\u0627\u067E\u0631\u0649\u0644","\u0645\u0627\u064A","\u0626\u0649\u064A\u06C7\u0646","\u0626\u0649\u064A\u0648\u0644","\u0626\u0627\u06CB\u063A\u06C7\u0633\u062A","\u0633\u0649\u0646\u062A\u06D5\u0628\u0649\u0631","\u0626\u06C6\u0643\u062A\u06D5\u0628\u0649\u0631","\u0646\u0648\u064A\u0627\u0628\u0649\u0631","\u062F\u0649\u0643\u0627\u0628\u0649\u0631"],wide:["\u064A\u0627\u0646\u06CB\u0627\u0631","\u0641\u06D0\u06CB\u0649\u0631\u0627\u0644","\u0645\u0627\u0631\u062A","\u0626\u0627\u067E\u0631\u0649\u0644","\u0645\u0627\u064A","\u0626\u0649\u064A\u06C7\u0646","\u0626\u0649\u064A\u0648\u0644","\u0626\u0627\u06CB\u063A\u06C7\u0633\u062A","\u0633\u0649\u0646\u062A\u06D5\u0628\u0649\u0631","\u0626\u06C6\u0643\u062A\u06D5\u0628\u0649\u0631","\u0646\u0648\u064A\u0627\u0628\u0649\u0631","\u062F\u0649\u0643\u0627\u0628\u0649\u0631"]},v={narrow:["\u064A","\u062F","\u0633","\u0686","\u067E","\u062C","\u0634"],short:["\u064A","\u062F","\u0633","\u0686","\u067E","\u062C","\u0634"],abbreviated:["\u064A\u06D5\u0643\u0634\u06D5\u0646\u0628\u06D5","\u062F\u06C8\u0634\u06D5\u0646\u0628\u06D5","\u0633\u06D5\u064A\u0634\u06D5\u0646\u0628\u06D5","\u0686\u0627\u0631\u0634\u06D5\u0646\u0628\u06D5","\u067E\u06D5\u064A\u0634\u06D5\u0646\u0628\u06D5","\u062C\u06C8\u0645\u06D5","\u0634\u06D5\u0646\u0628\u06D5"],wide:["\u064A\u06D5\u0643\u0634\u06D5\u0646\u0628\u06D5","\u062F\u06C8\u0634\u06D5\u0646\u0628\u06D5","\u0633\u06D5\u064A\u0634\u06D5\u0646\u0628\u06D5","\u0686\u0627\u0631\u0634\u06D5\u0646\u0628\u06D5","\u067E\u06D5\u064A\u0634\u06D5\u0646\u0628\u06D5","\u062C\u06C8\u0645\u06D5","\u0634\u06D5\u0646\u0628\u06D5"]},P={narrow:{am:"\u0626\u06D5",pm:"\u0686",midnight:"\u0643",noon:"\u0686",morning:"\u0626\u06D5\u062A\u0649\u06AF\u06D5\u0646",afternoon:"\u0686\u06C8\u0634\u062A\u0649\u0646 \u0643\u0649\u064A\u0649\u0646",evening:"\u0626\u0627\u062E\u0634\u0649\u0645",night:"\u0643\u0649\u0686\u06D5"},abbreviated:{am:"\u0626\u06D5",pm:"\u0686",midnight:"\u0643",noon:"\u0686",morning:"\u0626\u06D5\u062A\u0649\u06AF\u06D5\u0646",afternoon:"\u0686\u06C8\u0634\u062A\u0649\u0646 \u0643\u0649\u064A\u0649\u0646",evening:"\u0626\u0627\u062E\u0634\u0649\u0645",night:"\u0643\u0649\u0686\u06D5"},wide:{am:"\u0626\u06D5",pm:"\u0686",midnight:"\u0643",noon:"\u0686",morning:"\u0626\u06D5\u062A\u0649\u06AF\u06D5\u0646",afternoon:"\u0686\u06C8\u0634\u062A\u0649\u0646 \u0643\u0649\u064A\u0649\u0646",evening:"\u0626\u0627\u062E\u0634\u0649\u0645",night:"\u0643\u0649\u0686\u06D5"}},k={narrow:{am:"\u0626\u06D5",pm:"\u0686",midnight:"\u0643",noon:"\u0686",morning:"\u0626\u06D5\u062A\u0649\u06AF\u06D5\u0646\u062F\u06D5",afternoon:"\u0686\u06C8\u0634\u062A\u0649\u0646 \u0643\u0649\u064A\u0649\u0646",evening:"\u0626\u0627\u062E\u0634\u0627\u0645\u062F\u0627",night:"\u0643\u0649\u0686\u0649\u062F\u06D5"},abbreviated:{am:"\u0626\u06D5",pm:"\u0686",midnight:"\u0643",noon:"\u0686",morning:"\u0626\u06D5\u062A\u0649\u06AF\u06D5\u0646\u062F\u06D5",afternoon:"\u0686\u06C8\u0634\u062A\u0649\u0646 \u0643\u0649\u064A\u0649\u0646",evening:"\u0626\u0627\u062E\u0634\u0627\u0645\u062F\u0627",night:"\u0643\u0649\u0686\u0649\u062F\u06D5"},wide:{am:"\u0626\u06D5",pm:"\u0686",midnight:"\u0643",noon:"\u0686",morning:"\u0626\u06D5\u062A\u0649\u06AF\u06D5\u0646\u062F\u06D5",afternoon:"\u0686\u06C8\u0634\u062A\u0649\u0646 \u0643\u0649\u064A\u0649\u0646",evening:"\u0626\u0627\u062E\u0634\u0627\u0645\u062F\u0627",night:"\u0643\u0649\u0686\u0649\u062F\u06D5"}},b=function G(H,J){return String(H)},h={ordinalNumber:b,era:I({values:_,defaultWidth:"wide"}),quarter:I({values:f,defaultWidth:"wide",argumentCallback:function G(H){return H-1}}),month:I({values:F,defaultWidth:"wide"}),day:I({values:v,defaultWidth:"wide"}),dayPeriod:I({values:P,defaultWidth:"wide",formattingValues:k,defaultFormattingWidth:"wide"})};function O(G){return function(H){var J=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{},X=J.width,Y=X&&G.matchPatterns[X]||G.matchPatterns[G.defaultMatchWidth],Z=H.match(Y);if(!Z)return null;var B=Z[0],T=X&&G.parsePatterns[X]||G.parsePatterns[G.defaultParseWidth],q=Array.isArray(T)?c(T,function(K){return K.test(B)}):m(T,function(K){return K.test(B)}),U;U=G.valueCallback?G.valueCallback(q):q,U=J.valueCallback?J.valueCallback(U):U;var HG=H.slice(B.length);return{value:U,rest:HG}}}function m(G,H){for(var J in G)if(Object.prototype.hasOwnProperty.call(G,J)&&H(G[J]))return J;return}function c(G,H){for(var J=0;J<G.length;J++)if(H(G[J]))return J;return}function y(G){return function(H){var J=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{},X=H.match(G.matchPattern);if(!X)return null;var Y=X[0],Z=H.match(G.parsePattern);if(!Z)return null;var B=G.valueCallback?G.valueCallback(Z[0]):Z[0];B=J.valueCallback?J.valueCallback(B):B;var T=H.slice(Y.length);return{value:B,rest:T}}}var p=/^(\d+)(th|st|nd|rd)?/i,d=/\d+/i,g={narrow:/^(ب|ك)/i,wide:/^(مىيلادىدىن بۇرۇن|مىيلادىدىن كىيىن)/i},u={any:[/^بۇرۇن/i,/^كىيىن/i]},l={narrow:/^[1234]/i,abbreviated:/^چ[1234]/i,wide:/^چارەك [1234]/i},i={any:[/1/i,/2/i,/3/i,/4/i]},n={narrow:/^[يفمئامئ‍ئاسۆند]/i,abbreviated:/^(يانۋار|فېۋىرال|مارت|ئاپرىل|ماي|ئىيۇن|ئىيول|ئاۋغۇست|سىنتەبىر|ئۆكتەبىر|نويابىر|دىكابىر)/i,wide:/^(يانۋار|فېۋىرال|مارت|ئاپرىل|ماي|ئىيۇن|ئىيول|ئاۋغۇست|سىنتەبىر|ئۆكتەبىر|نويابىر|دىكابىر)/i},s={narrow:[/^ي/i,/^ف/i,/^م/i,/^ا/i,/^م/i,/^ى‍/i,/^ى‍/i,/^ا‍/i,/^س/i,/^ۆ/i,/^ن/i,/^د/i],any:[/^يان/i,/^فېۋ/i,/^مار/i,/^ئاپ/i,/^ماي/i,/^ئىيۇن/i,/^ئىيول/i,/^ئاۋ/i,/^سىن/i,/^ئۆك/i,/^نوي/i,/^دىك/i]},o={narrow:/^[دسچپجشي]/i,short:/^(يە|دۈ|سە|چا|پە|جۈ|شە)/i,abbreviated:/^(يە|دۈ|سە|چا|پە|جۈ|شە)/i,wide:/^(يەكشەنبە|دۈشەنبە|سەيشەنبە|چارشەنبە|پەيشەنبە|جۈمە|شەنبە)/i},r={narrow:[/^ي/i,/^د/i,/^س/i,/^چ/i,/^پ/i,/^ج/i,/^ش/i],any:[/^ي/i,/^د/i,/^س/i,/^چ/i,/^پ/i,/^ج/i,/^ش/i]},a={narrow:/^(ئە|چ|ك|چ|(دە|ئەتىگەن) ( ئە‍|چۈشتىن كىيىن|ئاخشىم|كىچە))/i,any:/^(ئە|چ|ك|چ|(دە|ئەتىگەن) ( ئە‍|چۈشتىن كىيىن|ئاخشىم|كىچە))/i},e={any:{am:/^ئە/i,pm:/^چ/i,midnight:/^ك/i,noon:/^چ/i,morning:/ئەتىگەن/i,afternoon:/چۈشتىن كىيىن/i,evening:/ئاخشىم/i,night:/كىچە/i}},t={ordinalNumber:y({matchPattern:p,parsePattern:d,valueCallback:function G(H){return parseInt(H,10)}}),era:O({matchPatterns:g,defaultMatchWidth:"wide",parsePatterns:u,defaultParseWidth:"any"}),quarter:O({matchPatterns:l,defaultMatchWidth:"wide",parsePatterns:i,defaultParseWidth:"any",valueCallback:function G(H){return H+1}}),month:O({matchPatterns:n,defaultMatchWidth:"wide",parsePatterns:s,defaultParseWidth:"any"}),day:O({matchPatterns:o,defaultMatchWidth:"wide",parsePatterns:r,defaultParseWidth:"any"}),dayPeriod:O({matchPatterns:a,defaultMatchWidth:"any",parsePatterns:e,defaultParseWidth:"any"})},GG={code:"ug",formatDistance:S,formatLong:V,formatRelative:w,localize:h,match:t,options:{weekStartsOn:0,firstWeekContainsDate:1}};window.dateFns=Q(Q({},window.dateFns),{},{locale:Q(Q({},($=window.dateFns)===null||$===void 0?void 0:$.locale),{},{ug:GG})})})();

//# debugId=304EC32C207F329D64756E2164756E21
