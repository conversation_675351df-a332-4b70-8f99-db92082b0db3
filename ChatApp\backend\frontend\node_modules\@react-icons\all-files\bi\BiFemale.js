// THIS FILE IS AUTO GENERATED
var GenIcon = require('../lib').GenIcon
module.exports.BiFemale = function BiFemale (props) {
  return GenIcon({"tag":"svg","attr":{"viewBox":"0 0 24 24"},"child":[{"tag":"circle","attr":{"cx":"12","cy":"4","r":"2"}},{"tag":"path","attr":{"d":"M14.948,7.684C14.813,7.275,14.431,7,14,7h-4C9.569,7,9.188,7.275,9.052,7.684l-2,6l1.775,0.593L8,18h2v4h2h2v-4h2 l-0.827-3.724l1.775-0.593L14.948,7.684z"}}]})(props);
};
