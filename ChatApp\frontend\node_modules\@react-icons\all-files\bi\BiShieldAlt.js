// THIS FILE IS AUTO GENERATED
var GenIcon = require('../lib').GenIcon
module.exports.BiShieldAlt = function BiShieldAlt (props) {
  return GenIcon({"tag":"svg","attr":{"viewBox":"0 0 24 24"},"child":[{"tag":"path","attr":{"d":"M20.342,8.447l1.105-0.553c0.266-0.133,0.458-0.376,0.526-0.665C22.041,6.941,21.978,6.638,21.8,6.4l-3-4 C18.611,2.148,18.314,2,18,2H6C5.686,2,5.389,2.148,5.2,2.4l-3,4C2.022,6.638,1.959,6.941,2.026,7.229 c0.068,0.289,0.261,0.532,0.526,0.665l1.105,0.553l-1.131,2.262C2.183,11.399,2,12.173,2,12.944v0.591 c0,2.486,1.564,4.744,3.894,5.618l3.431,1.286c0.745,0.279,1.407,0.706,1.969,1.268C11.488,21.902,11.744,22,12,22 s0.512-0.098,0.707-0.293c0.562-0.562,1.224-0.988,1.969-1.267l3.432-1.287C20.436,18.279,22,16.021,22,13.535v-0.591 c0-0.771-0.183-1.545-0.527-2.236L20.342,8.447z M20,13.535c0,1.657-1.043,3.163-2.596,3.745l-3.431,1.287 c-0.717,0.269-1.378,0.638-1.974,1.101c-0.596-0.463-1.256-0.832-1.974-1.102L6.596,17.28C5.043,16.698,4,15.192,4,13.535v-0.591 c0-0.463,0.109-0.928,0.316-1.342l1.131-2.261c0.493-0.986,0.092-2.19-0.895-2.684L4.519,6.642L6.5,4h11l1.981,2.642l-0.034,0.017 c-0.986,0.493-1.388,1.697-0.895,2.684l1.131,2.26C19.891,12.017,20,12.481,20,12.944V13.535z"}}]})(props);
};
