// THIS FILE IS AUTO GENERATED
var GenIcon = require('../lib').GenIcon
module.exports.GiBestialFangs = function GiBestialFangs (props) {
  return GenIcon({"tag":"svg","attr":{"viewBox":"0 0 512 512"},"child":[{"tag":"path","attr":{"d":"M361.906 25.938c-49.615.102-110.022 18.8-154.25 37.593-12.605-7.315-27.254-11.622-43.156-12.28-18.84-.78-39.426 3.525-60.47 13.875l-1.468.72-.093.03C51.863 87.46 22.14 140.77 19.062 200.625c91.48-18.65 15.3 110.685 57.968 156.406C181.473 441.578 284.58 484.333 390 487.53c44.707 0 67.482-12.576 70.156-40.53 14.36-46.12 15.182-98.99 13.22-148.47-12.752 42.9-25.627 85.79-47.72 128.095-5.745.157-12.06.025-18.906-.47-10.324 19.953-21.38 36.51-39.656 46.876 12.73-16.588 19.968-35.21 26-61.186 7.956-39.902 11.877-79.818 14.75-119.72L351.72 415.5c-2.474-.752-4.988-1.567-7.533-2.406-65.917-52.54-93.99-102.854-92.125-151.625 1.773-46.336 31.03-93.828 86.375-142 1.643-.338 3.29-.69 4.907-1 19.63 45.61 34.845 90.96 48.22 136.186 6.728-44.3 5.344-89.092-2.377-134.28-4.977-19.72-11.31-34.776-21.5-48.44 17.483 8.524 31.02 20.368 40.5 40.126 5.347 0 10.41.133 15.125.376 35.996 39.062 52.935 82.938 65.032 129.187 6.042-68.832-11.477-131.56-53.78-187.844l-3.752-6.81c-16.255-15.21-40.84-21.09-68.906-21.032zm-200.97 43.75c.903.005 1.8.026 2.69.062 12.338.5 23.395 3.804 33 9.094-9.716 15.956-24.405 26.127-42.064 32.03-16.195 5.416-34.8 6.806-53.125 5.157 6.505-11.26 12.63-23.118 15.094-35.936 15.916-7.18 30.878-10.494 44.407-10.406zm140.22 59.093c-42.165 42.782-66.048 86.653-67.78 131.97-1.766 46.156 19.81 91.9 65.25 136.97-32.14-11.12-66.783-24.655-94.688-44.033-33.387-18.577-59.818-42.03-73.625-71.062-15.45-32.487-13.78-71.5 10.468-113.344l16.157 9.376c-22.003 37.97-22.593 68.932-9.75 95.938 1.137 2.39 2.386 4.76 3.75 7.094-1.75-7.86-2.657-16.212-2.562-25.125.702-65.966 83.636-107.434 152.78-127.782z","fillRule":"evenodd"}}]})(props);
};
