/*! 🌼 daisyUI 5.0.42 - MIT License */ @layer utilities{.cally{font-size:.7rem;&::part(container){user-select:none;padding:.5rem 1rem}& ::part(th){block-size:auto;font-weight:400}&::part(header){direction:ltr}& ::part(head){opacity:.5;font-size:.7rem}&::part(button){border-radius:var(--radius-field);background:0 0;border:none;padding:.5rem}&::part(button):hover{background:var(--color-base-200)}& ::part(day){border-radius:var(--radius-field);font-size:.7rem}& ::part(button day today){background:var(--color-primary);color:var(--color-primary-content)}& ::part(selected){color:var(--color-base-100);background:var(--color-base-content);border-radius:var(--radius-field)}& ::part(range-inner){border-radius:0}& ::part(range-start){border-start-end-radius:0;border-end-end-radius:0}& ::part(range-end){border-start-start-radius:0;border-end-start-radius:0}& ::part(range-start range-end){border-radius:var(--radius-field)}& calendar-month{width:100%}}.react-day-picker{user-select:none;background-color:var(--color-base-100);border-radius:var(--radius-box);border:var(--border)solid var(--color-base-200);font-size:.75rem;display:inline-block;position:relative;overflow:clip;&[dir=rtl]{& .rdp-nav{& .rdp-chevron{transform-origin:50%;transform:rotate(180deg)}}}& *{box-sizing:border-box}& .rdp-day{text-align:center;width:2.25rem;height:2.25rem}& .rdp-day_button{cursor:pointer;font:inherit;color:inherit;border-radius:var(--radius-field);background:0 0;border:2px solid #0000;justify-content:center;align-items:center;width:2.25rem;height:2.25rem;margin:0;padding:0;display:flex;&:disabled{cursor:revert}&:hover{background-color:var(--color-base-200)}}& .rdp-caption_label{z-index:1;white-space:nowrap;border:0;align-items:center;display:inline-flex;position:relative}& .rdp-button_next{border-radius:var(--radius-field);&:hover{background-color:var(--color-base-200)}}& .rdp-button_previous{border-radius:var(--radius-field);&:hover{background-color:var(--color-base-200)}}& .rdp-button_next,& .rdp-button_previous{cursor:pointer;font:inherit;color:inherit;appearance:none;background:0 0;border:none;justify-content:center;align-items:center;width:2.25rem;height:2.25rem;margin:0;padding:0;display:inline-flex;position:relative;&:disabled{cursor:revert;opacity:.5}}& .rdp-chevron{fill:var(--color-base-content);width:1rem;height:1rem;display:inline-block}& .rdp-dropdowns{align-items:center;gap:.5rem;display:inline-flex;position:relative}& .rdp-dropdown{z-index:2;opacity:0;appearance:none;cursor:inherit;line-height:inherit;border:none;width:100%;margin:0;padding:0;position:absolute;inset-block:0;inset-inline-start:0;&:focus-visible{&~.rdp-caption_label{outline:5px auto highlight;outline:5px auto -webkit-focus-ring-color}}}& .rdp-dropdown_root{align-items:center;display:inline-flex;position:relative;&[data-disabled=true]{& .rdp-chevron{opacity:.5}}}& .rdp-month_caption{height:2.75rem;font-size:.75rem;font-weight:inherit;place-content:center;display:flex}& .rdp-months{flex-wrap:wrap;gap:2rem;max-width:fit-content;padding:.5rem;display:flex;position:relative}& .rdp-month_grid{border-collapse:collapse}& .rdp-nav{inset-block-start:0;inset-inline-end:0;justify-content:space-between;align-items:center;width:100%;height:2.75rem;padding-inline:.5rem;display:flex;position:absolute;top:.25rem}& .rdp-weekday{opacity:.6;text-align:center;padding:.5rem 0;font-size:smaller;font-weight:500}& .rdp-week_number{opacity:.6;text-align:center;border:none;border-radius:100%;width:2.25rem;height:2.25rem;font-size:small;font-weight:400}& .rdp-today:not(.rdp-outside){& .rdp-day_button{background:var(--color-primary);color:var(--color-primary-content)}}& .rdp-selected{font-weight:inherit;font-size:.75rem;& .rdp-day_button{color:var(--color-base-100);background-color:var(--color-base-content);border-radius:var(--radius-field);border:none;&:hover{background-color:var(--color-base-content)}}}& .rdp-outside{opacity:.75}& .rdp-disabled{opacity:.5}& .rdp-hidden{visibility:hidden;color:var(--color-base-content)}& .rdp-range_start{& .rdp-day_button{border-radius:var(--radius-field)0 0 var(--radius-field)}}& .rdp-range_start .rdp-day_button{background-color:var(--color-base-content);color:var(--color-base-100)}& .rdp-range_middle{background-color:var(--color-base-200)}& .rdp-range_middle .rdp-day_button{border:unset;border-radius:unset;color:inherit}& .rdp-range_end{color:var(--color-base-content);& .rdp-day_button{border-radius:0 var(--radius-field)var(--radius-field)0}}& .rdp-range_end .rdp-day_button{background-color:var(--color-base-content);color:var(--color-base-100)}& .rdp-range_start.rdp-range_end{background:revert}& .rdp-focusable{cursor:pointer}& .rdp-footer{border-top:var(--border)solid var(--color-base-200);padding:.5rem}}.pika-single{&:is(div){user-select:none;z-index:999;color:var(--color-base-content);background-color:var(--color-base-100);border-radius:var(--radius-box);border:var(--border)solid var(--color-base-200);padding:.5rem;font-size:.75rem;display:inline-block;position:relative;&:before,&:after{content:"";display:table}&:after{clear:both}&.is-hidden{display:none}&.is-bound{position:absolute}& .pika-lendar{float:left}& .pika-title{text-align:center;position:relative}& .pika-label{z-index:999;background-color:var(--color-base-100);margin:0;padding:5px 3px;display:inline-block;position:relative;overflow:hidden}& .pika-title{& select{cursor:pointer;z-index:999;opacity:0;margin:0;position:absolute;top:5px;left:0}}& .pika-prev,& .pika-next{cursor:pointer;color:#0000;border-radius:var(--radius-field);border:0;outline:none;width:2.25rem;height:2.25rem;font-size:1.2em;display:block;position:absolute;top:0;&:hover{background-color:var(--color-base-200)}&.is-disabled{cursor:default;opacity:.2}&:before{width:2.25rem;height:2.25rem;color:var(--color-base-content);line-height:2.25;display:inline-block}}& .pika-prev{left:0;&:before{content:"‹"}}& .pika-next{right:0;&:before{content:"›"}}& .pika-select{display:inline-block}& .pika-table{border-collapse:collapse;border-spacing:0;border:0;width:100%}& .pika-table{& th,& td{padding:0}& th{opacity:.6;text-align:center;width:2.25rem;height:2.25rem}}& .pika-button{cursor:pointer;text-align:right;text-align:center;border:0;outline:none;width:2.25rem;height:2.25rem;margin:0;padding:5px;display:block}& .pika-week{color:var(--color-base-content)}& .is-today{& .pika-button{background:var(--color-primary);color:var(--color-primary-content)}}& .is-selected,& .has-event{& .pika-button{&,&:hover{color:var(--color-base-100);background-color:var(--color-base-content);border-radius:var(--radius-field)}}}& .has-event{& .pika-button{background:var(--color-base-primary)}}& .is-disabled,& .is-inrange{& .pika-button{background:var(--color-base-primary)}}& .is-startrange{& .pika-button{color:var(--color-base-100);background:var(--color-base-content);border-radius:var(--radius-field)}}& .is-endrange{& .pika-button{color:var(--color-base-100);background:var(--color-base-content);border-radius:var(--radius-field)}}& .is-disabled{& .pika-button{pointer-events:none;cursor:default;color:var(--color-base-content);opacity:.3}}& .is-outside-current-month{& .pika-button{color:var(--color-base-content);opacity:.3}}& .is-selection-disabled{pointer-events:none;cursor:default}& .pika-button:hover,& .pika-row.pick-whole-week:hover .pika-button{color:var(--color-base-content);background-color:var(--color-base-200);border-radius:var(--radius-field)}& .pika-table abbr{font-weight:400;text-decoration:none}}}}