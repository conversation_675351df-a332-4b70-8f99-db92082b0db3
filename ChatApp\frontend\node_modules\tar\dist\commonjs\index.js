"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __exportStar = (this && this.__exportStar) || function(m, exports) {
    for (var p in m) if (p !== "default" && !Object.prototype.hasOwnProperty.call(exports, p)) __createBinding(exports, m, p);
};
var __importStar = (this && this.__importStar) || function (mod) {
    if (mod && mod.__esModule) return mod;
    var result = {};
    if (mod != null) for (var k in mod) if (k !== "default" && Object.prototype.hasOwnProperty.call(mod, k)) __createBinding(result, mod, k);
    __setModuleDefault(result, mod);
    return result;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.u = exports.types = exports.r = exports.t = exports.x = exports.c = void 0;
__exportStar(require("./create.js"), exports);
var create_js_1 = require("./create.js");
Object.defineProperty(exports, "c", { enumerable: true, get: function () { return create_js_1.create; } });
__exportStar(require("./extract.js"), exports);
var extract_js_1 = require("./extract.js");
Object.defineProperty(exports, "x", { enumerable: true, get: function () { return extract_js_1.extract; } });
__exportStar(require("./header.js"), exports);
__exportStar(require("./list.js"), exports);
var list_js_1 = require("./list.js");
Object.defineProperty(exports, "t", { enumerable: true, get: function () { return list_js_1.list; } });
// classes
__exportStar(require("./pack.js"), exports);
__exportStar(require("./parse.js"), exports);
__exportStar(require("./pax.js"), exports);
__exportStar(require("./read-entry.js"), exports);
__exportStar(require("./replace.js"), exports);
var replace_js_1 = require("./replace.js");
Object.defineProperty(exports, "r", { enumerable: true, get: function () { return replace_js_1.replace; } });
exports.types = __importStar(require("./types.js"));
__exportStar(require("./unpack.js"), exports);
__exportStar(require("./update.js"), exports);
var update_js_1 = require("./update.js");
Object.defineProperty(exports, "u", { enumerable: true, get: function () { return update_js_1.update; } });
__exportStar(require("./write-entry.js"), exports);
//# sourceMappingURL=index.js.map