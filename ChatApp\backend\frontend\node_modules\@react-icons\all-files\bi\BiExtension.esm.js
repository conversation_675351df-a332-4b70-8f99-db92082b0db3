// THIS FILE IS AUTO GENERATED
import { GenIcon } from '../lib';
export function BiExtension (props) {
  return GenIcon({"tag":"svg","attr":{"viewBox":"0 0 24 24"},"child":[{"tag":"path","attr":{"d":"M19,10V7c0-1.103-0.897-2-2-2h-3c0-1.654-1.346-3-3-3S8,3.346,8,5H5C3.897,5,3,5.897,3,7v3.881l0.659,0.239 C4.461,11.41,5,12.166,5,13s-0.539,1.59-1.341,1.88L3,15.119V19c0,1.103,0.897,2,2,2h3.881l0.239-0.659 C9.41,19.539,10.166,19,11,19s1.59,0.539,1.88,1.341L13.119,21H17c1.103,0,2-0.897,2-2v-3c1.654,0,3-1.346,3-3S20.654,10,19,10z M19,14h-2l-0.003,5h-2.545c-0.711-1.22-2.022-2-3.452-2s-2.741,0.78-3.452,2H5v-2.548C6.22,15.741,7,14.43,7,13 s-0.78-2.741-2-3.452V7h5V5c0-0.552,0.448-1,1-1s1,0.448,1,1v2h5v5h2c0.552,0,1,0.448,1,1S19.552,14,19,14z"}}]})(props);
};
