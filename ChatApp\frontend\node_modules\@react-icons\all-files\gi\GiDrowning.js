// THIS FILE IS AUTO GENERATED
var GenIcon = require('../lib').GenIcon
module.exports.GiDrowning = function GiDrowning (props) {
  return GenIcon({"tag":"svg","attr":{"viewBox":"0 0 512 512"},"child":[{"tag":"path","attr":{"d":"M195.78 21.25c.558 3.56 1.4 7.026 2.5 10.375-10.494 7.93-17.31 20.482-17.31 34.563 0 23.834 19.54 43.374 43.374 43.374 21.168 0 38.917-15.417 42.625-35.562 25.962-4.582 46.306-26.02 50.436-52.75h-19c-4.337 20.21-21.453 35-41.812 35-20.36 0-37.476-14.79-41.813-35h-19zm175.064 13.938c-25.434 0-46.25 20.815-46.25 46.25 0 19.075 11.697 35.546 28.28 42.593-7.11 9.418-11.312 21.134-11.312 33.782 0 7.75 1.605 15.145 4.47 21.875-12.57 5.384-21.438 17.91-21.438 32.375 0 19.32 15.837 35.157 35.156 35.157 18.684 0 34.16-14.802 35.156-33.25.955.048 1.908.06 2.875.06 30.937 0 56.22-25.28 56.22-56.218 0-26.182-18.118-48.315-42.438-54.5 3.53-6.522 5.532-13.976 5.532-21.874 0-25.434-20.816-46.25-46.25-46.25zm-163.97 13.5c9.535 13.508 24.047 23.037 40.814 25.562-3.3 9.72-12.44 16.625-23.344 16.625-13.734 0-24.688-10.953-24.688-24.688 0-6.883 2.746-13.05 7.22-17.5zm157.657 5.906c-1.73 2.925-2.75 6.323-2.75 9.968 0 10.838 8.79 19.626 19.626 19.626 6.814 0 12.826-3.478 16.344-8.75.42 1.927.656 3.938.656 6 0 8.38-3.65 15.838-9.47 20.874-6.774 1.082-13.154 3.37-18.905 6.657-14.95-.423-26.75-12.47-26.75-27.532 0-13.163 9.002-24.028 21.25-26.844zm-77.31 53.812c-19.32 0-35.19 15.868-35.19 35.188s15.87 35.187 35.19 35.187c19.318 0 35.186-15.867 35.186-35.186 0-19.32-15.868-35.188-35.187-35.188zm110.56 11.875c3.978 0 7.788.622 11.376 1.75-8.97 3.188-15.406 11.75-15.406 21.814 0 12.78 10.376 23.125 23.156 23.125 7.517 0 14.18-3.574 18.406-9.126-.016 20.82-16.705 37.5-37.53 37.5-2.595 0-5.122-.257-7.563-.75-5.147-8.898-14.05-15.376-24.5-17.188-3.473-5.694-5.47-12.39-5.47-19.594 0-12.556 6.06-23.605 15.438-30.406 7.315-.77 14.12-3.26 20.03-7.062.685-.037 1.37-.063 2.063-.063zm-110.56 6.845c9.217 0 16.5 7.25 16.5 16.47 0 9.218-7.282 16.5-16.5 16.5-9.22 0-16.5-7.282-16.5-16.5 0-9.22 7.28-16.47 16.5-16.47zM193.936 154.5c-28.534 0-51.875 23.34-51.875 51.875 0 27.282 21.344 49.764 48.157 51.688-9.678 17.452-15.47 39.067-15.47 62.5-.002 30.41 9.606 58.155 24.844 77.25l10.03 12.562-15.874 2.5c-31.947 5.028-52.09 14.733-65.375 28.344-11.19 11.464-17.854 26.476-21.97 45.342H400.75c-4.88-18.106-12.076-32.644-23.156-43.906-13.31-13.53-32.53-23.205-61.656-28.687l-15.594-2.94 10.156-12.186c15.99-19.265 26.47-47.184 26.47-78.28 0-29.982-9.56-56.965-24.5-76.095-14.943-19.132-34.85-30.314-56.626-30.314-3.65 0-7.25.324-10.78.938.484-2.84.75-5.747.75-8.72 0-28.534-23.34-51.874-51.876-51.874zm-9.156 19.938c-1.747 3.255-2.75 6.982-2.75 10.937 0 12.78 10.377 23.125 23.157 23.125 8.837 0 16.508-4.938 20.407-12.22 1 3.18 1.53 6.57 1.53 10.095 0 6.337-1.743 12.238-4.78 17.25-6.165 3.686-11.928 8.405-17.22 14-3.486 1.23-7.257 1.906-11.186 1.906-18.435 0-33.157-14.72-33.157-33.155 0-15.252 10.084-27.995 24-31.938zm171.75 21.437c5.43 5.877 12.062 10.598 19.533 13.75.114.795.187 1.607.187 2.438 0 9.218-7.28 16.468-16.5 16.468-9.22 0-16.47-7.25-16.47-16.468 0-8.112 5.623-14.716 13.25-16.187zM224.47 266.75c4.943.072 9.195 2.03 11.343 5.75 3.818 6.613-.49 16.256-9.625 21.53-9.137 5.276-19.62 4.208-23.438-2.405-3.817-6.612.49-16.256 9.625-21.53 3.997-2.31 8.248-3.4 12.094-3.345zm60.81 0c3.847-.056 8.13 1.036 12.126 3.344 9.136 5.275 13.443 14.92 9.625 21.53-3.817 6.614-14.33 7.682-23.467 2.407-9.137-5.274-13.444-14.917-9.625-21.53 2.147-3.72 6.4-5.678 11.343-5.75zm-30.56 58.97c22.9-.08 45.62 14.087 54.31 42.75-36.916-23.654-71.587-24.153-109.624 0 9.35-28.35 32.412-42.673 55.313-42.75z"}}]})(props);
};
