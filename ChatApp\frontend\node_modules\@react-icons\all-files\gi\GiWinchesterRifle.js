// THIS FILE IS AUTO GENERATED
var GenIcon = require('../lib').GenIcon
module.exports.GiWinchesterRifle = function GiWinchesterRifle (props) {
  return GenIcon({"tag":"svg","attr":{"viewBox":"0 0 512 512"},"child":[{"tag":"path","attr":{"d":"M323.8 149.6l10.4 13.8L491.8 43.8 481.4 30zm-95.1 72.2l25.3 33.3-68.7 64.7-21.5-10.1 9.3-45.7zM18.3 424.2l99.1-95.3s21.7 10.5 27.5 8.7c.5-.2 1-.4 1.4-.8 2.6-2 6-6.9 9.1-12l1.1.5 19 8.8L74.3 482c-6.8-40.3-51.6-57.5-56-57.8zm224.1-212.8l67.7-51.3 17.1 22.6c4.7 6.3 3.9 15.5-2.4 20.2l-56.4 42.8-.7-1zm-64.8 182.3c7.9 4.8 18.3 2.3 23.1-5.7l14-23.1c3.4-5.7 3.2-12.8-.6-18.3 1.3-.7 2.5-1.5 3.6-2.3 5.4-4 9-9.9 10.1-16.4 1.1-6.8-.5-13.8-4.4-20.3l-6.4 6.1c2.1 3.9 3 8.4 2.3 12.9-1 6.4-5.7 11.5-12.7 14.2-7.7-3.1-16.5-.2-20.7 6.9l-14 23.1c-4.8 7.8-2.3 18.1 5.7 22.9zm1.7-18.6l14.1-23.2c2.4-3.9 7.4-5.1 11.3-2.8l1.1.8c3.1 2.6 3.8 7.1 1.7 10.5l-14.1 23.2c-2.4 3.9-7.4 5.2-11.3 2.8-3.9-2.3-5.2-7.4-2.8-11.3zm167.6-189c-.5-3-1.5-5.9-2.8-8.7L488.5 67.9l5.2 6.9zm-192.7 84.5l-3.2 15.6c-7.7-3.3-10.2-12.1-18.1-9.2 2.4-8.6 16.4-5.3 21.3-6.4z"}}]})(props);
};
