// THIS FILE IS AUTO GENERATED
var GenIcon = require('../lib').GenIcon
module.exports.IoList = function IoList (props) {
  return GenIcon({"tag":"svg","attr":{"viewBox":"0 0 512 512"},"child":[{"tag":"path","attr":{"fill":"none","strokeLinecap":"round","strokeLinejoin":"round","strokeWidth":"48","d":"M160 144h288M160 256h288M160 368h288"}},{"tag":"circle","attr":{"cx":"80","cy":"144","r":"16","fill":"none","strokeLinecap":"round","strokeLinejoin":"round","strokeWidth":"32"}},{"tag":"circle","attr":{"cx":"80","cy":"256","r":"16","fill":"none","strokeLinecap":"round","strokeLinejoin":"round","strokeWidth":"32"}},{"tag":"circle","attr":{"cx":"80","cy":"368","r":"16","fill":"none","strokeLinecap":"round","strokeLinejoin":"round","strokeWidth":"32"}}]})(props);
};
