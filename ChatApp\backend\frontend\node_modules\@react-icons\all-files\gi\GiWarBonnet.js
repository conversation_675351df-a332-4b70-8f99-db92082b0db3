// THIS FILE IS AUTO GENERATED
var GenIcon = require('../lib').GenIcon
module.exports.GiWarBonnet = function GiWarBonnet (props) {
  return GenIcon({"tag":"svg","attr":{"viewBox":"0 0 512 512"},"child":[{"tag":"path","attr":{"d":"M276.7 23.68c3 5.05 7.5 10.74 13.1 16.69 1.9 1.97 3.9 3.98 6 6.01 6.4 3.81 13 7.81 19.5 12.04 40.9 26.54 81.8 60.38 104.5 94.68l15.3-3.5c-18.8-29.2-50.6-59.93-82.9-83.83-18.1-13.36-36.3-24.65-52.3-32.57-8.5-4.27-16.5-7.41-23.2-9.52zm-60.1 6.45c2.1 5.94 6.2 12.94 13.1 20.78 12.2 5.83 24.9 12.51 37.5 19.77 47.6 27.26 94.7 62.02 118.7 93.12l15-6c-21-29-58.1-60.09-95.4-84.28-20.6-13.33-41.1-24.65-58.9-32.62-11.6-5.23-21.9-8.76-30-10.77zm-64.4 15.64h-1.5c1.9 6.53 7.9 15.1 18.4 24.68.2.15.4.31.5.46 18.7 6.16 38.6 14.75 58.6 24.62 28.9 14.27 57.6 31.27 81.3 47.67 19.7 13.7 35.9 26.5 46.2 38l12.6-6.4 2.1-1.2c-21.2-26.4-66.7-61.2-112.2-87.31-25.1-14.41-50.2-26.39-70.9-33.53-14.6-5.02-27.2-7.02-35.1-6.99zM119.4 79c-2.1 0-4.1.1-5.9.28-4.8.52-8.2 1.71-10.7 3.3 3.7 9.76 14.9 22.62 31.8 35.42 4.8 3.6 10.1 7.3 15.7 10.9 10.3 2.5 20.8 5.4 31.4 8.6 30.8 9.3 61.9 21.3 88 33.6 25.2 11.8 45.4 23.3 57.1 34.4 4.7-4.6 9.6-8.9 14.7-12.9-8.7-9.2-23.6-21.7-42.2-34.6-22.9-15.8-51-32.4-79.1-46.3-28-13.89-56.1-25-78.5-29.9-8.4-1.84-16-2.8-22.3-2.8zm-33.61 59c-7.89.1-14.69.9-19.93 2.4-4.69 1.3-7.85 3-10.06 5 5.23 9.1 18.44 19.9 37.21 29.7 14.59 7.6 32.29 14.7 50.99 21.2 2.9.2 5.9.4 8.9.6 32.1 2.4 65.1 7.4 93.2 13.7 21.6 4.8 40 10.1 53.7 16.2l9.6-12.3c-10-7.7-26.8-17.5-47.3-27.1-25.2-11.9-55.7-23.6-85.6-32.7-30-9.1-59.5-15.4-82.46-16.5-2.86-.2-5.61-.2-8.25-.2zm373.41 26.1c-1.7 0-3.8.1-6.1.3-77.2 14.3-131.4 50.4-155.7 122.5l17.2 9.1c37.4-42.3 79.5-81.8 157.8-102.7 1.8-8.7 1.6-16.9-.6-21.6-1.9-4.2-4.4-7-10.9-7.6zm-11.1 11a9 9 0 0 1 9 9 9 9 0 0 1-9 9 9 9 0 0 1-9-9 9 9 0 0 1 9-9zm-38 12.5a9 9 0 0 1 9 9 9 9 0 0 1-9 9 9 9 0 0 1-9-9 9 9 0 0 1 9-9zm-41.3 21.8a9 9 0 0 1 9 9 9 9 0 0 1-9 9 9 9 0 0 1-9-9 9 9 0 0 1 9-9zm109.4.9c-51.9 13.6-86.1 35-114.4 60.6l.9 14.1c34.5-21.4 74.1-48.4 118.1-58zm-371.9 3.1c-14.31.3-27.51 1.3-38.8 3.3-11.29 1.9-20.64 4.8-27.03 8.2-4.29 2.3-7 4.7-8.72 7.1 7.06 7.7 22.32 15.4 42.76 20.9 15.98 4.3 34.79 7.4 54.49 9.6l8.1-1.8c31.7-6.7 64.9-10.9 93.8-12.2 7.2-.3 14.1-.5 20.7-.5 15.2.1 28.6 1.1 39.3 3.5l3.4-7.1c-11.5-5.3-30-11.3-52.1-16.2-27.2-6.1-59.5-11-90.7-13.3-12-.9-23.7-1.4-35-1.5h-4.8zM335.1 242a9 9 0 0 1 9 9 9 9 0 0 1-9 9 9 9 0 0 1-9-9 9 9 0 0 1 9-9zm-83.5 24.1c-6.3 0-13 .1-19.9.4-27.8 1.3-60.2 5.3-90.9 11.8-30.7 6.5-59.83 15.6-80.61 26.4-10.39 5.3-18.66 11.2-24.02 16.9-3.66 3.9-5.83 7.5-6.95 11.1 27.8 15.2 69.15 15.6 110.78 9 6.4-4.6 13-9.2 19.7-13.6 26.9-17.8 56-34 82.1-46.3 12.2-5.7 23.6-10.5 33.9-14.3-6.9-.8-15-1.3-24.1-1.4zm27.1 19.7c-8.9 3.3-18.7 7.4-29.2 12.3-25.2 11.9-53.8 27.8-79.9 45-26.1 17.3-49.9 36-65.4 52.8-7.73 8.5-13.4 16.5-16.35 23.1-1.92 4.3-2.64 7.8-2.54 10.7 1.98 0 4.73-.3 8.32-1.3 6.97-2 16.07-6 26.47-11.5 20.8-11.1 46.6-28.1 72.8-46.2.5-.4 1.1-.7 1.6-1.1 1.5-1.8 2.9-3.5 4.4-5.3 18.8-22.3 39.9-43.8 59.4-61.2 7-6.3 13.8-12 20.3-16.9 0-.2.1-.3.1-.4zm68.2 1.6c-3.8 3.9-7.6 7.9-11.3 12 1.7 62.6 3.1 125.4-1.3 188.6 3.9-1.3 7.7-2 11.6-1.7 3.1.2 5.9.9 8.7 2 .7-66.8-3.4-133.8-7.7-200.9zm33.8 9.2c-4.9 3-9.8 6-14.6 8.9.1 1.4.1 2.9.2 4.4L385 430.8c7.9-6.8 17.8-11.1 28.1-11.7-16.2-39.1-26.5-81.4-32.4-122.5zm-80 12.4c-5 7.1-10.9 16.7-16.5 28-9 18.1-17.8 39.8-24.3 61.1-6.5 21.3-10.7 42.2-10.8 57.8 0 7.9 1 14.3 2.7 18.6.9 2.6 2 4.2 3.3 5.5 1-.9 2.3-2 3.7-4 3-4 6.4-10.3 9.7-18 6.6-15.4 13.1-36.5 19.2-58.4 6.2-22 12-44.8 17.7-64.3 2.2-7.5 4.3-14.3 6.4-20.4zm-21.8.2c-2.9 2.4-5.8 4.8-8.7 7.4-18.7 16.7-39.4 37.7-57.6 59.3-18.2 21.6-33.9 43.9-43 62.4-4.6 9.2-7.4 17.6-8.3 23.9-.4 3.7-.3 6.4.3 8.7 1.5-.6 3.4-1.4 5.7-2.9 5.4-3.4 12.3-9.2 19.9-16.6 15.1-14.9 33.1-36.2 51.2-58.6 2.4-2.9 4.8-5.9 7.1-8.9 6.4-19.3 14.4-38.4 22.6-54.9 3.6-7.2 7.2-13.8 10.8-19.8z"}}]})(props);
};
