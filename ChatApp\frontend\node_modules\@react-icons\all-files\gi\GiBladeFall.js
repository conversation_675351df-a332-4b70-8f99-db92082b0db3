// THIS FILE IS AUTO GENERATED
var GenIcon = require('../lib').GenIcon
module.exports.GiBladeFall = function GiBladeFall (props) {
  return GenIcon({"tag":"svg","attr":{"viewBox":"0 0 512 512"},"child":[{"tag":"path","attr":{"d":"M54.51 19.81c-6.54 32.78-19.9 70.58-34.1 104.99v46.7C40.72 127.4 64.32 69.46 73.56 19.81zm74.49 0c22.6 35.58 18.2 82.49-2 137.49-2.7 7.4-5.7 14.9-8.9 22.5 12.5-14.4 24.5-29.5 35.8-45.1 11.7-42.17 13.1-81.58-3.5-114.89zm110.6 2.33l-4.8 12.61C193.4 141.4 113.3 218.8 20.41 292v23.7C115.3 242.1 200.5 163.5 246.8 54.3c23.2 23.64 33.8 46.7 35.7 69.9 2.1 26.2-6.9 53.4-24.6 81.8-9.5 15.3-21.4 30.8-35.2 46.5 13.8-6.5 27.3-13.4 40.5-20.7 3.7-5.3 7.3-10.6 10.6-15.9 18.9-30.5 29.9-61.7 27.3-93.2-2.6-31.49-18.9-62.4-51.4-91.53-3.4-3.01-6.7-6.01-10.1-9.03zM389 162l-9.5 9.4c-86.4 84.5-239.1 139.5-359.09 171.9v19.3C138.1 331.3 288.9 278.9 382.5 194c11.7 31 12.1 56.5 4.5 78.6-7 20.2-21.1 38-40.9 54.1 14.3-5.1 28.2-10.5 41.6-16.2 7.2-9.8 13-20.4 16.9-31.8 10.4-29.9 7.9-64.7-10.1-104.5-1.8-4.1-3.6-8.1-5.5-12.2zm94.5 121.5l-11 7.3c-41.3 24.8-82.7 40.1-120.3 53.6-107.7 35.2-242.6 57.9-331.79 65.4v60.3C130.5 462.4 442.3 453.5 473 325.5c3 27.1-1.6 48.6-11.9 66.6-13.3 23.1-36.6 40.8-67.7 54.6-61.9 27.4-153.5 38.4-245.8 48.2h143.7c40.6-7.4 78.1-17.1 109.7-31.2 33.4-14.8 60.4-34.6 76.3-62.3 15.9-27.7 19.9-62.7 9.4-105.1-1.1-4.2-2.2-8.5-3.2-12.8z"}}]})(props);
};
