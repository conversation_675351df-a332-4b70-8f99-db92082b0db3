{"version": 3, "file": "gen-mapping.umd.js", "sources": ["../src/sourcemap-segment.ts", "../src/gen-mapping.ts"], "sourcesContent": ["type GeneratedColumn = number;\ntype SourcesIndex = number;\ntype SourceLine = number;\ntype SourceColumn = number;\ntype NamesIndex = number;\n\nexport type SourceMapSegment =\n  | [GeneratedColumn]\n  | [GeneratedColumn, SourcesIndex, SourceLine, SourceColumn]\n  | [GeneratedColumn, SourcesIndex, SourceLine, SourceColumn, NamesIndex];\n\nexport const COLUMN = 0;\nexport const SOURCES_INDEX = 1;\nexport const SOURCE_LINE = 2;\nexport const SOURCE_COLUMN = 3;\nexport const NAMES_INDEX = 4;\n", "import { SetArray, put, remove } from '@jridgewell/set-array';\nimport { encode } from '@jridgewell/sourcemap-codec';\nimport { TraceMap, decodedMappings } from '@jridgewell/trace-mapping';\n\nimport {\n  COLUMN,\n  SOURCES_INDEX,\n  SOURCE_LINE,\n  SOURCE_COLUMN,\n  NAMES_INDEX,\n} from './sourcemap-segment';\n\nimport type { SourceMapInput } from '@jridgewell/trace-mapping';\nimport type { SourceMapSegment } from './sourcemap-segment';\nimport type { DecodedSourceMap, EncodedSourceMap, Pos, Mapping } from './types';\n\nexport type { DecodedSourceMap, EncodedSourceMap, Mapping };\n\nexport type Options = {\n  file?: string | null;\n  sourceRoot?: string | null;\n};\n\nconst NO_NAME = -1;\n\n/**\n * Provides the state to generate a sourcemap.\n */\nexport class GenMapping {\n  private declare _names: SetArray<string>;\n  private declare _sources: SetArray<string>;\n  private declare _sourcesContent: (string | null)[];\n  private declare _mappings: SourceMapSegment[][];\n  private declare _ignoreList: SetArray<number>;\n  declare file: string | null | undefined;\n  declare sourceRoot: string | null | undefined;\n\n  constructor({ file, sourceRoot }: Options = {}) {\n    this._names = new SetArray();\n    this._sources = new SetArray();\n    this._sourcesContent = [];\n    this._mappings = [];\n    this.file = file;\n    this.sourceRoot = sourceRoot;\n    this._ignoreList = new SetArray();\n  }\n}\n\ninterface PublicMap {\n  _names: GenMapping['_names'];\n  _sources: GenMapping['_sources'];\n  _sourcesContent: GenMapping['_sourcesContent'];\n  _mappings: GenMapping['_mappings'];\n  _ignoreList: GenMapping['_ignoreList'];\n}\n\n/**\n * Typescript doesn't allow friend access to private fields, so this just casts the map into a type\n * with public access modifiers.\n */\nfunction cast(map: unknown): PublicMap {\n  return map as any;\n}\n\n/**\n * A low-level API to associate a generated position with an original source position. Line and\n * column here are 0-based, unlike `addMapping`.\n */\nexport function addSegment(\n  map: GenMapping,\n  genLine: number,\n  genColumn: number,\n  source?: null,\n  sourceLine?: null,\n  sourceColumn?: null,\n  name?: null,\n  content?: null,\n): void;\nexport function addSegment(\n  map: GenMapping,\n  genLine: number,\n  genColumn: number,\n  source: string,\n  sourceLine: number,\n  sourceColumn: number,\n  name?: null,\n  content?: string | null,\n): void;\nexport function addSegment(\n  map: GenMapping,\n  genLine: number,\n  genColumn: number,\n  source: string,\n  sourceLine: number,\n  sourceColumn: number,\n  name: string,\n  content?: string | null,\n): void;\nexport function addSegment(\n  map: GenMapping,\n  genLine: number,\n  genColumn: number,\n  source?: string | null,\n  sourceLine?: number | null,\n  sourceColumn?: number | null,\n  name?: string | null,\n  content?: string | null,\n): void {\n  return addSegmentInternal(\n    false,\n    map,\n    genLine,\n    genColumn,\n    source,\n    sourceLine,\n    sourceColumn,\n    name,\n    content,\n  );\n}\n\n/**\n * A high-level API to associate a generated position with an original source position. Line is\n * 1-based, but column is 0-based, due to legacy behavior in `source-map` library.\n */\nexport function addMapping(\n  map: GenMapping,\n  mapping: {\n    generated: Pos;\n    source?: null;\n    original?: null;\n    name?: null;\n    content?: null;\n  },\n): void;\nexport function addMapping(\n  map: GenMapping,\n  mapping: {\n    generated: Pos;\n    source: string;\n    original: Pos;\n    name?: null;\n    content?: string | null;\n  },\n): void;\nexport function addMapping(\n  map: GenMapping,\n  mapping: {\n    generated: Pos;\n    source: string;\n    original: Pos;\n    name: string;\n    content?: string | null;\n  },\n): void;\nexport function addMapping(\n  map: GenMapping,\n  mapping: {\n    generated: Pos;\n    source?: string | null;\n    original?: Pos | null;\n    name?: string | null;\n    content?: string | null;\n  },\n): void {\n  return addMappingInternal(false, map, mapping as Parameters<typeof addMappingInternal>[2]);\n}\n\n/**\n * Same as `addSegment`, but will only add the segment if it generates useful information in the\n * resulting map. This only works correctly if segments are added **in order**, meaning you should\n * not add a segment with a lower generated line/column than one that came before.\n */\nexport const maybeAddSegment: typeof addSegment = (\n  map,\n  genLine,\n  genColumn,\n  source,\n  sourceLine,\n  sourceColumn,\n  name,\n  content,\n) => {\n  return addSegmentInternal(\n    true,\n    map,\n    genLine,\n    genColumn,\n    source,\n    sourceLine,\n    sourceColumn,\n    name,\n    content,\n  );\n};\n\n/**\n * Same as `addMapping`, but will only add the mapping if it generates useful information in the\n * resulting map. This only works correctly if mappings are added **in order**, meaning you should\n * not add a mapping with a lower generated line/column than one that came before.\n */\nexport const maybeAddMapping: typeof addMapping = (map, mapping) => {\n  return addMappingInternal(true, map, mapping as Parameters<typeof addMappingInternal>[2]);\n};\n\n/**\n * Adds/removes the content of the source file to the source map.\n */\nexport function setSourceContent(map: GenMapping, source: string, content: string | null): void {\n  const { _sources: sources, _sourcesContent: sourcesContent } = cast(map);\n  const index = put(sources, source);\n  sourcesContent[index] = content;\n}\n\nexport function setIgnore(map: GenMapping, source: string, ignore = true) {\n  const { _sources: sources, _sourcesContent: sourcesContent, _ignoreList: ignoreList } = cast(map);\n  const index = put(sources, source);\n  if (index === sourcesContent.length) sourcesContent[index] = null;\n  if (ignore) put(ignoreList, index);\n  else remove(ignoreList, index);\n}\n\n/**\n * Returns a sourcemap object (with decoded mappings) suitable for passing to a library that expects\n * a sourcemap, or to JSON.stringify.\n */\nexport function toDecodedMap(map: GenMapping): DecodedSourceMap {\n  const {\n    _mappings: mappings,\n    _sources: sources,\n    _sourcesContent: sourcesContent,\n    _names: names,\n    _ignoreList: ignoreList,\n  } = cast(map);\n  removeEmptyFinalLines(mappings);\n\n  return {\n    version: 3,\n    file: map.file || undefined,\n    names: names.array,\n    sourceRoot: map.sourceRoot || undefined,\n    sources: sources.array,\n    sourcesContent,\n    mappings,\n    ignoreList: ignoreList.array,\n  };\n}\n\n/**\n * Returns a sourcemap object (with encoded mappings) suitable for passing to a library that expects\n * a sourcemap, or to JSON.stringify.\n */\nexport function toEncodedMap(map: GenMapping): EncodedSourceMap {\n  const decoded = toDecodedMap(map);\n  return {\n    ...decoded,\n    mappings: encode(decoded.mappings as SourceMapSegment[][]),\n  };\n}\n\n/**\n * Constructs a new GenMapping, using the already present mappings of the input.\n */\nexport function fromMap(input: SourceMapInput): GenMapping {\n  const map = new TraceMap(input);\n  const gen = new GenMapping({ file: map.file, sourceRoot: map.sourceRoot });\n\n  putAll(cast(gen)._names, map.names);\n  putAll(cast(gen)._sources, map.sources as string[]);\n  cast(gen)._sourcesContent = map.sourcesContent || map.sources.map(() => null);\n  cast(gen)._mappings = decodedMappings(map) as GenMapping['_mappings'];\n  if (map.ignoreList) putAll(cast(gen)._ignoreList, map.ignoreList);\n\n  return gen;\n}\n\n/**\n * Returns an array of high-level mapping objects for every recorded segment, which could then be\n * passed to the `source-map` library.\n */\nexport function allMappings(map: GenMapping): Mapping[] {\n  const out: Mapping[] = [];\n  const { _mappings: mappings, _sources: sources, _names: names } = cast(map);\n\n  for (let i = 0; i < mappings.length; i++) {\n    const line = mappings[i];\n    for (let j = 0; j < line.length; j++) {\n      const seg = line[j];\n\n      const generated = { line: i + 1, column: seg[COLUMN] };\n      let source: string | undefined = undefined;\n      let original: Pos | undefined = undefined;\n      let name: string | undefined = undefined;\n\n      if (seg.length !== 1) {\n        source = sources.array[seg[SOURCES_INDEX]];\n        original = { line: seg[SOURCE_LINE] + 1, column: seg[SOURCE_COLUMN] };\n\n        if (seg.length === 5) name = names.array[seg[NAMES_INDEX]];\n      }\n\n      out.push({ generated, source, original, name } as Mapping);\n    }\n  }\n\n  return out;\n}\n\n// This split declaration is only so that terser can elminiate the static initialization block.\nfunction addSegmentInternal<S extends string | null | undefined>(\n  skipable: boolean,\n  map: GenMapping,\n  genLine: number,\n  genColumn: number,\n  source: S,\n  sourceLine: S extends string ? number : null | undefined,\n  sourceColumn: S extends string ? number : null | undefined,\n  name: S extends string ? string | null | undefined : null | undefined,\n  content: S extends string ? string | null | undefined : null | undefined,\n): void {\n  const {\n    _mappings: mappings,\n    _sources: sources,\n    _sourcesContent: sourcesContent,\n    _names: names,\n  } = cast(map);\n  const line = getLine(mappings, genLine);\n  const index = getColumnIndex(line, genColumn);\n\n  if (!source) {\n    if (skipable && skipSourceless(line, index)) return;\n    return insert(line, index, [genColumn]);\n  }\n\n  // Sigh, TypeScript can't figure out sourceLine and sourceColumn aren't nullish if source\n  // isn't nullish.\n  assert<number>(sourceLine);\n  assert<number>(sourceColumn);\n\n  const sourcesIndex = put(sources, source);\n  const namesIndex = name ? put(names, name) : NO_NAME;\n  if (sourcesIndex === sourcesContent.length) sourcesContent[sourcesIndex] = content ?? null;\n\n  if (skipable && skipSource(line, index, sourcesIndex, sourceLine, sourceColumn, namesIndex)) {\n    return;\n  }\n\n  return insert(\n    line,\n    index,\n    name\n      ? [genColumn, sourcesIndex, sourceLine, sourceColumn, namesIndex]\n      : [genColumn, sourcesIndex, sourceLine, sourceColumn],\n  );\n}\n\nfunction assert<T>(_val: unknown): asserts _val is T {\n  // noop.\n}\n\nfunction getLine(mappings: SourceMapSegment[][], index: number): SourceMapSegment[] {\n  for (let i = mappings.length; i <= index; i++) {\n    mappings[i] = [];\n  }\n  return mappings[index];\n}\n\nfunction getColumnIndex(line: SourceMapSegment[], genColumn: number): number {\n  let index = line.length;\n  for (let i = index - 1; i >= 0; index = i--) {\n    const current = line[i];\n    if (genColumn >= current[COLUMN]) break;\n  }\n  return index;\n}\n\nfunction insert<T>(array: T[], index: number, value: T) {\n  for (let i = array.length; i > index; i--) {\n    array[i] = array[i - 1];\n  }\n  array[index] = value;\n}\n\nfunction removeEmptyFinalLines(mappings: SourceMapSegment[][]) {\n  const { length } = mappings;\n  let len = length;\n  for (let i = len - 1; i >= 0; len = i, i--) {\n    if (mappings[i].length > 0) break;\n  }\n  if (len < length) mappings.length = len;\n}\n\nfunction putAll<T extends string | number>(setarr: SetArray<T>, array: T[]) {\n  for (let i = 0; i < array.length; i++) put(setarr, array[i]);\n}\n\nfunction skipSourceless(line: SourceMapSegment[], index: number): boolean {\n  // The start of a line is already sourceless, so adding a sourceless segment to the beginning\n  // doesn't generate any useful information.\n  if (index === 0) return true;\n\n  const prev = line[index - 1];\n  // If the previous segment is also sourceless, then adding another sourceless segment doesn't\n  // genrate any new information. Else, this segment will end the source/named segment and point to\n  // a sourceless position, which is useful.\n  return prev.length === 1;\n}\n\nfunction skipSource(\n  line: SourceMapSegment[],\n  index: number,\n  sourcesIndex: number,\n  sourceLine: number,\n  sourceColumn: number,\n  namesIndex: number,\n): boolean {\n  // A source/named segment at the start of a line gives position at that genColumn\n  if (index === 0) return false;\n\n  const prev = line[index - 1];\n\n  // If the previous segment is sourceless, then we're transitioning to a source.\n  if (prev.length === 1) return false;\n\n  // If the previous segment maps to the exact same source position, then this segment doesn't\n  // provide any new position information.\n  return (\n    sourcesIndex === prev[SOURCES_INDEX] &&\n    sourceLine === prev[SOURCE_LINE] &&\n    sourceColumn === prev[SOURCE_COLUMN] &&\n    namesIndex === (prev.length === 5 ? prev[NAMES_INDEX] : NO_NAME)\n  );\n}\n\nfunction addMappingInternal<S extends string | null | undefined>(\n  skipable: boolean,\n  map: GenMapping,\n  mapping: {\n    generated: Pos;\n    source: S;\n    original: S extends string ? Pos : null | undefined;\n    name: S extends string ? string | null | undefined : null | undefined;\n    content: S extends string ? string | null | undefined : null | undefined;\n  },\n) {\n  const { generated, source, original, name, content } = mapping;\n  if (!source) {\n    return addSegmentInternal(\n      skipable,\n      map,\n      generated.line - 1,\n      generated.column,\n      null,\n      null,\n      null,\n      null,\n      null,\n    );\n  }\n  assert<Pos>(original);\n  return addSegmentInternal(\n    skipable,\n    map,\n    generated.line - 1,\n    generated.column,\n    source as string,\n    original.line - 1,\n    original.column,\n    name,\n    content,\n  );\n}\n"], "names": ["SetArray", "put", "remove", "encode", "TraceMap", "decodedMappings"], "mappings": ";;;;;;IAWO,MAAM,MAAM,GAAG,CAAC,CAAC;IACjB,MAAM,aAAa,GAAG,CAAC,CAAC;IACxB,MAAM,WAAW,GAAG,CAAC,CAAC;IACtB,MAAM,aAAa,GAAG,CAAC,CAAC;IACxB,MAAM,WAAW,GAAG,CAAC;;ICQ5B,MAAM,OAAO,GAAG,CAAC,CAAC,CAAC;IAEnB;;IAEG;UACU,UAAU,CAAA;IASrB,IAAA,WAAA,CAAY,EAAE,IAAI,EAAE,UAAU,KAAc,EAAE,EAAA;IAC5C,QAAA,IAAI,CAAC,MAAM,GAAG,IAAIA,iBAAQ,EAAE,CAAC;IAC7B,QAAA,IAAI,CAAC,QAAQ,GAAG,IAAIA,iBAAQ,EAAE,CAAC;IAC/B,QAAA,IAAI,CAAC,eAAe,GAAG,EAAE,CAAC;IAC1B,QAAA,IAAI,CAAC,SAAS,GAAG,EAAE,CAAC;IACpB,QAAA,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC;IACjB,QAAA,IAAI,CAAC,UAAU,GAAG,UAAU,CAAC;IAC7B,QAAA,IAAI,CAAC,WAAW,GAAG,IAAIA,iBAAQ,EAAE,CAAC;SACnC;IACF,CAAA;IAUD;;;IAGG;IACH,SAAS,IAAI,CAAC,GAAY,EAAA;IACxB,IAAA,OAAO,GAAU,CAAC;IACpB,CAAC;aAoCe,UAAU,CACxB,GAAe,EACf,OAAe,EACf,SAAiB,EACjB,MAAsB,EACtB,UAA0B,EAC1B,YAA4B,EAC5B,IAAoB,EACpB,OAAuB,EAAA;QAEvB,OAAO,kBAAkB,CACvB,KAAK,EACL,GAAG,EACH,OAAO,EACP,SAAS,EACT,MAAM,EACN,UAAU,EACV,YAAY,EACZ,IAAI,EACJ,OAAO,CACR,CAAC;IACJ,CAAC;IAoCe,SAAA,UAAU,CACxB,GAAe,EACf,OAMC,EAAA;QAED,OAAO,kBAAkB,CAAC,KAAK,EAAE,GAAG,EAAE,OAAmD,CAAC,CAAC;IAC7F,CAAC;IAED;;;;IAIG;UACU,eAAe,GAAsB,CAChD,GAAG,EACH,OAAO,EACP,SAAS,EACT,MAAM,EACN,UAAU,EACV,YAAY,EACZ,IAAI,EACJ,OAAO,KACL;QACF,OAAO,kBAAkB,CACvB,IAAI,EACJ,GAAG,EACH,OAAO,EACP,SAAS,EACT,MAAM,EACN,UAAU,EACV,YAAY,EACZ,IAAI,EACJ,OAAO,CACR,CAAC;IACJ,EAAE;IAEF;;;;IAIG;UACU,eAAe,GAAsB,CAAC,GAAG,EAAE,OAAO,KAAI;QACjE,OAAO,kBAAkB,CAAC,IAAI,EAAE,GAAG,EAAE,OAAmD,CAAC,CAAC;IAC5F,EAAE;IAEF;;IAEG;aACa,gBAAgB,CAAC,GAAe,EAAE,MAAc,EAAE,OAAsB,EAAA;IACtF,IAAA,MAAM,EAAE,QAAQ,EAAE,OAAO,EAAE,eAAe,EAAE,cAAc,EAAE,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC;QACzE,MAAM,KAAK,GAAGC,YAAG,CAAC,OAAO,EAAE,MAAM,CAAC,CAAC;IACnC,IAAA,cAAc,CAAC,KAAK,CAAC,GAAG,OAAO,CAAC;IAClC,CAAC;IAEK,SAAU,SAAS,CAAC,GAAe,EAAE,MAAc,EAAE,MAAM,GAAG,IAAI,EAAA;IACtE,IAAA,MAAM,EAAE,QAAQ,EAAE,OAAO,EAAE,eAAe,EAAE,cAAc,EAAE,WAAW,EAAE,UAAU,EAAE,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC;QAClG,MAAM,KAAK,GAAGA,YAAG,CAAC,OAAO,EAAE,MAAM,CAAC,CAAC;IACnC,IAAA,IAAI,KAAK,KAAK,cAAc,CAAC,MAAM;IAAE,QAAA,cAAc,CAAC,KAAK,CAAC,GAAG,IAAI,CAAC;IAClE,IAAA,IAAI,MAAM;IAAE,QAAAA,YAAG,CAAC,UAAU,EAAE,KAAK,CAAC,CAAC;;IAC9B,QAAAC,eAAM,CAAC,UAAU,EAAE,KAAK,CAAC,CAAC;IACjC,CAAC;IAED;;;IAGG;IACG,SAAU,YAAY,CAAC,GAAe,EAAA;QAC1C,MAAM,EACJ,SAAS,EAAE,QAAQ,EACnB,QAAQ,EAAE,OAAO,EACjB,eAAe,EAAE,cAAc,EAC/B,MAAM,EAAE,KAAK,EACb,WAAW,EAAE,UAAU,GACxB,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC;QACd,qBAAqB,CAAC,QAAQ,CAAC,CAAC;QAEhC,OAAO;IACL,QAAA,OAAO,EAAE,CAAC;IACV,QAAA,IAAI,EAAE,GAAG,CAAC,IAAI,IAAI,SAAS;YAC3B,KAAK,EAAE,KAAK,CAAC,KAAK;IAClB,QAAA,UAAU,EAAE,GAAG,CAAC,UAAU,IAAI,SAAS;YACvC,OAAO,EAAE,OAAO,CAAC,KAAK;YACtB,cAAc;YACd,QAAQ;YACR,UAAU,EAAE,UAAU,CAAC,KAAK;SAC7B,CAAC;IACJ,CAAC;IAED;;;IAGG;IACG,SAAU,YAAY,CAAC,GAAe,EAAA;IAC1C,IAAA,MAAM,OAAO,GAAG,YAAY,CAAC,GAAG,CAAC,CAAC;QAClC,OACK,MAAA,CAAA,MAAA,CAAA,MAAA,CAAA,MAAA,CAAA,EAAA,EAAA,OAAO,CACV,EAAA,EAAA,QAAQ,EAAEC,qBAAM,CAAC,OAAO,CAAC,QAAgC,CAAC,EAC1D,CAAA,CAAA;IACJ,CAAC;IAED;;IAEG;IACG,SAAU,OAAO,CAAC,KAAqB,EAAA;IAC3C,IAAA,MAAM,GAAG,GAAG,IAAIC,qBAAQ,CAAC,KAAK,CAAC,CAAC;IAChC,IAAA,MAAM,GAAG,GAAG,IAAI,UAAU,CAAC,EAAE,IAAI,EAAE,GAAG,CAAC,IAAI,EAAE,UAAU,EAAE,GAAG,CAAC,UAAU,EAAE,CAAC,CAAC;IAE3E,IAAA,MAAM,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,MAAM,EAAE,GAAG,CAAC,KAAK,CAAC,CAAC;IACpC,IAAA,MAAM,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,QAAQ,EAAE,GAAG,CAAC,OAAmB,CAAC,CAAC;QACpD,IAAI,CAAC,GAAG,CAAC,CAAC,eAAe,GAAG,GAAG,CAAC,cAAc,IAAI,GAAG,CAAC,OAAO,CAAC,GAAG,CAAC,MAAM,IAAI,CAAC,CAAC;QAC9E,IAAI,CAAC,GAAG,CAAC,CAAC,SAAS,GAAGC,4BAAe,CAAC,GAAG,CAA4B,CAAC;QACtE,IAAI,GAAG,CAAC,UAAU;IAAE,QAAA,MAAM,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,WAAW,EAAE,GAAG,CAAC,UAAU,CAAC,CAAC;IAElE,IAAA,OAAO,GAAG,CAAC;IACb,CAAC;IAED;;;IAGG;IACG,SAAU,WAAW,CAAC,GAAe,EAAA;QACzC,MAAM,GAAG,GAAc,EAAE,CAAC;IAC1B,IAAA,MAAM,EAAE,SAAS,EAAE,QAAQ,EAAE,QAAQ,EAAE,OAAO,EAAE,MAAM,EAAE,KAAK,EAAE,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC;IAE5E,IAAA,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,QAAQ,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;IACxC,QAAA,MAAM,IAAI,GAAG,QAAQ,CAAC,CAAC,CAAC,CAAC;IACzB,QAAA,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;IACpC,YAAA,MAAM,GAAG,GAAG,IAAI,CAAC,CAAC,CAAC,CAAC;IAEpB,YAAA,MAAM,SAAS,GAAG,EAAE,IAAI,EAAE,CAAC,GAAG,CAAC,EAAE,MAAM,EAAE,GAAG,CAAC,MAAM,CAAC,EAAE,CAAC;gBACvD,IAAI,MAAM,GAAuB,SAAS,CAAC;gBAC3C,IAAI,QAAQ,GAAoB,SAAS,CAAC;gBAC1C,IAAI,IAAI,GAAuB,SAAS,CAAC;IAEzC,YAAA,IAAI,GAAG,CAAC,MAAM,KAAK,CAAC,EAAE;oBACpB,MAAM,GAAG,OAAO,CAAC,KAAK,CAAC,GAAG,CAAC,aAAa,CAAC,CAAC,CAAC;IAC3C,gBAAA,QAAQ,GAAG,EAAE,IAAI,EAAE,GAAG,CAAC,WAAW,CAAC,GAAG,CAAC,EAAE,MAAM,EAAE,GAAG,CAAC,aAAa,CAAC,EAAE,CAAC;IAEtE,gBAAA,IAAI,GAAG,CAAC,MAAM,KAAK,CAAC;wBAAE,IAAI,GAAG,KAAK,CAAC,KAAK,CAAC,GAAG,CAAC,WAAW,CAAC,CAAC,CAAC;IAC5D,aAAA;IAED,YAAA,GAAG,CAAC,IAAI,CAAC,EAAE,SAAS,EAAE,MAAM,EAAE,QAAQ,EAAE,IAAI,EAAa,CAAC,CAAC;IAC5D,SAAA;IACF,KAAA;IAED,IAAA,OAAO,GAAG,CAAC;IACb,CAAC;IAED;IACA,SAAS,kBAAkB,CACzB,QAAiB,EACjB,GAAe,EACf,OAAe,EACf,SAAiB,EACjB,MAAS,EACT,UAAwD,EACxD,YAA0D,EAC1D,IAAqE,EACrE,OAAwE,EAAA;QAExE,MAAM,EACJ,SAAS,EAAE,QAAQ,EACnB,QAAQ,EAAE,OAAO,EACjB,eAAe,EAAE,cAAc,EAC/B,MAAM,EAAE,KAAK,GACd,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC;QACd,MAAM,IAAI,GAAG,OAAO,CAAC,QAAQ,EAAE,OAAO,CAAC,CAAC;QACxC,MAAM,KAAK,GAAG,cAAc,CAAC,IAAI,EAAE,SAAS,CAAC,CAAC;QAE9C,IAAI,CAAC,MAAM,EAAE;IACX,QAAA,IAAI,QAAQ,IAAI,cAAc,CAAC,IAAI,EAAE,KAAK,CAAC;gBAAE,OAAO;YACpD,OAAO,MAAM,CAAC,IAAI,EAAE,KAAK,EAAE,CAAC,SAAS,CAAC,CAAC,CAAC;IACzC,KAAA;QAOD,MAAM,YAAY,GAAGJ,YAAG,CAAC,OAAO,EAAE,MAAM,CAAC,CAAC;IAC1C,IAAA,MAAM,UAAU,GAAG,IAAI,GAAGA,YAAG,CAAC,KAAK,EAAE,IAAI,CAAC,GAAG,OAAO,CAAC;IACrD,IAAA,IAAI,YAAY,KAAK,cAAc,CAAC,MAAM;YAAE,cAAc,CAAC,YAAY,CAAC,GAAG,OAAO,KAAP,IAAA,IAAA,OAAO,KAAP,KAAA,CAAA,GAAA,OAAO,GAAI,IAAI,CAAC;IAE3F,IAAA,IAAI,QAAQ,IAAI,UAAU,CAAC,IAAI,EAAE,KAAK,EAAE,YAAY,EAAE,UAAU,EAAE,YAAY,EAAE,UAAU,CAAC,EAAE;YAC3F,OAAO;IACR,KAAA;IAED,IAAA,OAAO,MAAM,CACX,IAAI,EACJ,KAAK,EACL,IAAI;cACA,CAAC,SAAS,EAAE,YAAY,EAAE,UAAU,EAAE,YAAY,EAAE,UAAU,CAAC;cAC/D,CAAC,SAAS,EAAE,YAAY,EAAE,UAAU,EAAE,YAAY,CAAC,CACxD,CAAC;IACJ,CAAC;IAMD,SAAS,OAAO,CAAC,QAA8B,EAAE,KAAa,EAAA;IAC5D,IAAA,KAAK,IAAI,CAAC,GAAG,QAAQ,CAAC,MAAM,EAAE,CAAC,IAAI,KAAK,EAAE,CAAC,EAAE,EAAE;IAC7C,QAAA,QAAQ,CAAC,CAAC,CAAC,GAAG,EAAE,CAAC;IAClB,KAAA;IACD,IAAA,OAAO,QAAQ,CAAC,KAAK,CAAC,CAAC;IACzB,CAAC;IAED,SAAS,cAAc,CAAC,IAAwB,EAAE,SAAiB,EAAA;IACjE,IAAA,IAAI,KAAK,GAAG,IAAI,CAAC,MAAM,CAAC;IACxB,IAAA,KAAK,IAAI,CAAC,GAAG,KAAK,GAAG,CAAC,EAAE,CAAC,IAAI,CAAC,EAAE,KAAK,GAAG,CAAC,EAAE,EAAE;IAC3C,QAAA,MAAM,OAAO,GAAG,IAAI,CAAC,CAAC,CAAC,CAAC;IACxB,QAAA,IAAI,SAAS,IAAI,OAAO,CAAC,MAAM,CAAC;gBAAE,MAAM;IACzC,KAAA;IACD,IAAA,OAAO,KAAK,CAAC;IACf,CAAC;IAED,SAAS,MAAM,CAAI,KAAU,EAAE,KAAa,EAAE,KAAQ,EAAA;IACpD,IAAA,KAAK,IAAI,CAAC,GAAG,KAAK,CAAC,MAAM,EAAE,CAAC,GAAG,KAAK,EAAE,CAAC,EAAE,EAAE;YACzC,KAAK,CAAC,CAAC,CAAC,GAAG,KAAK,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC;IACzB,KAAA;IACD,IAAA,KAAK,CAAC,KAAK,CAAC,GAAG,KAAK,CAAC;IACvB,CAAC;IAED,SAAS,qBAAqB,CAAC,QAA8B,EAAA;IAC3D,IAAA,MAAM,EAAE,MAAM,EAAE,GAAG,QAAQ,CAAC;QAC5B,IAAI,GAAG,GAAG,MAAM,CAAC;IACjB,IAAA,KAAK,IAAI,CAAC,GAAG,GAAG,GAAG,CAAC,EAAE,CAAC,IAAI,CAAC,EAAE,GAAG,GAAG,CAAC,EAAE,CAAC,EAAE,EAAE;IAC1C,QAAA,IAAI,QAAQ,CAAC,CAAC,CAAC,CAAC,MAAM,GAAG,CAAC;gBAAE,MAAM;IACnC,KAAA;QACD,IAAI,GAAG,GAAG,MAAM;IAAE,QAAA,QAAQ,CAAC,MAAM,GAAG,GAAG,CAAC;IAC1C,CAAC;IAED,SAAS,MAAM,CAA4B,MAAmB,EAAE,KAAU,EAAA;IACxE,IAAA,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,KAAK,CAAC,MAAM,EAAE,CAAC,EAAE;YAAEA,YAAG,CAAC,MAAM,EAAE,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC;IAC/D,CAAC;IAED,SAAS,cAAc,CAAC,IAAwB,EAAE,KAAa,EAAA;;;QAG7D,IAAI,KAAK,KAAK,CAAC;IAAE,QAAA,OAAO,IAAI,CAAC;QAE7B,MAAM,IAAI,GAAG,IAAI,CAAC,KAAK,GAAG,CAAC,CAAC,CAAC;;;;IAI7B,IAAA,OAAO,IAAI,CAAC,MAAM,KAAK,CAAC,CAAC;IAC3B,CAAC;IAED,SAAS,UAAU,CACjB,IAAwB,EACxB,KAAa,EACb,YAAoB,EACpB,UAAkB,EAClB,YAAoB,EACpB,UAAkB,EAAA;;QAGlB,IAAI,KAAK,KAAK,CAAC;IAAE,QAAA,OAAO,KAAK,CAAC;QAE9B,MAAM,IAAI,GAAG,IAAI,CAAC,KAAK,GAAG,CAAC,CAAC,CAAC;;IAG7B,IAAA,IAAI,IAAI,CAAC,MAAM,KAAK,CAAC;IAAE,QAAA,OAAO,KAAK,CAAC;;;IAIpC,IAAA,QACE,YAAY,KAAK,IAAI,CAAC,aAAa,CAAC;IACpC,QAAA,UAAU,KAAK,IAAI,CAAC,WAAW,CAAC;IAChC,QAAA,YAAY,KAAK,IAAI,CAAC,aAAa,CAAC;YACpC,UAAU,MAAM,IAAI,CAAC,MAAM,KAAK,CAAC,GAAG,IAAI,CAAC,WAAW,CAAC,GAAG,OAAO,CAAC,EAChE;IACJ,CAAC;IAED,SAAS,kBAAkB,CACzB,QAAiB,EACjB,GAAe,EACf,OAMC,EAAA;IAED,IAAA,MAAM,EAAE,SAAS,EAAE,MAAM,EAAE,QAAQ,EAAE,IAAI,EAAE,OAAO,EAAE,GAAG,OAAO,CAAC;QAC/D,IAAI,CAAC,MAAM,EAAE;YACX,OAAO,kBAAkB,CACvB,QAAQ,EACR,GAAG,EACH,SAAS,CAAC,IAAI,GAAG,CAAC,EAClB,SAAS,CAAC,MAAM,EAChB,IAAI,EACJ,IAAI,EACJ,IAAI,EACJ,IAAI,EACJ,IAAI,CACL,CAAC;IACH,KAAA;IAED,IAAA,OAAO,kBAAkB,CACvB,QAAQ,EACR,GAAG,EACH,SAAS,CAAC,IAAI,GAAG,CAAC,EAClB,SAAS,CAAC,MAAM,EAChB,MAAgB,EAChB,QAAQ,CAAC,IAAI,GAAG,CAAC,EACjB,QAAQ,CAAC,MAAM,EACf,IAAI,EACJ,OAAO,CACR,CAAC;IACJ;;;;;;;;;;;;;;;;;;;;"}