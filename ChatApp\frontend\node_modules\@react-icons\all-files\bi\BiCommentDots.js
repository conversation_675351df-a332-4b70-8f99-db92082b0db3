// THIS FILE IS AUTO GENERATED
var GenIcon = require('../lib').GenIcon
module.exports.BiCommentDots = function BiCommentDots (props) {
  return GenIcon({"tag":"svg","attr":{"viewBox":"0 0 24 24"},"child":[{"tag":"path","attr":{"d":"M20,2H4C2.897,2,2,2.897,2,4v18l5.333-4H20c1.103,0,2-0.897,2-2V4C22,2.897,21.103,2,20,2z M20,16H6.667L4,18V4h16V16z"}},{"tag":"circle","attr":{"cx":"15","cy":"10","r":"2"}},{"tag":"circle","attr":{"cx":"9","cy":"10","r":"2"}}]})(props);
};
