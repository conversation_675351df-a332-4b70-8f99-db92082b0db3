// THIS FILE IS AUTO GENERATED
var GenIcon = require('../lib').GenIcon
module.exports.BiSidebar = function BiSidebar (props) {
  return GenIcon({"tag":"svg","attr":{"viewBox":"0 0 24 24"},"child":[{"tag":"path","attr":{"d":"M20,3H4C2.897,3,2,3.897,2,5v14c0,1.103,0.897,2,2,2h6h2h8c1.103,0,2-0.897,2-2V5C22,3.897,21.103,3,20,3z M4,19V7h6v12H4z M12,19V7h8V5l0.002,14H12z"}},{"tag":"path","attr":{"d":"M6 10H8V12H6zM6 14H8V16H6z"}}]})(props);
};
