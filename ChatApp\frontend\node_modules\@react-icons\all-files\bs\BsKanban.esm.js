// THIS FILE IS AUTO GENERATED
import { GenIcon } from '../lib';
export function BsKanban (props) {
  return GenIcon({"tag":"svg","attr":{"viewBox":"0 0 16 16","fill":"currentColor"},"child":[{"tag":"path","attr":{"fillRule":"evenodd","d":"M13.5 1h-11a1 1 0 00-1 1v12a1 1 0 001 1h11a1 1 0 001-1V2a1 1 0 00-1-1zm-11-1a2 2 0 00-2 2v12a2 2 0 002 2h11a2 2 0 002-2V2a2 2 0 00-2-2h-11z","clipRule":"evenodd"}},{"tag":"rect","attr":{"width":"3","height":"5","x":"6.5","y":"2","rx":"1"}},{"tag":"rect","attr":{"width":"3","height":"9","x":"2.5","y":"2","rx":"1"}},{"tag":"rect","attr":{"width":"3","height":"12","x":"10.5","y":"2","rx":"1"}}]})(props);
};
