// THIS FILE IS AUTO GENERATED
var GenIcon = require('../lib').GenIcon
module.exports.GiBlackcurrant = function GiBlackcurrant (props) {
  return GenIcon({"tag":"svg","attr":{"viewBox":"0 0 512 512"},"child":[{"tag":"path","attr":{"d":"M21.594 18.563V58.53c7.485 2.273 14.984 4.845 22.468 7.69L68.5 114.874c5.683 26.117 11.908 54.147 17.813 79.97-21.55 8.027-33.854 31.305-27.782 53.968 3.206 11.96 10.9 21.496 20.814 27.28 7.937 10.297 21.637 15.43 34.906 11.876 11.538-3.093 20.058-12.052 23.22-22.814.002-.01-.004-.02 0-.03 8.537-10.803 12.055-25.367 8.218-39.688-5.156-19.24-21.998-32.203-40.844-33.344-5.366-23.444-10.99-48.83-16.28-73.03l-1.22-33.408c30.25 15.585 60.09 35.02 88.875 56.875l4.343 55.69c-6.83 35.67-10.075 73.427-10.407 113.093-21.993 3.015-38.937 21.866-38.937 44.687 0 9.47 2.92 18.242 7.905 25.5.99 17.338 15.524 31.28 33.094 31.28 13.61 0 25.384-8.357 30.468-20.186 11.36-8.193 18.78-21.517 18.78-36.594 0-20.566-13.775-37.908-32.593-43.344.313-45.45 4.485-88.007 13.5-127.406l9.47-14.03c21.597 18.362 42.378 37.815 62.06 57.655 11.742 14.26 23.233 28.653 34.314 43.28l.717 27.845c-13.258 26.784-22.87 56.604-29.812 88.97-.136-.002-.27 0-.406 0-24.92 0-45.126 20.204-45.126 45.124 0 10.433 3.554 20.045 9.5 27.687 3.22 14.917 16.58 26.22 32.406 26.22 12.197 0 22.93-6.717 28.688-16.625 11.876-8.127 19.656-21.804 19.656-37.28 0-18.263-10.862-33.994-26.47-41.095 7.347-34.23 17.694-65.003 32-91.844 24.256 34.71 45.723 71.097 62.407 110.563-9.073 8.254-14.78 20.166-14.78 33.405 0 24.918 20.174 45.125 45.094 45.125 6.448 0 12.577-1.344 18.125-3.78.57.03 1.138.03 1.717.03 18.203 0 33.157-14.955 33.157-33.156 0-9.67-4.22-18.42-10.906-24.5C457.652 411.98 441.267 400 422.094 400c-4.895 0-9.588.808-14 2.25-27.012-63.12-65.47-118.074-107.406-170.156-.865-1.34-1.753-2.665-2.625-4l11.28-.22c35.023 11.645 69.823 29.045 104.782 51.22-7.48 8.053-12.063 18.83-12.063 30.687 0 19.23 12.026 35.63 28.97 42.126 6.072 8.022 15.686 13.25 26.437 13.25 18.2 0 33.155-14.955 33.155-33.156 0-2.52-.3-4.98-.844-7.344 1.63-4.662 2.5-9.657 2.5-14.875 0-24.917-20.173-45.124-45.092-45.124-5.735 0-11.204 1.08-16.25 3.03-40.628-26.45-81.445-47.002-123.094-59.905l-35.625-17.31c-22.56-31.125-47.177-60.864-74.283-88.907l33.125-3.594c36.963 3.423 73.42 8.288 108.875 17.124-2.533 5.64-3.937 11.886-3.937 18.47 0 21.402 14.91 39.306 34.906 43.936 5.554 4.192 12.44 6.688 19.875 6.688 18.203 0 33.19-14.955 33.19-33.157 0-.968-.074-1.924-.158-2.874 1.57-4.584 2.438-9.478 2.438-14.594 0-24.917-20.205-45.125-45.125-45.125-10.99 0-21.048 3.938-28.875 10.47-42.296-11.308-85.51-16.73-128.75-20.438l-65.813-15.657c-17.245-15.39-35.4-30.16-54.562-44.25h-81.53zm369.187 118c8.103 0 14.47 6.367 14.47 14.468 0 8.102-6.367 14.47-14.47 14.47-8.1 0-14.467-6.368-14.467-14.47 0-8.1 6.366-14.468 14.468-14.468zM106.095 241.438c6.262.14 11.842 4.33 13.562 10.75 2.097 7.824-2.424 15.62-10.25 17.718-7.826 2.097-15.622-2.394-17.72-10.22-2.095-7.825 2.425-15.62 10.25-17.717.98-.263 1.94-.44 2.908-.5.422-.028.832-.042 1.25-.032zM457.47 317.53c8.1 0 14.467 6.37 14.467 14.47 0 8.1-6.366 14.47-14.468 14.47-8.103 0-14.47-6.37-14.47-14.47 0-8.102 6.367-14.47 14.47-14.47zm-285.25 47.626c8.1 0 14.468 6.368 14.468 14.47 0 8.1-6.367 14.468-14.47 14.468-8.1 0-14.468-6.368-14.468-14.47 0-8.1 6.367-14.468 14.47-14.468zm269.717 73.72c8.102 0 14.47 6.367 14.47 14.468 0 8.1-6.367 14.47-14.47 14.47-8.102-.002-14.468-6.37-14.468-14.47 0-8.102 6.365-14.47 14.467-14.47zm-166.437 1.5c8.102 0 14.47 6.367 14.47 14.468 0 8.1-6.368 14.47-14.47 14.47-8.102-.002-14.47-6.37-14.47-14.47 0-8.102 6.368-14.47 14.47-14.47z"}}]})(props);
};
