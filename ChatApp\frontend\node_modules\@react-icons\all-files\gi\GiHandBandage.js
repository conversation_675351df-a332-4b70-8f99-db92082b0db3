// THIS FILE IS AUTO GENERATED
var GenIcon = require('../lib').GenIcon
module.exports.GiHandBandage = function GiHandBandage (props) {
  return GenIcon({"tag":"svg","attr":{"viewBox":"0 0 512 512"},"child":[{"tag":"path","attr":{"d":"M238 20c-8.6 1.31-16.5 5.4-20.7 9.7l26.1 158.7-17.6 5.6-73-148c-5.9-.8-14.3.61-22.2 4.81-8.1 4.3-14.4 11-17.2 17.39l69.1 142.9-15.2 10.5-94.55-100.2c-5.8 1.2-13.1 5.5-19.1 12.1-6.2 7-10.2 15.8-11 22.8l96.05 103.6c69.9-31.4 140.4-54.2 210.9-76.1l10.4-47.2c-8.5.6-16.6-.6-24.1-3.2-7.2-2.5-13.9-6.2-20.2-10.8l-11.7 53-18.3-.5-24.2-151.39c-8.2-4.02-15.2-4.5-23.5-3.71zm94.4 11.13l-16.1 68.34c8.4 8.03 16.9 13.93 25.5 16.93 8.6 3 17.5 3.3 27.8 0l16.9-69.73v-.11c-3.7-14.58-42.4-21.77-54.1-15.43zM438.2 196.1c-5.7.1-12.1 1.4-19.1 3.9-19 6.6-41.6 21.3-64.6 35.5l-.5-.4c-59.7 34.3-118.1 69.2-169.8 110.4v2.1c21.6 2.9 51.6 11.2 80.3 20.6 23 7.6 44.6 15.6 59.9 22.6-2.3-22-2.6-42.5-1.6-60.9-8.7 4.2-17 7.6-25.3 9.9-18.1 5.1-36.5 4.7-52.7-5.1l9.7-16c11.2 6.8 23.1 7.3 38 3.1 14.9-4.2 32.3-13.5 51.4-25.6 35.9-22.7 77.7-54.9 125.5-78.2-5.9-11.3-12.3-17.2-19.1-19.9-3.6-1.4-7.7-2.1-12.1-2zm-93.1 7.9c-43.3 13.6-86.5 27.6-129.3 43.7 16.3 4.3 32.8 11 45.4 21.7 26.2-16.5 53.1-32.3 80.1-47.8 1.5-6 2.5-11.6 3.8-17.6zm-160.7 56c-11 4.5-21.9 9.1-32.8 13.9l15.7 16.9.2.1c8.1 8.4 12.8 19.6 15.1 33 20.1-15.5 41-30.1 62.5-44.1-16.1-10.7-41.3-17.2-60.7-19.8zm-53.2 51.1c-4.7-.1-12.6 2-21 5.7-9.1 4.1-18.98 10-28.03 16-4.74 3.1-9.18 6.3-13.25 9.2 12.94.6 26.58 1.7 40.38 4.9 19.1 4.5 37.9 13.4 51 29.9 8.5-17.2 8.2-29.5 1.2-41-9.5-12.9-22.7-24.2-29.4-24.7zm52.1 54.6c-1.1 13.4-3.4 27.9-5.6 39.5 61.2 9.5 110.1 34.1 160.2 55.3-4.7-16.7-8.2-32.8-10.6-48.3-11.6-6.2-39.4-17.3-68.4-26.9-27.4-9-56.6-16.8-75.6-19.6zm-21.2 29.7c-18 9.3-39.4 17.1-69.54 17.8-6.97 17.7-14.01 37.1-31.64 55.6 22.6.7 37.95-3.1 51.38-11 16.5-9.7 27.2-27 46.9-51.4zm11.9 27.4c-3.5 15.6-7.9 32.1-12.8 49.3 20.7-13.2 44.9-27.1 71.9-34.8-18.7-6.2-38.2-11.3-59.1-14.5zm92.1 26.7c-38.4 2.9-73.6 23.8-103.1 42.9h185.3c-1.2-3.2-2.3-6.4-3.4-9.6-26.9-10.8-52.5-22.7-78.8-33.3z"}}]})(props);
};
