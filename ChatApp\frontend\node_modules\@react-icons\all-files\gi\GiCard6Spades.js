// THIS FILE IS AUTO GENERATED
var GenIcon = require('../lib').GenIcon
module.exports.GiCard6Spades = function GiCard6Spades (props) {
  return GenIcon({"tag":"svg","attr":{"viewBox":"0 0 512 512"},"child":[{"tag":"path","attr":{"d":"M119.436 36c-16.126 0-29.2 17.237-29.2 38.5v363c0 21.263 13.074 38.5 29.2 38.5h275.298c16.126 0 29.198-17.237 29.198-38.5v-363c0-21.263-13.072-38.5-29.198-38.5H119.436zm26.652 8.047s46.338 33.838 47.271 63.068c.776 24.287-25.024 32.122-40.775 18.586l13.633 32.653H126.1l13.613-32.635c-15.535 13.88-40.004 5.349-40.756-18.604-.88-28.01 47.13-63.068 47.13-63.068zm112.644 132.488c11.57 0 21.398 3.17 29.48 9.51 6.737 5.151 10.106 11.333 10.106 18.545 0 4.834-1.506 8.796-4.517 11.887-2.932 3.09-6.54 4.634-10.819 4.634-3.962 0-7.37-1.306-10.222-3.922-2.774-2.615-4.16-5.705-4.16-9.271 0-2.377.951-5.073 2.853-8.084 1.744-2.774 2.615-4.952 2.615-6.537 0-2.22-1.071-4.042-3.21-5.469-2.854-1.823-6.775-2.734-11.768-2.734-7.687 0-14.701 2.418-21.041 7.252s-11.728 13.155-16.166 24.963c-4.359 11.728-6.537 24.923-6.537 39.584 0 2.14.117 5.35.355 9.628 6.181-8.241 12.681-14.146 19.496-17.712 6.816-3.567 14.304-5.348 22.467-5.348 13.155 0 24.17 4.477 33.045 13.432 8.955 8.875 13.434 20.129 13.434 33.76 0 16.404-5.271 30.035-15.811 40.892-10.54 10.857-23.577 16.285-39.11 16.285-10.936 0-20.683-2.893-29.242-8.678-8.558-5.864-15.57-14.977-21.039-27.34-5.389-12.362-8.084-26.785-8.084-43.269 0-17.83 3.328-33.998 9.985-48.5 6.657-14.502 15.137-25.36 25.44-32.572 10.38-7.291 21.197-10.936 32.45-10.936zm-7.607 78.574c-9.906 0-18.03 3.607-24.37 10.819-6.26 7.132-9.39 17.196-9.39 30.193 0 14.106 3.052 24.883 9.155 32.332 6.102 7.45 13.868 11.174 23.298 11.174 9.352 0 16.88-3.525 22.586-10.578 5.706-7.053 8.559-17.594 8.559-31.621 0-14.899-2.655-25.675-7.965-32.332-5.23-6.657-12.522-9.987-21.873-9.987zm94.652 98.54h40.118L372.262 386.3c15.75-13.536 41.551-5.701 40.775 18.586-.933 29.23-47.271 63.068-47.271 63.068s-48.012-35.059-47.133-63.068c.751-23.953 25.222-32.485 40.758-18.604l-13.614-32.635z"}}]})(props);
};
