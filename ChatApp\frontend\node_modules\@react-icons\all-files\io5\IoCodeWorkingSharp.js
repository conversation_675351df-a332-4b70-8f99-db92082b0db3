// THIS FILE IS AUTO GENERATED
var GenIcon = require('../lib').GenIcon
module.exports.IoCodeWorkingSharp = function IoCodeWorkingSharp (props) {
  return GenIcon({"tag":"svg","attr":{"viewBox":"0 0 512 512"},"child":[{"tag":"circle","attr":{"cx":"256","cy":"256","r":"26","strokeLinecap":"square","strokeMiterlimit":"10","strokeWidth":"10"}},{"tag":"circle","attr":{"cx":"346","cy":"256","r":"26","strokeLinecap":"square","strokeMiterlimit":"10","strokeWidth":"10"}},{"tag":"circle","attr":{"cx":"166","cy":"256","r":"26","strokeLinecap":"square","strokeMiterlimit":"10","strokeWidth":"10"}},{"tag":"path","attr":{"fill":"none","strokeLinecap":"square","strokeMiterlimit":"10","strokeWidth":"42","d":"M160 368L32 256l128-112m192 224l128-112-128-112"}}]})(props);
};
