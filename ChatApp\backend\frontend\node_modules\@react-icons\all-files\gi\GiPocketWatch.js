// THIS FILE IS AUTO GENERATED
var GenIcon = require('../lib').GenIcon
module.exports.GiPocketWatch = function GiPocketWatch (props) {
  return GenIcon({"tag":"svg","attr":{"viewBox":"0 0 512 512"},"child":[{"tag":"path","attr":{"d":"M254.84 255.64a127.867 127.867 0 1 0 61.614 76.03 127.04 127.04 0 0 0-61.615-76.03zm-31.233 212.62a103.9 103.9 0 0 1-79.58-8.33 104.52 104.52 0 1 1 79.58 8.33zm54.65-125.248a87.963 87.963 0 1 0-59.328 109.373 87.414 87.414 0 0 0 59.327-109.373zm-37.1 38.962a8.28 8.28 0 0 1-9.56 6.747l-37.256-6.436a8.362 8.362 0 0 1-.827-.186h-.124a8.28 8.28 0 0 1-.88-.32l-.24-.104a8.175 8.175 0 0 1-.9-.487h-.07l-59.174-37.244a8.28 8.28 0 0 1 8.817-14.012l57.755 36.324 35.67 6.158a8.28 8.28 0 0 1 6.76 9.562zm164.75-76.838c10.09 34.016 15.046 67.203 13.94 93.478-1.12 26.69-8.156 38.176-14.365 40.018-6.21 1.842-18.368-3.943-33.87-25.706-15.244-21.42-29.194-51.95-39.325-85.955-22.53-75.947-14.933-128.942.424-133.495 15.36-4.553 50.668 35.703 73.197 111.66zm-25.8-95.517a136.808 136.808 0 0 0-12.914-15.772q13.712 4.625 26.42 16.65c19.124 18.13 35.33 48.71 44.498 83.886 8.963 34.512 10.35 69.884 3.85 97.06a99.894 99.894 0 0 1-6.405 18.523c.362-3.322.632-6.86.797-10.67 1.17-28.044-4.015-63.126-14.612-98.88-10.597-35.754-25.354-67.98-41.632-90.798zM357.104 16.1h-16.868v.125a8.765 8.765 0 0 1-.518 6.737 8.765 8.765 0 0 1-5.174 4.398 8.838 8.838 0 0 1-11.125-5.65l-1.748-5.61h-17.417l3.508 10.743c.094.29.218.56.322.838a25.168 25.168 0 0 0-11.456 10.35l-11.383 19.662a25.302 25.302 0 0 0-3.322 10.576 25.457 25.457 0 0 0-7.864 1.263l-19.662 6.437a25.21 25.21 0 0 0-7.358-11.59l-16.92-15.16a25.2 25.2 0 0 0-18.327-6.458 25.758 25.758 0 0 0-2.992.34 25.426 25.426 0 0 0-24.464-18.627H161.64a25.416 25.416 0 0 0-25.096 21.64 25.56 25.56 0 0 0-5.38.175 25.22 25.22 0 0 0-16.755 9.84l-13.692 18.11a25.416 25.416 0 0 0 3.177 34.08 25.364 25.364 0 0 0-4.036 22.435l6.456 21.732a25.24 25.24 0 0 0 9.17 13.11 32.267 32.267 0 0 0 25.933 51.496h1.19l4.273 14.395a151.463 151.463 0 0 1 5.91-1.904q4.738-1.406 9.51-2.472l-4.316-14.56a32.267 32.267 0 0 0-3.912-57.394 25.27 25.27 0 0 0 .962-17.075l-6.457-21.733a25.24 25.24 0 0 0-7.244-11.55l13.63-18.036a25.23 25.23 0 0 0 4.842-11.517c.62 0 1.252.072 1.883.072h22.706a25.406 25.406 0 0 0 4.346-.382 25.21 25.21 0 0 0 7.513 12.138l16.807 15.12a25.28 25.28 0 0 0 16.94 6.48 25.57 25.57 0 0 0 6.002-.726c.062.228.124.445.197.673a25.426 25.426 0 0 0 24.103 17.51 25.478 25.478 0 0 0 7.906-1.273l21.576-7.047a25.416 25.416 0 0 0 17.447-22.022 25.52 25.52 0 0 0 6.56-.88 25.21 25.21 0 0 0 15.41-11.828l11.384-19.662a25.333 25.333 0 0 0 1.83-21.37A25.437 25.437 0 0 0 357.104 16.1zM122.19 147.808l-6.457-21.732a8.745 8.745 0 0 1 7.027-11.207c.414 0 .828-.063 1.242-.114h.186a8.838 8.838 0 0 1 8.465 6.312l6.458 21.732a8.817 8.817 0 0 1-4.987 10.618c-.63.145-1.263.31-1.894.497l-.415.134a8.838 8.838 0 0 1-9.603-6.24zm19.23 21.308a15.875 15.875 0 1 1-5.465 1.035 25.94 25.94 0 0 0 1.914-.487l.796-.26a15.668 15.668 0 0 1 2.773-.288zm.237-92.382l-13.64 18.12a8.776 8.776 0 0 1-5.474 3.353c-.497 0-1.035.083-1.5.145a8.817 8.817 0 0 1-7.11-14.124l13.69-18.11a8.827 8.827 0 1 1 14.034 10.617zm50.594-23.036q-.61.89-1.148 1.81a8.807 8.807 0 0 1-6.8 3.21H161.64a8.827 8.827 0 1 1 0-17.645h22.704a8.807 8.807 0 0 1 7.948 12.625zm44.334 35.496a8.817 8.817 0 0 1-12.418.673l-16.89-15.16a8.827 8.827 0 0 1-2.07-10.35q.57-.828 1.036-1.697c.103-.125.196-.26.31-.384a8.838 8.838 0 0 1 12.42-.672l16.898 15.17a8.817 8.817 0 0 1 .766 12.42zm53.202 8.486a8.765 8.765 0 0 1-5.175 4.397l-21.493 7.058a8.827 8.827 0 1 1-5.484-16.775l21.587-7.048a8.827 8.827 0 0 1 10.607 12.377zm36.416-42.513L314.82 74.83A8.827 8.827 0 0 1 299.533 66l11.383-19.662a8.843 8.843 0 1 1 15.327 8.827z"}}]})(props);
};
