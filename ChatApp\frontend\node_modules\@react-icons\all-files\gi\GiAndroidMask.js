// THIS FILE IS AUTO GENERATED
var GenIcon = require('../lib').GenIcon
module.exports.GiAndroidMask = function GiAndroidMask (props) {
  return GenIcon({"tag":"svg","attr":{"viewBox":"0 0 512 512"},"child":[{"tag":"path","attr":{"d":"M265.344 18.563v302.863h-18.69V18.59c-61.403 3.005-115.44 34.412-149.238 81.336l35.022 60.658c3.862-1.037 7.92-1.598 12.11-1.598 25.747 0 46.62 20.872 46.62 46.62 0 22.556-16.02 41.37-37.3 45.687v174.084c13.712 21.43 27.6 41.98 40.712 60.754V369.01h122.838v117.117c13.11-18.773 27-39.324 40.713-60.754V251.294c-21.282-4.317-37.302-23.13-37.302-45.688 0-25.747 20.873-46.62 46.62-46.62 4.19 0 8.25.562 12.113 1.6l35.13-60.847c-33.74-47-87.758-78.375-149.346-81.175zM426.03 117.47l-29.74 51.52c10.824 8.537 17.782 21.76 17.782 36.615 0 22.54-15.995 41.343-37.254 45.68V395.38c9.833-16.237 19.31-32.676 28.014-48.927 27.3-50.976 46.564-100.37 46.564-132.36 0-35.127-9.22-68.104-25.365-96.622zm-339.973.157c-16.196 28.486-25.46 61.408-25.46 96.465 0 31.992 19.267 81.385 46.567 132.36 8.704 16.253 18.182 32.694 28.016 48.933v-144.1c-21.26-4.336-37.254-23.14-37.254-45.68 0-14.856 6.957-28.08 17.783-36.617l-29.653-51.36zm127.213 270.07V488.98h85.46V387.696h-85.46z"}}]})(props);
};
