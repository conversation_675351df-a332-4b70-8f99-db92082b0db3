// THIS FILE IS AUTO GENERATED
import { GenIcon } from '../lib';
export function GiFishEscape (props) {
  return GenIcon({"tag":"svg","attr":{"viewBox":"0 0 512 512"},"child":[{"tag":"path","attr":{"d":"M467.2 29.51c-.9-.02-1.7-.02-2.6 0-25.5.59-76.1 16.5-94.7 27.23-25.2 14.39-64.9 65.26-78 86.66-19.1-5.2-52.7 13.6-67.5 36 20.3-2.1 68.6-6.1 50.9 33.7 34.9-15.5 20.6-41.9 27.4-51.1 15.2-20.4 112-29.6 125.9-41.7 29.8-24.3 54.3-81.49 50.2-87.23-1.7-2.35-5.9-3.44-11.6-3.56zM269 41.17C217 54.45 111.5 98.91 77.29 152.1l23.11 13.1C134.4 116 220.2 71.19 269 41.17zm158.8 4.64c3.9-.12 7.8 1.9 9.8 5.54 3.1 5.3 1.3 12.08-4 15.08-5.3 3.1-12.1 1.2-15.1-4.1-3.1-5.28-1.2-12.08 4.1-14.98 1.6-.98 3.4-1.48 5.2-1.54zM237.1 103.3C188.3 127 77.43 218.7 62.99 290.3l39.41-2c14.7-72.2 86.5-149.3 134.7-185zM17 310v18h287c-3.2 7.7-8.9 15-11 22-2 7-2 17 5 23 30.2 31.8 51.7 75.5 58.6 122h18.2c-7.1-51.3-30.8-99.2-64.8-134-1-2-1-2 0-5 1-4 5-9 8-14s7-10 6-18c0-5-4-10-7-12-4-2-8-2-13-2H17zm45 80c-14.1.7-29.68 2.4-45 4.4v18.3c22.83-3 46.65-5.5 62-4.7 41 2 81 29 128 32 24 1 56-3 82-7 10-2 19-3 27-5-3.8-5-5.2-12.2-9-16-6 1-13 2-20 3-27 4-59 8-79 7-40-2-80-29-128-32H62z"}}]})(props);
};
