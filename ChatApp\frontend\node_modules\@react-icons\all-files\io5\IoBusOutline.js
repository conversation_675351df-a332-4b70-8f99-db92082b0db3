// THIS FILE IS AUTO GENERATED
var GenIcon = require('../lib').GenIcon
module.exports.IoBusOutline = function IoBusOutline (props) {
  return GenIcon({"tag":"svg","attr":{"viewBox":"0 0 512 512"},"child":[{"tag":"rect","attr":{"width":"352","height":"192","x":"80","y":"112","fill":"none","strokeLinecap":"round","strokeLinejoin":"round","strokeWidth":"32","rx":"32","ry":"32"}},{"tag":"rect","attr":{"width":"352","height":"128","x":"80","y":"304","fill":"none","strokeLinecap":"round","strokeLinejoin":"round","strokeWidth":"32","rx":"32","ry":"32"}},{"tag":"path","attr":{"fill":"none","strokeLinecap":"round","strokeLinejoin":"round","strokeWidth":"32","d":"M400 112H112a32.09 32.09 0 01-32-32h0a32.09 32.09 0 0132-32h288a32.09 32.09 0 0132 32h0a32.09 32.09 0 01-32 32zM144 432v22a10 10 0 01-10 10h-28a10 10 0 01-10-10v-22zm272 0v22a10 10 0 01-10 10h-28a10 10 0 01-10-10v-22z"}},{"tag":"circle","attr":{"cx":"368","cy":"368","r":"16","fill":"none","strokeLinejoin":"round","strokeWidth":"32"}},{"tag":"circle","attr":{"cx":"144","cy":"368","r":"16","fill":"none","strokeLinejoin":"round","strokeWidth":"32"}},{"tag":"path","attr":{"fill":"none","strokeLinecap":"round","strokeLinejoin":"round","strokeWidth":"32","d":"M256 112v192M80 80v288M432 80v288"}}]})(props);
};
