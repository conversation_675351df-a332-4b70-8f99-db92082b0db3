import { formatDistance } from "./ko/_lib/formatDistance.js";
import { formatLong } from "./ko/_lib/formatLong.js";
import { formatRelative } from "./ko/_lib/formatRelative.js";
import { localize } from "./ko/_lib/localize.js";
import { match } from "./ko/_lib/match.js";

/**
 * @category Locales
 * @summary Korean locale.
 * @language Korean
 * @iso-639-2 kor
 * <AUTHOR> [@angdev](https://github.com/angdev)
 * <AUTHOR> [@iamssen](https://github.com/iamssen)
 * <AUTHOR> [@so99ynoodles](https://github.com/so99ynoodles)
 */
export const ko = {
  code: "ko",
  formatDistance: formatDistance,
  formatLong: formatLong,
  formatRelative: formatRelative,
  localize: localize,
  match: match,
  options: {
    weekStartsOn: 0 /* Sunday */,
    firstWeekContainsDate: 1,
  },
};

// Fallback for modularized imports:
export default ko;
