// THIS FILE IS AUTO GENERATED
var GenIcon = require('../lib').GenIcon
module.exports.CgStyle = function CgStyle (props) {
  return GenIcon({"tag":"svg","attr":{"viewBox":"0 0 24 24","fill":"none"},"child":[{"tag":"path","attr":{"fillRule":"evenodd","clipRule":"evenodd","d":"M13 21V13H21V21H13ZM15 15H19L19 19H15V15Z","fill":"currentColor"}},{"tag":"path","attr":{"fillRule":"evenodd","clipRule":"evenodd","d":"M3 11L3 3L11 3V11H3ZM5 5L9 5V9L5 9L5 5Z","fill":"currentColor"}},{"tag":"path","attr":{"d":"M18 6V12H16V8L12 8V6L18 6Z","fill":"currentColor"}},{"tag":"path","attr":{"d":"M12 18H6L6 12H8L8 16H12V18Z","fill":"currentColor"}}]})(props);
};
