// THIS FILE IS AUTO GENERATED
var GenIcon = require('../lib').GenIcon
module.exports.GiPigFace = function GiPigFace (props) {
  return GenIcon({"tag":"svg","attr":{"viewBox":"0 0 512 512"},"child":[{"tag":"path","attr":{"d":"M249.22 20.688c-42.737 0-83.466 33.6-113.69 85.593-30.22 51.995-49.186 121.333-49.186 187.626 0 66.294 18.728 116.544 48.156 150.188 29.428 33.644 69.626 51.094 114.72 51.094 45.09 0 85.32-17.45 114.75-51.094 29.426-33.644 48.155-83.894 48.155-150.188 0-66.294-18.996-135.63-49.22-187.625-30.22-51.993-70.95-85.593-113.686-85.593zm.624 138.843c47.212-.188 94.522 14.425 122.187 44.407-73.1-38.23-167.19-37.75-243.75 0 27.234-29.23 74.352-44.217 121.564-44.406zm-4.25 60.814c4.714-.017 9.42.03 14.094.156 49.863 1.33 96.673 10.52 125.437 28.063l-9.72 15.968c-12.062-7.355-30.064-13.554-51.25-18-9.755 39.85-42.47 33.866-41.405-6-7.727-.67-15.606-1.13-23.563-1.343-14.584-.388-29.44 0-44.03 1.188 1.156 40.128-31.814 46.093-41.47 5.78-17.625 3.593-33.968 8.563-47.843 14.97L118 244.155c34.337-15.855 78.872-23.11 122.875-23.78 1.572-.024 3.147-.025 4.72-.03zm4.156 51.344c.533-.022 1.08-.027 1.625 0 8.74.425 15.003 6.474 19.875 13.875 4.872 7.4 8.99 17.176 12.844 29.562l-17.844 5.563c-3.54-11.373-7.338-19.85-10.625-24.844-2.876-4.37-4.886-5.285-5.156-5.438-.232.077-1.725.596-4.47 4.625-3.25 4.77-7.132 13.392-10.813 25.564l-17.906-5.406c4.003-13.235 8.228-23.238 13.282-30.657 4.74-6.954 11.197-12.523 19.188-12.842zm52.156 31.937c36.06 16.695 61.968 53.982 52.03 105.375 2.384 1.498 4.685 3.06 6.908 4.688l-11.03 15.062c-23.702-17.36-63.367-27.507-102.408-28-39.04-.493-77.314 8.803-98.375 25.78l-11.75-14.56c2.278-1.837 4.674-3.566 7.157-5.22-6.94-49.057 15.778-84.185 48.22-101.156l8.687 16.53c-25.165 13.166-42.41 36.606-39.188 75 24.58-10.84 54.94-15.448 85.5-15.062 31.358.397 63.007 6.13 88.97 17.75 5.932-40.864-13.804-65.904-42.563-79.218l7.843-16.97z"}}]})(props);
};
