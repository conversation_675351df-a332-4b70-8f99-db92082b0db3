// THIS FILE IS AUTO GENERATED
var GenIcon = require('../lib').GenIcon
module.exports.GiTrinacria = function GiTrinacria (props) {
  return GenIcon({"tag":"svg","attr":{"viewBox":"0 0 512 512"},"child":[{"tag":"path","attr":{"d":"M264.8 40.41c-43.4 3.83-98.3 32.01-146.8 54.41h-.1l-.1.11c-5.9 2.58-9.1.51-12.1-.73s-5.9-2.9-9-4.87c-6.36-3.95-13.6-9.17-20.76-14.29-7.15-5.11-14.25-10.1-19.59-13.26-1.41-.83-2.13-1.09-3.2-1.63l-4.46 7.67c4.65 9.52 13.82 24.61 23.63 37.58 5.6 7.4 11.37 14.2 16.25 18.8 4.85 4.6 9.43 6 8.13 6 1.8.1 1.9 0 3.9-2 2.1-2.1 4.9-6.2 8.9-10.6l2.4-2.6 3.5-.4c17.6-1.8 38.5-.9 57.8-4.6 19.1-3.6 35.7-10.83 47.2-28.38 0-.11.1-.13.1-.23l15.4 10.37c0 .1-.1.12-.1.23-10 14.41-20.7 35.41-29.2 55.31 9.8-3.8 20.3-5.8 31.4-5.8 8.6 0 16.9 1.2 24.7 3.5 2.6-12.5 4.5-25.2 5.6-37.3 2.1-21.05 2-40.43.1-53.46-.8-6.52-2.4-11.48-3.5-13.62l-.1-.21zm197.5 99.49c-1.6 0-3.1 0-4.8.2-8.7.2-18.6.8-27.8 1.9-9.3 1.2-17.9 2.8-24.4 4.7-5.9 1.8-9.5 4.6-9.4 4.3-.7 1.2-.7 1.6 0 4.2.7 2.9 2.8 7.2 4.7 12.8l1.2 3.4-1.5 3.3c-7.4 16.2-18.6 34-25 52.4-6.5 18.4-8.5 36.4 1.1 55.1 0 .1.1.2.1.3l-16.7 8.1c-.1-.1-.1-.2-.1-.3-7.5-15.7-20.2-35.2-33-52.4-1.7 20.1-10 38.2-22.8 52.3 8.5 7.3 17.3 14.3 26 20.5 17.2 12.2 34 21.9 46.3 26.8 6 2.4 11 3.7 13.5 3.8h.2c18.2-39.5 21.3-101.4 26.2-154.4.6-6.5 4-8.3 6.6-10.3 2.6-2 5.5-3.7 8.7-5.5 6.6-3.5 14.8-7.1 22.8-10.8 8.1-3.6 15.9-7.2 21.3-10.3 1.5-.7 2-1.3 3-2l-4.3-7.6c-3.5-.2-7.4-.5-11.9-.5zM395.9 151c.1-.1.1-.1.1-.2-.1.1-.1.1-.1.2zM238 159.5c-39.3 0-71 31.7-71 71s31.7 71 71 71 71-31.7 71-71-31.7-71-71-71zM190 215h96v18h-96v-18zm-38.6 36.1c-9.7 3.5-19.2 7.3-28.2 11.4-19.3 8.8-35.96 18.5-46.32 26.7-5.17 4-8.71 7.8-10 9.8-.12.2 0 .1-.12.2 24.99 35.6 76.94 69.2 120.54 100h.1c5.2 3.8 5 7.7 5.5 10.9.4 3.2.4 6.5.3 10.2-.2 7.5-1.2 16.4-2 25.2-.8 8.7-1.7 17.4-1.7 23.6-.1 1.6.1 2.4.1 3.6h8.8c5.9-8.8 14.4-24.3 20.8-39.3 3.7-8.5 6.6-16.9 8.1-23.5 1.6-6.3.6-11.1 1.2-10.2-.8-1.3-1-1.4-3.8-2.1-2.8-.8-7.6-1.2-13.5-2.4l-3.5-.7-2.1-3c-10.3-14.4-20.1-32.9-32.7-47.8-12.8-14.8-27.2-25.5-48.3-26.7h-.3l1.4-18.6h.3c15 1.3 34.4.5 53.1-1.3-13.5-12-23.4-27.9-27.7-46zm86.6.9a32 16 0 0 1 32 16 32 16 0 0 1-32 16 32 16 0 0 1-32-16 32 16 0 0 1 32-16z"}}]})(props);
};
