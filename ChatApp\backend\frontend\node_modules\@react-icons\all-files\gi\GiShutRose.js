// THIS FILE IS AUTO GENERATED
var GenIcon = require('../lib').GenIcon
module.exports.GiShutRose = function GiShutRose (props) {
  return GenIcon({"tag":"svg","attr":{"viewBox":"0 0 512 512"},"child":[{"tag":"path","attr":{"d":"M428.023 17.34c-86.043.487-145.782 76.552-110.164 148.85-56.288 31.21-86.892 73.135-108.387 115.72-20.313-3.606-33.09-19.605-43.448-39.65-6.768 44.82 6.93 72.25 15.243 104.312-17.695 43.045-35.085 81.538-71.348 107.38h-.004c-26.502 18.903-51.61 14.403-63.865.622-6.127-6.89-9.442-16.272-7.98-28.256 1.45-11.88 7.926-26.43 22.246-42.21 11.966-10.25 21.688-14.278 28.155-14.956 6.552-.687 9.855 1.215 12.337 4.328 4.963 6.227 5.048 20.812-7.48 31.327l12.013 14.316c19.1-16.03 22.676-41.486 10.082-57.29-6.297-7.9-17.036-12.51-28.9-11.267-11.865 1.245-24.787 7.552-38.885 19.76l-.41.356-.366.4c-16.57 18.11-25.296 36.22-27.34 52.973-2.045 16.753 2.876 32.044 12.566 42.94 19.38 21.79 56.223 25.322 88.68 2.175l.002-.002c6.138-4.375 11.773-9.047 16.992-13.967 29.887-7.727 58.963-10.74 92.3-37.436-21.192.977-40.373-1.702-53.542-15.13 13.405-25.286 23.348-52.815 34.78-79.87 10.99-26.018 23.196-51.737 40.874-75.557 27.692.905 54.74 2.943 90.135-12.492-21.436-4.717-39.77-12.685-48.723-30.168 10.31-8.11 21.865-15.774 34.94-22.893 63.25 43.663 173.666-17.293 159.263-111.2L453 78.142l24.03-41.7-46.327 9.394 11.002-27.94c-4.625-.4-9.19-.58-13.682-.554zM177.857 47.475C99.85 123.65 112.403 221.302 190.082 226.61c99.86 6.987 1.396-107.536-12.225-179.135zm110.387 218.523c-18.154-.118-31.885 10.172-38.142 40.106-15.92 76.243 102.983 90.25 179.15 12.224-48.11-8.213-103.834-52.09-141.008-52.33z"}}]})(props);
};
