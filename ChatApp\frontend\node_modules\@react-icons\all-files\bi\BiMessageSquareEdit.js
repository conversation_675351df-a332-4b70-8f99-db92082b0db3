// THIS FILE IS AUTO GENERATED
var GenIcon = require('../lib').GenIcon
module.exports.BiMessageSquareEdit = function BiMessageSquareEdit (props) {
  return GenIcon({"tag":"svg","attr":{"viewBox":"0 0 24 24"},"child":[{"tag":"path","attr":{"d":"M16,2H8C4.691,2,2,4.691,2,8v13c0,0.553,0.447,1,1,1h13c3.309,0,6-2.691,6-6V8C22,4.691,19.309,2,16,2z M20,16 c0,2.206-1.794,4-4,4H4V8c0-2.206,1.794-4,4-4h8c2.206,0,4,1.794,4,4V16z"}},{"tag":"path","attr":{"d":"M7 14.987L7 16.986 8.999 16.986 14.528 11.464 12.53 9.466z"}},{"tag":"path","attr":{"transform":"rotate(-134.999 15.233 8.761)","d":"M13.82 7.684H16.647V9.839H13.82z"}}]})(props);
};
