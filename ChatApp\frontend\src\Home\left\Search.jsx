import {useEffect, useState } from 'react'
import useConversation from '../../stateManagement/useConversation';
import GetAllUser from '../../Context/useGetMessage';
import toast from "react-hot-toast";

const Search = ({ onMobileSelect }) => {
  const {allUsers} = GetAllUser();
  const [search, setSearch] = useState("");
 
  
  console.log("HRR",allUsers);
  
  const { setSelectedConversation } = useConversation();


  // useEffect(() => {
  // allUsers.filter(user => user.fullname?.toLowerCase().includes(search.toLowerCase()))
    
  // }, [])
  

  const handleSubmit = async (e) => {
    e.preventDefault();
    if (!search) return;
    const conversation =  await allUsers.find((user) =>
      user.fullname?.toLowerCase().includes(search.toLowerCase())
    );
    // console.log(conversation)
    if (conversation) {
      setSelectedConversation(conversation);
      setSearch("");
      // Close mobile menu when a user is selected
      if (onMobileSelect) onMobileSelect();
    } else {
      toast.error("User not found");
    }
  }
   
  // const handleChange=(e)=>{
  //   setSearch(e.target.value)
     console.log(search);

  // }
  

  useEffect(() => {
    console.log(search)
  }, [search]);       
  return (
    <div className='py-2'>
      <form onSubmit={handleSubmit}>
        <div className="relative">
          <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
            <svg className="h-5 w-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
            </svg>
          </div>

          {/* <input
            type="text"
            className="w-full pl-10 pr-4 py-2 bg-gray-100 dark:bg-gray-600 border border-gray-200 dark:border-gray-500 rounded-lg focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-transparent text-gray-900 dark:text-white placeholder-gray-500 dark:placeholder-gray-400"
            placeholder="Search or start new chat"
            value={search}
            onChange={(e) => setSearch(e.target.value)}
             onKeyDown={(e) => {
                if(e.key === 'Enter') 
                  setSearch('');
               }}
          /> */}

          {search && (
            <button
              type="button"
              // onClick={() => setSearch('')}
             
              className="absolute inset-y-0 right-0 pr-3 flex items-center"
            >
            
              <svg className="h-4 w-4 text-gray-400 hover:text-gray-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
              </svg>
            </button>
          )}
        </div>

       
      </form>
    </div>
  )
}

export default Search