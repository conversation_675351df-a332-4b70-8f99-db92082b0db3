// THIS FILE IS AUTO GENERATED
var GenIcon = require('../lib').GenIcon
module.exports.RiCapsuleLine = function RiCapsuleLine (props) {
  return GenIcon({"tag":"svg","attr":{"viewBox":"0 0 24 24"},"child":[{"tag":"g","attr":{},"child":[{"tag":"path","attr":{"fill":"none","d":"M0 0H24V24H0z"}},{"tag":"path","attr":{"d":"M19.778 4.222c2.343 2.343 2.343 6.142 0 8.485l-7.07 7.071c-2.344 2.343-6.143 2.343-8.486 0-2.343-2.343-2.343-6.142 0-8.485l7.07-7.071c2.344-2.343 6.143-2.343 8.486 0zm-5.656 11.313L8.465 9.878l-2.829 2.83c-1.562 1.561-1.562 4.094 0 5.656 1.562 1.562 4.095 1.562 5.657 0l2.829-2.83zm4.242-9.899c-1.562-1.562-4.095-1.562-5.657 0L9.88 8.464l5.657 5.657 2.828-2.828c1.562-1.562 1.562-4.095 0-5.657z"}}]}]})(props);
};
