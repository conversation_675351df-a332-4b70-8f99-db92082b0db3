// THIS FILE IS AUTO GENERATED
var GenIcon = require('../lib').GenIcon
module.exports.BiPowerOff = function BiPowerOff (props) {
  return GenIcon({"tag":"svg","attr":{"viewBox":"0 0 24 24"},"child":[{"tag":"path","attr":{"d":"M12,21c4.411,0,8-3.589,8-8c0-3.35-2.072-6.221-5-7.411v2.223c1.79,1.04,3,2.973,3,5.188c0,3.309-2.691,6-6,6s-6-2.691-6-6 c0-2.215,1.21-4.149,3-5.188V5.589C6.072,6.779,4,9.65,4,13C4,17.411,7.589,21,12,21z"}},{"tag":"path","attr":{"d":"M11 2H13V12H11z"}}]})(props);
};
