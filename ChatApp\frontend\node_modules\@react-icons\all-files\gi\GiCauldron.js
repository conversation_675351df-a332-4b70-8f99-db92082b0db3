// THIS FILE IS AUTO GENERATED
var GenIcon = require('../lib').GenIcon
module.exports.GiCauldron = function GiCauldron (props) {
  return GenIcon({"tag":"svg","attr":{"viewBox":"0 0 512 512"},"child":[{"tag":"path","attr":{"d":"M410.478 374.033c50.192-40.902 82.002-101.322 82.002-168.791 0-39.775-11.145-77.055-30.493-109.388a207.118 207.118 0 0 0 5.876-1.474c12.638-3.438 20.085-16.457 16.66-29.095-3.425-12.638-16.424-20.108-29.093-16.702-.733.199-74.898 19.832-193.895 19.832-110.53 0-202.801-19.902-203.718-20.103-12.818-2.797-25.447 5.308-28.254 18.101-2.804 12.798 5.292 25.445 18.091 28.253.315.07 1.27.276 2.707.574-19.575 32.472-30.84 69.98-30.84 110.002 0 68.303 32.587 129.4 83.864 170.31l-24.663 55.324c-5.338 11.965.042 25.993 12.007 31.327 12.296 5.259 26.798-2.42 31.332-12.012l21.674-48.613c33.399 17.026 71.63 26.698 112.263 26.698 41.476 0 80.435-10.097 114.325-27.786l22.157 49.702c7.795 11.978 19.23 17.286 31.331 12.011 11.966-5.334 17.346-19.361 12.007-31.326zm-90.163-242.518c-7.512 24.69-2.9 36.895 3.11 54.952 5.987 17.983-6.469 29.056-14.63 29.056-8.156 0-21.41-11.499-14.918-29.056 13.532-36.59-8.026-54.037-8.615-53.989-.033.003-8.379 11.894-7.907 26.89 1.118 35.315 13.779 94.118-1.723 94.118-15.826 0 .463-81.382-31.972-116.972-26.704 1.119-26.015-1.388-47.65-2.632-23.353 16.934-8.423 44.864-21.866 44.864-15.428 0-1.586-21.75-8.458-31.726-17.374-25.214-37.565-7.444-38.913 3.145.222 18.93 4.249 27.993 5.204 33.82 1.826 11.155-6.168 20.464-13.773 20.464-7.609 0-13.777-9.16-13.777-20.464 0-5.677 5.037-18.096 6.905-33.982-5.262-21.376-13.898-38.007-33.576-45.124 83.853 9.403 240.356 19.834 344.19 3.476-31.871 12.565-71.261 20.029-101.69 23.006.013.054.078.098.06.154z"}}]})(props);
};
