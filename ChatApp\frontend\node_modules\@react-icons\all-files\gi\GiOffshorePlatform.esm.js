// THIS FILE IS AUTO GENERATED
import { GenIcon } from '../lib';
export function GiOffshorePlatform (props) {
  return GenIcon({"tag":"svg","attr":{"viewBox":"0 0 512 512"},"child":[{"tag":"path","attr":{"d":"M180.102 32v23H148.82l-10.666 128h18.063l4.056-48.686 39.237 25.112L156.783 183h9.319v22.742l33.365 17.219-33.365 13.346V247h21.732l38.213-15.285L227.32 247h18.063l-16-192h-31.281V32h-18zm-13.895 41h46.613l.993 11.9L166.207 73zm205.895 14v64h16.459l79.54-26.514V368h18V87h-114zm-208.217 3.973l37.277 9.32-39.764 20.523 2.487-29.843zM326.102 105v46h30v-46h-30zm84.724 0h57.276v.514l-37.67 12.556L410.826 105zm-20.724 7.816l18.695 12.463-18.695 6.233v-18.696zm-173.963.002l3.158 37.9-34.193-21.882 31.035-16.018zm4.619 55.444l3.957 47.474-46.502-24 42.545-23.474zm89.344.738v46h110v-46h-110zm-240 32v46h78v-46h-78zm15 14h32v18h-32v-18zm257 18v14h46v-14h-46zm-304 32v46h398v-46h-398zm2.5 64l43.5 58v-30l-21-28h-22.5zm61.5 0v126.576c-3.1-.362-6.122-.576-9-.576-13.178 0-29.267 4.285-42.848 8.385s-24.356 8.205-24.356 8.205l6.407 16.82s10.23-3.894 23.152-7.795c12.922-3.9 28.832-7.615 37.645-7.615 8.812 0 24.722 3.715 37.644 7.615 12.922 3.9 23.152 7.795 23.152 7.795l3.204 1.22 3.203-1.22s10.23-3.894 23.152-7.795c12.922-3.9 28.832-7.615 37.645-7.615 8.812 0 24.722 3.715 37.644 7.615 12.922 3.9 23.152 7.795 23.152 7.795l3.204 1.22 3.203-1.22s10.23-3.894 23.152-7.795c12.922-3.9 28.832-7.615 37.645-7.615 8.812 0 24.722 3.715 37.644 7.615 12.922 3.9 23.152 7.795 23.152 7.795l2.643 1.008 66.744-16.688-4.367-17.46-61.336 15.334c-3.107-1.152-10.661-3.907-21.633-7.22-11.256-3.397-24.23-6.915-35.847-8.023V329h-30v129.053c-6.795 1.53-13.588 3.442-19.848 5.332-10.511 3.173-17.698 5.77-21.152 7.045-3.455-1.274-10.641-3.872-21.153-7.045-13.58-4.1-29.67-8.385-42.847-8.385-7.104 0-15.054 1.263-23 3.053V329h-18v133.838c-.613.182-1.245.365-1.848.547-10.511 3.173-17.698 5.77-21.152 7.045-3.455-1.274-10.641-3.872-21.153-7.045-1.256-.38-2.553-.758-3.847-1.137V329h-30zm162.5 0l43.5 58v-30l-21-28h-22.5zm130.5 0l-21 28v30l43.5-58h-22.5z"}}]})(props);
};
