// THIS FILE IS AUTO GENERATED
var GenIcon = require('../lib').GenIcon
module.exports.GiFossil = function GiFossil (props) {
  return GenIcon({"tag":"svg","attr":{"viewBox":"0 0 512 512"},"child":[{"tag":"path","attr":{"d":"M337.268 21.412L97.643 43.66 17.125 159.672l30.592 194.662.25.088L60.9 439.168l166.438 56.05 119.598-10.36 127.832-60.915 5.377-84.605 7.605-85.133-10.02-31.236-35.45-10.642.548-96.982.276-.328-.872-2.717-104.964-90.888zM221.338 58.39c27.232.292 53.762 7.89 75.67 21.313 26.706 16.363 46.742 41.84 51.01 73.23 2.406 17.697-1.08 35.415-9.15 51.063 24.128 7.656 41.976 13.968 55.943 25.027 16.303 12.91 25.883 31.77 34.08 62.31l1.483 5.534-4.256 3.834c-35.05 31.564-81.466 52.728-131.297 58.57-51.316 6.02-103.156-5.577-144.302-30.446-41.147-24.87-71.802-63.446-78.82-110.922C60.755 143.87 116.99 68.67 203.38 59.264c4.17-.454 8.342-.732 12.503-.836 1.82-.046 3.64-.06 5.455-.04zm-.215 18.702c-1.592-.02-3.186-.01-4.78.027-3.648.086-7.298.325-10.94.722-20.212 2.2-38.422 8.412-54.114 17.515 4.263 22.597 19.14 36.716 41.003 46.123 9.088-7.047 20.425-11.79 33.28-12.925h.003c1.504-.133 3.006-.202 4.504-.225-.274-17.968 4.715-33.666 11.166-49.455-6.608-1.082-13.345-1.7-20.123-1.783zm38.535 6.13c-7.05 16.748-11.816 30.916-10.82 47.737 5.064 1.534 9.877 3.708 14.264 6.487 5.04 3.194 9.55 7.294 13.09 12.127l48.736-11.43c-6.917-17.315-20.272-31.837-37.684-42.505-8.4-5.148-17.72-9.33-27.586-12.417zM134.906 106.62c-33.29 26.87-50.667 68.3-44.72 108.548v.002c.183 1.24.39 2.472.61 3.7 28.092 10.36 52.05 12.284 85.534-3.616-3.505-6.465-5.89-13.582-6.828-21.24-1.658-13.547 1.935-27.047 9.443-38.258-20.607-10.497-37.12-26.62-44.04-49.137zm289.108 31.18l-.39 68.927-45.843-13.76 46.234-55.168zm-193.323 9.216c-1.16.003-2.32.054-3.473.156-.523.046-1.033.114-1.547.176l14.02 20.65c8.896-1.71 18.57.49 25.402 6.56.172-1.28.212-2.564.078-3.826-.75-7.038-5.032-13.038-12.072-17.5-5.28-3.346-12.004-5.54-18.932-6.084-1.155-.09-2.315-.135-3.475-.132zm-23.323 6.644c-13.68 8.462-21.077 23.678-19.314 38.082v.002c.393 3.216 1.172 6.306 2.27 9.27l28.066-11.325c.303-4.362 1.994-8.657 5.05-12.356l-16.073-23.674zm122.217 2.588l-46.068 10.805c.085.564.177 1.125.238 1.7.645 6.055-.246 12.067-2.377 17.55l34.928 18.935c10.45-14.344 15.387-31.86 13.28-48.99zm-59.127 45.393c-.254.215-.518.416-.777.624-1.303 2.125-3 4.114-5.096 5.88-2.283 1.923-4.854 3.426-7.588 4.518l-2.793 24.46c1.878-.047 3.758-.13 5.63-.317 17.064-1.695 31.717-8.165 43.165-17.522l-32.54-17.642zm-45.07 5.38l-24.764 9.992c3.253 3.334 7.042 6.348 11.293 8.994 6.95 4.326 15.077 7.51 23.678 9.353l2.45-21.467c-4.424-1.106-8.586-3.194-12.02-6.272-.218-.196-.427-.4-.637-.6zm102.855 13.234c-15.41 18.853-38.614 32.373-66.562 35.148-.73.073-1.46.116-2.192.172 8.186 18.072 10.37 32.67 6.744 46.588-3.447 13.235-11.147 24.704-21.238 38.715 15.69 1.695 31.723 1.702 47.65-.166 43.894-5.146 84.95-23.416 116.46-50.16-7.193-24.805-14.51-37.858-25.895-46.874-11.237-8.897-28.577-15.043-54.968-23.422zm-140.53 10.22c-33.997 16.75-62.925 17.738-90.94 9.962 11.302 29.815 33.998 54.615 63.412 72.392 19.095 11.54 40.97 20 64.074 24.785 12.61-17.636 21.18-29.762 23.888-40.162 2.844-10.915 1.06-22.145-9.806-42.554-13.078-1.858-25.54-6.326-36.3-13.023-5.22-3.25-10.053-7.073-14.327-11.398zm272.32 130.657l-3.22 50.667-97.4 46.414 14.133-62.933 86.488-34.146zm-392.083.352l143.605 50.743 6.398 60.113-140.297-47.246-9.707-63.61zm285.304 38.854l-14.936 66.51-101.224 8.77-6.54-61.454 122.7-13.826z"}}]})(props);
};
