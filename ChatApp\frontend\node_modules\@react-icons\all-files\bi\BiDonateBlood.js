// THIS FILE IS AUTO GENERATED
var GenIcon = require('../lib').GenIcon
module.exports.BiDonateBlood = function BiDonateBlood (props) {
  return GenIcon({"tag":"svg","attr":{"viewBox":"0 0 24 24"},"child":[{"tag":"path","attr":{"d":"M4 21h9.62c1.169 0 2.276-.509 3.037-1.397l5.102-5.952c.217-.253.294-.597.205-.918s-.332-.576-.647-.682l-1.968-.656c-.95-.317-2.042-.122-2.823.503l-3.185 2.547-.617-1.235C12.042 11.847 10.671 11 9.146 11H4c-1.103 0-2 .897-2 2v6C2 20.103 2.897 21 4 21zM4 13h5.146c.763 0 1.448.423 1.789 1.105L11.382 15H10 8 7v2h1 2 3c.001 0 .002 0 .002 0 .001 0 .001 0 .001 0 .001 0 .001 0 .002 0 0 0 .002 0 .006 0 .001 0 .002 0 .002 0 .001 0 .001 0 .001 0 .156-.002.308-.041.442-.11.001 0 .002 0 .003-.001.001-.001.003-.001.004-.002 0 0 .001 0 .001 0l.001 0c.001 0 .001 0 .001 0 .001 0 .001 0 .002-.001 0 0 .001 0 .001 0l.001 0c.001 0 .001 0 .001 0 .001 0 .001 0 .001 0 .001 0 .001 0 .001-.001.011.003.003-.001.003-.001.012 0 .002-.001.002-.001.001 0 .001 0 .001 0 .002 0 .002 0 .002-.001.001 0 .002-.001.003-.001s.001 0 .002-.001c.002 0 .001-.001.002-.001v0c.001 0 .002-.001.003-.001s.001 0 .002-.001c0 0 .001-.001.002-.001.001 0 .002-.001.003-.002s.001 0 .002-.001c.003-.001.001-.001.002-.001v0c.002-.001.003-.001.003-.001.001 0 .001 0 .002-.001 0 0 0 0 .001 0l.002-.001c.001 0 .001 0 .001 0 .001 0 .001 0 .002-.001v0c.001 0 .001 0 .002-.001.011-.001.003-.001.003-.001 0-.001.003-.001.002-.001.038-.023.075-.049.11-.078l4.146-3.317c.261-.208.623-.273.94-.167l.557.186-4.133 4.823C14.759 18.745 14.205 19 13.62 19H4V13zM13.761 2.326C13.3 2.832 11 5.457 11 7.5c0 1.93 1.57 3.5 3.5 3.5S18 9.43 18 7.5c0-2.043-2.3-4.668-2.761-5.174C14.86 1.91 14.14 1.91 13.761 2.326zM16 7.5C16 8.327 15.327 9 14.5 9S13 8.327 13 7.5c0-.708.738-1.934 1.5-2.934C15.262 5.566 16 6.792 16 7.5z"}}]})(props);
};
