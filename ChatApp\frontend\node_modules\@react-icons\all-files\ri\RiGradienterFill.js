// THIS FILE IS AUTO GENERATED
var GenIcon = require('../lib').GenIcon
module.exports.RiGradienterFill = function RiGradienterFill (props) {
  return GenIcon({"tag":"svg","attr":{"viewBox":"0 0 24 24"},"child":[{"tag":"g","attr":{},"child":[{"tag":"path","attr":{"fill":"none","d":"M0 0h24v24H0z"}},{"tag":"path","attr":{"d":"M12 22C6.477 22 2 17.523 2 12S6.477 2 12 2s10 4.477 10 10-4.477 10-10 10zM8.126 11H4.062a8.079 8.079 0 0 0 0 2h4.064a4.007 4.007 0 0 1 0-2zm7.748 0a4.007 4.007 0 0 1 0 2h4.064a8.079 8.079 0 0 0 0-2h-4.064zM12 14a2 2 0 1 0 0-4 2 2 0 0 0 0 4z"}}]}]})(props);
};
