// THIS FILE IS AUTO GENERATED
var GenIcon = require('../lib').GenIcon
module.exports.GiBananaPeel = function GiBananaPeel (props) {
  return GenIcon({"tag":"svg","attr":{"viewBox":"0 0 512 512"},"child":[{"tag":"path","attr":{"d":"M179.004 28.59c-27.103-.228-59.62 3.852-85.3 11.76-13.603 4.19-25.234 9.464-32.825 15.035-7.592 5.57-10.564 10.6-10.603 15.332-.28 34.684 6.272 74.84 15.356 109.914 8.183 31.6 18.565 58.72 26.967 74.063l367.086-70.306c-1.167-5.557-3.892-15.144-10.04-30.635-2.507-6.32-12.206-13.61-28.44-18.856-16.236-5.245-37.98-8.552-61.41-9.914-46.86-2.722-100.836 2.32-133.308 11.666l-9.408 2.71-.023-.116c-4.746 3.855-8.45 8.89-9.192 13.295l-1.496 8.875-17.75-2.99 1.495-8.875c2.28-13.532 12.047-23.497 22.963-29.34-1.27-5.404-2.737-11.043-4.39-16.828l-2.668-.148c-8.39-.46-15.76 2.225-23.487 7.608-7.727 5.383-15.46 13.517-23.156 22.846l-5.73 6.943-13.883-11.456 5.728-6.944c8.155-9.885 16.756-19.196 26.752-26.16 8.978-6.254 19.37-10.56 30.828-10.855-2.477-7.444-5.162-14.87-7.988-22.043l-4.242.182c-21.626.93-29.778 3.125-48.686 19.216l-6.853 5.832-11.666-13.707 6.853-5.834c19.92-16.95 35.208-22.107 57.094-23.354-2.817-6.128-5.696-11.84-8.58-16.916zM459.02 202.842l-208.19 39.875 12.865 12.582 193.785-36.78 1.54-15.678zM209.26 250.68l-111.04 21.267 2.555 14.272 106.74-20.26 1.745-15.28zm6.898 67.187c-1.93.008-3.322.324-4.496.645 1.007 12.28 1.89 21.67 2.22 26.802.135 2.085.15 3.93.138 5.784 5.136-1.573 10.24-2.392 15.253-2.528 1.41-5.27 4.942-11.547 12.27-19.783.183-.285.252-.444.257-.375.006.09.015-.237-.63-1.074-1.287-1.674-5.192-4.53-10.03-6.442-4.838-1.91-10.546-3.048-14.98-3.03zm-153.174 46.27c-.936.143-1.622.452-2.5 1.345-1.18 1.202-2.628 3.658-3.654 7.282-2.052 7.246-2.28 18.665-.166 30.36 2.114 11.693 6.475 23.732 12.395 32.85 2.324 3.58 4.842 6.625 7.505 9.112 15.763-4.76 32.333-8.89 47.444-11.45-24.98-15.224-38.57-30.71-46.569-43.736-5.478-8.922-8.535-16.58-10.925-21.113-1.99-3.773-2.544-4.354-3.53-4.65zm168.97 1.773c-6.16-.012-12.7 1.703-19.94 5.326-2.462 1.232-4.116 3.314-5.463 7.98-1.348 4.664-1.838 11.484-1.476 19.26.724 15.547 4.603 34.716 7.23 51.08l2.882 17.936-16.014-8.572c-18.284-9.787-35.306-11.132-54.543-9.754-20.922 1.498-52.502 9.324-78.234 18.08-12.865 4.378-24.37 9.007-32.316 12.98-3.467 1.734-5.86 3.234-7.635 4.467 1.764 2.986 4.14 4.224 8.172 5.217 5.598 1.38 14.486.904 24.733-1.045 20.493-3.897 45.75-13.088 67.92-14.85 35.257-2.805 75.38 10.294 97.177 11.417 7.785.4 17.53 1.202 24.658.332 3.565-.435 6.33-1.335 7.64-2.14 1.097-.67 1.298-.95 1.478-1.755-.266-1.334-.757-3.473-1.473-6.292-1.533-6.03-3.872-14.574-6.338-23.683-4.93-18.22-10.373-37.42-11.28-48.15l17.165-4.48c14.838 32.71 20.124 55.845 23.88 69.555 1.88 6.855 3.6 11.144 4.57 12.47.967 1.328.13 1.122 3.237 1.122h.49l.48.053c7.68.833 28.17-2.197 45.196-5.883 17.027-3.686 31.438-7.666 31.438-7.666l.5-.137.51-.08c34.406-5.364 65.797 8.805 88.408 17.04 11.305 4.118 20.553 6.367 24.81 5.844 2.13-.262 2.894-.67 3.95-1.836.543-.6 1.174-1.598 1.79-3.04-19.358-13.836-68.408-45.78-102.544-49.49-11.62-1.264-25.05-1.387-36.828.805-11.777 2.193-21.532 6.655-27.288 13.49l-9.603 11.405-5.62-13.81c-12.367-30.39-27.037-55.573-42.538-69.596-9.69-8.765-18.912-13.582-29.182-13.602zm241.204 19.565c-19.144 11.233-45.09 23.978-73.46 36.513 10.647 2.604 21.453 6.62 31.908 11.352 32.338-10.455 41.97-22.153 44.207-30.647 1.52-5.77-.156-11.773-2.654-17.218z"}}]})(props);
};
