// THIS FILE IS AUTO GENERATED
var GenIcon = require('../lib').GenIcon
module.exports.CgDistributeVertical = function CgDistributeVertical (props) {
  return GenIcon({"tag":"svg","attr":{"viewBox":"0 0 24 24","fill":"none"},"child":[{"tag":"path","attr":{"d":"M9 11H15V13H9V11Z","stroke":"currentColor","strokeOpacity":"0.5","strokeWidth":"2"}},{"tag":"path","attr":{"d":"M19 7H5V5H19V7Z","fill":"currentColor"}},{"tag":"path","attr":{"d":"M19 19H5V17H19V19Z","fill":"currentColor"}}]})(props);
};
