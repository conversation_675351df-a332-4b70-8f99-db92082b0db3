/*! 🌼 daisyUI 5.0.42 - MIT License */ @layer utilities{.breadcrumbs{max-width:100%;padding-block:.5rem;overflow-x:auto;&>menu,&>ul,&>ol{white-space:nowrap;align-items:center;min-height:min-content;display:flex;&>li{align-items:center;display:flex;&>*{cursor:pointer;align-items:center;gap:.5rem;display:flex;&:hover{@media (hover:hover){&{text-decoration-line:underline}}}&:focus{--tw-outline-style:none;outline-style:none;@media (forced-colors:active){&{outline-offset:2px;outline:2px solid #0000}}}&:focus-visible{outline-offset:2px;outline:2px solid}}&+:before{content:"";opacity:.4;background-color:#0000;border-top:1px solid;border-right:1px solid;width:.375rem;height:.375rem;margin-left:.5rem;margin-right:.75rem;display:block;rotate:45deg}[dir=rtl] &+:before{rotate:-135deg}}}}@media (width>=640px){.sm\:breadcrumbs{max-width:100%;padding-block:.5rem;overflow-x:auto;&>menu,&>ul,&>ol{white-space:nowrap;align-items:center;min-height:min-content;display:flex;&>li{align-items:center;display:flex;&>*{cursor:pointer;align-items:center;gap:.5rem;display:flex;&:hover{@media (hover:hover){&{text-decoration-line:underline}}}&:focus{--tw-outline-style:none;outline-style:none;@media (forced-colors:active){&{outline-offset:2px;outline:2px solid #0000}}}&:focus-visible{outline-offset:2px;outline:2px solid}}&+:before{content:"";opacity:.4;background-color:#0000;border-top:1px solid;border-right:1px solid;width:.375rem;height:.375rem;margin-left:.5rem;margin-right:.75rem;display:block;rotate:45deg}[dir=rtl] &+:before{rotate:-135deg}}}}}@media (width>=768px){.md\:breadcrumbs{max-width:100%;padding-block:.5rem;overflow-x:auto;&>menu,&>ul,&>ol{white-space:nowrap;align-items:center;min-height:min-content;display:flex;&>li{align-items:center;display:flex;&>*{cursor:pointer;align-items:center;gap:.5rem;display:flex;&:hover{@media (hover:hover){&{text-decoration-line:underline}}}&:focus{--tw-outline-style:none;outline-style:none;@media (forced-colors:active){&{outline-offset:2px;outline:2px solid #0000}}}&:focus-visible{outline-offset:2px;outline:2px solid}}&+:before{content:"";opacity:.4;background-color:#0000;border-top:1px solid;border-right:1px solid;width:.375rem;height:.375rem;margin-left:.5rem;margin-right:.75rem;display:block;rotate:45deg}[dir=rtl] &+:before{rotate:-135deg}}}}}@media (width>=1024px){.lg\:breadcrumbs{max-width:100%;padding-block:.5rem;overflow-x:auto;&>menu,&>ul,&>ol{white-space:nowrap;align-items:center;min-height:min-content;display:flex;&>li{align-items:center;display:flex;&>*{cursor:pointer;align-items:center;gap:.5rem;display:flex;&:hover{@media (hover:hover){&{text-decoration-line:underline}}}&:focus{--tw-outline-style:none;outline-style:none;@media (forced-colors:active){&{outline-offset:2px;outline:2px solid #0000}}}&:focus-visible{outline-offset:2px;outline:2px solid}}&+:before{content:"";opacity:.4;background-color:#0000;border-top:1px solid;border-right:1px solid;width:.375rem;height:.375rem;margin-left:.5rem;margin-right:.75rem;display:block;rotate:45deg}[dir=rtl] &+:before{rotate:-135deg}}}}}@media (width>=1280px){.xl\:breadcrumbs{max-width:100%;padding-block:.5rem;overflow-x:auto;&>menu,&>ul,&>ol{white-space:nowrap;align-items:center;min-height:min-content;display:flex;&>li{align-items:center;display:flex;&>*{cursor:pointer;align-items:center;gap:.5rem;display:flex;&:hover{@media (hover:hover){&{text-decoration-line:underline}}}&:focus{--tw-outline-style:none;outline-style:none;@media (forced-colors:active){&{outline-offset:2px;outline:2px solid #0000}}}&:focus-visible{outline-offset:2px;outline:2px solid}}&+:before{content:"";opacity:.4;background-color:#0000;border-top:1px solid;border-right:1px solid;width:.375rem;height:.375rem;margin-left:.5rem;margin-right:.75rem;display:block;rotate:45deg}[dir=rtl] &+:before{rotate:-135deg}}}}}@media (width>=1536px){.\32 xl\:breadcrumbs{max-width:100%;padding-block:.5rem;overflow-x:auto;&>menu,&>ul,&>ol{white-space:nowrap;align-items:center;min-height:min-content;display:flex;&>li{align-items:center;display:flex;&>*{cursor:pointer;align-items:center;gap:.5rem;display:flex;&:hover{@media (hover:hover){&{text-decoration-line:underline}}}&:focus{--tw-outline-style:none;outline-style:none;@media (forced-colors:active){&{outline-offset:2px;outline:2px solid #0000}}}&:focus-visible{outline-offset:2px;outline:2px solid}}&+:before{content:"";opacity:.4;background-color:#0000;border-top:1px solid;border-right:1px solid;width:.375rem;height:.375rem;margin-left:.5rem;margin-right:.75rem;display:block;rotate:45deg}[dir=rtl] &+:before{rotate:-135deg}}}}}}