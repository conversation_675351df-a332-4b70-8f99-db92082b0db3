// THIS FILE IS AUTO GENERATED
var GenIcon = require('../lib').GenIcon
module.exports.SiStylus = function SiStylus (props) {
  return GenIcon({"tag":"svg","attr":{"viewBox":"0 0 24 24","role":"img"},"child":[{"tag":"title","attr":{},"child":[]},{"tag":"path","attr":{"d":"M13.672 8.86c-.446-.354-1.698.239-2.052 1.118-.445 1.117-1.105 2.747-1.752 3.461-.683.751-.751.171-.683-.262.158-1.02 1.153-3.382 1.697-4.048-.202-.299-1.52-.256-2.436 1.166-.342.537-1.123 2.326-1.99 3.736-.19.305-.428.092-.244-.622.207-.825.818-3.089 1.605-4.872 2.064-.409 4.255-.696 5.934-.702.226-.061.378-.263 0-.275-1.447-.049-3.62.122-5.653.379.391-.782.812-1.404 1.239-1.667-.464-.293-1.404-.177-1.941.617a10.55 10.55 0 0 0-.702 1.245c-1.49.232-2.766.494-3.413.739-.671.256-.598 1.068-.189.915.849-.317 1.996-.647 3.272-.94-.812 1.832-1.447 3.993-1.599 4.872-.379 2.137.946 2.124 1.593 1.282.702-.922 2.167-4.164 2.393-4.505.067-.116.159-.055.11.048-1.636 3.266-1.496 4.53-.171 4.249.598-.128 1.63-1.153 1.899-1.685.055-.128.171-.116.146-.061-1.038 2.693-2.356 4.872-3.241 5.556-.806.616-1.405-.721 1.446-2.638.422-.286.226-.677-.25-.543-1.471.232-5.683 1.569-7.533 2.851-.141.098-.269.177-.263.379.006.116.208.073.306.012 2.393-1.435 4.352-1.996 6.599-2.467.03.013.067.019.097.007.104-.025.098.03.031.073a3.992 3.992 0 0 1-.342.177c-1.514.592-2.43 1.898-2.106 2.564.275.574 1.758.366 2.46-.012 1.722-.934 2.973-2.766 3.828-5.293.745-2.241 1.685-4.78 1.905-4.854zm-9.976 7.235c.623-.745.696-1.514.214-2.943-.305-.903-.812-1.599-.44-2.161.397-.598 1.24-.018.538.781l.14.098c.843.098 1.258-1.056.629-1.386-1.661-.867-3.114.8-2.473 2.729.275.818.66 1.685.348 2.375-.268.592-.787.94-1.135.952-.727.037-.244-1.63.592-2.045.073-.036.177-.085.079-.207-1.031-.116-1.636.36-1.984 1.025-1.013 1.935 1.923 2.65 3.492.782zm19.981-2.064c-2.802-.366-8.845.122-11.513.831-.794.207-.574.628-.171.549.006 0 .177-.043.183-.043 2.192-.427 7.509-.8 10.61-.207.373.067 1.49-1.05.891-1.13zm-9.224-.329c.781-.391 1.941-2.809 2.704-4.133.055-.098.153-.019.098.048-1.929 3.321-1.111 3.706-.348 3.657 1.02-.061 1.96-1.526 2.167-1.856.086-.128.135-.024.086.068-.049.152-.226.421-.391.787-.232.519.012.72.214.812.317.153 1.184.055 1.318-.476-.866-.018 1.209-4.109 1.423-4.359-.58-.336-1.477.031-1.887.836-.872 1.728-1.605 3.12-2.063 3.144-.891.049 1.026-3.852 1.337-3.974-.189-.275-1.404-.159-2.082.891-.244.379-1.733 3.016-2.1 3.45-.647.769-.696.11-.513-.66.061-.262.165-.598.3-.97.427-.965.885-1.27 1.166-1.582 1.886-2.093 2.967-3.791 2.539-4.456-.378-.592-1.642-.33-2.454.891-1.496 2.241-2.875 5.311-3.052 6.716-.171 1.404.848 1.507 1.538 1.166zm7.375-3.297c.238.586.598 1.166.384 1.679-.177.439-.409.623-.665.665-.36.061-.263-1.068.354-1.404.055-.03.134-.177.061-.262-.781-.043-1.221.329-1.459.824-.69 1.447 1.563 1.843 2.668.421.439-.568.458-1.129.036-2.149-.268-.647-.677-1.129-.421-1.557.275-.451.934-.061.44.544l.11.061c.641.036.903-.818.415-1.032-1.288-.549-2.491.836-1.923 2.21zm-6.581-.812c.067-.153.11-.195.226-.452.671-1.477 1.514-3.034 2.094-3.76.36-.379.866.134-.049 1.538a14.17 14.17 0 0 1-1.813 2.265v.006c-.171.189-.324.348-.391.44-.049.061-.104.049-.067-.037z"}}]})(props);
};
