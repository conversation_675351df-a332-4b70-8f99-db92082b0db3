// THIS FILE IS AUTO GENERATED
var GenIcon = require('../lib').GenIcon
module.exports.BiChevronUpSquare = function BiChevronUpSquare (props) {
  return GenIcon({"tag":"svg","attr":{"viewBox":"0 0 24 24"},"child":[{"tag":"path","attr":{"d":"M5,21h14c1.103,0,2-0.897,2-2V5c0-1.103-0.897-2-2-2H5C3.897,3,3,3.897,3,5v14C3,20.103,3.897,21,5,21z M5,5h14l0.001,14H5 V5z"}},{"tag":"path","attr":{"d":"M6.293 13.293L7.707 14.707 12 10.414 16.293 14.707 17.707 13.293 12 7.586z"}}]})(props);
};
