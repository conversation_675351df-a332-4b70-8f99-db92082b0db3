// THIS FILE IS AUTO GENERATED
var GenIcon = require('../lib').GenIcon
module.exports.GiWitchFace = function GiWitchFace (props) {
  return GenIcon({"tag":"svg","attr":{"viewBox":"0 0 512 512"},"child":[{"tag":"path","attr":{"d":"M228.9 19.9c-4.9.43-15.1 4.46-26.5 11.06-25.6 15.53-47.9 32.91-70.1 50.7 8.1-2.06 16.1-4.11 24.7-6.64 26.2-7.79 50.2-15.16 76.7-19.46l-2.8 12.88c-7.5 35.06-24.6 70.56-37.7 103.76 18.2 8.8 43.3 12.9 66.5 12.8 22.3-.1 43.1-4.8 52.8-9.2-4.7-50.2 1.2-101.67-23.9-139.54-14.4-16.7-40.5-17.98-59.7-16.36zm-44.4 125.7c-40.1 3.6-82.3 5.4-117.98 22.9-11.22 5.7-16.88 11.7-18.44 15.6-1.55 3.9-1.19 6.8 4.08 12.5 5.27 5.6 15.87 12.3 31.76 18.4 31.78 12.2 84.28 22.3 157.48 25.8 32 1.6 79.6-2.1 123.6-12.8 43-10.3 82.2-27.9 100.5-50.8-41.4-19.6-94.9-23.7-136.6-27.6.5 12.9 1.2 23.4 2.5 35.3l-4 3.1c-14.3 10.7-39.2 14.8-67.6 15-28.4.1-59.5-4.8-82.7-19-12.8-6.7 3.3-28.5 7.4-38.4zM389 240.3c-3.1.9-6.2 1.7-9.3 2.6 2 4.1 3.1 8.6 3.1 13.2 0 7.4-2.7 13.9-7 19.3 10.3 1.1 20.3 2.2 30.2 3.4-6.2-13.5-12.2-27-17-38.5zm-272.2 3.2C98.34 310.6 63.15 371 24.15 439.6c19.27-9.2 34.68-24.2 47.91-42.1 20.77-29 34.34-60.1 50.14-91.2l16.3 7.7c-17.8 33.3-31.3 65.9-50.61 92.4-18.43 28.1-39.59 55.5-63.16 79.3 6.87-.9 13.71-2 20.52-3.2 27.94-27.9 57.95-55.3 65.45-79.9l17.2 5.3c-7.5 24.5-27.4 45.8-48.43 66.4 11.28-3.4 21.63-7.4 29.73-11.5 15.4-12.5 23.7-28.3 29.1-45.4 7-21.8 8.4-45.6 12.4-65.6l17.7 3.6c-3.6 17.9-5.1 42.8-12.9 67.5-4 12.7-10 25.4-18.8 36.9 13.6 11.2 28.9 21.4 39.6 32.8 11.9-54 13.5-106.6 14-164.4l18 .1c-.3 35.4-1 69.3-4.2 103 6.9 15.7 11.9 28 16.2 39.8l10.6-212.8c-46.2-2.6-84.1-7.8-114.1-14.8zm244.3 3.9c-4.7 1.1-9.5 2-14.2 2.9 2.6 7.1-4.9 13.1-10.1 13.2-5 0-9.3-3-10.6-7.2.1 7.5 7.9 14.8 19.3 14.8 11.5 0 19.3-7.4 19.3-15-.4-3.4-1.8-6.6-3.7-8.7zm-52.9 8.7c-3.8.5-7.6.8-11.3 1.2.6 2.3.9 4.8.9 7.3 0 19.4-18.4 33.4-38.9 33.4-4.1 0-8.1-.6-11.9-1.6l-1.7 35.8c7.2 13.1 12.5 21.7 18.3 27.1 7.7 7 17.5 10.6 39.2 13.4 6.4 1.1 11.4 8.7 12.4 13.9 1 5.2.2 10.4-2.1 15.4-18.4 26.2-48 12.2-71.4 2.1l-.8 16.1c14.7 26.8 27.7 51.5 63 68.1 11.4 4.1 25.3 5.5 37.2.9l-20.7-82.4 6-3.3c18.9-10.6 28.7-24.2 36.9-39.6-25.7-2.8-49-9.6-74-11.2-9.7-.6-16.7-7.1-20.8-14-4.1-7-6.2-14.9-4.7-22.9 1.3-13.6 38.3-10.7 44-10.6l16.6 14.9c-16.5-.4-28.2-.3-42.5 3.9 1.1 4.1 3.8 10.3 8.5 10.8 49.2 3.1 84.3 21.4 136 6.3 16.1-3.7 49.3 15.8 61.5 23.4-2.4-20.6-7.1-41-19.5-54.3-43.9-13.8-89.8-15.1-122.9-21.2-33.2-6.1-37.3-19.3-37.3-32.9zm-30.2 2.5c-3.6.2-7.1.3-10.5.4 2.8 7.1-4.8 13.2-10.1 13.3-3.7 0-7-1.7-8.9-4.2l-.5 9.5c3.1 1.5 6.8 2.4 10.9 2.4 12.5 0 20.9-7.9 20.9-15.4 0-2.1-.6-4.1-1.8-6zM243.7 364l-1 20c11.9 6.7 25.5 11.5 41.6 15.5 6.2.9 14.6-2.2 13.2-9.4-21.2-3-35.3-7.7-46.1-17.6-2.8-2.6-5.3-5.4-7.7-8.5zm154.7.5c-5.3.4-10.5.6-15.5.6-.8 1.5-1.5 3-2.3 4.6-8.4 16.3-19.7 33-39.5 45.8l7.6 30.2c11.7 11.9 23.4 24 35.4 34.5-5.3-15.5-7.2-29.9-7.7-50.5l-.6-23.2 16 16.7c6.9 7.2 19.9 13.6 34.4 17.4 7.9 2.1 16.2 3.4 24.2 4-20.3-24.9-38.7-53.5-52-80.1z"}}]})(props);
};
