// THIS FILE IS AUTO GENERATED
var GenIcon = require('../lib').GenIcon
module.exports.GiPirateHook = function GiPirateHook (props) {
  return GenIcon({"tag":"svg","attr":{"viewBox":"0 0 512 512"},"child":[{"tag":"path","attr":{"d":"M378.095 20.322c34.035-.38 61.06 18.682 75.365 44.432 15.26 27.467 18.663 54.957 5.458 85.535-.823 1.904-3.993 14.39-3.395 21.108.48 5.396 3.53 27.773.846 29.18-1.433 1.14-11.05-18.545-14.056-23.332-5.136-8.18-12.894-18.784-8.16-29.746 9.785-22.657 7.77-49.972-3.422-70.12-11.193-20.146-29.84-33.194-57.29-30.815-29.632 2.567-46.207 11.872-57.257 24.796-11.05 12.926-16.824 30.83-20.016 51.652-6.383 41.642.04 92.605-22.4 128.912-.26.42-.543.825-.85 1.213l-34.538 43.814c-3.714.45-6.83.52-9.275.267-4.443-.46-6.53-1.636-8.043-3.213-1.874-1.953-3.33-6.086-3.67-12.584.***************.098.035l34.39-43.627c14.326-23.78 11.526-72.68 18.588-118.756 3.55-23.167 10.218-46.2 25.953-64.605 15.736-18.405 40.29-30.82 74.774-33.807 2.33-.202 4.63-.312 6.898-.338zM199.078 278.008c.035 0 .**************-.242 11.4 1.905 21.17 8.895 28.455 4.834 5.038 11.675 7.875 19.172 8.652 5.648.585 11.8.17 18.693-1.094 1.59 4.98 2.696 10.352 3.36 16.1 2.022 17.458-.235 37.965-4.944 58.556-5.345 23.368-13.753 46.676-21.968 66.2-59.667-12.71-112.163-55.15-150.262-115.952 16.522-13.195 37.502-28.216 59.393-40.012 18.782-10.12 38.147-17.76 55.256-20.11 4.276-.586 8.375-.865 12.298-.8zm-140.91 72.504c39.234 61.705 93.608 106.474 156.816 121.162-3.164 6.837-6.137 12.883-8.688 17.824-2.686 1.485-6.664 2.413-12.337 2.137-6.73-.327-15.344-2.327-24.822-5.842-18.955-7.03-41.36-19.988-61.728-35.492-20.368-15.503-38.77-33.628-50.168-50.366-10.84-15.92-14.417-29.478-11.256-38.434 3.198-3.048 7.347-6.81 12.183-10.988z"}}]})(props);
};
