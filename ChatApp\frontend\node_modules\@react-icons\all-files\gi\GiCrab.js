// THIS FILE IS AUTO GENERATED
var GenIcon = require('../lib').GenIcon
module.exports.GiCrab = function GiCrab (props) {
  return GenIcon({"tag":"svg","attr":{"viewBox":"0 0 512 512"},"child":[{"tag":"path","attr":{"d":"M269.03 60.03C168.74 59.907 68.5 103.35 19.564 202.563c58.543-9.985 113.65 6.082 165.75 34 70.184-39.588 144.73-44.868 214.5-18.875 10.514-41.038 42.6-63.99 89.468-74.5-54.013-53.032-137.15-83.052-220.25-83.156zm72.126 163.407c-11.402 0-20.594 9.247-20.594 21.094 0 11.85 9.193 21.096 20.594 21.096 11.4 0 20.594-9.246 20.594-21.094 0-11.846-9.192-21.093-20.594-21.093zm-82.812 4.188c-11.402 0-20.594 9.246-20.594 21.094 0 11.845 9.193 21.092 20.594 21.092 11.4 0 20.594-9.247 20.594-21.094 0-11.85-9.192-21.095-20.594-21.095zm201.47 4.188c-4.164.11-8.78 1.334-13.94 3.812-11.792 5.665-25.755 18.744-38.624 40.813L404 282l-6.344-1.063c-27.112-4.514-46.836-.19-64.062 11.813-13.355 9.305-25.297 23.88-36.594 43.375 21.91-4.96 44.527-8.67 68.5-8.313l1.625.032c7.183-5.527 14.684-12.313 22.75-20.813-5.452 13.003-10.846 26.15-17.938 37.91-.017.032-.044.06-.062.092-10.86 19.943-33.42 36.072-55.594 49.72 30.027 21.8 61.94 26.562 90 18.906 31.256-8.527 57.875-32.584 70.565-67.656 10.892-30.1 14.177-55.068 12.562-73.625-1.614-18.557-8.06-30.293-15.47-35.875-3.703-2.79-7.76-4.426-12.373-4.656-.577-.03-1.156-.047-1.75-.03zM52.31 268.406c-16.702.463-26.298 6.886-31.28 17.25-5.316 11.055-5.09 28.77 4.812 50.906 9.902 22.137 29.235 48.22 59.843 74.938 35.545 31.025 79.76 41.25 119.563 31.28 36.588-9.163 69.667-35.298 89.125-79.905-20.686-.45-39.67-1.264-56.656-6.28-.13-.03-.25-.065-.376-.095-30.142-7.197-48.195-22.108-71.22-44.906 17.366 9.184 32.465 14.25 46 16.594l.127-.063c25.762-15.026 38.305-20.79 59.5-27.156-48.484-31.7-90.278-26.67-129.22-3.284l-2.218 1.344h-5.344l-2.314-1.467c-31.643-20.31-58.067-28.794-76.906-29.157-1.177-.022-2.324-.03-3.438 0z","fillRule":"evenodd"}}]})(props);
};
