{"name": "uuidv4", "version": "6.2.13", "description": "uuidv4 creates v4 UUIDs.", "contributors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "jan-hend<PERSON>.g<PERSON><PERSON><PERSON><PERSON>@thenativeweb.io"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "hannes.leut<PERSON><PERSON>@thenativeweb.io"}], "private": false, "main": "build/lib/uuidv4.js", "types": "build/lib/uuidv4.d.ts", "dependencies": {"@types/uuid": "8.3.4", "uuid": "8.3.2"}, "devDependencies": {"assertthat": "6.4.0", "roboter": "12.7.1", "semantic-release-configuration": "2.0.7"}, "scripts": {}, "repository": {"type": "git", "url": "git://github.com/thenativeweb/uuidv4.git"}, "keywords": ["guid", "uuid"], "license": "MIT"}