// THIS FILE IS AUTO GENERATED
var GenIcon = require('../lib').GenIcon
module.exports.GiSpiralShell = function GiSpiralShell (props) {
  return GenIcon({"tag":"svg","attr":{"viewBox":"0 0 512 512"},"child":[{"tag":"path","attr":{"d":"M485.096 39.047c-7.14.375-49.73 5.767-52.11 23.037-9.77-2.646-19.678-1.8-26.85 3.115-5.393 3.694-8.374 9.102-9.01 15.17-11.383-2.674-22.25-1.52-29.87 4.1-6.4 4.722-9.476 11.842-9.45 19.932-4.608-1.094-9.15-1.7-13.51-1.777-.584-.01-1.163-.01-1.74-.002-8.644.127-16.51 2.363-22.69 6.922-6.807 5.02-10.638 12.188-11.67 20.422-22.614-6.658-44.65-5.202-59.692 5.894-12.258 9.043-17.78 22.947-17.025 38.652-46.313-12-87.766-.726-90.414 55.246-4.322 91.366-104.763 126.175-107.828 254.664 71.16-106.47 255.07-279.767 239.6-137.213-8.058 74.248-92.804 24.706-211.91 115.454 108.46-74.824 224.278-15.26 232.486-121.47 19.974 10.955 29.233 6.274 46.232-6.266 24.394-17.993 27.178-41.515 16.985-65.127 6.975-1.565 13.346-4.3 18.792-8.318 16.943-12.497 21.037-34.275 13.43-57.277 7.5-.54 14.307-2.75 19.787-6.79 11.34-8.366 14.457-22.676 10.042-37.99 7.16.034 13.692-1.72 18.775-5.47 9.79-7.222 11.822-20.045 6.822-33.39 4.467-.53 8.62-1.952 12.12-4.35 8.628-5.912 11.097-16.204 7.616-26.59 17.763-3.348 20.07-43.588 21.08-50.578z"}}]})(props);
};
