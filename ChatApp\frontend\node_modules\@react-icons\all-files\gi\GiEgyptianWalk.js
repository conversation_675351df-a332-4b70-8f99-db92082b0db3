// THIS FILE IS AUTO GENERATED
var GenIcon = require('../lib').GenIcon
module.exports.GiEgyptianWalk = function GiEgyptianWalk (props) {
  return GenIcon({"tag":"svg","attr":{"viewBox":"0 0 512 512"},"child":[{"tag":"path","attr":{"d":"M248.512 18.479c-32.578-.085-52.916 34.52-54.649 58.357l23.64 14.547 26.333-8.41s-.994 22.179-5.809 21.927c-25.344-.124-79.06 1.741-114.797 5.588-3.243 26.137-5.325 58.266-4.015 84.403l-42.358 9.47c5.398 7.571 28.207 18.895 54.94 16.56 7.804-25.385 20.65-61.55 19.463-85.552 22.023 4.867 44.19 8.627 66.969 7.447 3.798 22.326 3.662 43.358 10.376 63.83 22.296 6.157 44.447 14.398 63.29 20.788 7.68-27.1 11.071-54.355 15.066-83.452 0 0 81.216-.021 85.223.551 4.006.573-1.135-91.85-1.135-91.85C411.28 48.71 419.999 42 430.525 31.237c-20.892-5.236-37.465-3.409-56.656 1.44l-7.416 81.246c-21.052-5.688-59.138-10.703-90.572-9.717l-3.77-18.81c10.162 2.381 24.457 7.756 33.532 8.582 5.676-1.62 3.628-14.765 3.187-15.877-17.932-1.545-14.697.558-19.248-16.198l16.053.153c-7.84-26.946-34.732-43.519-57.123-43.576zm-15.155 208.525c-1.813 4.053-4.194 9.634-7.05 16.539-5.98 14.452-13.723 33.818-21.377 53.184-11.625 29.41-18.16 46.38-22.985 58.837l149.034-52.888-42.09-56.91-55.532-18.762zm103.456 93.049c-15.556 5.35-30.83 11.465-46.178 17.375 8.913 14.955 15.794 30.925 28.902 43.783-2.248 18.194-3.509 26.486 1.717 38.06 9.383 20.785 49.22 47.514 49.22 47.514-1.627 6.569-8.79 19.485 0 26.748h59.239c4.798-5.58 2.227-10.342 1.43-15.3-46.64 3.791-65.323-57.482-83.561-92.442-.228-22.753-4.511-44.493-10.77-65.738zm-112.739 40.011l-54.865 19.471c-7.346 8.974-16.394 12.54-22.52 21.28-16.725 23.86-27.963 61.48-38.632 78.415-1.998 4.348-.924 11.468.859 14.303h68.107c4.58 0-.572-12.586-.572-12.586-19.358.345-29.845-2.456-37.773-12.017 7.137-26.23 33.068-43.45 50.365-66.971 16.006-10.358 25.745-27.624 35.031-41.895z"}}]})(props);
};
