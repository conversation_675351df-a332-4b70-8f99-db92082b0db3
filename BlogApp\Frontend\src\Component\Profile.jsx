import React, { useState } from "react";
import styled from "styled-components";



import API from '../axios.js';

// import { useNavigate } from 'react-router-dom';



// import Cards from "./Cards";
import ActionAreaCard from "../Component/Cards";
import { useAuth } from "../Context/AuthProvider";
import { Popover ,Button,} from "@radix-ui/themes";
import UploadBlogPost from "../Pages/UploadBlogPost.jsx";
import Logout from "./Logout.jsx";
import RecipeReviewCard from "../Component/Cards";
import {Link} from 'react-router-dom'
const Profile = () => {


   const {authUser}=useAuth()


   const [popHover, setPopHover] = useState(false)
   console.log(authUser)
   


    // const navigate= useNavigate()

    


  return (
    <div>
      <div className=" bg-blue-300 px-3 py-3 "><Link to="/home">Backto home</Link></div>
      <Logout/>

      <div className="flex justify-center items-center flex-col bg-amber-50 p-3">
        <StyledWrapper>
          
          <div className="card">
            <div className="profileImage">
              <svg viewBox="0 0 128 128">
                <circle r={60} fill="transparent" cy={64} cx={64} />
                <circle r={48} fill="transparent" cy={64} cx={64} />
                <path
                  fill="#191919"
                  d="m64 14a32 32 0 0 1 32 32v41a6 6 0 0 1 -6 6h-52a6 6 0 0 1 -6-6v-41a32 32 0 0 1 32-32z"
                />
                <path
                  opacity={1}
                  fill="#191919"
                  d="m62.73 22h2.54a23.73 23.73 0 0 1 23.73 23.73v42.82a4.45 4.45 0 0 1 -4.45 4.45h-41.1a4.45 4.45 0 0 1 -4.45-4.45v-42.82a23.73 23.73 0 0 1 23.73-23.73z"
                />
                <circle r={7} fill="#fbc0aa" cy={65} cx={89} />
                <path
                  fill="#4bc190"
                  d="m64 124a59.67 59.67 0 0 0 34.69-11.06l-3.32-9.3a10 10 0 0 0 -9.37-6.64h-43.95a10 10 0 0 0 -9.42 6.64l-3.32 9.3a59.67 59.67 0 0 0 34.69 11.06z"
                />
                <path
                  opacity=".3"
                  fill="#356cb6"
                  d="m45 110 5.55 2.92-2.55 8.92a60.14 60.14 0 0 0 9 1.74v-27.08l-12.38 10.25a2 2 0 0 0 .38 3.25z"
                />
                <path
                  opacity=".3"
                  fill="#356cb6"
                  d="m71 96.5v27.09a60.14 60.14 0 0 0 9-1.74l-2.54-8.93 5.54-2.92a2 2 0 0 0 .41-3.25z"
                />
                <path
                  fill="#fff"
                  d="m57 123.68a58.54 58.54 0 0 0 14 0v-25.68h-14z"
                />
                <path
                  strokeWidth={14}
                  strokeLinejoin="round"
                  strokeLinecap="round"
                  stroke="#fbc0aa"
                  fill="none"
                  d="m64 88.75v9.75"
                />
                <circle r={7} fill="#fbc0aa" cy={65} cx={39} />
                <path
                  fill="#ffd8ca"
                  d="m64 91a25 25 0 0 1 -25-25v-16.48a25 25 0 1 1 50 0v16.48a25 25 0 0 1 -25 25z"
                />
                <path
                  fill="#191919"
                  d="m91.49 51.12v-4.72c0-14.95-11.71-27.61-26.66-28a27.51 27.51 0 0 0 -28.32 27.42v5.33a2 2 0 0 0 2 2h6.81a8 8 0 0 0 6.5-3.33l4.94-6.88a18.45 18.45 0 0 1 1.37 1.63 22.84 22.84 0 0 0 17.87 8.58h13.45a2 2 0 0 0 2.04-2.03z"
                />
                <path
                  style={{
                    fill: "none",
                    strokeLinecap: "round",
                    stroke: "#fff",
                    strokeMiterlimit: 10,
                    strokeWidth: 2,
                    opacity: ".1",
                  }}
                  d="m62.76 36.94c4.24 8.74 10.71 10.21 16.09 10.21h5"
                />
                <path
                  style={{
                    fill: "none",
                    strokeLinecap: "round",
                    stroke: "#fff",
                    strokeMiterlimit: 10,
                    strokeWidth: 2,
                    opacity: ".1",
                  }}
                  d="m71 35c2.52 5.22 6.39 6.09 9.6 6.09h3"
                />
                <circle r={3} fill="#515570" cy="62.28" cx={76} />
                <circle r={3} fill="#515570" cy="62.28" cx={52} />
                <ellipse
                  ry="2.98"
                  rx="4.58"
                  opacity=".1"
                  fill="#f85565"
                  cy="69.67"
                  cx="50.42"
                />
                <ellipse
                  ry="2.98"
                  rx="4.58"
                  opacity=".1"
                  fill="#f85565"
                  cy="69.67"
                  cx="77.58"
                />
                <g strokeLinejoin="round" strokeLinecap="round" fill="none">
                  <path strokeWidth={4} stroke="#fbc0aa" d="m64 67v4" />
                  <path
                    strokeWidth={2}
                    stroke="#515570"
                    opacity=".2"
                    d="m55 56h-9.25"
                  />
                  <path
                    strokeWidth={2}
                    stroke="#515570"
                    opacity=".2"
                    d="m82 56h-9.25"
                  />
                </g>
                <path
                  opacity=".4"
                  fill="#f85565"
                  d="m64 84c5 0 7-3 7-3h-14s2 3 7 3z"
                />
                <path
                  fill="#f85565"
                  d="m65.07 78.93-.55.55a.73.73 0 0 1 -1 0l-.55-.55c-1.14-1.14-2.93-.93-4.27.47l-1.7 1.6h14l-1.66-1.6c-1.34-1.4-3.13-1.61-4.27-.47z"
                />
              </svg>
            </div>
            <div className="textContainer">
             <p className="name">{authUser?.user?.name}</p>
              
              <p className="profile">{authUser?.user?.email}</p>

            </div>
          </div>
        </StyledWrapper>
        <div className=" bg-zinc-700 text-white p-2 rounded-lg m-2 w-52">
          {/* <Link to="/upload"> */}
            {" "}
            {/* <div className="text-center">Create Post</div> */}
            {/* <Popover.Root> */}
	{/* <Popover.Trigger> */}
		<Button onClick={()=>setPopHover(true)}>
		
			Add Post
		</Button>
	{/* </Popover.Trigger> */}

  {/* *?  uploadblogPost */}
  

	{/* <Popover.Content width="360px"> */}
		<div className='flex justify-center '>
    {popHover && <UploadBlogPost onclose={()=>setPopHover(false)}/>}
    </div>
		
	{/* </Popover.Content> */}

{/* </Popover.Root> */}

          {/* </Link> */}
        </div>
      </div>

      <RecipeReviewCard/>
      
        
    </div>
 
  );
};


const StyledWrapper = styled.div`
  .card {
    width: 210px;
    height: 280px;
    // background: rgb(39, 39, 39);
    border-radius: 12px;
    box-shadow: 0px 0px 30px rgba(0, 0, 0, 0.123);
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: flex-start;
    transition-duration: 0.5s;
  }

  .profileImage {
    // background: linear-gradient(to right,rgb(54, 54, 54),rgb(32, 32, 32));
    margin-top: 20px;
    width: 170px;
    height: 170px;
    border-radius: 50%;
    box-shadow: 5px 10px 20px rgba(0, 0, 0, 0.329);
  }

  .textContainer {
    width: 100%;
    text-align: left;
    padding: 20px;
    display: flex;
    flex-direction: column;
    gap: 10px;
  }

  .name {
    font-size: 0.9em;
    font-weight: 600;
    color: black;
    letter-spacing: 0.5px;
  }

  .profile {
    font-size: 0.84em;
    color: black;
    letter-spacing: 0.2px;
  }

  .card:hover {
    background-color: rgb(43, 43, 43);
    transition-duration: 0.5s;
    .name {
      font-size: 0.9em;
      font-weight: 600;
      color: white;
      letter-spacing: 0.5px;
    }
    .profile {
      font-size: 0.84em;
      color: rgb(190, 194, 194);
      letter-spacing: 0.2px;
    }
  }

  .form-container {
    width: 400px;
    background: linear-gradient(#212121, #212121) padding-box,
                linear-gradient(145deg, transparent 35%,#e81cff, #40c9ff) border-box;
    border: 2px solid transparent;
    padding: 32px 24px;
    font-size: 14px;
    font-family: inherit;
    color: white;
    display: flex;
    flex-direction: column;
    gap: 20px;
    box-sizing: border-box;
    border-radius: 16px;
  }

  .form-container button:active {
    scale: 0.95;
  }

  .form-container .form {
    display: flex;
    flex-direction: column;
    gap: 20px;
  }

  .form-container .form-group {
    display: flex;
    flex-direction: column;
    gap: 2px;
  }

  .form-container .form-group label {
    display: block;
    margin-bottom: 5px;
    color: #717171;
    font-weight: 600;
    font-size: 12px;
  }

  .form-container .form-group input {
    width: 100%;
    padding: 12px 16px;
    border-radius: 8px;
    color: #fff;
    font-family: inherit;
    background-color: transparent;
    border: 1px solid #414141;
  }

  .form-container .form-group textarea {
    width: 100%;
    padding: 12px 16px;
    border-radius: 8px;
    resize: none;
    color: #fff;
    height: 96px;
    border: 1px solid #414141;
    background-color: transparent;
    font-family: inherit;
  }

  .form-container .form-group input::placeholder {
    opacity: 0.5;
  }

  .form-container .form-group input:focus {
    outline: none;
    border-color: #e81cff;
  }

  .form-container .form-group textarea:focus {
    outline: none;
    border-color: #e81cff;
  }

  .form-container .form-submit-btn {
    display: flex;
    align-items: flex-start;
    justify-content: center;
    align-self: flex-start;
    font-family: inherit;
    color: #717171;
    font-weight: 600;
    width: 40%;
    background: #313131;
    border: 1px solid #414141;
    padding: 12px 16px;
    font-size: inherit;
    gap: 8px;
    margin-top: 8px;
    cursor: pointer;
    border-radius: 6px;
  }

  .form-container .form-submit-btn:hover {
    background-color: #fff;
    border-color: #fff;
  }
`;



  

export default Profile;
