// THIS FILE IS AUTO GENERATED
var GenIcon = require('../lib').GenIcon
module.exports.GiSnout = function GiSnout (props) {
  return GenIcon({"tag":"svg","attr":{"viewBox":"0 0 512 512"},"child":[{"tag":"path","attr":{"d":"M257.1 42.4c-50.7 0-103.2 19.37-144 58-5.6 4.2-8.9 10-10.9 16.5-11.22 1.1-21.49 5.3-30.23 11.8-12.79 9.6-22.51 23.6-29.86 40.1-14.72 33-20.24 76.2-17.46 117.2 2.78 41 13.56 80 35.07 104.3 10.75 12.1 24.71 20.5 40.98 22 8.3.8 17-.3 26.1-3.1 8.8 18.1 24.4 32.1 43.1 41.8 24.1 12.6 53.7 18.6 83.5 18.6 29.8 0 59.8-6 84.7-18.5 19.6-9.8 36.3-23.9 46.5-42.2 9.4 3.1 18.4 4.2 27 3.4 16.3-1.5 30.2-9.9 41-22C474.1 366 484.8 327 487.4 286c2.7-41.1-3-84.2-18-117.2-7.5-16.5-17.4-30.6-30.4-40.1-8.4-6.2-18.2-10.3-28.8-11.6-2-6.4-5.2-12.1-10.5-16.3-39.2-38.98-91.8-58.4-142.6-58.4zM99.56 136c-.15 11.1 1.74 22.7 5.24 33.2 3.9 11.7 9.4 22.3 19.2 28.1 4.9 3 11.4 4.3 17.5 2.6 5.8-1.5 10.9-5.3 15.5-10.4 8.9-8.1 24.5-17.1 38.5-21.3 7.1-2.1 13.7-3 18.6-2.6 4.4.4 7 1.6 8.8 3.4 6.5 15.5 4 27.8-.3 38.9-2.1 5.7-4.8 11-7.1 16-2.2 5-5.1 9.9-2.8 17.4 2.7 9.1 10 15 18 19 5.3 2.7 11.1 4.6 17.1 5.5v59.3c-43.4 18.5-73.1 36.9-103.8 55l-.2.2-.2.1c-16.6 10.9-30.1 14.3-41.2 13.3-11.04-1-20.31-6.3-28.69-15.8-3.9-4.4-7.48-9.7-10.73-15.8a15.53 15.53 0 0 0 2.66.2 15.53 15.53 0 0 0 15.26-15.8 15.53 15.53 0 0 0-15.79-15.3 15.53 15.53 0 0 0-12.24 6.3c-2.71-8.6-4.93-17.9-6.58-27.6a15.53 15.53 0 0 0 8.35 2.3 15.53 15.53 0 0 0 15.27-15.7 15.53 15.53 0 0 0-15.79-15.3 15.53 15.53 0 0 0-10.74 4.6c0-.4-.1-.7-.1-1.1-.85-12.6-.81-25.5.1-38.2a15.53 15.53 0 0 0 12.26 5.7 15.53 15.53 0 0 0 15.26-15.8 15.53 15.53 0 0 0-15.79-15.2 15.53 15.53 0 0 0-9.29 3.3c2.73-17.7 7.2-34.3 13.36-48.1 6.45-14.4 14.67-25.8 23.97-32.7 5.11-3.8 10.47-6.4 16.41-7.7zm313.24.3c5.4 1.4 10.4 3.9 15.1 7.4 9.5 7 17.9 18.3 24.5 32.8 6.2 13.7 10.8 30.2 13.6 47.8a15.53 15.53 0 0 0-9.2-3.2 15.53 15.53 0 0 0-15.8 15.3 15.53 15.53 0 0 0 15.3 15.8 15.53 15.53 0 0 0 12.3-5.7c.9 12.7 1 25.6.2 38.3 0 .7-.1 1.3-.1 2a15.53 15.53 0 0 0-11.8-5.7 15.53 15.53 0 0 0-15.8 15.3 15.53 15.53 0 0 0 15.3 15.8 15.53 15.53 0 0 0 9.6-3.2c-1.7 10.6-4.1 20.6-7 29.9a15.53 15.53 0 0 0-13.2-7.7 15.53 15.53 0 0 0-15.7 15.2 15.53 15.53 0 0 0 15.2 15.8 15.53 15.53 0 0 0 4.2-.5c-3.3 6.2-6.9 11.7-10.9 16.2-8.4 9.5-17.7 14.8-28.7 15.8-11.1 1-24.6-2.4-41.2-13.3l-.2-.1-.2-.1c-29.8-17.7-58.7-35.7-101.9-53.7v-60.7c5.9-1.1 11.6-3.2 16.6-6 7.5-4.2 14.1-10 16.6-18.5 2.3-7.4-.5-12-2.6-16.9-2-4.9-4.6-10.2-6.7-16-4-11.3-6.5-24.1-.7-39.5 1.8-1.7 4.4-2.9 8.8-3.3 5.2-.4 12.1.5 19.4 2.6 14.5 4.2 30.5 13.2 39.3 21.2 4.4 5.1 9.2 8.9 14.8 10.5 6 1.7 12.5.3 17.3-2.6 9.6-6 14.8-16.5 18.6-28.2 3.3-10.4 5.1-21.8 5-32.8zm-2.9 114.8a15.53 15.53 0 0 0-15.8 15.3 15.53 15.53 0 0 0 15.3 15.8 15.53 15.53 0 0 0 15.8-15.3 15.53 15.53 0 0 0-15.3-15.8zm-307.8.1A15.53 15.53 0 0 0 86.85 267a15.53 15.53 0 0 0 15.85 15.2 15.53 15.53 0 0 0 15.2-15.8 15.53 15.53 0 0 0-15.8-15.2zm8 50A15.53 15.53 0 0 0 94.87 317a15.53 15.53 0 0 0 15.83 15.3 15.53 15.53 0 0 0 15.2-15.8 15.53 15.53 0 0 0-15.8-15.3zm290.7 0a15.53 15.53 0 0 0-15.7 15.3 15.53 15.53 0 0 0 15.2 15.8 15.53 15.53 0 0 0 15.8-15.3 15.53 15.53 0 0 0-15.3-15.8zm-145.5 41.1c44 17.9 72.6 35.7 103.6 53.9l-.4-.2c3 2 5.9 3.7 8.8 5.3-8 13.8-21 24.8-37.5 33.1-21.7 10.8-49.2 16.5-76.4 16.5-27.2 0-54.2-5.7-74.9-16.5-15.7-8.1-27.7-18.9-34.5-32.5 3.2-1.8 6.5-3.7 9.8-5.9l-.4.3c31.1-18.3 59.8-36.1 101.9-54z"}}]})(props);
};
