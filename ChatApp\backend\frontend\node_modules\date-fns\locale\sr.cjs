"use strict";
exports.sr = void 0;
var _index = require("./sr/_lib/formatDistance.cjs");
var _index2 = require("./sr/_lib/formatLong.cjs");
var _index3 = require("./sr/_lib/formatRelative.cjs");
var _index4 = require("./sr/_lib/localize.cjs");
var _index5 = require("./sr/_lib/match.cjs");

/**
 * @category Locales
 * @summary Serbian cyrillic locale.
 * @language Serbian
 * @iso-639-2 srp
 * <AUTHOR> [@rogyvoje](https://github.com/rogyvoje)
 */
const sr = (exports.sr = {
  code: "sr",
  formatDistance: _index.formatDistance,
  formatLong: _index2.formatLong,
  formatRelative: _index3.formatRelative,
  localize: _index4.localize,
  match: _index5.match,
  options: {
    weekStartsOn: 1 /* Monday */,
    firstWeekContainsDate: 1,
  },
});
