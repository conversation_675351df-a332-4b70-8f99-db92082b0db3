// THIS FILE IS AUTO GENERATED
import { GenIcon } from '../lib';
export function FcConferenceCall (props) {
  return GenIcon({"tag":"svg","attr":{"version":"1","viewBox":"0 0 48 48","enableBackground":"new 0 0 48 48"},"child":[{"tag":"circle","attr":{"fill":"#FFA726","cx":"12","cy":"21","r":"5"}},{"tag":"g","attr":{"fill":"#455A64"},"child":[{"tag":"path","attr":{"d":"M2,34.7c0,0,2.8-6.3,10-6.3s10,6.3,10,6.3V38H2V34.7z"}},{"tag":"path","attr":{"d":"M46,34.7c0,0-2.8-6.3-10-6.3s-10,6.3-10,6.3V38h20V34.7z"}}]},{"tag":"circle","attr":{"fill":"#FFB74D","cx":"24","cy":"17","r":"6"}},{"tag":"path","attr":{"fill":"#607D8B","d":"M36,34.1c0,0-3.3-7.5-12-7.5s-12,7.5-12,7.5V38h24V34.1z"}},{"tag":"circle","attr":{"fill":"#FFA726","cx":"36","cy":"21","r":"5"}},{"tag":"circle","attr":{"fill":"#FFA726","cx":"12","cy":"21","r":"5"}},{"tag":"circle","attr":{"fill":"#FFA726","cx":"36","cy":"21","r":"5"}}]})(props);
};
