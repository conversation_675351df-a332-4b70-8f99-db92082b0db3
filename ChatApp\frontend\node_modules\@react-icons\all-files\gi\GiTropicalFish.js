// THIS FILE IS AUTO GENERATED
var GenIcon = require('../lib').GenIcon
module.exports.GiTropicalFish = function GiTropicalFish (props) {
  return GenIcon({"tag":"svg","attr":{"viewBox":"0 0 512 512"},"child":[{"tag":"path","attr":{"d":"M162.624 52.61C97.727 52.9 43.2 71.024 20.26 77.065c180.533-16.45 207.453 29.552 202.45 80.002-18.22 37.848-25.254 55.3-27.918 70.57-.1-.087-.194-.175-.297-.263-.137 1.532-.252 3.068-.353 4.604-1.174 9.332-.916 18.495-.844 31.853.232 42.953 20.353 78.605 35.563 123.858-19.41 34.982-62.905 40.407-111.138 40.017 15.68 4.238 57.39 35.154 183.93-18.266-18.39 38.19-101.927 66.29-104.35 80.708 95.706-8.18 156.83-50.263 172.442-113.7 26.875-16.655 51.02-37.67 71.22-88.49 4.977-15.1 26.364-16.513 42.844-20.37 8.092-1.892 12.803-17.88 0-19.664-16.856-3.743-41.087-1.758-50.568-18.963C359.963 85.33 251.557 52.22 162.624 52.61zM68.11 170.79c-6.35-.04-11.513.988-14.995 3.296 52.984 29.347 72.777 42.044 73.205 52.064.306 18.476-56.007 23.49-57.59 33.362-.602 3.75 45.222 28.75 51.972 38.277 8.175 14.697-18.264 35.887-52.675 58.995 7.18 11.277 72.52-13.598 116.69-36.805-7.365-34.743-12.074-70.132-6.827-105.548-32.393-23.226-82.256-43.47-109.78-43.643zm309.805 34.105c17.18 0 31.3 14.12 31.3 31.3s-14.12 31.3-31.3 31.3-31.298-14.12-31.298-31.3 14.12-31.3 31.298-31.3zm-71.578 12.435c4.01.062 8.036.29 12.076.705l.487.05.478.102c7.506 1.608 10.558 7.368 12.705 12.56 2.147 5.19 3.38 11.144 4.006 17.52 1.25 12.75.433 26.886-5.97 37.98-3.817 6.613-9.487 8.915-16.46 11.564-6.97 2.65-15.513 4.687-25.05 5.833-19.078 2.29-42.133 1.066-62.616-8.352l-19.9-9.147 20.58-7.487c9.96-3.622 17.247-7.86 22.49-11.594-8.457-3.524-18.93-9.306-28.448-18.88l-10.3-10.36 13.886-4.54c26.63-8.707 53.964-16.39 82.035-15.952zm71.578 5.565c-7.45 0-13.298 5.85-13.298 13.3 0 5.78 3.525 10.582 8.582 12.45a13.773 13.008 0 0 1-1.802-6.397 13.773 13.008 0 0 1 13.773-13.008 13.773 13.008 0 0 1 4.546.746c-2.2-4.245-6.607-7.09-11.8-7.09zm-71.863 12.443c-19.507-.27-39.51 4.09-59.924 10.098 11.728 7.197 22.06 8.86 22.06 8.86l14.735 2.25-8.855 11.99s-3.546 4.686-10.194 10.02c-2.56 2.052-5.814 4.29-9.447 6.55 10.906 1.81 22.13 1.855 32.033.665 8.256-.99 15.592-2.804 20.81-4.786 5.216-1.982 8.043-5.09 7.26-3.736 2.872-4.975 4.633-17.148 3.645-27.227-.494-5.038-1.58-9.627-2.724-12.393-.48-1.16-.863-1.54-1.085-1.813-2.764-.23-5.53-.438-8.313-.476z"}}]})(props);
};
