// THIS FILE IS AUTO GENERATED
var GenIcon = require('../lib').GenIcon
module.exports.GiPoliceBadge = function GiPoliceBadge (props) {
  return GenIcon({"tag":"svg","attr":{"viewBox":"0 0 512 512"},"child":[{"tag":"path","attr":{"d":"m 237.59142,23.950128 c -15.48769,9.432598 -34.17058,12.318766 -44.1673,12.318766 -10.00366,0 -23.79926,-0.194925 -43.9503,-11.829131 -8.35467,-4.823577 -9.86134,-5.708699 -14.34686,-5.708699 -12.91322,0 -71.396413,58.375934 -72.363034,66.334414 -0.966622,7.958452 32.120593,28.163932 33.71248,57.150952 1.591887,28.98703 -19.183619,61.80636 -27.756183,87.39706 -17.765566,52.9498 -22.581636,58.94861 -22.581622,85.85861 3.91678,77.65208 56.609859,131.68607 130.916129,144.25034 31.84641,4.83094 44.87352,9.46439 59.11229,21.0237 5.93519,4.34906 12.47815,10.68983 19.74398,11.52033 l 0,0.0278 c 0.0299,-0.003 0.0591,-0.01 0.089,-0.0142 0.0299,0.003 0.0591,0.0114 0.089,0.0142 l 0,-0.0278 c 7.26582,-0.8305 13.80879,-7.17127 19.74397,-11.52033 14.23877,-11.55931 27.26588,-16.19276 59.11229,-21.0237 74.30628,-12.56427 126.99934,-66.59826 130.91614,-144.25034 0,-26.91 -4.81606,-32.90881 -22.58162,-85.85861 -8.57257,-25.5907 -29.34807,-58.41003 -27.75619,-87.39706 1.59189,-28.98702 34.6791,-49.1925 33.71248,-57.150952 -0.96662,-7.95848 -59.44983,-66.334414 -72.36304,-66.334414 -4.48553,0 -5.99218,0.885122 -14.34685,5.708699 -20.15104,11.634206 -33.94664,11.829131 -43.95031,11.829131 -9.99671,0 -28.6796,-2.886168 -44.16729,-12.318766 -4.52967,-2.74526 -10.03057,-5.344254 -18.40861,-5.344254 -6.60122,0 -13.34704,2.294383 -18.4086,5.344254 z"}}]})(props);
};
