// THIS FILE IS AUTO GENERATED
var GenIcon = require('../lib').GenIcon
module.exports.GrDos = function GrDos (props) {
  return GenIcon({"tag":"svg","attr":{"viewBox":"0 0 24 24"},"child":[{"tag":"path","attr":{"fillRule":"evenodd","d":"M0,4.54603675 L4.93388435,4.42224753 C5.89173496,4.40127916 6.77758573,4.50447894 7.67366806,4.81357302 L7.67366806,4.98877266 C6.90124863,5.64776074 6.34520769,6.43066436 5.84032455,7.30628357 L2.84298349,7.32712563 L2.8634466,15.8248762 L4.63514814,15.9481601 L6.44802853,15.9275707 C7.52954204,15.9169602 8.52857152,14.1868164 8.62153974,13.2494289 L8.88907602,10.2935194 C9.00263368,8.99562738 9.75446367,7.9965979 10.9183349,7.46076745 C11.6497018,8.68653329 11.9689011,10.0461936 11.9689011,11.4778537 C11.9689011,15.9794864 9.17795963,18.8219646 4.77927415,18.6572491 L0,18.4719443 L0,4.54603675 Z M20.7035438,8.71765744 C20.4667022,7.34751295 19.3235467,6.66780912 17.9531496,6.66780912 C17.0163936,6.66780912 15.4814074,7.0793451 15.4301233,8.25382683 C15.4193865,8.31584776 15.4301233,8.36700554 15.4505864,8.41904754 L16.0066274,10.0154231 C16.1200587,10.344854 16.1406481,10.7052322 16.1406481,11.0556315 C16.1406481,11.4570622 16.0891114,11.8588719 16.0378273,12.2606816 C14.0602315,11.7045143 12.6693712,10.74679 12.6693712,8.52174206 C12.6693712,5.52427469 15.0900819,4.12305658 17.8504551,4.12305658 C20.9199223,4.12305658 23.3199172,5.55547462 23.5158326,8.71765744 L20.7035438,8.71765744 Z M12.4634769,14.0839198 L15.2136185,14.1046356 C15.5019968,15.8658529 16.6658681,16.3501466 18.3963907,16.3501466 C19.4779043,16.3501466 21.0949956,16.0927156 21.0949956,14.7123396 C21.0949956,13.9913305 20.5284705,13.6202155 19.9517138,13.3011425 L20.0957135,10.5511273 C22.2896878,11.1480944 24,11.9723032 24,14.5472452 C24,17.5965019 21.0949956,18.8839097 18.4169802,18.8839097 C16.5012789,18.8839097 14.0189263,18.3276162 12.9989284,16.5149884 C12.6797291,15.9585685 12.3812455,15.2376858 12.3812455,14.5886767 C12.3812455,14.5372662 12.3912244,14.4857295 12.4017086,14.4343191 L12.4634769,14.0839198 Z M19.7047038,11.4570369 C19.7047038,13.033202 19.282431,14.506041 18.561422,15.8864171 C18.3757382,15.8965223 18.1904333,15.9169854 18.0050021,15.9169854 C17.4075297,15.9169854 15.9552801,15.515681 15.9552801,14.7844405 C15.9552801,14.6610302 15.9757432,14.537241 16.0170484,14.4236833 L16.5627314,12.600824 C16.6762891,12.2401932 16.6351102,11.8074362 16.6351102,11.4471843 C16.6351102,10.0357347 15.996459,9.15001021 15.9447959,8.18180172 C15.9141012,7.50235052 17.2016354,7.18251962 17.6549818,7.18251962 C17.9639495,7.18251962 18.2726647,7.18251962 18.5816325,7.2133406 C19.3234835,8.52184311 19.7047038,9.9432717 19.7047038,11.4570369 Z M10.6096071,17.0296989 L11.2583637,16.1030482 L12.3397509,16.1232587 C12.8549919,17.2460774 13.6995375,18.0083916 14.7295143,18.6676323 C13.9775579,18.9149581 13.2359595,19.0383683 12.4530559,19.0383683 C11.3097741,19.0383683 10.2383658,18.780811 9.17769436,18.3791276 L10.6096071,17.0296989 Z M12.5356662,6.70871009 C12.3705719,6.69847853 12.2058564,6.68837329 12.0412673,6.68837329 C10.3003867,6.68837329 8.71411639,8.21249639 8.50822209,9.92242964 L8.05487567,13.640906 C7.99310738,14.1663786 7.53976097,14.7018301 7.19971958,15.0830504 C6.91159387,15.3917655 6.5716788,15.4536601 6.14927969,15.4536601 L6.09774295,15.4536601 C5.56216513,14.2179153 5.28401835,12.9406128 5.28401835,11.591184 C5.28401835,7.35784556 7.65306599,4 12.0617304,4 C12.9269917,4 13.7508216,4.16471544 14.5644199,4.39132549 C13.6995375,5.02985046 13.019581,5.74075423 12.5356662,6.70871009 Z"}}]})(props);
};
