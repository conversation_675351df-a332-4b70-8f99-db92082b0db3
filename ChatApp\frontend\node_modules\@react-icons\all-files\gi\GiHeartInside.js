// THIS FILE IS AUTO GENERATED
var GenIcon = require('../lib').GenIcon
module.exports.GiHeartInside = function GiHeartInside (props) {
  return GenIcon({"tag":"svg","attr":{"viewBox":"0 0 512 512"},"child":[{"tag":"path","attr":{"d":"M303.25 20.813c-19.18.348-39.962 9.117-56.5 25.656-22.422 22.42-30.52 52.633-22.75 76.093-65.983-30.33-59.733-32.19-123.344-73-10.072-6.463-19.472-9.42-27.844-9.813-.872-.04-1.743-.045-2.593-.03-12.75.2-22.962 6.374-29.532 14.936C29.474 69.27 28.334 90.84 51.656 109.094c31.026 24.285 58.81 41.01 79 59.437 20.19 18.43 32.648 40.622 28.344 70.064-3.158 21.608-13.658 37.998-26.438 51.47-12.78 13.47-27.778 24.454-41.468 36.655-27.38 24.4-50.33 51.783-45.063 114.28 3.328 39.483 34.19 55.117 59.69 52.375 12.748-1.37 23.477-7.368 29.374-17.5 5.896-10.132 7.696-25.406-1.03-47.72-7.595-19.415 3.133-40.834 18.374-57.092 15.24-16.26 36.798-28.82 58.843-25 6.177 1.07 11.454 4.72 15.064 9.156 3.61 4.434 5.964 9.587 7.937 15.217 3.948 11.262 6.27 24.706 9.126 38.594 5.712 27.78 13.663 55.97 33.063 68.47 37.963 24.468 75.257 17.39 91.905.438 8.324-8.477 11.914-18.828 9.125-31.125-2.79-12.298-12.677-27.19-34.25-41.875-23.664-16.11-32.655-48.258-33.844-80.094-1.19-31.836 5.287-64.078 20.125-84.03 6.88-9.25 17.516-13.15 29.626-17.44 12.11-4.288 26.207-8.474 40.75-14.686 29.086-12.426 59.667-32.198 79.156-76.782 17.078-39.068 3.342-64.286-15.312-73.47-9.327-4.59-20.13-5.16-30.438-.655-10.307 4.507-20.43 14.22-27.437 31.782-13.14 32.934-39.188 51.677-70.406 56.407-8.096 1.225-16.526 1.577-25.22 1.155 7.504-4.07 14.71-9.367 21.25-15.906 29.4-29.402 34.242-72.228 10.844-95.626-10.237-10.237-24.176-15.053-39.094-14.782zm-87.688 129.343c15.512-.115 31.634 10.905 35.813 30.75 28.278-25.368 67.325-2.347 56.72 33.906-10.762 36.783-76.172 43.73-88.064 61.97 1.293-23.756-45.864-63.673-34.655-102.063 4.877-16.708 17.335-24.468 30.188-24.564z","fillRule":"evenodd"}}]})(props);
};
