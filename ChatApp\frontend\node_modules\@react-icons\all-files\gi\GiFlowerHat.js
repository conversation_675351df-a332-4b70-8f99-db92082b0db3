// THIS FILE IS AUTO GENERATED
var GenIcon = require('../lib').GenIcon
module.exports.GiFlowerHat = function GiFlowerHat (props) {
  return GenIcon({"tag":"svg","attr":{"viewBox":"0 0 512 512"},"child":[{"tag":"path","attr":{"d":"M256 29.59c-45.5 0-91.7 19.17-126.2 47.91-32.32 26.9-53.88 61.9-56.52 96.1 2.48.6 4.98 1.2 7.48 1.8 61.74 14.2 123.34 20.9 185.04 20.1-1.8-4.2-2.6-8.9-1.8-13.8 1.6-8.9 8.2-15.4 15.8-18.9 7.6-3.6 16.8-4.7 26.1-3 .4.1.8.2 1.3.3-.3-1.1-.4-2.3-.6-3.4-1.4-9.7 0-18.9 3.9-26.6 3.9-7.6 10.9-14.1 19.9-15.3 9-1.3 17.5 3 23.3 9.2 5.4 6 9.2 13.7 10.8 22.6.2-.3.5-.6.7-.9 6.2-7.6 13.9-12.8 22.2-15.2 8.2-2.4 17.6-1.7 24.7 4.1 7.1 5.7 9.8 14.8 9.2 23.3-.6 7.3-3.2 14.6-7.8 21.3 2.2-.5 4.5-.9 6.7-1.4 1.7-.3 3.4-.7 5.1-1.1 1.1-.2 2.3-.5 3.4-.7 3.3-.8 6.7-1.6 10-2.4-2.6-34.2-24.2-69.2-56.5-96.1-34.5-28.74-80.7-47.91-126.2-47.91zm77.7 102.91c-.2 0-.5.1-.8.1-2.2.3-4.4 1.8-6.4 5.6-1.9 3.9-3 9.6-2.1 16 .7 5.1 2.6 9.6 4.9 12.8 5.1-3.1 10.9-4.9 17.2-5 .7-3.2 1-7.1.5-11-.9-6.3-3.6-11.5-6.4-14.7-2.6-2.8-4.9-3.8-6.9-3.8zm62.7 14.6c-1.2 0-2.5.2-4.1.6-4.1 1.3-9.1 4.4-13.1 9.4-4 4.9-6 10.4-6.4 14.7v1.3c2.3 2.4 4.3 5.2 5.9 8.2 1.4.2 3.1 0 5.2-.6 4-1.2 9.1-4.4 13-9.3 4.1-5 6-10.4 6.4-14.7.3-4.3-.8-6.8-2.5-8.2-1.1-.8-2.5-1.4-4.4-1.4zm-98 30c-4.3-.1-8.2.7-11 1.9-3.8 1.8-5.3 3.9-5.6 5.9-.4 1.9.3 4.4 3.2 7.3 2.9 2.9 7.9 5.7 13.9 6.8 4.4.8 8.7.5 12.1-.4.2-5.6 1.6-11 3.9-15.8-3-2.3-7.2-4.4-12.1-5.3-1.5-.2-3-.4-4.4-.4zm48.8 2.9c-9.8 0-18.2 8.5-18.2 19.8s8.4 19.8 18.2 19.8c9.8 0 18.2-8.5 18.2-19.8S357 180 347.2 180zM68.76 191c-6.58 12.8-27.22 51.2-52.15 80.6 164.29 42.5 314.49 42.5 478.79 0-24.9-29.4-45.6-67.8-52.2-80.6-4.9 1.2-9.8 2.3-14.6 3.4 4.4 5.3 7.1 12 6.2 19.3-1 9-7.2 16.2-14.7 20.2-7.6 4.1-16.7 5.8-26.5 4.7-2.9-.3-5.6-.8-8.3-1.6.8 2.8 1.5 5.7 1.8 8.8 1.1 9.8-.6 18.9-4.7 26.5-4.1 7.5-11.3 13.7-20.3 14.7-9.1 1-17.4-3.5-23.1-9.9-4.8-5.6-8.2-12.6-9.8-20.6-7 6.8-15.4 11-23.9 12.4-8.4 1.3-17.7-.6-24-7.1-6.3-6.6-7.9-15.9-6.2-24.3 1.7-8.2 6.1-16.2 12.8-22.9-1.4-.5-2.7-1-4-1.6-71.7 2.5-143.5-4.8-215.14-22zm325.04 6.9c-4 .1-7.6 1-10.4 2.2-.1 5.1-1.1 10-2.9 14.5l.2.2c3.3 2.7 8.7 5.3 14.9 6 6.4.7 12.1-.6 15.9-2.6 3.8-2.1 5.2-4.4 5.5-6.5.2-2.2-.7-4.7-3.9-7.6-3.3-2.8-8.6-5.3-15-6-.8-.1-1.5-.1-2.3-.2zm-77.5 21.5c-.4 0-.9 0-1.3.1-4.2.7-9.5 3.2-14.1 7.6-4.6 4.4-7.3 9.6-8.2 13.8-.8 4.3.1 6.8 1.6 8.4 1.5 1.6 4 2.5 8.3 1.8 4.2-.7 9.5-3.2 14.1-7.6 4.6-4.4 7.3-9.6 8.2-13.8v-.2c-3.4-2.8-6.4-6.2-8.6-10.1zm47.3 14.1c-4.7 2.5-10.1 4-15.8 4.1-1.2 3.5-1.8 8-1.3 12.7.7 6.4 3.2 11.7 6 15 2.9 3.2 5.4 4.1 7.6 3.9 2.2-.3 4.4-1.7 6.5-5.5 2-3.8 3.3-9.5 2.6-15.9-.7-5.9-2.9-11-5.6-14.3zM148 315c7.4 52.7 17.9 91.2 30.6 118.1 9.2 19.7 19.6 33.4 31.6 41.5 12.1 8.1 26.1 9.8 38.8 5.6 25.4-8.4 46-36.2 62.4-74.3 10.9-25.1 19.6-55.1 25.2-88-6.2.6-12.3 1-18.5 1.5-5.4 29.8-13.5 56.9-23.2 79.4-15.4 35.8-35.4 59-51.5 64.3-8.1 2.7-15 2-23.1-3.4-8.1-5.5-17.1-16.4-25.4-34.2-11.2-23.6-21.1-59.2-28.4-108.4-6.1-.7-12.3-1.3-18.5-2.1z"}}]})(props);
};
