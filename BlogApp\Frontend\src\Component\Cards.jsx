// import * as React from 'react';
// import Card from '@mui/material/Card';
// import CardContent from '@mui/material/CardContent';
// import CardMedia from '@mui/material/CardMedia';
// import Typography from '@mui/material/Typography';
// import CardActionArea from '@mui/material/CardActionArea';
// // import { useAuth } from '../Context/AuthProvider';
// import API from '../axios';
// import{usePost } from '../Context/PostProvider';
// import { useEffect } from 'react';
// // import { useEffect } from "react";




//  function ActionAreaCard() {
// //  const {authUser}= useAuth()

//        const   {userPost,setUserPost} = usePost()



//   useEffect( () => {

//    const userPost= async ()=>{
//      const res= await API.get('/api/user/userPosts', {
//         headers:{
//             'Content-Type':'application/json',
            
           
//         }
//     })

//     // console.log(res)

//     setUserPost(res.data)
//    }

// userPost()
  
// }, [userPost])



//   return (


//     <div className=' grid grid-cols-4'>
//      {    userPost.map((post ,id)=>(

//       <Card sx={{ maxWidth: 345 }} key={id}   className='m-2 '>
//       <CardActionArea >
//        <div className=''> 
//         <CardMedia
//           component="img"
//           height="140"
//           image="https://images.unsplash.com/photo-1532614338840-ab30cf10ed36?auto=format&fit=crop&w=318"
//           alt="green iguana"
//         /></div>
//         <CardContent>
//           <Typography gutterBottom variant="h5" component="div">
//             {post.title}  
//           </Typography>
//           <Typography variant="body2" sx={{ color: 'text.secondary' }}>
//            {post.content}
//           </Typography>
//         </CardContent>
//       </CardActionArea>
//     </Card>
//      ))}
//     </div>

    
//   );
// }

// export default ActionAreaCard;


import * as React from 'react';
import { styled } from '@mui/material/styles';
import Card from '@mui/material/Card';
import CardHeader from '@mui/material/CardHeader';
import CardMedia from '@mui/material/CardMedia';
import CardContent from '@mui/material/CardContent';
import CardActions from '@mui/material/CardActions';
import Collapse from '@mui/material/Collapse';
import Avatar from '@mui/material/Avatar';
import IconButton from '@mui/material/IconButton';
import Typography from '@mui/material/Typography';
import { red } from '@mui/material/colors';
import FavoriteIcon from '@mui/icons-material/Favorite';
import ShareIcon from '@mui/icons-material/Share';
import ExpandMoreIcon from '@mui/icons-material/ExpandMore';
import MoreVertIcon from '@mui/icons-material/MoreVert';

import API from '../axios';
import{usePost } from '../Context/PostProvider';
import { useEffect } from 'react';
import { useAuth } from '../Context/AuthProvider';
const ExpandMore = styled((props) => {
  const { expand, ...other } = props;
  return <IconButton {...other} />;
})(({ theme }) => ({
  marginLeft: 'auto',
  transition: theme.transitions.create('transform', {
    duration: theme.transitions.duration.shortest,
  }),
  variants: [
    {
      props: ({ expand }) => !expand,
      style: {
        transform: 'rotate(0deg)',
      },
    },
    {
      props: ({ expand }) => !!expand,
      style: {
        transform: 'rotate(180deg)',
      },
    },
  ],
}));

 function RecipeReviewCard() {
  

const {authUser} =useAuth()
// console.log(authUser)
    const   {userPost,setUserPost} = usePost()



  useEffect( () => {

   const userPost= async ()=>{
     const res= await API.get('/api/user/userPosts', {
        headers:{
            'Content-Type':'application/json',
            
           
        }
    })

    // console.log(res)

    setUserPost(res.data)
   }

userPost()
  
}, [userPost])

  return (
  <div className='grid grid-cols-4 '>
    {userPost.map((post, id) => (
    <Card  key={id} sx={{ maxWidth: 345 }} className='m-2  '>

      
        <React.Fragment key={id}>
          <CardHeader
            avatar={
              <div className='flex items-center justify-between gap-3'>
                <Avatar sx={{ bgcolor: red[500] }} aria-label="recipe">
                {authUser.name}
              </Avatar>
              <h3 className='text-sm capitalize'>{authUser.user.name}</h3>
              </div>
            }
            action={
              <IconButton aria-label="settings">
                <MoreVertIcon />
              </IconButton>
            }
            // title={post.title}
            subheader={post.date }
          />
          <CardMedia
            component="img"
            height="194"
            image="https://images.unsplash.com/photo-1532614338840-ab30cf10ed36?auto=format&fit=crop&w=318"
            alt="Post image"
          />
          <CardContent>
            <h2>{post.title}</h2>
            <Typography variant="body2" color="text.secondary">
              {post.content}
            </Typography>
          </CardContent>

          <CardActions disableSpacing>
            <IconButton aria-label="add to favorites">
              <FavoriteIcon />
            </IconButton>
            <IconButton aria-label="share">
              <ShareIcon />
            </IconButton>

            
          </CardActions>
          
        </React.Fragment>
  
      
    </Card>
      ))}
    </div>
  );
}

export default RecipeReviewCard;