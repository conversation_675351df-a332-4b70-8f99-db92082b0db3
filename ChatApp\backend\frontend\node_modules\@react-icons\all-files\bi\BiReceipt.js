// THIS FILE IS AUTO GENERATED
var GenIcon = require('../lib').GenIcon
module.exports.BiReceipt = function BiReceipt (props) {
  return GenIcon({"tag":"svg","attr":{"viewBox":"0 0 24 24"},"child":[{"tag":"path","attr":{"d":"M21,11h-3V4c0-0.553-0.447-1-1-1H3C2.447,3,2,3.447,2,4v14c0,1.654,1.346,3,3,3h14c1.654,0,3-1.346,3-3v-6 C22,11.447,21.553,11,21,11z M5,19c-0.552,0-1-0.448-1-1V5h12v13c0,0.351,0.061,0.688,0.171,1H5z M20,18c0,0.552-0.448,1-1,1 s-1-0.448-1-1v-5h2V18z"}},{"tag":"path","attr":{"d":"M6 7H14V9H6zM6 11H14V13H6zM11 15H14V17H11z"}}]})(props);
};
