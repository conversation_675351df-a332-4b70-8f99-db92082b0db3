// THIS FILE IS AUTO GENERATED
var GenIcon = require('../lib').GenIcon
module.exports.GiBread = function GiBread (props) {
  return GenIcon({"tag":"svg","attr":{"viewBox":"0 0 512 512"},"child":[{"tag":"path","attr":{"d":"M439.037 70.988c-.863.015-1.87.09-3.025.256-4.62.668-11.12 2.598-18.8 5.764-15.356 6.332-35.49 17.488-58.085 32.056-10.66 6.874-21.892 14.53-33.48 22.78 10.542 1.434 21.866 4.025 32.61 7.295 13.316 4.05 25.552 8.98 34.42 14.663 4.432 2.842 8.204 5.667 10.84 10.35 1.316 2.34 2.297 5.443 1.917 8.794-.38 3.352-2.178 6.43-4.3 8.51-8.985 8.797-21.99 10.87-35.734 10.88-13.745.01-28.705-2.57-42.283-6.702-13.578-4.132-25.66-9.55-34.033-16.967-1.918-1.698-3.696-3.562-5.182-5.647-12.937 10.092-26.03 20.69-39.058 31.636 13.723 1.595 30.11 5.017 45.906 9.217 15.972 4.247 31.2 9.237 42.393 14.174 5.595 2.468 10.117 4.77 13.834 7.762 1.858 1.494 3.706 3.09 5.08 6.24 1.373 3.147.824 8.837-1.67 11.794l-.063.074-.064.072c-9.658 10.965-24.988 14.673-41.48 15.645-16.493.97-34.706-1.22-51.31-5.46-16.602-4.24-31.486-10.272-41.71-18.745-3.056-2.533-5.797-5.367-7.832-8.655-11.588 10.454-22.92 21.07-33.858 31.705 3.804.238 7.784.764 11.934 1.514 13.726 2.48 29.222 7.414 43.584 13.4 14.362 5.984 27.44 12.87 36.582 20.155 4.57 3.642 8.357 7.21 10.637 12.444 1.14 2.617 1.822 5.92 1.12 9.33-.705 3.412-2.815 6.448-5.245 8.46-10.528 8.725-25.07 9.825-40.282 8.405-15.21-1.42-31.55-5.902-46.17-11.995-14.62-6.092-27.44-13.526-35.783-22.62-2.49-2.712-4.678-5.668-6.158-8.98-12.3 12.98-23.7 25.79-33.927 38.192 2.974 1.324 6.326 2.778 10.225 4.416 10.34 4.345 23.245 9.548 35.516 14.53 12.27 4.98 23.877 9.728 31.904 13.294 4.013 1.784 6.97 3.142 9.402 4.598.608.364 1.188.723 1.944 1.328.755.605 2.044.798 3.318 4.643.637 1.922.535 5.66-1.16 8.107-1.435 2.072-2.972 2.808-4.147 3.244-9.86 8.365-23.137 10.813-36.383 10.158-14.2-.7-28.93-4.89-41.896-10.94-12.967-6.05-24.194-13.735-31.11-23.372-.09-.125-.173-.257-.26-.383-1.19 1.71-2.36 3.41-3.484 5.092-13.493 20.18-22.052 38.325-24.396 51.848-2.266 13.074-.056 20.56 8.332 26.537 27.173 9.512 68.542-3.463 113.99-32.473 46-29.36 95.61-73.934 139.207-121.496 43.6-47.562 81.272-98.21 103.74-139.24 11.235-20.515 18.607-38.696 21.18-52.12 1.287-6.714 1.34-12.173.547-15.96-.76-3.624-2.037-5.587-4.006-7.15-.15-.068-.588-.24-1.63-.362-.577-.067-1.296-.112-2.16-.098zM309.3 148.638c-2.248.014-4.24.143-5.882.378-.615.087-1.15.2-1.645.318-1.166.877-2.333 1.76-3.503 2.646.392.687 1.174 1.82 2.746 3.213 4.83 4.278 15.34 9.57 27.34 13.223 12.003 3.652 25.61 5.93 37.03 5.922 8.56-.006 15.623-1.592 19.878-3.613-.705-.58-1.298-1.125-2.3-1.768-6.332-4.058-17.682-8.866-29.946-12.598-12.265-3.733-25.61-6.527-36.29-7.42-2.672-.22-5.178-.32-7.427-.303zm-79.116 63.08c-1.102.005-2.105.042-2.996.108-1.53.114-2.527.43-3.11.625l-1.197 1.05c-.515.858-.555 1.393-.413 2.023.225 1 1.515 3.343 4.78 6.047 6.526 5.41 19.736 11.35 34.677 15.166 14.94 3.816 31.724 5.76 45.797 4.93 9.23-.544 16.816-2.534 22.28-5.12-1.302-.668-2.52-1.323-4.12-2.03-9.73-4.29-24.446-9.178-39.755-13.248-15.31-4.07-31.336-7.372-43.785-8.788-3.113-.353-6.01-.583-8.574-.69-1.283-.055-2.48-.08-3.582-.073zm-60.93 64.622c-.52.004-1.02.02-1.496.043-3.812.194-5.854 1.242-5.852 1.24-2.023 1.676-1.628 1.627-1.578 2.225.05.597.81 2.758 3.38 5.558 5.14 5.6 16.37 12.726 29.444 18.174 13.074 5.448 28.073 9.49 40.92 10.688 10.977 1.024 19.784-.492 24.715-3.026-.896-1.007-2.168-2.29-3.834-3.617-6.77-5.395-18.957-12.064-32.29-17.62-13.33-5.555-27.958-10.148-39.86-12.298-5.208-.94-9.903-1.398-13.55-1.367zm-68.772 65.744c.314 1.124.86 2.463 2.116 4.21 3.898 5.432 13.047 12.403 24.097 17.56 11.05 5.156 23.983 8.722 35.176 9.275 4.2.206 7.93-.144 11.382-.755-7.07-3.01-14.585-6.175-23.92-9.965-12.24-4.97-25.176-10.182-35.717-14.61-5.057-2.126-9.463-4.037-13.133-5.716zm92.657 21.596c-.023 0-.1.026-.122.027l.625.033c-.062-.003-.165-.072-.504-.06z"}}]})(props);
};
