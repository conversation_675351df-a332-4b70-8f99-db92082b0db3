// THIS FILE IS AUTO GENERATED
var GenIcon = require('../lib').GenIcon
module.exports.BiBookReader = function BiBookReader (props) {
  return GenIcon({"tag":"svg","attr":{"viewBox":"0 0 24 24"},"child":[{"tag":"path","attr":{"d":"M21,8c-0.202,0-4.85,0.029-9,2.008C7.85,8.029,3.202,8,3,8C2.447,8,2,8.447,2,9v9.883c0,0.271,0.11,0.53,0.305,0.719 c0.195,0.188,0.48,0.305,0.729,0.28l0.127-0.001c0.683,0,4.296,0.098,8.416,2.025c0.016,0.008,0.034,0.005,0.05,0.011 C11.746,21.966,11.871,22,12,22s0.254-0.034,0.374-0.083c0.016-0.006,0.034-0.003,0.05-0.011c4.12-1.928,7.733-2.025,8.416-2.025 l0.127,0.001c0.238,0.025,0.533-0.092,0.729-0.28C21.89,19.413,22,19.153,22,18.883V9C22,8.447,21.553,8,21,8z M4,10.049 c1.485,0.111,4.381,0.48,7,1.692v7.742c-3-1.175-5.59-1.494-7-1.576V10.049z M20,17.907c-1.41,0.082-4,0.401-7,1.576v-7.742 c2.619-1.212,5.515-1.581,7-1.692V17.907z"}},{"tag":"circle","attr":{"cx":"12","cy":"5","r":"3"}}]})(props);
};
