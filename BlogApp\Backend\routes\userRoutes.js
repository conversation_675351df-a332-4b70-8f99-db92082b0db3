
import { signup , login, logout } from "../Contoller/userController.js";
import userModelGithub from "../model/userModelGithub.js";
import userModel from "../model/userModel.js";
import jwt from "jsonwebtoken";


import express from 'express'

const router = express.Router()     // modular routing

router.post('/signup',signup)
router.post('/login',login)
router.post('/logout',logout)

// router.get('/github/:code',githubLogin)
router.get("/github/:code", async (req, res) => {

    try{
    const { code } = req.params
    // console.log(code)
    const response= await fetch(`https://github.com/login/oauth/access_token?client_id=Ov23liy3BsN6P3trYEpe&client_secret=7c5bf32a5b934f711761037b4bc86ffbc1eb0a78&code=${code}`, {
        method: "POST",
        headers: {
            "Accept": "application/json"
        }
    })
    const result = await response.json()
    console.log( "heelo",result)

    const access_token = result.access_token

     const userRes = await fetch("https://api.github.com/user", {
        method: "GET",
      headers: { Authorization: `Bearer ${access_token}` },
    });
    const userData = await userRes.json();
    console.log(userData)

    // Get GitHub user emails
    const emailRes = await fetch("https://api.github.com/user/emails", {
        method: "GET",
      headers: { Authorization: `Bearer ${access_token}` },
    });
    const emails = await emailRes.json();
    console.log(emails)

    //!Get primary email string
    const primaryEmail = emails.find((e) => e.primary)?.email;
    console.log(primaryEmail)

    // Store or find user in MongoDB
    let user = await userModelGithub.findOne({ email: primaryEmail });
    if (!user) {
      user = await userModelGithub.create({
        githubId: userData.id,
       name: userData.name || userData.login,
        email: primaryEmail,
      
      });
    }

    // Create JWT
    const jwtToken = jwt.sign(
      { id: user._id, email: user.email },
      process.env.JWT_SECRET,
      { expiresIn: "7d" }
    );

    // Set JWT as cookie
    res.cookie("token", jwtToken, {
      httpOnly: true,       // can’t access via JS
      secure: true,         // only over HTTPS in production
      sameSite: "strict",   // prevent CSRF
      maxAge: 7 * 24 * 60 * 60 * 1000, // 7 days
    });

    res.json({ message:"github login successfully" ,user,access_token}); // don’t send token in body anymore
  } catch (err) {
    console.error(err);
    res.status(500).json({ error: "GitHub login failed" });
  }

    // const result = await response.json()
    // console.log( "heelo",result)

    // res.json({
    //     access_token: result.access_token
    // });

})

// router.get('/github',async (req, res) => {
//      const response=  await fetch(`https://api.github.com/user`, {
//          method: "GET",
//         headers: {
//             "Authorization": req.get('Authorization')
//         }
//     })

//     const result = await response.json()
//     console.log( "heelo",result)

// router.get('/github',async (req, res) => {
//      const response1=  await fetch(`https://api.github.com/user/emails`, {
//          method: "GET",
//         headers: {
//             "Authorization": req.get('Authorization')
//         }
//     })

//     const result = await response1.json()
//          const email = result.filter(e=> e.primary )


//       const exitUser = await userModel.findOne({email})

    
// })

  
// // const { id: githubId, login: username } = result;
// // const { id: githubId, login: username } = result;

 

// // const githubAccessToken = req.get('Authorization').split(' ')[1];x
// //       let user = await userModel.findOne({ githubId });
// //       console.log(githubAccessToken)
// //   if (!user) {
// //     const  user = new userModelGithub({ githubId, username, accessToken: githubAccessToken });
// //     await user.save();
// //   } else {
// //     user.accessToken = githubAccessToken; // update token
// //     await user.save();
// //   }
// // const token = jwt.sign(
// //       { userId: user._id, username: user.username },
// //       JWT_SECRET,
// //       { expiresIn: "1h" }
// //     );

// //     res.json({
// //         user: result
// //     });



// })






export default router