// THIS FILE IS AUTO GENERATED
var GenIcon = require('../lib').GenIcon
module.exports.BiMicrophoneOff = function BiMicrophoneOff (props) {
  return GenIcon({"tag":"svg","attr":{"viewBox":"0 0 24 24"},"child":[{"tag":"path","attr":{"d":"M21.707 20.293l-3.388-3.388C19.368 15.553 20 13.861 20 12.021h-2c0 1.289-.415 2.478-1.109 3.456l-1.452-1.452C15.787 13.434 16 12.755 16 12.021v-6C16 3.804 14.215 2 12.021 2c-.07 0-.14.009-.209.025C9.693 2.124 8 3.878 8 6.021v.565L3.707 2.293 2.293 3.707l18 18L21.707 20.293zM10 6.021c0-1.103.897-2 2-2 .054 0 .109-.005.164-.015C13.188 4.08 14 4.956 14 6.021v6c0 .172-.029.335-.071.494L10 8.586V6.021zM6 12.021H5 4c0 4.072 3.06 7.436 7 7.931v2.069h2v-2.07c.778-.099 1.524-.305 2.218-.611l-1.558-1.558c-.527.152-1.083.239-1.66.239C8.691 18.021 6 15.329 6 12.021z"}},{"tag":"path","attr":{"d":"M8.011,12.132c0.06,2.115,1.762,3.817,3.877,3.877L8.011,12.132z"}}]})(props);
};
