// THIS FILE IS AUTO GENERATED
import { GenIcon } from '../lib';
export function BiShieldX (props) {
  return GenIcon({"tag":"svg","attr":{"viewBox":"0 0 24 24"},"child":[{"tag":"path","attr":{"d":"M20.48,6.105l-8-4c-0.281-0.141-0.613-0.141-0.895,0l-8,4C3.279,6.259,3.072,6.559,3.038,6.9 c-0.011,0.107-0.961,10.767,8.589,15.014C11.756,21.972,11.895,22,12.033,22s0.277-0.028,0.406-0.086 c9.55-4.247,8.6-14.906,8.589-15.014C20.994,6.559,20.787,6.259,20.48,6.105z M12.033,19.897C5.265,16.625,4.944,9.642,4.999,7.635 l7.034-3.517l7.029,3.515C19.1,9.622,18.734,16.651,12.033,19.897z"}},{"tag":"path","attr":{"d":"M14.293 8.293L12 10.586 9.707 8.293 8.293 9.707 10.586 12 8.293 14.293 9.707 15.707 12 13.414 14.293 15.707 15.707 14.293 13.414 12 15.707 9.707z"}}]})(props);
};
