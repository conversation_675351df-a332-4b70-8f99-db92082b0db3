// THIS FILE IS AUTO GENERATED
var GenIcon = require('../lib').GenIcon
module.exports.GiCaterpillar = function GiCaterpillar (props) {
  return GenIcon({"tag":"svg","attr":{"viewBox":"0 0 512 512"},"child":[{"tag":"path","attr":{"d":"M248.5 135.7c19.6 32.3 23.9 68.6 17.7 102.5 15.1 2.7 27.5 13.6 39.9 27.6 2.7 3.1 5.5 6.4 8.3 9.8 10.6-11.3 17.7-28.2 20.2-47.2 2.8-21.8-.7-45.4-10-63.1-.3-.4-.7-.9-1-1.3a22.65 43.28 6.281 0 1-26.1 32.2 22.65 43.28 6.281 0 1-17.5-45.9 22.65 43.28 6.281 0 1 2.2-11.6c-8-1.9-17.3-2.9-28.1-3zm-20.5 1.5c-5.2.6-10 1.5-14.3 2.6a37.86 20.25 64.9 0 1 2.1 3.9 37.86 20.25 64.9 0 1-.9 43.9 37.86 20.25 64.9 0 1-34.4-23.9 37.86 20.25 64.9 0 1-2.1-4.7c-6.3 6.5-10.9 14.1-14.7 22.7 21.7 14.6 33.3 33.9 38.5 53.9 2.3 8.5 3.5 17.1 4.2 25.6 1.2-1.5 2.5-2.9 3.8-4.3 10.3-11.1 21.8-17.6 37.8-19.2 7-33.8 2.3-69.4-20-100.5zM157.2 199c-1.6 4.9-3.1 10-4.7 15.1a25.71 14.31 34.53 0 1 12.6 26.1 25.71 14.31 34.53 0 1-22.4 1.1c-1.5 3.5-3.2 6.9-5.1 10.3 15.2 6.9 24.5 18.6 29.1 31.8 2.3 6.6 3.8 13.5 4.7 20.4 6.8-7.8 12.5-14.8 17.6-21.1 0-14.8-.7-29.4-4.2-42.5-4.1-15.8-11.7-29.6-27.6-41.2zm326 22.1c-13.4 17.1-18.8 39.2-21.3 59.5.1.1.3.1.5.2 5.8 2.4 11.5 5 16.8 8 2.4-20.5 7.1-42.5 18.2-56.7zm-130.7 8.6c0 .4-.1.7-.1 1.1-3 22.6-11.7 44.4-26.7 59.1 8 10.3 16.6 21.7 26.4 33.6 1.2 1.5 4.3 3.6 8.7 5.8 18.2-16.4 27.5-33.5 28.2-51.9-2.8-1.3-5.4-2.8-7.8-4.5a14.31 25.71 27.97 0 1-19.3 8 14.31 25.71 27.97 0 1-.6-29.4 14.31 25.71 27.97 0 1 .5-.9l-1.8-3.3c-2.8-5.6-5.3-11.5-7.5-17.6zM242 256c-7.9 1.5-12.8 5.5-19.9 13.2-1.4 1.5-2.9 3.2-4.4 5a20.74 15.03 67.92 0 0 .2.4 20.74 15.03 67.92 0 0 22.5 6.5A20.74 15.03 67.92 0 0 242 256zm30.9 4.7a15.58 20.74 40.18 0 0-6.6 25 15.58 20.74 40.18 0 0 23.9 1.6 15.58 20.74 40.18 0 0 5-5.1c-1.3-1.5-2.6-3.1-3.9-4.5-6.7-7.7-12.8-13.3-18.4-17zm-145.3 5.8c-.2.2-.4.5-.6.7a25.71 14.31 62.03 0 1-2.5 25.3 25.71 14.31 62.03 0 1-19.4-8.2c-1.2.4-2.5.8-3.8 1.2 6.2 11.2 8.8 23.2 9.3 34.1.2 6-.1 11.8-.8 17.1 6.4-.9 12.6-2.1 18.2-3.5 14-3.5 25-9.1 26.8-11 .1-.1.1-.2.2-.2-1-12.2-2.2-23.7-5.3-32.7-3.8-10.8-9.1-18.7-22.1-22.8zm279 16.2c-2.1 19.3-11.6 37.2-27 53.3.5.1.9.2 1.4.3 11.9 2.8 25.9 4.7 40 5.4 10.3-17 15.5-35.7 13-52-1.3-.3-2.7-.7-4-1.1a10.65 21.91 5.821 0 1-10.8 10.5 10.65 21.91 5.821 0 1-8.3-15.4c-1.5-.4-2.9-.7-4.3-1zM81.97 288c-4.53.2-9.08.2-13.55.2a25.89 16.75 75.16 0 1-11.93 20.9 25.89 16.75 75.16 0 1-20.99-18.5c-3.11.9-5.69 2.1-7.62 3.6-4.52 3.6-7.62 9-6.95 22.8.38 7.8 5.04 12.5 15.86 16.6 10.81 4.2 26.75 6 43.35 5.7 3.69-.1 7.41-.3 11.12-.5 1.02-5.7 1.65-12 1.38-18.5-.46-11-3.12-22.1-10.67-32.3zm370.63 7.8c.6 15.6-3.8 31.4-11.1 46 13.9-.5 26.7-2.3 35.7-5.2 5.6-1.9 9.7-4.2 11.7-6.1 2.1-1.9 2.3-2.7 2.2-4.5-.3-4.9-3.2-9.6-9.3-14.7-6-5-15-9.8-24.9-13.8-1.4-.6-2.9-1.1-4.3-1.7zm-259.6 9c-6 7.2-12.8 15.1-21 23.9a20.77 16.07 52.56 0 0 4 4.4 20.77 16.07 52.56 0 0 24.7.2 20.77 16.07 52.56 0 0-6-27.1 20.77 16.07 52.56 0 0-1.7-1.4zm125.5 6.9a14.85 20.66 27.26 0 0-3.2 26.7 14.85 20.66 27.26 0 0 22.4-2.5c-.3-.3-.6-.7-.9-1-6.7-8.2-12.6-15.8-18.3-23.2zm-171.7 34.5c-4.8 1.7-10.1 3.1-15.8 4.5-3.1.8-6.4 1.5-9.8 2.1a20.74 13.19 72.3 0 0 2.8 6.9 20.74 13.19 72.3 0 0 19.9 8.2 20.74 13.19 72.3 0 0 2.9-21.7zm-103.96 8.2a13.25 20.94 0 0 0 0 .2 13.25 20.94 0 0 0 13.25 20.9 13.25 20.94 0 0 0 13.12-18.2c-9.16-.2-18.08-1.1-26.37-2.9zm435.16.3c-8.3 2.4-17.7 3.8-27.7 4.6a20.48 14.24 70.89 0 0 3.2 5.7 20.48 14.24 70.89 0 0 21.1 3.9 20.48 14.24 70.89 0 0 3.4-14.2zm-96.6.4a20.76 15.54 83.84 0 0 1.2 7.4 20.76 15.54 83.84 0 0 20.4 12.7 20.76 15.54 83.84 0 0 9.4-15.9c-10.7-.8-21.3-2.2-31-4.2z"}}]})(props);
};
