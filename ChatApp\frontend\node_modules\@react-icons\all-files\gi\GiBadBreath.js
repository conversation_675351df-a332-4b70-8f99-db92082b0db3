// THIS FILE IS AUTO GENERATED
var GenIcon = require('../lib').GenIcon
module.exports.GiBadBreath = function GiBadBreath (props) {
  return GenIcon({"tag":"svg","attr":{"viewBox":"0 0 512 512"},"child":[{"tag":"path","attr":{"d":"M19.455 17.016l1.717 345.656c15.677 8.52 35.932 6.66 49.75-1.625 16.377-10.994 19.86-25.201 11.346-39.719C23.28 223.543 36.424 131.91 87.955 80.486c51.53-51.424 140.25-61.074 224.688-1.627 13.64 9.602 20.365 9.61 24.593 7.657 18.702-19.697 21.81-43.01 26.063-68.813zM192.08 56.295c-4.418.057-8.754.3-13.033.75l98.97 66.691 16.53-34.595C261.985 71.343 224.73 56.293 192.08 56.295zm222.44 11.06a12.86 12.86 0 0 0-3.666.649c-13.063 6.389-8.956 24.292-6.124 34.324-4.158 1.963-7.591 5.365-10.12 9.055-4.25 6.199-6.926 14.053-7.885 22.847-1.433 15.702 4.198 36.571 19.574 38.665 18.646-.32 26.357-20.765 27.6-35.34 11.739 11.885 32.68 23.59 45.173 11.619 11.144-14.748.83-32.292-9.181-42.354-9.272-8.11-19.986-15.22-31.756-13.851-4.028-9.687-12.146-25.91-23.615-25.614zM120.957 89.566l-3.73 17.61c57.04 18.075 102.46 11.942 144.98 59.682 15.383 17.271 33.365 57.686 53.016 69.8 32.681 12.43 53.701-6.904 81.691-12.902 37.77-.187 56.574 56.11 65.28 86.252l17.353-4.781c-14.027-43.894-48.127-105.21-86.27-99.1-21.857 3.607-51.379 24.162-68.85 15.064-12.645-16.527-33.477-59.212-45.416-71.924-54.314-42.71-91.503-45.302-158.054-59.7zm322.44 21.526c9.425 5.394 25.77 11.65 22.802 25.347.136-.023.176-.027.076.075-.1.1-.098.06-.076-.075-15.947 1.56-21.222-15.714-22.803-25.347zm-32.057 8.238c5.913 9.524 8.832 28.129-3.078 35.565.102.09.13.122-.012.107-.141-.015-.107-.041.012-.107-7.759-8.476-3.879-29.655 3.078-35.565zm-322.041 2.078l-7.338 16.436c49.115 25.31 108.604 49.194 151.504 88.33 20.722 23.473 27.135 60.83 51.875 75.26 20.624 13.592 58.792-6.191 75.812 3.017 29.144 25.403 43.935 56.729 57.233 92.516l16.94-6.086c-16.039-34.888-32.092-81.84-65.151-102.006-20.793-14.056-58.732 5.845-76.078-3.168-24.742-18.789-28.585-53.611-48.334-72.678-50.073-39.844-98.611-65.607-156.463-91.62zm-9.783 40.252L68 175.494s78.82 66.142 107.45 104.465c16.519 26.386 20.846 65.049 44.046 81.766 26.927 20.17 81.684-3.604 102.795 12.418 22.115 27.829 28.526 61.222 32.219 96.017l17.92-1.685c-6.519-35.668-11.242-84.852-38.825-108.332-27.252-22.923-80.876 1.677-103.87-13.223-23.15-20.998-23.657-55.409-39.866-77.734C157.864 226.344 79.516 161.66 79.516 161.66zm-17.782 32.856l.002.004v-.004zm.002.004c.081 32.477 9.462 69.047 31.094 107.84l22.125-13.126zm113.145 180.115c-20.795 3.241-45.16 21.84-43.932 43.533-11.214 3.854-30.078 12.365-26.533 25.762 5.607 13.416 23.722 10.372 33.902 8.136 1.714 4.268 4.908 7.896 8.442 10.64 5.937 4.607 13.62 7.741 22.344 9.218 8.723 1.476 17.007 1.046 24.13-1.352 7.124-2.397 14.19-7.448 15.621-15.908 1.432-8.46-3.578-15.55-9.515-20.158-8.078-5.154-16.073-8.394-24.137-9.477 7.187-5.969 14.071-14.446 17.004-22.283 2.555-7.068 2.996-15.741-2.736-22.125-3.941-4.389-9.272-5.992-14.59-5.986zM176 392.57c.012.004.035.031.076.077.095.105.055.102-.078.072-1.023 8.207-9.939 17.275-16.185 21.045-3.507-.307-7.321-.122-10.463.222 3.622-10.53 18.25-21.9 26.648-21.267-.009-.077-.014-.154.002-.149zm-3.053 51.162c7.58.111 14.604 2.256 18.055 7.91.096-.096.13-.122.105.018-.023.14-.045.106-.105-.017-9.297 6.526-28.506 2.379-35.322-5.17 5.143-1.682 11.373-2.827 17.267-2.74z"}}]})(props);
};
