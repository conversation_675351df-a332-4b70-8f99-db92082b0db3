// THIS FILE IS AUTO GENERATED
var GenIcon = require('../lib').GenIcon
module.exports.BiTone = function BiTone (props) {
  return GenIcon({"tag":"svg","attr":{"viewBox":"0 0 24 24"},"child":[{"tag":"path","attr":{"d":"M12,2C6.486,2,2,6.486,2,12s4.486,10,10,10s10-4.486,10-10S17.514,2,12,2z M11,11H4.069c0.088-0.698,0.264-1.369,0.52-2 H11V11z M11,15H4.589c-0.256-0.631-0.432-1.302-0.52-2H11V15z M11,4.069V7H5.765C7.035,5.42,8.891,4.334,11,4.069z M5.765,17H11 v2.931C8.891,19.666,7.035,18.58,5.765,17z M13,19.931V4.069c3.939,0.495,7,3.858,7,7.931S16.939,19.436,13,19.931z"}}]})(props);
};
