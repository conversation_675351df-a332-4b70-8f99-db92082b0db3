import React from "react";
import styled from "styled-components";
// import useNavigate from 'useNavigate'
import { useForm } from "react-hook-form"
import API from '../axios.js'

import { Link } from 'react-router-dom'
import { useAuth } from "../Context/AuthProvider.jsx";

import { useNavigate } from "react-router-dom"


const Login = () => {
  // const navigate = useNavigate()
  const {  setAuthUser } = useAuth()

  const navigate = useNavigate()

  const {
    register,
    handleSubmit,

    formState: { errors },
  } = useForm()





  const onSubmit = async (data) => {
    const user = {

      email: data.email,
      password: data.password,

    }

    try {
      const res = await API.post('/api/user/login', user, {

        headers: {
          'Content-Type': 'application/json'
        }

      })
      localStorage.setItem("userData", JSON.stringify(res.data))
      setAuthUser(res.data)
      console.log(res)


      alert("Login successful")
      navigate("/profile")

    } catch (error) {
      console.log("Login ", error)


    }






  }

  return (
    <div className="w-full h-screen flex justify-center items-center bg-amber-50">
      <StyledWrapper>
        <div className="form-box " onSubmit={handleSubmit(onSubmit)}>
          <form className="form">
            <span className="title">Log in</span>
            <span className="subtitle">
              Create a free account with your email.
            </span>
            <div className="form-container">

              <input type="email" className="input" placeholder="Email"  {...register("email", { require: true })} />
              {errors.email && <span>This field is required</span>}
              <input type="password" className="input" placeholder="Password" {...register("password", { require: true })} />
              {errors.password && <span>This field is required</span>}

            </div>
            <button>Login</button>
          </form>
          <div className="form-section">
            <p>
              Have not account? <Link to="/signup">Sign up</Link>{" "}
            </p>
          </div>
        </div>
      </StyledWrapper>
    </div>
  );
};

const StyledWrapper = styled.div`
  .form-box {
    max-width: 300px;
    background: #f1f7fe;
    overflow: hidden;
    border-radius: 16px;
    color: #010101;
  }

  .form {
    position: relative;
    display: flex;
    flex-direction: column;
    padding: 32px 24px 24px;
    gap: 16px;
    text-align: center;
  }

  /*Form text*/
  .title {
    font-weight: bold;
    font-size: 1.6rem;
  }

  .subtitle {
    font-size: 1rem;
    color: #666;
  }

  /*Inputs box*/
  .form-container {
    overflow: hidden;
    border-radius: 8px;
    background-color: #fff;
    margin: 1rem 0 0.5rem;
    width: 100%;
  }

  .input {
    background: none;
    border: 0;
    outline: 0;
    height: 40px;
    width: 100%;
    border-bottom: 1px solid #eee;
    font-size: 0.9rem;
    padding: 8px 15px;
  }

  .form-section {
    padding: 16px;
    font-size: 0.85rem;
    background-color: #e0ecfb;
    box-shadow: rgb(0 0 0 / 8%) 0 -1px;
  }

  .form-section a {
    font-weight: bold;
    color: #0066ff;
    transition: color 0.3s ease;
  }

  .form-section a:hover {
    color: #005ce6;
    text-decoration: underline;
  }

  /*Button*/
  .form button {
    background-color: #0066ff;
    color: #fff;
    border: 0;
    border-radius: 24px;
    padding: 10px 16px;
    font-size: 1rem;
    font-weight: 600;
    cursor: pointer;
    transition: background-color 0.3s ease;
  }

  .form button:hover {
    background-color: #005ce6;
  }
`;

export default Login;
