// THIS FILE IS AUTO GENERATED
var GenIcon = require('../lib').GenIcon
module.exports.GiAppleMaggot = function GiAppleMaggot (props) {
  return GenIcon({"tag":"svg","attr":{"viewBox":"0 0 512 512"},"child":[{"tag":"path","attr":{"d":"M234.094 19.406l-18.688.313.47 27.468c-11.52.584-23.032 2.315-34.345 5.124l-8.343-29.25-17.968 5.125 8.436 29.532c-10.708 3.84-21.092 8.698-30.97 14.5l-17-28.376-16.03 9.593L117.094 82.5c-9.14 6.74-17.66 14.39-25.375 22.906l-22.657-21.97-13 13.44L79.936 120c-6.268 8.728-11.766 18.15-16.312 28.28L39.72 137.063 31.78 154l25.157 11.813c-3.008 9.477-5.243 19.44-6.593 29.906l-23.282-1.19-.937 18.657 22.813 1.157c-.24 8.777.117 17.857 1.124 27.22l-20.624 1.874 1.687 18.625 20.22-1.844c-.053 6.906.35 13.867 1.25 20.81L36.874 285l4.563 18.125 14.687-3.72c8.055 31.428 26.478 61.905 56.906 87.095.53 4.372 3.31 8.302 7.72 10.188 6.615 2.827 14.01-.142 16.844-6.782 2.833-6.64-.133-14.046-6.75-16.875-1.654-.706-3.364-1.045-5.03-1.06-.314-.004-.63.01-.94.03-.005-.005-.024.005-.03 0-22.936-18.977-38.142-40.818-46.688-63.5-15.91-50.433-.102-86.988 30.22-110.188-12.37-2.92-25.05-8.367-35.282-17.28l12.28-14.095c7.942 6.917 19.41 11.684 30.376 13.844 8.676 1.71 17.147 1.58 22.47.626 8.057-3.338 16.542-6.042 25.31-8.156-.99-.597-1.98-1.213-2.968-1.844-16.615-10.62-32.4-26.322-41.656-46.53l16.97-7.782c7.398 16.155 20.704 29.616 34.75 38.594 11.26 7.198 23.117 11.25 31.5 12.218 8.696-.37 17.41-.205 26 .5-12.34-14.543-22.488-33.296-25.69-54.375l18.5-2.81c2.67 17.568 11.75 34.19 22.783 46.686 6.34 7.185 13.405 12.895 19.78 16.75 7.88 2.2 15.427 4.923 22.5 8.156-5.584-15.06-8.527-32.087-6.594-49.406l18.563 2.063c-1.972 17.66 2.515 36.073 9.936 51 7.422 14.924 18.17 26.132 25.375 29.655l4.876 2.406.344 5.407c1.682 25.836 10.346 49.825 28.906 68.72 15.068 15.338 37.1 27.508 68.28 34-27.347-34.898-47.333-65.726-50.03-102.157l-.437-6.03 5.31-2.876c5.316-2.887 10.85-6.908 16.064-11.938 14.927-14.4 20.86-31.03 20.25-47.187-.772.143-1.56.218-2.375.218-7.122 0-12.908-5.755-12.908-12.875 0-5.796 3.828-10.686 9.094-12.313-.12-.26-.22-.524-.344-.78-3.824-7.99-9.19-15.287-15.812-21.345-2.188 3.98-6.412 6.656-11.28 6.656-7.122 0-12.908-5.753-12.908-12.874 0-2.67.827-5.158 2.22-7.22-8.033-2.987-16.87-4.512-26.282-4.124-8.12.337-17.212 2.52-26.688 6.344L322.75 81.28l-.188-.155.063-.125-.063-.188c.075-.03.145-.064.22-.093l13.437-24.22-16.345-9.063L307.375 70c-8.002-5.064-16.423-9.26-25.156-12.625l6.5-26-18.126-4.53-6.22 24.905c-9.73-2.427-19.725-3.884-29.812-4.47l-.468-27.874zm98.125 116.47c7.12 0 12.905 5.754 12.905 12.874 0 7.12-5.786 12.906-12.906 12.906-7.122 0-12.876-5.786-12.876-12.906 0-7.12 5.754-12.875 12.875-12.875zm113.03 24.343c-.4 15.935-5.573 32.132-16.47 46.5 27.434 13.336 52.025 35.102 61.408 75.5 4.953-50.454-4.958-96.694-44.938-122zm-92.188 13.343c7.12 0 12.875 5.754 12.875 12.875 0 7.12-5.754 12.906-12.875 12.906-7.12 0-12.906-5.786-12.906-12.906 0-7.12 5.786-12.875 12.906-12.875zM214 269.625c-2.285-.01-4.37.135-6.156.344-3.29 22.39-4.268 41.092-3.063 60.686-7.654-4.135-15.73-6.97-23.968-8.312-20.05-3.272-36.837 2.83-49.062 15.375-4.404 4.52-8.177 9.94-11.188 16.124 1.782-.308 3.583-.502 5.407-.5 4.093.003 8.24.8 12.218 2.5 15.912 6.803 23.386 25.487 16.593 41.406-6.438 15.09-23.557 22.61-38.874 17.563 9.237 39.225 38.394 72.143 72.375 77.687 4.746.774 11.172.88 16.69.063 5.515-.817 10.023-2.85 11.28-4l6.094-5.594 6.312 5.405c1.572 1.345 5.7 2.798 11.063 3 5.36.202 11.617-.662 16.56-1.938 39.435-10.158 72.708-58.216 70.72-105.78-1.72-41.132-30.873-67.235-62.97-66.626H264c-4.374.092-9.26.883-14.344 2.19-7.944 2.043-15.61 5.534-22.78 10.217 2.367-15.41 7.606-30.056 15.655-45.875-5.336-11.122-18.628-13.89-28.53-13.937z"}}]})(props);
};
