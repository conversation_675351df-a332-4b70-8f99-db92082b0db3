// THIS FILE IS AUTO GENERATED
var GenIcon = require('../lib').GenIcon
module.exports.BiShare = function BiShare (props) {
  return GenIcon({"tag":"svg","attr":{"viewBox":"0 0 24 24"},"child":[{"tag":"path","attr":{"fill":"none","d":"M10.025,9C9.474,9,9.003,8.552,9.003,8V6.414L4.417,11l4.586,4.586V14c0-0.501,0.371-0.925,0.868-0.991 c0.663-0.088,1.327-0.133,1.975-0.133c3.577,0,6.204,1.331,7.858,2.55C18.14,9.201,10.936,9,10.025,9z"}},{"tag":"path","attr":{"d":"M11.003,7.054V4c0-0.404-0.243-0.769-0.617-0.924c-0.373-0.154-0.804-0.069-1.09,0.217l-7,7 c-0.391,0.391-0.391,1.023,0,1.414l7,7c0.287,0.287,0.717,0.372,1.09,0.217c0.374-0.155,0.617-0.52,0.617-0.924v-3.096 c0.284-0.02,0.565-0.029,0.843-0.029c5.426,0,8.239,3.572,8.362,3.73C20.4,18.859,20.697,19,21.003,19 c0.106,0,0.214-0.017,0.318-0.052c0.407-0.137,0.682-0.518,0.682-0.948C22.003,8.876,13.917,7.319,11.003,7.054z M11.846,12.875 c-0.647,0-1.312,0.045-1.975,0.133C9.374,13.075,9.003,13.499,9.003,14v1.586L4.417,11l4.586-4.586V8c0,0.552,0.471,1,1.022,1 c0.91,0,8.114,0.201,9.679,6.425C18.05,14.207,15.423,12.875,11.846,12.875z"}}]})(props);
};
