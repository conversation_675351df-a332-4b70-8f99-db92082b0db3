/*! 🌼 daisyUI 5.0.42 - MIT License */ @layer utilities{:root .prose{--tw-prose-body:color-mix(in oklab,var(--color-base-content)80%,#0000);--tw-prose-headings:var(--color-base-content);--tw-prose-lead:var(--color-base-content);--tw-prose-links:var(--color-base-content);--tw-prose-bold:var(--color-base-content);--tw-prose-counters:var(--color-base-content);--tw-prose-bullets:color-mix(in oklab,var(--color-base-content)50%,#0000);--tw-prose-hr:color-mix(in oklab,var(--color-base-content)20%,#0000);--tw-prose-quotes:var(--color-base-content);--tw-prose-quote-borders:color-mix(in oklab,var(--color-base-content)20%,#0000);--tw-prose-captions:color-mix(in oklab,var(--color-base-content)50%,#0000);--tw-prose-code:var(--color-base-content);--tw-prose-pre-code:var(--color-neutral-content);--tw-prose-pre-bg:var(--color-neutral);--tw-prose-th-borders:color-mix(in oklab,var(--color-base-content)50%,#0000);--tw-prose-td-borders:color-mix(in oklab,var(--color-base-content)20%,#0000);--tw-prose-kbd:color-mix(in oklab,var(--color-base-content)80%,#0000);& :where(code):not(pre>code){background-color:var(--color-base-200);border-radius:var(--radius-selector);border:var(--border)solid var(--color-base-300);font-weight:inherit;padding-inline:.5em;&:before,&:after{display:none}}}}