// THIS FILE IS AUTO GENERATED
var GenIcon = require('../lib').GenIcon
module.exports.GiWrappedSweet = function GiWrappedSweet (props) {
  return GenIcon({"tag":"svg","attr":{"viewBox":"0 0 512 512"},"child":[{"tag":"path","attr":{"d":"M391.22 9.22c-21.755 67.887-29.008 99.75-52.25 146.218 2.776 2.15 5.5 4.42 8.124 6.843 23.768-21.825 42.788-47.49 51.937-85.5l18.158 4.376c-10.093 41.93-31.86 71.302-57.375 94.813 1.442 1.81 2.832 3.657 4.156 5.53 27.306-3.97 52.29-12.325 74.56-32.47l12.533 13.876c-23.42 21.182-49.982 31.05-76.938 35.875.75 1.56 1.477 3.138 2.156 4.72 53.284 5.685 96.878-3.05 122.408-44.094C431.28 144.456 480.78 24.198 391.217 9.22zM247.06 153.937c-9.422-.058-18.308 1.46-25.78 4.625l-.095-.188c-10.542 4.937-20.434 11.78-29.156 20.5-35.073 35.074-39.537 88.93-13.436 128.813-4.858-12.255-7.025-25.792-5.28-39.97 2.61-21.226 13.892-43.415 35.842-64.687l13 13.407c-19.616 19.01-28.3 37.187-30.312 53.563-2.014 16.376 2.574 31.435 11.375 44.53 15.493 23.06 44.764 38.645 69.093 39.595 23.7-1.754 46.925-11.707 65.093-29.875 40.22-40.22 40.22-105.156 0-145.375-2.658-2.66-5.42-5.13-8.28-7.438 9.885 11.527 16.984 25.703 19.28 42.063 2.973 21.18-2.147 45.52-17.844 71.75l-16.062-9.594c14.027-23.44 17.7-43.222 15.406-59.562-2.293-16.34-10.578-29.69-22.47-40.063-16.347-14.26-39.644-21.967-60.373-22.093zM133.47 317.78c-50.013.115-67.71 4.92-116.345 55.283 66.358-2.98 34.08 106.974 107.47 126.156 3.573-48.6 22.248-86.363 58.468-155.626-23.81 15.56-44.668 34.515-60 63.687l-16.563-8.686c14.987-28.514 35.14-48.585 57.125-64.375-25.9 2.17-51.153 8.562-76.688 24.686l-9.968-15.78c22.406-14.15 44.986-21.59 67.28-25.282-3.718-.023-7.382-.07-10.78-.063z","fillRule":"evenodd"}}]})(props);
};
