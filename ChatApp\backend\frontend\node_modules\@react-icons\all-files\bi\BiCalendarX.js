// THIS FILE IS AUTO GENERATED
var GenIcon = require('../lib').GenIcon
module.exports.BiCalendarX = function BiCalendarX (props) {
  return GenIcon({"tag":"svg","attr":{"viewBox":"0 0 24 24"},"child":[{"tag":"path","attr":{"d":"M8.293 16.293L9.707 17.707 12 15.414 14.293 17.707 15.707 16.293 13.414 14 15.707 11.707 14.293 10.293 12 12.586 9.707 10.293 8.293 11.707 10.586 14z"}},{"tag":"path","attr":{"d":"M19,4h-2V2h-2v2H9V2H7v2H5C3.897,4,3,4.897,3,6v2v12c0,1.103,0.897,2,2,2h14c1.103,0,2-0.897,2-2V8V6 C21,4.897,20.103,4,19,4z M19.002,20H5V8h14L19.002,20z"}}]})(props);
};
