// THIS FILE IS AUTO GENERATED
var GenIcon = require('../lib').GenIcon
module.exports.BiPulse = function BiPulse (props) {
  return GenIcon({"tag":"svg","attr":{"viewBox":"0 0 24 24"},"child":[{"tag":"path","attr":{"d":"M16.97,4.757c-0.107-0.431-0.487-0.74-0.932-0.756c-0.432-0.025-0.846,0.261-0.986,0.683l-3.186,9.554L8.914,7.594 C8.751,7.227,8.372,6.986,7.981,7c-0.402,0.008-0.761,0.255-0.91,0.628L5.323,12H2v2h3.323c0.823,0,1.552-0.494,1.856-1.257 l0.869-2.172l3.037,6.835C11.247,17.769,11.606,18,12,18c0.016,0,0.032,0,0.048-0.001c0.413-0.021,0.771-0.291,0.9-0.683 l2.914-8.742l0.979,3.911C17.063,13.377,17.861,14,18.781,14H22v-2h-3.22L16.97,4.757z"}}]})(props);
};
