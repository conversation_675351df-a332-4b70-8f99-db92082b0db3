// THIS FILE IS AUTO GENERATED
var GenIcon = require('../lib').GenIcon
module.exports.GiLeatherVest = function GiLeatherVest (props) {
  return GenIcon({"tag":"svg","attr":{"viewBox":"0 0 512 512"},"child":[{"tag":"path","attr":{"d":"M260.375 19.656c-38.78 0-73.995 3.935-100.5 12.407-13.253 4.235-24.39 9.556-32.813 16.78-8.42 7.226-14.124 17.087-14.124 28.094 0 4.472.917 8.784 2.53 12.876 3.433 52.216-35.145 118.26-77.28 151.406 9.894 50.565 32.645 102.465 66.53 149.81 39.964 32.405 91.668 49.857 144.282 52.314v-33.75c-16.175-.15-31.88-.668-44.5-1.594l1.375-18.625c11.96.878 27.288 1.356 43.125 1.5V350.97c-15.294.57-29.984 2.542-41.063 5.936l-5.5-17.875c13.523-4.142 29.83-6.512 46.563-7.155v-33.5c-4.892.263-9.767.762-14.594 1.438-28.855 1.45-53.472 5.125-75.906 10.593l-.22-.28c28.03-20.89 64.05-31.314 100.064-31.313 36.015 0 72.034 10.425 100.062 31.312l-.125.188c-22.516-5.463-47.247-9.113-76.25-10.532-4.746-.656-9.533-1.148-14.342-1.405v33.5c16.734.643 33.04 3.013 46.562 7.156l-5.47 17.876c-11.084-3.396-25.787-5.366-41.092-5.937v39.905c15.843-.144 31.16-.622 43.125-1.5L312.188 408c-12.627.927-28.317 1.445-44.5 1.594v34c54.5-1.12 108.566-18.29 150.187-51.47 35.375-47.383 58.567-98.53 68.5-148.405-43.943-32.66-85.096-102.138-80.344-156 1.132-3.463 1.75-7.065 1.75-10.782 0-11.008-5.67-20.87-14.092-28.094-8.422-7.225-19.59-12.546-32.844-16.782-26.506-8.47-61.687-12.406-100.47-12.406zm0 18.688c37.48 0 71.312 4.03 94.78 11.53 11.736 3.752 20.793 8.394 26.345 13.157 5.552 4.765 7.594 8.92 7.594 13.907 0 2.495-.582 5.05-1.875 7.688-36.805-16.793-82.838-25.218-128.876-25.22-44.633 0-89.28 7.903-125.5 23.69-.83-2.1-1.22-4.145-1.22-6.157 0-4.99 2.042-9.143 7.595-13.907 5.55-4.762 14.608-9.404 26.343-13.155 23.47-7.5 57.334-11.53 94.812-11.53zm-2.03 40.468c41.273 0 82.56 6.716 115.186 20.188-5.193 3.626-11.645 7.13-19.155 10.28-23.424 9.827-56.93 16.22-94 16.22s-70.607-6.393-94.03-16.22c-8.22-3.446-15.152-7.322-20.564-11.31C177.984 85.2 218.167 78.81 258.345 78.812zm-35.908 63.25c6.253.724 12.667 1.266 19.22 1.625.814 5.004 1.705 10.514 2.624 16.688 2.72 18.253 5.47 39.27 5.47 52.875 0 15.196-5.87 28.974-16.03 39.594-10.163 10.62-24.316 18.284-41.25 23.28-18.763 5.537-41.102 7.852-66.032 6.72-23.82-3.486-42.194-13.06-59.97-27.656 48.897 11.737 91.53 11.613 120.72 3 14.638-4.32 25.797-10.72 33.03-18.282 7.235-7.56 10.845-16.048 10.845-26.656 0-10.86-2.608-32.175-5.282-50.125-1.202-8.075-2.384-15.295-3.343-21.063zm71.688.47c-.946 5.705-2.108 12.72-3.28 20.593-2.675 17.95-5.283 39.265-5.283 50.125 0 10.608 3.61 19.096 10.844 26.656 7.234 7.56 18.393 13.962 33.03 18.28 29.11 8.59 71.588 8.735 120.314-2.905-17.762 14.562-36.125 24.13-59.938 27.595-24.778 1.092-46.988-1.242-65.656-6.75-16.935-4.997-31.088-12.66-41.25-23.28-10.162-10.62-16.03-24.4-16.03-39.595 0-13.606 2.78-34.622 5.5-52.875.905-6.08 1.788-11.517 2.593-16.47 6.518-.276 12.915-.738 19.155-1.374zm121.25 274.655c-88.88 60.702-217.204 60.827-306.25.375l-2.375 39.188c6.073 8.603 23.877 18.598 49.406 25.094 27.31 6.95 62.423 10.798 98.313 11.125 35.888.326 72.623-2.834 103.374-9.47 28.162-6.078 51.078-15.495 63.47-26.03l-5.94-40.283z"}}]})(props);
};
