// THIS FILE IS AUTO GENERATED
var GenIcon = require('../lib').GenIcon
module.exports.GiFlyingFox = function GiFlyingFox (props) {
  return GenIcon({"tag":"svg","attr":{"viewBox":"0 0 512 512"},"child":[{"tag":"path","attr":{"d":"M46.244 18l170.172 72.932 7.586 18.982L242.18 155.4l26.775 11.487-4.412 49.513c3.896-2.256 7.913-4.446 12.102-6.49l.685-.334.733-.215a38.637 38.637 0 0 1 5.28-1.15l3.018-33.858 21.488 9.216 64.54-25.793L494 209.895v-19.583L352.756 129.78c10.44-5.566 17.607-16.574 17.607-29.145 0-18.12-14.88-33-33-33-8.92 0-17.05 3.61-23.01 9.433 0-.145.01-.288.01-.433 0-18.12-14.88-33-33-33-18.12 0-33 14.88-33 33 0 3.096.443 6.093 1.254 8.94L91.937 18H46.245zm235.12 43.635c8.39 0 15 6.61 15 15s-6.61 15-15 15c-8.392 0-15-6.61-15-15s6.608-15 15-15zm56 24c8.39 0 15 6.61 15 15s-6.61 15-15 15c-8.392 0-15-6.61-15-15s6.608-15 15-15zm-32.99 14.566c0 .146-.01.29-.01.435 0 3.096.442 6.093 1.253 8.94l-8.86-3.796a33.383 33.383 0 0 0 7.618-5.58zm-55.595 23.212l77.7 33.328-18.388 7.348-51.965-22.29-7.348-18.386zm-88.813 44.465c-1.564.03-3.078.278-4.53.736-5.804 1.835-10.49 6.994-13.12 15.508-2.627 8.515-2.697 19.943.974 31.56 3.672 11.617 10.297 20.93 17.34 26.386 7.045 5.457 13.845 6.983 19.65 5.15 5.804-1.836 10.49-6.993 13.118-15.507 2.63-8.515 2.7-19.943-.972-31.56-3.672-11.617-10.296-20.927-17.34-26.384-5.283-4.093-10.428-5.977-15.12-5.89zm128.265 57.992c-1.4.004-2.946.266-4.543.68-16.685 8.293-31.973 20.153-49.28 29.423-17.458 9.35-37.674 15.72-61.853 10.865-11.962-1.176-16.464 4.17-17.71 9.943-1.218 5.632.82 12.564 11.155 16.76 55.347 5.065 101.237-31.265 126.685-52.144.002 0 0-.002.002-.003 4.028-3.38 5.42-6.192 5.8-7.913.38-1.72.105-2.63-.76-3.83-1.296-1.8-4.77-3.796-9.498-3.78zm-115.675 40.968c.095.01.18.01.277.02l-.883-.137c.204.043.404.077.607.118zm104.746 9.178c-5.806 4.074-12.11 8.176-18.862 12.086 4.013 9.28 7.354 19.894 9.945 31.99 1.61.113 3.294.26 4.955.398l3.963-44.474zm-34.82 20.457c-20.36 9.566-43.922 16.344-69.917 15.54 2.902 4.44 6.194 8.82 9.725 13.038 5.8 6.925 12.3 13.454 18.59 19.218 7.647-1.047 19.705-4.422 30-9.143 6.385-2.927 12.267-6.335 16.405-9.57a29.504 29.504 0 0 0 3.274-2.98c-2.216-10.03-4.95-18.662-8.078-26.102zm17.345 42.033c-.493.404-.95.832-1.457 1.228-5.738 4.487-12.654 8.39-19.985 11.752-6.59 3.022-13.37 5.63-19.96 7.563 5.682 4.404 9.88 7.312 11.093 8.052 8.19 5.004 20.968 8.01 34.627 9.54 3.935-12.394.912-24.61-4.317-38.136zm19.342.512c4.085 12.243 6.466 25.26 3.312 38.824 2.596.06 5.167.077 7.684.05 21.71-.245 39.418-2.925 39.418-2.925l14.576-2.213-4.693 13.976c-7.496 22.323-10.164 38.087-17.156 59.038l55.614 24.933 9.418-18.973-39.594-16.97 4.678-8.667c3.575-6.625 9.348-20.202 13.79-33.983 3.962-12.288 6.61-24.973 6.827-32.623-.047-.03-.026-.033-.076-.062-1.7-1.004-4.682-2.357-8.45-3.71-7.535-2.71-18.256-5.546-30.214-8.087-17.38-3.692-37.464-6.792-55.133-8.607zm32.142 55.933c-5.626.428-12.035.78-18.986.905l-26.097 43.653 35.194 11.67 1.342-3.64a265.75 265.75 0 0 0 4.83-14.364l-14.076-5.15 17.793-33.073z"}}]})(props);
};
