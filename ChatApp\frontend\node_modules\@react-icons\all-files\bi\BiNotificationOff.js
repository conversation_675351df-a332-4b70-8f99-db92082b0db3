// THIS FILE IS AUTO GENERATED
var GenIcon = require('../lib').GenIcon
module.exports.BiNotificationOff = function BiNotificationOff (props) {
  return GenIcon({"tag":"svg","attr":{"viewBox":"0 0 24 24"},"child":[{"tag":"path","attr":{"d":"M21.71 20.296l-1.786-1.786C19.969 18.347 20 18.178 20 18v-7h-2v5.586L7.414 6H13V4H6C5.822 4 5.653 4.031 5.49 4.076l-1.78-1.78L2.296 3.71l18 18L21.71 20.296zM4 8.121V18c0 1.103.897 2 2 2h9.879l-2-2H6v-7.879L4 8.121z"}},{"tag":"circle","attr":{"cx":"18","cy":"6","r":"3"}}]})(props);
};
