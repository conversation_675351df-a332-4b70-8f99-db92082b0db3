// THIS FILE IS AUTO GENERATED
var GenIcon = require('../lib').GenIcon
module.exports.GiSnakeJar = function GiSnakeJar (props) {
  return GenIcon({"tag":"svg","attr":{"viewBox":"0 0 512 512"},"child":[{"tag":"path","attr":{"d":"M320.9 35.5c-1.6-.01-3.1-.01-4.7.02-43.7.6-99.6 12.79-143.5 34.77-25 12.56-46.1 28.28-59.2 46.11-13.1 17.7-18.54 37.1-13.4 59.8 3.5 15 13.9 26.9 29.8 36.8 15.9 10 36.9 17.3 58.9 22.4 44.2 10.1 93 11.1 113.9 9.4l19-1.5-10.9 15.6c-17.1 24.5-51.7 37.4-86.1 48-4.6 1.4-9.2 2.8-13.8 4.1h125.7c5.4-3.1 23.9-14.2 43.7-29.8 11.6-9.1 22.9-19.3 30.4-28.9 7.6-9.6 10.7-18.1 9.7-23.5-2.8-15.9-12.9-24.9-29.9-31.4s-40.5-9-65.2-9.6c-24.7-.5-50.7.8-73.6 1.3-22.9.6-42.2.7-56.3-4.2-9.2-3.2-16.3-8.2-19.5-15.7-3.3-7.4-1.5-15.7 2.2-22.3 7.5-13.4 22.5-25 40.7-35.4 18.3-10.5 39.7-19.28 60.1-23.86 20.4-4.58 39.9-5.6 54.8 3.22 27 16.04 51.4 30.74 72.6 38.14 17.2 6.1 31.3 7.6 44.3 2.5-6.4-8.6-18.2-23.9-36.5-43.03-24.9-25.86-57.2-50.94-79.9-52.5-4.3-.26-8.7-.44-13.3-.47zm28.6 17.93c4.7.08 10.3 2.45 15.9 6.99 13.9 11.24 13.5 18.32 8.2 32.7-7.2-11.47-19.8-22.21-37.1-28.25 2.1-7.82 6.9-11.53 13-11.44zM137 329v30h238v-30H137zm27.5 48l-42.7 57 13.2 53h242l13.2-53-42.7-57h-183z"}}]})(props);
};
