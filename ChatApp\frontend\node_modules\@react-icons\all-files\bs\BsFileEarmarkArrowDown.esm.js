// THIS FILE IS AUTO GENERATED
import { GenIcon } from '../lib';
export function BsFileEarmarkArrowDown (props) {
  return GenIcon({"tag":"svg","attr":{"viewBox":"0 0 16 16","fill":"currentColor"},"child":[{"tag":"path","attr":{"d":"M4 1h5v1H4a1 1 0 00-1 1v10a1 1 0 001 1h8a1 1 0 001-1V6h1v7a2 2 0 01-2 2H4a2 2 0 01-2-2V3a2 2 0 012-2z"}},{"tag":"path","attr":{"d":"M9 4.5V1l5 5h-3.5A1.5 1.5 0 019 4.5z"}},{"tag":"path","attr":{"fillRule":"evenodd","d":"M5.646 9.146a.5.5 0 01.708 0L8 10.793l1.646-1.647a.5.5 0 01.708.708l-2 2a.5.5 0 01-.708 0l-2-2a.5.5 0 010-.708z","clipRule":"evenodd"}},{"tag":"path","attr":{"fillRule":"evenodd","d":"M8 6a.5.5 0 01.5.5v4a.5.5 0 01-1 0v-4A.5.5 0 018 6z","clipRule":"evenodd"}}]})(props);
};
