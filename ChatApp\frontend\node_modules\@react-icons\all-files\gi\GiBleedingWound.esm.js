// THIS FILE IS AUTO GENERATED
import { GenIcon } from '../lib';
export function GiBleedingWound (props) {
  return GenIcon({"tag":"svg","attr":{"viewBox":"0 0 512 512"},"child":[{"tag":"path","attr":{"d":"M26.996 47.947c11.726 44.806 56.176 129.96 67.496 242.934-6.597 76.494-22.66 98.81-22.66 152.74 0 27.602 11.33 38.038 23.254 38.038 11.662 0 23.72-11.823 23.72-40.896 0-56.606-16.937-73.84-23.283-151.65 6.472-83.65 59.715-45.933 59.715 2.765 0-112.652 101.99-85.16 116.024-34.77-5.164 35.11-15.028 45.947-15.028 75.368 0 16.633 8.51 28.86 16.74 28.86 8.416 0 16.41-11.433 16.41-27.226 0-27.953-9.303-41.066-14.515-75.825 15.447-37.68 115.544-34.583 115.845-1.754-3.41 26.414-12.764 32.13-12.764 51.16 0 9.714 6.58 16.855 12.943 16.855 6.506 0 12.685-6.677 12.685-15.9 0-18.435-9.164-25.838-12.596-52.854 14.138-49.16 86.57-19.867 92.008-73.298-51.22 45.91-357.175 26.76-455.994-134.545zm128.85 266.22c-4.676 31.802-17.635 40.28-17.635 61.724 0 10.642 8.592 18.346 17.636 18.346 8.844 0 17.988-8.24 17.988-19.45 0-22.338-13.464-28.757-17.988-60.62z"}}]})(props);
};
