// THIS FILE IS AUTO GENERATED
var GenIcon = require('../lib').GenIcon
module.exports.BiNotification = function BiNotification (props) {
  return GenIcon({"tag":"svg","attr":{"viewBox":"0 0 24 24"},"child":[{"tag":"circle","attr":{"cx":"18","cy":"6","r":"3"}},{"tag":"path","attr":{"d":"M18,19H5V6h8c0-0.712,0.153-1.387,0.422-2H5C3.897,4,3,4.897,3,6v13c0,1.103,0.897,2,2,2h13c1.103,0,2-0.897,2-2v-8.422 C19.387,10.847,18.712,11,18,11V19z"}}]})(props);
};
