// THIS FILE IS AUTO GENERATED
var GenIcon = require('../lib').GenIcon
module.exports.GiJeweledChalice = function GiJeweledChalice (props) {
  return GenIcon({"tag":"svg","attr":{"viewBox":"0 0 512 512"},"child":[{"tag":"path","attr":{"d":"M261.938 16.28c-44.12 0-84.047 5.327-113.72 14.282-14.835 4.478-27.12 9.802-36.374 16.407-9.238 6.59-16.223 15.3-16.25 26l-.03.03c0 13.315 1.05 26.348 3.03 39 3.29-1.478 6.858-2.368 10.562-2.5 1.437-.05 2.874.004 4.344.188 11.763 1.47 21.494 9.193 28.688 19.187 7.193 9.994 12.08 22.69 13.437 36.375 1.357 13.686-1.013 26.1-7.063 35.594-2.544 3.994-5.964 7.404-9.937 9.937 30.266 41.24 74.044 67.345 123.313 67.345 48.71 0 92.033-25.536 122.25-65.97-4.95-2.59-9.168-6.572-12.188-11.31-6.05-9.495-8.42-21.91-7.063-35.595 1.357-13.686 6.276-26.38 13.47-36.375 7.193-9.994 16.893-17.718 28.656-19.188.735-.09 1.464-.157 2.187-.187.723-.03 1.438-.026 2.156 0 4.872.174 9.506 1.677 13.594 4.094 2.143-13.137 3.28-26.702 3.28-40.563V73c-.012-10.712-7.002-19.433-16.25-26.03-9.254-6.606-21.538-11.93-36.374-16.407-29.672-8.956-69.6-14.282-113.72-14.282zM152.78 48.72c-4.477 3.726-6.936 7.742-6.936 11.936 0 19.348 51.99 35.032 116.094 35.032C326.04 95.688 378 80 378 60.656c0-4.2-2.48-8.206-6.97-11.937 13.2 4.046 23.653 8.826 30.158 13.468 6.634 4.734 8.406 8.483 8.406 10.843 0 2.36-1.772 6.11-8.406 10.845-6.635 4.734-17.36 9.62-30.938 13.72-27.155 8.194-65.717 13.468-108.313 13.468-42.595 0-81.157-5.274-108.312-13.47-13.577-4.097-24.303-8.984-30.938-13.718-6.634-4.734-8.406-8.484-8.406-10.844 0-2.36 1.773-6.108 8.407-10.843 6.495-4.634 16.922-9.425 30.094-13.468zm-43.25 79.405c-.522-.007-1.012.024-1.5.094-2.917.418-5.155 2.195-7.5 5.874-3.124 4.904-5.242 13.385-4.218 23.72.818 8.25 3.468 16.2 7.094 22.686 1.035-8.94 8.625-15.875 17.844-15.875 6.984 0 13.026 3.994 16 9.813.1-2.316.036-4.772-.22-7.344-1.023-10.334-4.93-20.226-10.03-27.313-5.1-7.086-11.195-10.953-15.813-11.53-.577-.072-1.133-.118-1.656-.125zm301.5 0c-.522.007-1.078.053-1.655.125-4.618.577-10.71 4.444-15.813 11.53-5.1 7.088-9.006 16.98-10.03 27.314-.158 1.58-.234 3.105-.25 4.594 3.29-4.295 8.456-7.063 14.28-7.063 9.926 0 17.97 8.043 17.97 17.97 0 .23-.023.457-.032.686 4.477-6.908 7.81-15.998 8.75-25.468 1.024-10.333-1.094-18.814-4.22-23.718-2.342-3.68-4.58-5.456-7.5-5.875-.485-.07-.976-.102-1.5-.095zm-148.905 11.813c11.85 0 22.082 6.965 28.75 16.53 6.668 9.566 10.406 21.998 10.406 35.563 0 13.566-3.737 25.998-10.405 35.564-6.668 9.565-16.9 16.53-28.75 16.53-11.85 0-22.05-6.965-28.72-16.53C226.74 218.028 223 205.597 223 192.03c0-13.564 3.74-25.996 10.406-35.56 6.668-9.567 16.868-16.532 28.72-16.532zm0 18.687c-4.606 0-9.29 2.624-13.406 8.53-4.12 5.908-7.032 14.83-7.032 24.876 0 4.65.64 9.052 1.718 13.032 1.935-7.837 8.97-13.656 17.406-13.656 9.926 0 17.97 8.043 17.97 17.97 0 .62-.033 1.24-.094 1.843 2.38-5.356 3.906-11.975 3.906-19.19 0-10.044-2.945-18.967-7.063-24.874-4.116-5.907-8.8-8.53-13.405-8.53zm-39.22 128.72c-1.274 6.034-2.6 11.995-4 17.81.5.183 1.005.357 1.532.533 9.685 3.217 24.14 5.437 40.125 5.437 15.77 0 30.07-2.166 39.75-5.313-1.495-5.43-2.922-10.995-4.312-16.625-9.97 2.145-21.07 3.344-32.78 3.344-14.76 0-28.546-1.884-40.314-5.186zm-8.655 35.967c-11.726 42.656-26.91 77.18-44.656 95.375.498 1.15 1.65 2.71 4.125 4.72 3.802 3.088 10.298 6.512 18.718 9.406 16.838 5.787 41.21 9.625 68.125 9.625 26.913 0 51.317-3.838 68.156-9.625 8.418-2.894 14.914-6.318 18.717-9.407 3.163-2.568 4.162-4.385 4.407-5.562-17.226-18.314-33.287-52.22-46.28-94.094-12.233 3.898-27.832 6.063-45.002 6.063-17.664 0-33.65-2.294-46.03-6.407-.096-.03-.187-.062-.282-.094zm152.563 106.25c-2 3.152-4.644 5.948-7.594 8.344-6.39 5.19-14.64 9.226-24.44 12.594-19.6 6.737-45.566 10.625-74.217 10.625-28.652 0-54.588-3.888-74.188-10.625-9.8-3.368-18.05-7.405-24.438-12.594-2.89-2.348-5.484-5.082-7.468-8.156-.4.18-.788.398-1.19.563-11.745 4.817-21.073 11.418-23.468 21.53-4.48 18.93 58.848 38.31 131.875 38.313 73.028.002 132.407-17.273 132.407-38.312 0-8.636-11.4-13.37-27.28-22.28z"}}]})(props);
};
