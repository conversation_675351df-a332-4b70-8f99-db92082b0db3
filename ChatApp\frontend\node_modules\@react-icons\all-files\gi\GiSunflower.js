// THIS FILE IS AUTO GENERATED
var GenIcon = require('../lib').GenIcon
module.exports.GiSunflower = function GiSunflower (props) {
  return GenIcon({"tag":"svg","attr":{"viewBox":"0 0 512 512"},"child":[{"tag":"path","attr":{"d":"M274 26.5c-10.161 19.207-21.438 38.715-28.715 58.063-8.69 23.102-11.63 44.996-1.693 67.173 13.223-1.447 27.547-.183 39.744 2.809 12.33-23.057 14.079-46.848 9.973-70.748C289.933 64.15 282.524 44.657 274 26.5zM138.518 47.807c4.509 17.9 8.552 37.348 13.855 56.334 8.076 28.912 19.233 55.27 37.055 70.73a104.882 104.882 0 0 1 37.006-19.625c-6.189-20.148-23.477-43.439-44.252-65.334-14.327-15.099-29.39-29.04-43.664-42.105zm234.304 32.525c-17.615 14.32-33.925 25.364-46.11 36.711-13.703 12.762-22.372 25.14-24.421 44.736a105.643 105.643 0 0 1 31.793 24.114c16.86-14.798 25.494-31.17 30.734-50.211 4.5-16.351 6.28-35.29 8.004-55.35zM60.547 131.234c8.7 18.074 19.14 36.126 32.246 51.147 15.944 18.273 35.498 31.934 61.164 36.928 8.262-10.316 15.239-23.396 23.03-32.37-10.91-21.855-29.799-33.447-53.284-41.32-19.598-6.57-41.846-10.14-63.156-14.385zm419.176 29.586c-18.258 2.726-38.008 4.836-57.424 8.244-30.068 5.28-57.828 13.983-74.904 30.739l16.084 38.074c20.79-3.984 45.954-19.028 70.046-37.748 16.436-12.771 31.79-26.39 46.198-39.309zM256 169c-48.155 0-87 38.845-87 87s38.845 87 87 87 87-38.845 87-87-38.845-87-87-87zm0 12.201c41.204 0 74.799 33.595 74.799 74.799 0 41.204-33.595 74.799-74.799 74.799-41.204 0-74.799-33.595-74.799-74.799 0-41.204 33.595-74.799 74.799-74.799zm15.777 8.682l-4.222 17.496 14.402 3.476 4.223-17.496zm-40.351 8.687v18h18.873v-18zm-27.647 18.883l-8.94 15.395 15.567 9.039 8.94-15.395zm92.41 3.002l-16.884 1.49 1.582 17.932 16.884-1.49zm-39.587 9.732l-15.825 10.12 9.7 15.164 15.824-10.12zm-126.334 2.766c-19.262.194-39.851 5.321-54.51 11.799-15.367 7.173-30.94 18.094-47.332 29.785 22.322 4.13 41.304 9.39 57.842 11.313 20.545 2.387 46.184-1.584 64.468-16.21l-1.918-35.103c-5.857-1.164-12.13-1.648-18.55-1.584zm178.664 13.879l-6.383 16.828 14.4 5.463 6.383-16.828zm-100.077 2.397l-.546 17.992 16.388.498.547-17.992zm172.834 4.457c-6.146.068-12.51.747-19.367 2.08-1.959 15.376-6.896 31.081-9.855 43.84 18.294 14.066 38.106 17.06 61.488 14.693 20.415-2.067 43.486-8.631 67.697-15.301-15.874-13.122-32.566-25.334-50.322-33.695-17.507-7.256-31.706-11.345-47.02-11.61a93.005 93.005 0 0 0-2.62-.008zm-100.263 11.869l-20.362.498.442 17.996 20.361-.498zM215.809 280.8l-4.52 17.424 18.676 4.177 4.52-17.424zm86.115.584l-12.912 9.931 10.972 14.27 12.912-9.932zm-145.2 15.838c-23.384 1.195-36.822 10.464-50.605 25.886-11.52 12.892-28.695 33.236-43.455 51.936 21.769-.505 47.299-4.865 64.227-9.998 19.952-6.05 36.586-16.937 51.19-38.754-8.307-10.52-13.16-18.75-21.356-29.07zm110.77 5.351l-15.892.496.562 17.993 15.893-.497zm72.1 16.877c-5.326 12.205-30.778 23.962-35.227 31.528 27.547 39.306 81.094 54.027 126.317 56.8-13.04-18.243-26.832-38.452-42.409-55.164-14.764-15.84-30.642-28.043-48.681-33.164zm-146.858 20.285c-13.155 15.84-23.681 41.258-31.543 68.727-5.727 20.01-10.082 40.067-14.084 59 13.128-12.977 27.824-26.34 41.483-40.553 21.13-21.987 38.386-45.374 42.303-68.953a104.661 104.661 0 0 1-38.159-18.22zm97.137 21.35l-43.373 3.172c-7.303 45.857 19.494 90.883 47.504 119.898 11.935-41.049 20.331-92.31-4.131-123.07z"}}]})(props);
};
