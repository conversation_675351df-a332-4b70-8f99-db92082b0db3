// THIS FILE IS AUTO GENERATED
var GenIcon = require('../lib').GenIcon
module.exports.BiTaskX = function BiTaskX (props) {
  return GenIcon({"tag":"svg","attr":{"viewBox":"0 0 24 24"},"child":[{"tag":"path","attr":{"d":"M3,20c0,1.103,0.897,2,2,2h14c1.103,0,2-0.897,2-2V5c0-1.103-0.897-2-2-2h-2c0-0.553-0.447-1-1-1H8C7.447,2,7,2.447,7,3H5 C3.897,3,3,3.897,3,5V20z M5,5h2v2h10V5h2v15H5V5z"}},{"tag":"path","attr":{"d":"M14.292 10.295L12 12.587 9.708 10.295 8.294 11.709 10.586 14.001 8.294 16.293 9.708 17.707 12 15.415 14.292 17.707 15.706 16.293 13.414 14.001 15.706 11.709z"}}]})(props);
};
