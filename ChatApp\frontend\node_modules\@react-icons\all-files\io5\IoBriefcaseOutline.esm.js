// THIS FILE IS AUTO GENERATED
import { GenIcon } from '../lib';
export function IoBriefcaseOutline (props) {
  return GenIcon({"tag":"svg","attr":{"viewBox":"0 0 512 512"},"child":[{"tag":"rect","attr":{"width":"448","height":"320","x":"32","y":"128","fill":"none","strokeLinejoin":"round","strokeWidth":"32","rx":"48","ry":"48"}},{"tag":"path","attr":{"fill":"none","strokeLinecap":"round","strokeLinejoin":"round","strokeWidth":"32","d":"M144 128V96a32 32 0 0132-32h160a32 32 0 0132 32v32m112 112H32m288 0v24a8 8 0 01-8 8H200a8 8 0 01-8-8v-24"}}]})(props);
};
