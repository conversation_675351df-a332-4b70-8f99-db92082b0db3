// THIS FILE IS AUTO GENERATED
var GenIcon = require('../lib').GenIcon
module.exports.BiSlideshow = function BiSlideshow (props) {
  return GenIcon({"tag":"svg","attr":{"viewBox":"0 0 24 24"},"child":[{"tag":"path","attr":{"d":"M20,3H4C2.897,3,2,3.897,2,5v10c0,1.103,0.897,2,2,2h7v3H8v2h3h2h3v-2h-3v-3h7c1.103,0,2-0.897,2-2V5 C22,3.897,21.103,3,20,3z M4,15V5h16l0.001,10H4z"}},{"tag":"path","attr":{"d":"M10 13L15 10 10 7z"}}]})(props);
};
