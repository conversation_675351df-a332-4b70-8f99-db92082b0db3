// THIS FILE IS AUTO GENERATED
var GenIcon = require('../lib').GenIcon
module.exports.GiTribalPendant = function GiTribalPendant (props) {
  return GenIcon({"tag":"svg","attr":{"viewBox":"0 0 512 512"},"child":[{"tag":"path","attr":{"d":"M50.826 18C63.09 37.645 78.948 61.515 97.252 85.465c8.806 11.522 18.178 23.057 27.992 34.156a27.138 27.138 0 0 1 5.83-7.068 27.138 27.138 0 0 1 7.824-4.688c-9.537-10.77-18.713-22.036-27.345-33.33C96.514 54.86 83.159 35.21 72.133 18zm389.041 0c-11.026 17.21-24.381 36.859-39.42 56.535-8.631 11.294-17.806 22.559-27.343 33.328a27.132 27.132 0 0 1 7.824 4.688 27.133 27.133 0 0 1 5.83 7.068c9.813-11.098 19.184-22.633 27.99-34.154 18.304-23.95 34.163-47.82 46.426-67.465zm-78.385 105.752a19.11 19.11 0 0 0-3.462.459c-4.923 1.087-10.623 4.336-15.194 9.654-4.571 5.318-6.93 11.445-7.265 16.475-.336 5.03 1.081 8.54 3.648 10.746 2.567 2.206 6.247 3.079 11.17 1.992 4.923-1.087 10.626-4.338 15.197-9.656 4.571-5.318 6.929-11.443 7.264-16.473.335-5.03-1.08-8.541-3.647-10.748-1.925-1.655-4.477-2.559-7.71-2.45zm-210.964.002c-3.233-.11-5.784.794-7.71 2.45-2.566 2.206-3.983 5.715-3.648 10.745.335 5.03 2.695 11.155 7.266 16.473 4.57 5.318 10.27 8.57 15.193 9.656 4.923 1.087 8.607.216 11.174-1.99s3.98-5.718 3.645-10.748c-.336-5.03-2.691-11.155-7.262-16.473s-10.275-8.57-15.197-9.656a19.083 19.083 0 0 0-3.461-.457zm43.199 35.035c-1.272 5.96-4.184 11.532-8.965 15.738 12.015 8.42 24.372 15.304 36.965 19.95l-4.297-21.487c-7.848-3.8-15.778-8.584-23.703-14.2zm124.568 0c-7.925 5.617-15.857 10.402-23.705 14.201l-4.297 21.487c12.593-4.646 24.95-11.53 36.965-19.95-4.781-4.206-7.69-9.778-8.963-15.738zM234.98 169l12.4 62h17.242l12.4-62zm-8.141 51.082c-2.807 3.896-5.507 8.361-8.043 13.434C208.032 255.04 201 285.87 201 320c0 34.13 7.032 64.959 17.795 86.484C229.558 428.01 243.19 439 256 439c12.81 0 26.442-10.99 37.205-32.516C303.968 384.96 311 354.13 311 320c0-34.13-7.032-64.959-17.795-86.484-2.536-5.073-5.236-9.538-8.043-13.434L279.38 249H232.62zm-43.127 8.217a86.606 86.606 0 0 0-4.041.058c-28.134 1.064-52.15 15.419-73.293 35.168-19.685 18.388-35.63 41.18-48.29 61.676 21.541-12.732 46.572-26.079 70.741-35.183 18.61-7.01 36.408-11.642 52.168-10.774 1.722.095 3.422.274 5.102.518 2.933-18.506 7.86-35.372 14.42-49.733-5.759-1.105-11.364-1.682-16.807-1.73zm144.578 0c-5.443.048-11.048.625-16.807 1.73 6.56 14.36 11.487 31.227 14.42 49.733 1.68-.244 3.38-.423 5.102-.518 15.76-.868 33.558 3.763 52.168 10.774 24.17 9.104 49.2 22.451 70.74 35.183-12.66-20.495-28.604-43.288-48.289-61.676-21.143-19.75-45.159-34.104-73.293-35.168a86.606 86.606 0 0 0-4.04-.058zM235.916 263h40.168L295 284.62v69.962l-39 62.398-39-62.398V284.62zm8.168 18L235 291.38v58.038l21 33.6 21-33.6v-58.037L267.916 281zm-61.055 41.436c-35.701 3.372-54.91 22.392-68.078 47.367-9.357 17.748-14.703 38.448-18.797 58.22 14.458-18.095 29.949-35.572 45.639-48.222 12.3-9.918 24.722-17.233 37.904-18.688a34.251 34.251 0 0 1 6.535-.076c-1.98-12.225-3.083-25.174-3.203-38.601zm145.942 0c-.12 13.427-1.222 26.376-3.203 38.601a34.246 34.246 0 0 1 6.535.076c13.182 1.455 25.603 8.77 37.904 18.688 15.69 12.65 31.181 30.127 45.639 48.222-4.094-19.772-9.44-40.472-18.797-58.22-13.168-24.975-32.377-43.995-68.078-47.367zm-92.84 129.7L256 491.874l19.87-39.736C269.69 455.27 263.05 457 256 457c-7.05 0-13.69-1.729-19.87-4.863z"}}]})(props);
};
