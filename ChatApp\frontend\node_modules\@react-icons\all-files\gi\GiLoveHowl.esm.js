// THIS FILE IS AUTO GENERATED
import { GenIcon } from '../lib';
export function GiLoveHowl (props) {
  return GenIcon({"tag":"svg","attr":{"viewBox":"0 0 512 512"},"child":[{"tag":"path","attr":{"d":"M363.844 30.03C322.35 30.595 280.21 52.186 253 99.313c-41.913-117.77-236.493-76.29-232 64.5 1.226 38.39 16.562 72.577 38.875 103.657l106.47-35.5c-19.878-1.048-40.956-9.436-68.75-32.595 32.054-18.916 57.893-20.767 90.936-23.47 26.713-14.39 66.464-26.884 84.095-17.5 30.984-18.317 69.612-41.87 108.625-56.655 5.083 19.926 5.332 44.544 3.5 67.75l-10.5-14.03c-51.46 11.565-77.358 112.79-.594 70.686l10.53-16.937c1.766 15.882 3.392 30.556.002 43.78-34.038 24.007-68.83 43.753-103.375 59.594-17.754 15.61-52.626 65.57-62.844 108.937 19.995 20.072 34.94 39.36 40.093 58.595 20.665-77.113 240.783-175.82 236.375-316.313-2.648-84.358-65.862-134.66-130.594-133.78zm-104.03 147.532c-13.527.067-25.784 3.437-37.158 9.094 17.228 19.24 47.604 22.858 43.03-8.937-1.984-.13-3.94-.167-5.873-.157z"}}]})(props);
};
