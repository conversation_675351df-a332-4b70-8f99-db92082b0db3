// THIS FILE IS AUTO GENERATED
var GenIcon = require('../lib').GenIcon
module.exports.GiRegeneration = function GiRegeneration (props) {
  return GenIcon({"tag":"svg","attr":{"viewBox":"0 0 512 512"},"child":[{"tag":"path","attr":{"d":"M229.594 16c-73.58 4.91-128.97 66.775-128.97 142.344 0 18.565 3.507 36.337 9.907 52.594 6.526 16.573 14.974 35.78 81.72 99.062l19.47-11.375c-1.224-3.453-1.876-7.155-1.876-11.03 0-15.672 10.893-28.772 25.437-32.033v-22.75c-56.316-3.484-101.03-50.67-101.03-108.437 0-56.834 40.318-103.563 95.344-108.375zm12.75 137.313c-29.654 0-57.053 9.766-79.22 26.28 6.817 9.665 15.288 18.155 24.907 24.938 15.6-10.34 34.262-16.374 54.314-16.374 21.142 0 40.753 6.698 56.844 18.094 14.128-3.45 28.403-6.214 42.875-7.75-24.47-27.71-60.07-45.188-99.72-45.188zM355 209.093c-17.672.308-46.292 5.044-106.53 23.438v22.69c3.57.656 7.083 1.967 10.405 3.905 13.433 7.834 19.226 23.895 14.75 38.25l19.5 11.375c31.146-47.53 93.95-63.04 143.47-34.156 48.717 28.416 68.605 87.043 45.217 137.594 32.582-66.832 7.25-146.247-57.53-184.032-15.916-9.282-32.896-15.096-50.032-17.625-5.46-.805-11.217-1.576-19.25-1.436zm-239.47 36.813c-11.52 35.264-8.7 75.154 11.126 109.844 14.828 25.944 36.886 45.05 62.125 56.188 4.878-10.796 7.934-22.474 8.94-34.282-16.666-8.476-31.163-21.767-41.19-39.312-10.57-18.498-14.63-39.005-12.905-58.78-10.022-10.637-19.54-21.764-28.094-33.658zM375.19 273.5c-11.693 1.13-23.22 4.29-33.844 9.313 1.066 18.82-3.1 38.174-13.125 55.718-10.573 18.5-26.094 32.307-43.908 40.69-4.105 14.085-8.893 27.944-14.812 41.374 35.99-7.553 68.768-29.967 88.594-64.656 14.827-25.946 20.167-54.788 17.094-82.438zM267.25 308.844c-2.35 2.796-5.24 5.218-8.563 7.156-13.432 7.834-30.12 4.875-40.187-6.22l-19.47 11.345c25.173 51.015 7.052 113.74-42.468 142.625C107.844 492.166 47.638 480.238 16 434.5c40.998 61.92 121.75 79.472 186.53 41.688 15.916-9.282 29.36-21.24 40.095-34.97 10.946-13.994 23.187-30.992 44.063-121.03l-19.438-11.344z"}}]})(props);
};
