// THIS FILE IS AUTO GENERATED
var GenIcon = require('../lib').GenIcon
module.exports.GiKoala = function GiKoala (props) {
  return GenIcon({"tag":"svg","attr":{"viewBox":"0 0 512 512"},"child":[{"tag":"path","attr":{"d":"M419.3 22.75C394.9 95.5 364.6 168.1 331.9 240.5c8.5-3 17.6-6 26.8-7.4 10.2-1.5 21.2-.9 30.4 5.2 16.9-44.3 33.8-88 48.8-125.6 15.6-38.98 28.6-70.3 38.2-89.95zm-104.9 2.33c-17.7.19-28.3 6.53-35.4 15.67-1.5 1.88-2.8 3.9-3.9 6.04 4.4 1.62 8.7 3.46 12.8 5.54 8.3-6.69 16.6-10.28 24.7-10.75.7 0 1.3-.1 2-.1 9.7.1 18.3 5.01 23.6 11.91 10.5 13.45 11.8 34.23.7 50.41 3.9 8.8 6.6 18.5 7.7 28.9 0 .2 0 .5.1.7 9.2-2.4 14.6-8 18.4-16.4 4.5-10 5.4-23.99 2.5-38.09-2.2-10.74-6.6-21.41-12.7-30.3 7.5 1.22 12.5 3.8 18.1 6.04-11.7-14.24-22-18.89-37.8-24.5-6.2-3.34-13.1-5.16-20.8-5.07zM108.2 54.85c-.7 0-1.3 0-1.9.1-8.27.43-17.24 3.46-27.14 10.37a46.584 46.584 0 0 0-7.57 6.58c-12.68 10.83-23.36 24.41-35.72 35.6 7.25-2.2 13.74-6 21.12-8.22-4.64 16.42-4.48 34.32-7.53 51.22 4.03-5.7 6.62-12.2 10.84-17.9-.3 18.4 8.26 35.9 12.5 53.9 1.53-7.6.87-15.2 3.82-22.9 4.87 6.3 10.39 11.4 16.1 14.7 10.28 6 20.58 7.1 32.68.1l1.6-.8h.1c-2.5-5.9-4.4-12.4-5.4-19.4-6.2-3.5-10.4-9.6-14-16.1-5.2-9.1-8.87-20-9.47-30.7-.4-5.3 0-10.6 1.77-15.67 1.9-5.07 5.5-9.98 10.7-12.85 4-2.14 8.4-3.14 13.2-3.04 6 .11 12.7 1.89 20.2 5.1 1.9-2.48 3.8-4.89 5.9-7.25-7.7-9.58-18-17.69-29.4-20.99-4-1.16-8.1-1.83-12.4-1.8zm121.3 1.51c-3.2 0-6.5.1-9.7.27-19 1.31-40.2 14.38-56.1 32.51-15.8 18.16-25.8 40.96-25.1 58.66 1.3 30.6 17.8 46.5 39.6 56.1 21.8 9.6 48.9 11.1 65.7 9 16.7-2 40.3-9.9 58-23.2 17.7-13.4 29.4-30.9 26.9-55.1-2.7-25.6-16.7-45.57-36.8-59.22-17.6-11.94-39.9-18.7-62.5-19.02zm85.8 3.08h-1.6c-2.4.14-5.4.97-9 2.97 8.8 6.31 16.6 13.86 23.1 22.5 1.7-7.75 0-15.77-3.7-20.46-2.4-3.12-5.1-4.82-8.8-5.02zM279.5 94.4a16 16 0 0 1 16 16 16 16 0 0 1-16 16 16 16 0 0 1-16-16 16 16 0 0 1 16-16zm-156.6 3.49c-1.8 0-2.9.33-3.6.69-1.1.56-1.7 1.26-2.4 3.12-.7 2-1.1 5-.9 8.6.5 6.4 2.9 14.5 6.1 21 2.1-10.6 6.2-21.1 11.9-31.1-5-1.81-8.6-2.34-11.1-2.31zm83.8 15.31a16 16 0 0 1 16 16 16 16 0 0 1-16 16 16 16 0 0 1-16-16 16 16 0 0 1 16-16zm44.9 13.5c8.9-.1 17.6 8.9 22.9 16.4 6.8 9.5 7.7 34.3 7.7 34.3 3 15-34.7 26.3-41.1 13 0 0-11.2-22.5-9.6-34.3 1.5-11.2 6.4-26.4 17.3-29 .9-.2 1.8-.3 2.8-.4zM168.1 219c-40.8 26.6-66.3 62.9-79.47 99.1-14.56 39.9-13.8 79.6-3.14 104.2 10.54 24.5 30.71 40.3 55.11 49.7 24.4 9.5 52.9 12.1 78 9.7 20.7-2 36.3-13.1 51.6-27.6 15.2-14.4 29.4-32 47.4-44.4l1.1-.7 1.2-.4c15.3-5.1 24.6-13.2 27.7-21.1 3.1-7.9 1.8-17.2-9.4-29.3 1.2 1.3 0-.1-3.3-.2-3.3-.2-8.1.2-13.3 1.2-9.4 1.8-20.5 5.7-28.1 10.3-7.8 21.5-24.4 43.8-51.6 63.5l-10.4-14.6c56-40.3 57.2-86.2 39.4-107.5-8.9-10.6-22.5-16.1-40.1-12.4-17.6 3.7-39.3 17.2-61.2 46.6l-14.4-10.8c23.7-31.7 48.7-48.5 72-53.3 5.8-1.2 11.5-1.7 16.9-1.4 16.4.9 30.8 8.2 40.5 19.9 2.2 2.6 4.2 5.5 5.9 8.5 31.2-.9 62.5-.6 78.6-10.4 12-7.3 18.2-15.8 19.7-22.9 1.6-7-.2-13.4-8-20.3-4.7-4-10.7-4.9-19.5-3.6-8.8 1.3-19.4 5.1-29.9 8.8l-3 1.1-3-1.1c-37.9-13.3-46.7-25.4-57-32.9l-1.8-.3c-7.2 2.1-14.1 3.5-20.5 4.3-19.9 2.4-49.1 1-75.1-10.4-1-.4-1.9-.9-2.9-1.3zm189.5 102.2c-17.9 4.5-38.2 4.3-58 4.5.1 5.9.3 11.9 0 17.8 0 1.1-.1 2.2-.1 3.4 6.1-2.4 12.6-4.1 18.7-5.3 6.3-1.2 12.2-1.8 17.7-1.5 4.5.3 9.1.7 13.2 3.8 2.8-7.5 5.6-14.9 8.5-22.7zm-42.8 113.9c-10.4 9.5-20.7 21.1-32.3 32-8.7 8.2-18.2 16.1-29.2 22.1 10.3.1 23.1.1 40.7-.2 1-2.4 2-4.9 3.6-9.1 4.1-10.3 10.1-25.9 17.2-44.8z"}}]})(props);
};
