// THIS FILE IS AUTO GENERATED
var GenIcon = require('../lib').GenIcon
module.exports.DiGrails = function DiGrails (props) {
  return GenIcon({"tag":"svg","attr":{"version":"1.1","viewBox":"0 0 34 32"},"child":[{"tag":"path","attr":{"d":"M16.922 2.029c-7.716 0-13.971 6.255-13.971 13.971s6.255 13.971 13.971 13.971c7.716 0 13.971-6.255 13.971-13.971s-6.255-13.971-13.971-13.971zM13.224 17.246c-0.178 0.106-0.35 0.178-0.568 0.238-0.172 0.047-0.344 0.106-0.382 0.13-0.107 0.068-0.289 0.377-0.391 0.66-0.127 0.354-0.172 0.721-0.131 1.061 0.038 0.314 0.168 0.915 0.252 1.172 0.070 0.213 0.387 0.889 0.543 1.158 0.13 0.224 0.136 0.299 0.030 0.426-0.17 0.204-0.53 0.356-1.030 0.435-0.43 0.068-0.788 0.079-1.134 0.035-0.639-0.082-1.090-0.234-1.243-0.418-0.141-0.169-0.136-0.193 0.193-0.857 0.321-0.649 0.408-0.879 0.535-1.414 0.066-0.279 0.079-0.4 0.079-0.755-0.001-0.497-0.046-0.706-0.222-1.034-0.219-0.41-0.313-0.484-0.79-0.617-0.574-0.161-1.346-0.682-2.049-1.383-0.353-0.353-0.701-0.895-0.918-1.432-0.18-0.446-0.341-1.379-0.248-1.439 0.024-0.015 0.807-0.024 1.594-0.026s1.579 0.004 1.618 0.019c0.028 0.011 0.077 0.081 0.109 0.158 0.114 0.268 0.827 1.233 1.161 1.569 0.644 0.649 1.003 0.929 1.793 1.405 0.397 0.239 0.823 0.432 1.287 0.584 0.242 0.079 0.227 0.136-0.091 0.325zM18.266 19.632c0.002 0.639 0.009 0.721 0.080 1.026 0.080 0.342 0.401 1.328 0.546 1.675 0.147 0.353 0.633 1.369 0.744 1.555 0.131 0.219 0.135 0.294 0.023 0.472-0.231 0.367-1.026 0.654-2.126 0.765-0.539 0.055-0.84 0.054-1.343-0.002-0.515-0.057-0.838-0.122-1.186-0.238-0.164-0.055-0.334-0.108-0.377-0.118-0.127-0.031-0.43-0.251-0.515-0.374-0.157-0.229-0.162-0.213 0.329-1.205 0.446-0.902 0.499-1.033 0.723-1.791 0.189-0.64 0.244-0.886 0.278-1.263 0.043-0.474 0.018-1.226-0.049-1.514-0.116-0.492-0.405-1.018-0.778-1.415-0.253-0.27-0.493-0.436-0.793-0.548-0.851-0.32-1.748-0.824-2.393-1.348-0.333-0.27-0.967-0.893-1.217-1.196-0.674-0.816-1.265-1.984-1.475-2.914-0.107-0.473-0.185-1.038-0.173-1.249l0.008-0.159v0.001h16.568l0.010 0.136c0.025 0.344-0.182 1.371-0.374 1.855-0.059 0.149-0.145 0.384-0.191 0.522s-0.109 0.288-0.14 0.333c-0.031 0.045-0.115 0.192-0.186 0.324-0.585 1.092-1.713 2.248-2.903 2.974-0.404 0.247-1.073 0.571-1.433 0.694-0.156 0.053-0.324 0.133-0.374 0.176-0.247 0.217-0.587 0.583-0.704 0.758-0.072 0.108-0.146 0.206-0.166 0.218-0.064 0.040-0.304 0.635-0.362 0.899-0.046 0.211-0.056 0.38-0.054 0.951zM26.404 16.568c-0.493 0.432-0.947 0.692-1.574 0.905-0.391 0.132-0.406 0.141-0.511 0.282-0.159 0.213-0.353 0.615-0.387 0.802-0.051 0.279-0.036 0.824 0.032 1.161 0.135 0.673 0.285 1.093 0.626 1.758 0.21 0.41 0.217 0.428 0.182 0.533-0.079 0.242-0.524 0.448-1.154 0.534-0.75 0.103-1.525 0.016-2.012-0.226-0.209-0.104-0.395-0.288-0.395-0.391 0-0.020 0.135-0.303 0.299-0.629 0.43-0.856 0.63-1.556 0.636-2.224 0.003-0.378-0.048-0.6-0.221-0.972-0.184-0.392-0.306-0.485-0.868-0.658-0.178-0.055-0.291-0.111-0.644-0.32-0.201-0.119-0.171-0.153 0.291-0.311 0.536-0.184 1.214-0.563 1.851-1.037 0.339-0.252 1.16-1.058 1.437-1.411 0.242-0.307 0.532-0.741 0.641-0.957 0.043-0.087 0.105-0.171 0.136-0.189 0.039-0.021 1.287-0.034 2.238-0.032 0.57 0.002 1.034 0.006 1.066 0.019 0.037 0.014 0.047 0.066 0.047 0.245 0 0.294-0.225 1.175-0.364 1.424-0.462 0.829-0.69 1.115-1.351 1.693z"}}]})(props);
};
