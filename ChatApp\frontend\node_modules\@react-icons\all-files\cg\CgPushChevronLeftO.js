// THIS FILE IS AUTO GENERATED
var GenIcon = require('../lib').GenIcon
module.exports.CgPushChevronLeftO = function CgPushChevronLeftO (props) {
  return GenIcon({"tag":"svg","attr":{"viewBox":"0 0 24 24","fill":"none"},"child":[{"tag":"path","attr":{"d":"M10 16H8V8H10V16Z","fill":"currentColor"}},{"tag":"path","attr":{"d":"M15.2974 7.75732L16.7116 9.17153L13.8832 12L16.7116 14.8284L15.2974 16.2426L11.0547 11.9999L15.2974 7.75732Z","fill":"currentColor"}},{"tag":"path","attr":{"fillRule":"evenodd","clipRule":"evenodd","d":"M1 12C1 18.0751 5.92487 23 12 23C18.0751 23 23 18.0751 23 12C23 5.92487 18.0751 1 12 1C5.92487 1 1 5.92487 1 12ZM3 12C3 16.9706 7.02944 21 12 21C16.9706 21 21 16.9706 21 12C21 7.02944 16.9706 3 12 3C7.02944 3 3 7.02944 3 12Z","fill":"currentColor"}}]})(props);
};
