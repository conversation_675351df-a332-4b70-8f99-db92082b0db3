import express from 'express'
import dotenv from 'dotenv'
import mongoose from 'mongoose';
import postRoutes from './routes/postRoutes.js';
import userRoutes from './routes/userRoutes.js'
import cookieParser from 'cookie-parser';
import cors from  'cors'
import postModel from './model/userModel.js';
import { userPost } from './Contoller/postController.js';
const app = express()
app.use(cors({
    origin: process.env.Fronted_URL,  // frontend url
    credentials:true
})) 
app.use(cookieParser())
app.use(express.json());
app.use(express.urlencoded({extended:true}));


dotenv.config()
 const PORT = process.env.Port;
 const MONGODB_URI=process.env.MONGODB_URI

app.use('/api/user',postRoutes)
app.use('/api/user',userRoutes)
 

app.get('/',function(req,res){
    res.send("hello world from backend")
})

try {
    mongoose.connect(MONGODB_URI)
.then(
    console.log("mongodb connection")
)
} catch (error) {
    console.log(`Mongodb not connected: ${error.message}`);
    
}

app.get('/api/test', async function(req,res){
   const use =  await postModel.find()
   res.send(use)
})

app.listen(PORT,function(){
    console.log(`server is running on ${PORT}`);
    
})