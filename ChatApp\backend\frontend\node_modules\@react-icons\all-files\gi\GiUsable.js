// THIS FILE IS AUTO GENERATED
var GenIcon = require('../lib').GenIcon
module.exports.GiUsable = function GiUsable (props) {
  return GenIcon({"tag":"svg","attr":{"viewBox":"0 0 512 512"},"child":[{"tag":"path","attr":{"d":"M256 16C123.45 16 16 123.453 16 256c0 132.548 107.45 240 240 240 132.548 0 240-107.452 240-240S388.548 16 256 16zm0 60c99.41 0 180 80.59 180 180s-80.59 180-180 180S76 355.41 76 256 156.59 76 256 76zm-15 45c-7.5 0-10.802 7.833-9.563 14.313l19.907 91.218c.73 9.53-7.815 8.665-12.625 4.19l-45.064-61.282c-2.706-4.937-10.62-10.596-17.594-6.782-6.973 3.815-6.41 12.334-4 17.344l40.344 62.938c10.388 16.62 12.386 32.35 12.563 52.812-.34 13.825-11.4 20.024-21.97 20.688l-43.53-3.844c-11.047.024-23.47 7.79-23.47 18.406 0 10.617 17.494 9.182 27 11.625 0 0 62.778 18.345 86.844 18.375 16.422.017 16.003 21.326 25.687 21.28 9.16-.04 41.118-11.973 46.5-14.655 5.385-2.682 29.853-15.973 30.126-22.22.323-7.4-13.844-20.096-9.344-28.217 10.413-27.692.305-43.128 7.47-71.688C355.758 223.666 376 181 376 181c0-4.82-2.55-8.258-6.47-10.813-4.212-1.82-11.514-.874-13.374 3.72l-30.594 54.75c-4.69 4.277-10.706 3.163-12.125-4.5l2-86.47c.74-2.896-3.292-10.392-10.28-11.406-6.99-1.01-10.62 3.866-10.938 6.657l-6.22 87.5c-1.63 4.043-7.696 4.632-9.406.25L256.72 130.72c.19-5.952-5.877-9.72-15.72-9.72z"}}]})(props);
};
