// THIS FILE IS AUTO GENERATED
var GenIcon = require('../lib').GenIcon
module.exports.BiWifi2 = function BiWifi2 (props) {
  return GenIcon({"tag":"svg","attr":{"viewBox":"0 0 24 24"},"child":[{"tag":"path","attr":{"d":"M17.671,14.307C16.184,12.819,14.17,12,12,12s-4.184,0.819-5.671,2.307l1.414,1.414c1.11-1.11,2.621-1.722,4.257-1.722 c1.636,0.001,3.147,0.612,4.257,1.722L17.671,14.307z"}},{"tag":"path","attr":{"d":"M20.437,11.292c-4.572-4.573-12.301-4.573-16.873,0l1.414,1.414c3.807-3.807,10.238-3.807,14.045,0L20.437,11.292z"}},{"tag":"circle","attr":{"cx":"12","cy":"18","r":"2"}}]})(props);
};
