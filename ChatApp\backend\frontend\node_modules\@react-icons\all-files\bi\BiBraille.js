// THIS FILE IS AUTO GENERATED
var GenIcon = require('../lib').GenIcon
module.exports.BiBraille = function BiBraille (props) {
  return GenIcon({"tag":"svg","attr":{"viewBox":"0 0 24 24"},"child":[{"tag":"circle","attr":{"cx":"4","cy":"7","r":"2"}},{"tag":"circle","attr":{"cx":"9","cy":"12","r":"2"}},{"tag":"circle","attr":{"cx":"15","cy":"7","r":"2"}},{"tag":"circle","attr":{"cx":"15","cy":"12","r":"2"}},{"tag":"circle","attr":{"cx":"15","cy":"17","r":"2"}},{"tag":"circle","attr":{"cx":"20","cy":"7","r":"2"}},{"tag":"circle","attr":{"cx":"4","cy":"17","r":"2"}}]})(props);
};
