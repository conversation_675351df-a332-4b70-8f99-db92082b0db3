(()=>{var B;function I(G){return I=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(J){return typeof J}:function(J){return J&&typeof Symbol=="function"&&J.constructor===Symbol&&J!==Symbol.prototype?"symbol":typeof J},I(G)}function x(G,J){var H=Object.keys(G);if(Object.getOwnPropertySymbols){var X=Object.getOwnPropertySymbols(G);J&&(X=X.filter(function(Z){return Object.getOwnPropertyDescriptor(G,Z).enumerable})),H.push.apply(H,X)}return H}function q(G){for(var J=1;J<arguments.length;J++){var H=arguments[J]!=null?arguments[J]:{};J%2?x(Object(H),!0).forEach(function(X){E(G,X,H[X])}):Object.getOwnPropertyDescriptors?Object.defineProperties(G,Object.getOwnPropertyDescriptors(H)):x(Object(H)).forEach(function(X){Object.defineProperty(G,X,Object.getOwnPropertyDescriptor(H,X))})}return G}function E(G,J,H){if(J=N(J),J in G)Object.defineProperty(G,J,{value:H,enumerable:!0,configurable:!0,writable:!0});else G[J]=H;return G}function N(G){var J=z(G,"string");return I(J)=="symbol"?J:String(J)}function z(G,J){if(I(G)!="object"||!G)return G;var H=G[Symbol.toPrimitive];if(H!==void 0){var X=H.call(G,J||"default");if(I(X)!="object")return X;throw new TypeError("@@toPrimitive must return a primitive value.")}return(J==="string"?String:Number)(G)}var W=Object.defineProperty,XG=function G(J,H){for(var X in H)W(J,X,{get:H[X],enumerable:!0,configurable:!0,set:function Z(Y){return H[X]=function(){return Y}}})},D={lessThanXSeconds:{one:{standalone:"\u043C\u0430\u045A\u0435 \u043E\u0434 1 \u0441\u0435\u043A\u0443\u043D\u0434\u0435",withPrepositionAgo:"\u043C\u0430\u045A\u0435 \u043E\u0434 1 \u0441\u0435\u043A\u0443\u043D\u0434\u0435",withPrepositionIn:"\u043C\u0430\u045A\u0435 \u043E\u0434 1 \u0441\u0435\u043A\u0443\u043D\u0434\u0443"},dual:"\u043C\u0430\u045A\u0435 \u043E\u0434 {{count}} \u0441\u0435\u043A\u0443\u043D\u0434\u0435",other:"\u043C\u0430\u045A\u0435 \u043E\u0434 {{count}} \u0441\u0435\u043A\u0443\u043D\u0434\u0438"},xSeconds:{one:{standalone:"1 \u0441\u0435\u043A\u0443\u043D\u0434\u0430",withPrepositionAgo:"1 \u0441\u0435\u043A\u0443\u043D\u0434\u0435",withPrepositionIn:"1 \u0441\u0435\u043A\u0443\u043D\u0434\u0443"},dual:"{{count}} \u0441\u0435\u043A\u0443\u043D\u0434\u0435",other:"{{count}} \u0441\u0435\u043A\u0443\u043D\u0434\u0438"},halfAMinute:"\u043F\u043E\u043B\u0430 \u043C\u0438\u043D\u0443\u0442\u0435",lessThanXMinutes:{one:{standalone:"\u043C\u0430\u045A\u0435 \u043E\u0434 1 \u043C\u0438\u043D\u0443\u0442\u0435",withPrepositionAgo:"\u043C\u0430\u045A\u0435 \u043E\u0434 1 \u043C\u0438\u043D\u0443\u0442\u0435",withPrepositionIn:"\u043C\u0430\u045A\u0435 \u043E\u0434 1 \u043C\u0438\u043D\u0443\u0442\u0443"},dual:"\u043C\u0430\u045A\u0435 \u043E\u0434 {{count}} \u043C\u0438\u043D\u0443\u0442\u0435",other:"\u043C\u0430\u045A\u0435 \u043E\u0434 {{count}} \u043C\u0438\u043D\u0443\u0442\u0430"},xMinutes:{one:{standalone:"1 \u043C\u0438\u043D\u0443\u0442\u0430",withPrepositionAgo:"1 \u043C\u0438\u043D\u0443\u0442\u0435",withPrepositionIn:"1 \u043C\u0438\u043D\u0443\u0442\u0443"},dual:"{{count}} \u043C\u0438\u043D\u0443\u0442\u0435",other:"{{count}} \u043C\u0438\u043D\u0443\u0442\u0430"},aboutXHours:{one:{standalone:"\u043E\u043A\u043E 1 \u0441\u0430\u0442",withPrepositionAgo:"\u043E\u043A\u043E 1 \u0441\u0430\u0442",withPrepositionIn:"\u043E\u043A\u043E 1 \u0441\u0430\u0442"},dual:"\u043E\u043A\u043E {{count}} \u0441\u0430\u0442\u0430",other:"\u043E\u043A\u043E {{count}} \u0441\u0430\u0442\u0438"},xHours:{one:{standalone:"1 \u0441\u0430\u0442",withPrepositionAgo:"1 \u0441\u0430\u0442",withPrepositionIn:"1 \u0441\u0430\u0442"},dual:"{{count}} \u0441\u0430\u0442\u0430",other:"{{count}} \u0441\u0430\u0442\u0438"},xDays:{one:{standalone:"1 \u0434\u0430\u043D",withPrepositionAgo:"1 \u0434\u0430\u043D",withPrepositionIn:"1 \u0434\u0430\u043D"},dual:"{{count}} \u0434\u0430\u043D\u0430",other:"{{count}} \u0434\u0430\u043D\u0430"},aboutXWeeks:{one:{standalone:"\u043E\u043A\u043E 1 \u043D\u0435\u0434\u0435\u0459\u0443",withPrepositionAgo:"\u043E\u043A\u043E 1 \u043D\u0435\u0434\u0435\u0459\u0443",withPrepositionIn:"\u043E\u043A\u043E 1 \u043D\u0435\u0434\u0435\u0459\u0443"},dual:"\u043E\u043A\u043E {{count}} \u043D\u0435\u0434\u0435\u0459\u0435",other:"\u043E\u043A\u043E {{count}} \u043D\u0435\u0434\u0435\u0459\u0435"},xWeeks:{one:{standalone:"1 \u043D\u0435\u0434\u0435\u0459\u0443",withPrepositionAgo:"1 \u043D\u0435\u0434\u0435\u0459\u0443",withPrepositionIn:"1 \u043D\u0435\u0434\u0435\u0459\u0443"},dual:"{{count}} \u043D\u0435\u0434\u0435\u0459\u0435",other:"{{count}} \u043D\u0435\u0434\u0435\u0459\u0435"},aboutXMonths:{one:{standalone:"\u043E\u043A\u043E 1 \u043C\u0435\u0441\u0435\u0446",withPrepositionAgo:"\u043E\u043A\u043E 1 \u043C\u0435\u0441\u0435\u0446",withPrepositionIn:"\u043E\u043A\u043E 1 \u043C\u0435\u0441\u0435\u0446"},dual:"\u043E\u043A\u043E {{count}} \u043C\u0435\u0441\u0435\u0446\u0430",other:"\u043E\u043A\u043E {{count}} \u043C\u0435\u0441\u0435\u0446\u0438"},xMonths:{one:{standalone:"1 \u043C\u0435\u0441\u0435\u0446",withPrepositionAgo:"1 \u043C\u0435\u0441\u0435\u0446",withPrepositionIn:"1 \u043C\u0435\u0441\u0435\u0446"},dual:"{{count}} \u043C\u0435\u0441\u0435\u0446\u0430",other:"{{count}} \u043C\u0435\u0441\u0435\u0446\u0438"},aboutXYears:{one:{standalone:"\u043E\u043A\u043E 1 \u0433\u043E\u0434\u0438\u043D\u0443",withPrepositionAgo:"\u043E\u043A\u043E 1 \u0433\u043E\u0434\u0438\u043D\u0443",withPrepositionIn:"\u043E\u043A\u043E 1 \u0433\u043E\u0434\u0438\u043D\u0443"},dual:"\u043E\u043A\u043E {{count}} \u0433\u043E\u0434\u0438\u043D\u0435",other:"\u043E\u043A\u043E {{count}} \u0433\u043E\u0434\u0438\u043D\u0430"},xYears:{one:{standalone:"1 \u0433\u043E\u0434\u0438\u043D\u0430",withPrepositionAgo:"1 \u0433\u043E\u0434\u0438\u043D\u0435",withPrepositionIn:"1 \u0433\u043E\u0434\u0438\u043D\u0443"},dual:"{{count}} \u0433\u043E\u0434\u0438\u043D\u0435",other:"{{count}} \u0433\u043E\u0434\u0438\u043D\u0430"},overXYears:{one:{standalone:"\u043F\u0440\u0435\u043A\u043E 1 \u0433\u043E\u0434\u0438\u043D\u0443",withPrepositionAgo:"\u043F\u0440\u0435\u043A\u043E 1 \u0433\u043E\u0434\u0438\u043D\u0443",withPrepositionIn:"\u043F\u0440\u0435\u043A\u043E 1 \u0433\u043E\u0434\u0438\u043D\u0443"},dual:"\u043F\u0440\u0435\u043A\u043E {{count}} \u0433\u043E\u0434\u0438\u043D\u0435",other:"\u043F\u0440\u0435\u043A\u043E {{count}} \u0433\u043E\u0434\u0438\u043D\u0430"},almostXYears:{one:{standalone:"\u0433\u043E\u0442\u043E\u0432\u043E 1 \u0433\u043E\u0434\u0438\u043D\u0443",withPrepositionAgo:"\u0433\u043E\u0442\u043E\u0432\u043E 1 \u0433\u043E\u0434\u0438\u043D\u0443",withPrepositionIn:"\u0433\u043E\u0442\u043E\u0432\u043E 1 \u0433\u043E\u0434\u0438\u043D\u0443"},dual:"\u0433\u043E\u0442\u043E\u0432\u043E {{count}} \u0433\u043E\u0434\u0438\u043D\u0435",other:"\u0433\u043E\u0442\u043E\u0432\u043E {{count}} \u0433\u043E\u0434\u0438\u043D\u0430"}},S=function G(J,H,X){var Z,Y=D[J];if(typeof Y==="string")Z=Y;else if(H===1)if(X!==null&&X!==void 0&&X.addSuffix)if(X.comparison&&X.comparison>0)Z=Y.one.withPrepositionIn;else Z=Y.one.withPrepositionAgo;else Z=Y.one.standalone;else if(H%10>1&&H%10<5&&String(H).substr(-2,1)!=="1")Z=Y.dual.replace("{{count}}",String(H));else Z=Y.other.replace("{{count}}",String(H));if(X!==null&&X!==void 0&&X.addSuffix)if(X.comparison&&X.comparison>0)return"\u0437\u0430 "+Z;else return"\u043F\u0440\u0435 "+Z;return Z};function $(G){return function(){var J=arguments.length>0&&arguments[0]!==void 0?arguments[0]:{},H=J.width?String(J.width):G.defaultWidth,X=G.formats[H]||G.formats[G.defaultWidth];return X}}var M={full:"EEEE, d. MMMM yyyy.",long:"d. MMMM yyyy.",medium:"d. MMM yy.",short:"dd. MM. yy."},R={full:"HH:mm:ss (zzzz)",long:"HH:mm:ss z",medium:"HH:mm:ss",short:"HH:mm"},L={full:"{{date}} '\u0443' {{time}}",long:"{{date}} '\u0443' {{time}}",medium:"{{date}} {{time}}",short:"{{date}} {{time}}"},V={date:$({formats:M,defaultWidth:"full"}),time:$({formats:R,defaultWidth:"full"}),dateTime:$({formats:L,defaultWidth:"full"})},j={lastWeek:function G(J){var H=J.getDay();switch(H){case 0:return"'\u043F\u0440\u043E\u0448\u043B\u0435 \u043D\u0435\u0434\u0435\u0459\u0435 \u0443' p";case 3:return"'\u043F\u0440\u043E\u0448\u043B\u0435 \u0441\u0440\u0435\u0434\u0435 \u0443' p";case 6:return"'\u043F\u0440\u043E\u0448\u043B\u0435 \u0441\u0443\u0431\u043E\u0442\u0435 \u0443' p";default:return"'\u043F\u0440\u043E\u0448\u043B\u0438' EEEE '\u0443' p"}},yesterday:"'\u0458\u0443\u0447\u0435 \u0443' p",today:"'\u0434\u0430\u043D\u0430\u0441 \u0443' p",tomorrow:"'\u0441\u0443\u0442\u0440\u0430 \u0443' p",nextWeek:function G(J){var H=J.getDay();switch(H){case 0:return"'\u0441\u043B\u0435\u0434\u0435\u045B\u0435 \u043D\u0435\u0434\u0435\u0459\u0435 \u0443' p";case 3:return"'\u0441\u043B\u0435\u0434\u0435\u045B\u0443 \u0441\u0440\u0435\u0434\u0443 \u0443' p";case 6:return"'\u0441\u043B\u0435\u0434\u0435\u045B\u0443 \u0441\u0443\u0431\u043E\u0442\u0443 \u0443' p";default:return"'\u0441\u043B\u0435\u0434\u0435\u045B\u0438' EEEE '\u0443' p"}},other:"P"},w=function G(J,H,X,Z){var Y=j[J];if(typeof Y==="function")return Y(H);return Y};function O(G){return function(J,H){var X=H!==null&&H!==void 0&&H.context?String(H.context):"standalone",Z;if(X==="formatting"&&G.formattingValues){var Y=G.defaultFormattingWidth||G.defaultWidth,T=H!==null&&H!==void 0&&H.width?String(H.width):Y;Z=G.formattingValues[T]||G.formattingValues[Y]}else{var U=G.defaultWidth,A=H!==null&&H!==void 0&&H.width?String(H.width):G.defaultWidth;Z=G.values[A]||G.values[U]}var C=G.argumentCallback?G.argumentCallback(J):J;return Z[C]}}var _={narrow:["\u043F\u0440.\u043D.\u0435.","\u0410\u0414"],abbreviated:["\u043F\u0440. \u0425\u0440.","\u043F\u043E. \u0425\u0440."],wide:["\u041F\u0440\u0435 \u0425\u0440\u0438\u0441\u0442\u0430","\u041F\u043E\u0441\u043B\u0435 \u0425\u0440\u0438\u0441\u0442\u0430"]},F={narrow:["1.","2.","3.","4."],abbreviated:["1. \u043A\u0432.","2. \u043A\u0432.","3. \u043A\u0432.","4. \u043A\u0432."],wide:["1. \u043A\u0432\u0430\u0440\u0442\u0430\u043B","2. \u043A\u0432\u0430\u0440\u0442\u0430\u043B","3. \u043A\u0432\u0430\u0440\u0442\u0430\u043B","4. \u043A\u0432\u0430\u0440\u0442\u0430\u043B"]},v={narrow:["1.","2.","3.","4.","5.","6.","7.","8.","9.","10.","11.","12."],abbreviated:["\u0458\u0430\u043D","\u0444\u0435\u0431","\u043C\u0430\u0440","\u0430\u043F\u0440","\u043C\u0430\u0458","\u0458\u0443\u043D","\u0458\u0443\u043B","\u0430\u0432\u0433","\u0441\u0435\u043F","\u043E\u043A\u0442","\u043D\u043E\u0432","\u0434\u0435\u0446"],wide:["\u0458\u0430\u043D\u0443\u0430\u0440","\u0444\u0435\u0431\u0440\u0443\u0430\u0440","\u043C\u0430\u0440\u0442","\u0430\u043F\u0440\u0438\u043B","\u043C\u0430\u0458","\u0458\u0443\u043D","\u0458\u0443\u043B","\u0430\u0432\u0433\u0443\u0441\u0442","\u0441\u0435\u043F\u0442\u0435\u043C\u0431\u0430\u0440","\u043E\u043A\u0442\u043E\u0431\u0430\u0440","\u043D\u043E\u0432\u0435\u043C\u0431\u0430\u0440","\u0434\u0435\u0446\u0435\u043C\u0431\u0430\u0440"]},P={narrow:["1.","2.","3.","4.","5.","6.","7.","8.","9.","10.","11.","12."],abbreviated:["\u0458\u0430\u043D","\u0444\u0435\u0431","\u043C\u0430\u0440","\u0430\u043F\u0440","\u043C\u0430\u0458","\u0458\u0443\u043D","\u0458\u0443\u043B","\u0430\u0432\u0433","\u0441\u0435\u043F","\u043E\u043A\u0442","\u043D\u043E\u0432","\u0434\u0435\u0446"],wide:["\u0458\u0430\u043D\u0443\u0430\u0440","\u0444\u0435\u0431\u0440\u0443\u0430\u0440","\u043C\u0430\u0440\u0442","\u0430\u043F\u0440\u0438\u043B","\u043C\u0430\u0458","\u0458\u0443\u043D","\u0458\u0443\u043B","\u0430\u0432\u0433\u0443\u0441\u0442","\u0441\u0435\u043F\u0442\u0435\u043C\u0431\u0430\u0440","\u043E\u043A\u0442\u043E\u0431\u0430\u0440","\u043D\u043E\u0432\u0435\u043C\u0431\u0430\u0440","\u0434\u0435\u0446\u0435\u043C\u0431\u0430\u0440"]},f={narrow:["\u041D","\u041F","\u0423","\u0421","\u0427","\u041F","\u0421"],short:["\u043D\u0435\u0434","\u043F\u043E\u043D","\u0443\u0442\u043E","\u0441\u0440\u0435","\u0447\u0435\u0442","\u043F\u0435\u0442","\u0441\u0443\u0431"],abbreviated:["\u043D\u0435\u0434","\u043F\u043E\u043D","\u0443\u0442\u043E","\u0441\u0440\u0435","\u0447\u0435\u0442","\u043F\u0435\u0442","\u0441\u0443\u0431"],wide:["\u043D\u0435\u0434\u0435\u0459\u0430","\u043F\u043E\u043D\u0435\u0434\u0435\u0459\u0430\u043A","\u0443\u0442\u043E\u0440\u0430\u043A","\u0441\u0440\u0435\u0434\u0430","\u0447\u0435\u0442\u0432\u0440\u0442\u0430\u043A","\u043F\u0435\u0442\u0430\u043A","\u0441\u0443\u0431\u043E\u0442\u0430"]},k={narrow:{am:"\u0410\u041C",pm:"\u041F\u041C",midnight:"\u043F\u043E\u043D\u043E\u045B",noon:"\u043F\u043E\u0434\u043D\u0435",morning:"\u0443\u0458\u0443\u0442\u0440\u0443",afternoon:"\u043F\u043E\u043F\u043E\u0434\u043D\u0435",evening:"\u0443\u0432\u0435\u0447\u0435",night:"\u043D\u043E\u045B\u0443"},abbreviated:{am:"\u0410\u041C",pm:"\u041F\u041C",midnight:"\u043F\u043E\u043D\u043E\u045B",noon:"\u043F\u043E\u0434\u043D\u0435",morning:"\u0443\u0458\u0443\u0442\u0440\u0443",afternoon:"\u043F\u043E\u043F\u043E\u0434\u043D\u0435",evening:"\u0443\u0432\u0435\u0447\u0435",night:"\u043D\u043E\u045B\u0443"},wide:{am:"AM",pm:"PM",midnight:"\u043F\u043E\u043D\u043E\u045B",noon:"\u043F\u043E\u0434\u043D\u0435",morning:"\u0443\u0458\u0443\u0442\u0440\u0443",afternoon:"\u043F\u043E\u0441\u043B\u0435 \u043F\u043E\u0434\u043D\u0435",evening:"\u0443\u0432\u0435\u0447\u0435",night:"\u043D\u043E\u045B\u0443"}},b={narrow:{am:"AM",pm:"PM",midnight:"\u043F\u043E\u043D\u043E\u045B",noon:"\u043F\u043E\u0434\u043D\u0435",morning:"\u0443\u0458\u0443\u0442\u0440\u0443",afternoon:"\u043F\u043E\u043F\u043E\u0434\u043D\u0435",evening:"\u0443\u0432\u0435\u0447\u0435",night:"\u043D\u043E\u045B\u0443"},abbreviated:{am:"AM",pm:"PM",midnight:"\u043F\u043E\u043D\u043E\u045B",noon:"\u043F\u043E\u0434\u043D\u0435",morning:"\u0443\u0458\u0443\u0442\u0440\u0443",afternoon:"\u043F\u043E\u043F\u043E\u0434\u043D\u0435",evening:"\u0443\u0432\u0435\u0447\u0435",night:"\u043D\u043E\u045B\u0443"},wide:{am:"AM",pm:"PM",midnight:"\u043F\u043E\u043D\u043E\u045B",noon:"\u043F\u043E\u0434\u043D\u0435",morning:"\u0443\u0458\u0443\u0442\u0440\u0443",afternoon:"\u043F\u043E\u0441\u043B\u0435 \u043F\u043E\u0434\u043D\u0435",evening:"\u0443\u0432\u0435\u0447\u0435",night:"\u043D\u043E\u045B\u0443"}},h=function G(J,H){var X=Number(J);return X+"."},m={ordinalNumber:h,era:O({values:_,defaultWidth:"wide"}),quarter:O({values:F,defaultWidth:"wide",argumentCallback:function G(J){return J-1}}),month:O({values:v,defaultWidth:"wide",formattingValues:P,defaultFormattingWidth:"wide"}),day:O({values:f,defaultWidth:"wide"}),dayPeriod:O({values:b,defaultWidth:"wide",formattingValues:k,defaultFormattingWidth:"wide"})};function Q(G){return function(J){var H=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{},X=H.width,Z=X&&G.matchPatterns[X]||G.matchPatterns[G.defaultMatchWidth],Y=J.match(Z);if(!Y)return null;var T=Y[0],U=X&&G.parsePatterns[X]||G.parsePatterns[G.defaultParseWidth],A=Array.isArray(U)?c(U,function(K){return K.test(T)}):y(U,function(K){return K.test(T)}),C;C=G.valueCallback?G.valueCallback(A):A,C=H.valueCallback?H.valueCallback(C):C;var JG=J.slice(T.length);return{value:C,rest:JG}}}function y(G,J){for(var H in G)if(Object.prototype.hasOwnProperty.call(G,H)&&J(G[H]))return H;return}function c(G,J){for(var H=0;H<G.length;H++)if(J(G[H]))return H;return}function g(G){return function(J){var H=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{},X=J.match(G.matchPattern);if(!X)return null;var Z=X[0],Y=J.match(G.parsePattern);if(!Y)return null;var T=G.valueCallback?G.valueCallback(Y[0]):Y[0];T=H.valueCallback?H.valueCallback(T):T;var U=J.slice(Z.length);return{value:T,rest:U}}}var d=/^(\d+)\./i,p=/\d+/i,u={narrow:/^(пр\.н\.е\.|АД)/i,abbreviated:/^(пр\.\s?Хр\.|по\.\s?Хр\.)/i,wide:/^(Пре Христа|пре нове ере|После Христа|нова ера)/i},l={any:[/^пр/i,/^(по|нова)/i]},i={narrow:/^[1234]/i,abbreviated:/^[1234]\.\s?кв\.?/i,wide:/^[1234]\. квартал/i},n={any:[/1/i,/2/i,/3/i,/4/i]},s={narrow:/^(10|11|12|[123456789])\./i,abbreviated:/^(јан|феб|мар|апр|мај|јун|јул|авг|сеп|окт|нов|дец)/i,wide:/^((јануар|јануара)|(фебруар|фебруара)|(март|марта)|(април|априла)|(мја|маја)|(јун|јуна)|(јул|јула)|(август|августа)|(септембар|септембра)|(октобар|октобра)|(новембар|новембра)|(децембар|децембра))/i},r={narrow:[/^1/i,/^2/i,/^3/i,/^4/i,/^5/i,/^6/i,/^7/i,/^8/i,/^9/i,/^10/i,/^11/i,/^12/i],any:[/^ја/i,/^ф/i,/^мар/i,/^ап/i,/^мај/i,/^јун/i,/^јул/i,/^авг/i,/^с/i,/^о/i,/^н/i,/^д/i]},o={narrow:/^[пусчн]/i,short:/^(нед|пон|уто|сре|чет|пет|суб)/i,abbreviated:/^(нед|пон|уто|сре|чет|пет|суб)/i,wide:/^(недеља|понедељак|уторак|среда|четвртак|петак|субота)/i},a={narrow:[/^п/i,/^у/i,/^с/i,/^ч/i,/^п/i,/^с/i,/^н/i],any:[/^нед/i,/^пон/i,/^уто/i,/^сре/i,/^чет/i,/^пет/i,/^суб/i]},e={any:/^(ам|пм|поноћ|(по)?подне|увече|ноћу|после подне|ујутру)/i},t={any:{am:/^a/i,pm:/^p/i,midnight:/^поно/i,noon:/^под/i,morning:/ујутру/i,afternoon:/(после\s|по)+подне/i,evening:/(увече)/i,night:/(ноћу)/i}},GG={ordinalNumber:g({matchPattern:d,parsePattern:p,valueCallback:function G(J){return parseInt(J,10)}}),era:Q({matchPatterns:u,defaultMatchWidth:"wide",parsePatterns:l,defaultParseWidth:"any"}),quarter:Q({matchPatterns:i,defaultMatchWidth:"wide",parsePatterns:n,defaultParseWidth:"any",valueCallback:function G(J){return J+1}}),month:Q({matchPatterns:s,defaultMatchWidth:"wide",parsePatterns:r,defaultParseWidth:"any"}),day:Q({matchPatterns:o,defaultMatchWidth:"wide",parsePatterns:a,defaultParseWidth:"any"}),dayPeriod:Q({matchPatterns:e,defaultMatchWidth:"any",parsePatterns:t,defaultParseWidth:"any"})},HG={code:"sr",formatDistance:S,formatLong:V,formatRelative:w,localize:m,match:GG,options:{weekStartsOn:1,firstWeekContainsDate:1}};window.dateFns=q(q({},window.dateFns),{},{locale:q(q({},(B=window.dateFns)===null||B===void 0?void 0:B.locale),{},{sr:HG})})})();

//# debugId=68D121129D95787964756E2164756E21
