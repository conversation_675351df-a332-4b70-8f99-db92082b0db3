// THIS FILE IS AUTO GENERATED
var GenIcon = require('../lib').GenIcon
module.exports.GiRollingDices = function GiRollingDices (props) {
  return GenIcon({"tag":"svg","attr":{"viewBox":"0 0 512 512"},"child":[{"tag":"path","attr":{"d":"M138.798 35.342L28.73 114.268l95.777 29.095 111.305-87.09-97.014-20.93zm112.986 31.082l-118.047 89.96 51.07 131.102 8.534-7.455 4.23-15.708a18.338 13.102 76.863 0 1-9.08-20.45 18.338 13.102 76.863 0 1 10.997-13.727 18.338 13.102 76.863 0 1 3.62.53 18.338 13.102 76.863 0 1 3.113 1.544l7.94-29.48a9 9 0 0 1 .353-1.04 9 9 0 0 1 .058-.128 9 9 0 0 1 .32-.685 9 9 0 0 1 .09-.153 9 9 0 0 1 .37-.625 9 9 0 0 1 .534-.723 9 9 0 0 1 .066-.074 9 9 0 0 1 .54-.594 9 9 0 0 1 .65-.593 9 9 0 0 1 .004-.002 9 9 0 0 1 .46-.342 9 9 0 0 1 .266-.197 9 9 0 0 1 .502-.3 9 9 0 0 1 .27-.157 9 9 0 0 1 .44-.208 9 9 0 0 1 .38-.178 9 9 0 0 1 .437-.152 9 9 0 0 1 .41-.143 9 9 0 0 1 .404-.1 9 9 0 0 1 .47-.114 9 9 0 0 1 .51-.07 9 9 0 0 1 .37-.05 9 9 0 0 1 .01 0 9 9 0 0 1 .01-.003l33.624-2.873a18.338 13.102 76.863 0 1 10.326-9.777 18.338 13.102 76.863 0 1 3.622.53 18.338 13.102 76.863 0 1 8.527 7.327l13.043-1.113-39.442-123.783zM137.25 74.03a9.8 19.77 77.916 0 1 12.798 8.734 9.8 19.77 77.916 0 1-21.938 11.998 9.8 19.77 77.916 0 1-16.57-8.602 9.8 19.77 77.916 0 1 21.938-12 9.8 19.77 77.916 0 1 3.77-.13zm100.228 23.517a18.338 13.102 76.863 0 1 .002 0 18.338 13.102 76.863 0 1 3.62.53 18.338 13.102 76.863 0 1 12.112 21.94 18.338 13.102 76.863 0 1-14.617 13.196 18.338 13.102 76.863 0 1-12.114-21.94 18.338 13.102 76.863 0 1 10.998-13.726zM24.22 131.71l46.992 114.124 94.236 40.38-45.988-125.57-95.24-28.935zm147.886 17.43a18.338 13.102 76.863 0 1 3.622.528 18.338 13.102 76.863 0 1 12.11 21.94 18.338 13.102 76.863 0 1-14.616 13.197 18.338 13.102 76.863 0 1-12.112-21.94 18.338 13.102 76.863 0 1 10.996-13.726zm-75.123 13.016a19.454 9.134 59.254 0 1 16.955 15.078 19.454 9.134 59.254 0 1-.425 19.485A19.454 9.134 59.254 0 1 95.6 181.78a19.454 9.134 59.254 0 1 .424-19.48 19.454 9.134 59.254 0 1 .96-.144zm263.393 40.21l-112.102 9.577 113.762 79.926 113.598-16.956-115.258-72.55zM70.82 212.022A19.454 9.134 59.254 0 1 87.777 227.1a19.454 9.134 59.254 0 1-.425 19.484 19.454 9.134 59.254 0 1-17.913-14.938 19.454 9.134 59.254 0 1 .425-19.482 19.454 9.134 59.254 0 1 .96-.14zm157.378 7.813L186.66 374.023l115.616 99.454 47.147-168.47-121.225-85.17zm126.987 11.168a21.76 8.898 15.267 0 1 19.693 4.783 21.76 8.898 15.267 0 1 7.607 14.244 21.76 8.898 15.267 0 1-28.886-3.182 21.76 8.898 15.267 0 1-7.61-14.244 21.76 8.898 15.267 0 1 9.195-1.6zM487.78 291.3L366.9 309.343l-46.823 167.316 116.297-31.77L487.78 291.3zm-181.808 10.8a25.834 15.573 84.277 0 1 4.238.943 25.834 15.573 84.277 0 1 12.873 31.72 25.834 15.573 84.277 0 1-18.105 17.893 25.834 15.573 84.277 0 1-12.874-31.72 25.834 15.573 84.277 0 1 13.868-18.836zm154.086 11.636a13.237 21.96 28.62 0 1 7.673 4.13 13.237 21.96 28.62 0 1-6.176 28.435 13.237 21.96 28.62 0 1-21.287 3.878 13.237 21.96 28.62 0 1 6.175-28.434 13.237 21.96 28.62 0 1 13.616-8.008zM391.362 324.4a13.237 21.96 28.62 0 1 7.672 4.13 13.237 21.96 28.62 0 1-6.176 28.435 13.237 21.96 28.62 0 1-21.287 3.877 13.237 21.96 28.62 0 1 6.177-28.434 13.237 21.96 28.62 0 1 13.615-8.008zm-173.996 13.305a25.834 15.573 84.277 0 1 4.24.945 25.834 15.573 84.277 0 1 12.872 31.72 25.834 15.573 84.277 0 1-18.106 17.894 25.834 15.573 84.277 0 1-12.873-31.72 25.834 15.573 84.277 0 1 13.866-18.84zm212.278 60.87a13.237 21.96 28.62 0 1 7.67 4.13 13.237 21.96 28.62 0 1-6.174 28.434 13.237 21.96 28.62 0 1-21.287 3.876 13.237 21.96 28.62 0 1 6.175-28.434 13.237 21.96 28.62 0 1 13.616-8.008zm-70.332 19.488a13.237 21.96 28.62 0 1 7.67 4.132 13.237 21.96 28.62 0 1-6.174 28.434 13.237 21.96 28.62 0 1-21.287 3.874 13.237 21.96 28.62 0 1 6.176-28.434 13.237 21.96 28.62 0 1 13.616-8.007z"}}]})(props);
};
