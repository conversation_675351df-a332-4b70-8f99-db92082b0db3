// THIS FILE IS AUTO GENERATED
var GenIcon = require('../lib').GenIcon
module.exports.GiFrog = function GiFrog (props) {
  return GenIcon({"tag":"svg","attr":{"viewBox":"0 0 512 512"},"child":[{"tag":"path","attr":{"d":"M335.7 88.94c-4.742.194-9.563 1.486-14.204 4.165-38.934 22.48-89.77 21.953-127.79.002-6.09-3.516-12.285-4.61-18.145-3.892 5.914 7.778 9.438 17.572 9.438 28.09 0 23.15-17.037 42.83-39.176 45.095-12.775 14.92-21.553 31.807-24.386 49.983 44.73-23.79 90.947-35.572 137.064-35.508 46.15.064 92.197 11.987 136.56 35.62-2.69-18.15-11.216-35.043-23.794-49.92-.585.026-1.17.048-1.76.048-24.18 0-43.447-20.7-43.447-45.318 0-10.64 3.6-20.543 9.64-28.364zm-194.15 3.216c-12.67 0-23.277 10.85-23.277 25.15 0 14.297 10.608 25.147 23.278 25.147 12.67 0 23.276-10.85 23.276-25.148s-10.606-25.15-23.275-25.15zm227.956 0c-12.67 0-23.277 10.85-23.277 25.15 0 14.297 10.607 25.147 23.276 25.147 12.67 0 23.277-10.85 23.277-25.148s-10.608-25.15-23.277-25.15zm67.572 93.367c-8.525.088-17.893 1.546-27.853 4.243 6.926 19.457 8.57 40.725 2.695 62.656-4.26 15.896.933 37.475 11.7 54.758l4.69 7.53-7.02 5.43c-19.765 15.28-36.44 25.107-46.104 35.264-9.664 10.158-13.887 19.59-10.915 40.875l1.525 10.91c3.596 4.7 7.678 9.43 12.142 14.06 19.876-14.55 36.01-23.887 68.344-4.094-6.738-18.804 15.938-29.762 46.72-29.78-36.91-15.88-64.98-25.62-86.438-30.376 67.492-72.188 97.182-127.96 66-159.188-8.172-8.183-19.356-12.034-33.28-12.28-.73-.014-1.463-.016-2.204-.01zm-361.617.002c-.806-.01-1.606-.008-2.397.006-13.925.248-25.14 4.1-33.313 12.282-31.182 31.227-1.492 87 66 159.188-21.456 4.756-49.528 14.497-86.438 30.375 30.782.02 53.458 10.977 46.72 29.78 32.332-19.792 48.468-10.454 68.343 4.095 6.713-6.962 12.572-14.146 17.188-21.12l.537-3.85c2.972-21.283-1.25-30.716-10.914-40.874-9.664-10.157-26.34-19.984-46.106-35.265l-7.02-5.427 4.692-7.53c10.73-17.228 15.858-39.233 11.7-54.76-5.782-21.572-4.185-42.44 2.536-61.56-11.336-3.388-21.954-5.216-31.527-5.338zm183.038 9.66c-46.096-.065-92.3 12.827-137.574 38.846.47 4.387 1.292 8.825 2.494 13.31v.002c5.453 20.354.593 42.93-9.484 62.297 15.89 11.634 30.343 20.526 41.478 32.23 10.36 10.89 16.795 25.132 16.955 43.712-1.096 16.308-9.157 39.273-22.347 59.244 24.59-14.237 42.134-15.333 45.29 3.492 14.097-17.783 25.698-20.386 38.985-8.035-3.745-31.452-11.117-52.887-17.258-65.097-14.896-36.567-42.816-61.484-73.742-83.424l11.36-16.014c38.788 27.517 76.798 62.663 89.124 119.566 9.628.705 19.25.65 28.85-.16 12.362-56.81 50.334-91.918 89.085-119.408l11.36 16.016c-31.19 22.127-59.333 47.28-74.13 84.363-6.045 12.357-13.14 33.493-16.793 64.158 13.29-12.35 24.89-9.748 38.987 8.035 3.153-18.825 20.697-17.73 45.288-3.492-13.51-20.455-21.645-44.058-22.42-60.424.415-18.01 6.81-31.872 16.95-42.533 11.135-11.705 25.586-20.595 41.474-32.23-10.064-19.29-14.99-41.736-9.48-62.302 1.198-4.467 2.028-8.89 2.51-13.266-44.85-25.79-90.852-38.82-136.964-38.886z"}}]})(props);
};
