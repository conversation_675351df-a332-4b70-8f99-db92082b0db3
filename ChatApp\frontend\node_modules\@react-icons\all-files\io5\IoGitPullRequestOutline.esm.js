// THIS FILE IS AUTO GENERATED
import { GenIcon } from '../lib';
export function IoGitPullRequestOutline (props) {
  return GenIcon({"tag":"svg","attr":{"viewBox":"0 0 512 512"},"child":[{"tag":"circle","attr":{"cx":"128","cy":"416","r":"48","fill":"none","strokeLinecap":"round","strokeLinejoin":"round","strokeWidth":"32"}},{"tag":"path","attr":{"fill":"none","strokeLinecap":"round","strokeLinejoin":"round","strokeWidth":"32","d":"M128 144v224m160-208l-64-64 64-64"}},{"tag":"circle","attr":{"cx":"128","cy":"96","r":"48","fill":"none","strokeLinecap":"round","strokeLinejoin":"round","strokeWidth":"32"}},{"tag":"circle","attr":{"cx":"384","cy":"416","r":"48","fill":"none","strokeLinecap":"round","strokeLinejoin":"round","strokeWidth":"32"}},{"tag":"path","attr":{"fill":"none","strokeLinecap":"round","strokeLinejoin":"round","strokeWidth":"32","d":"M240 96h84a60 60 0 0160 60v212"}}]})(props);
};
