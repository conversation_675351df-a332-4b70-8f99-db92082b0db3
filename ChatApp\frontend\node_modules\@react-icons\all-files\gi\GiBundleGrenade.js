// THIS FILE IS AUTO GENERATED
var GenIcon = require('../lib').GenIcon
module.exports.GiBundleGrenade = function GiBundleGrenade (props) {
  return GenIcon({"tag":"svg","attr":{"viewBox":"0 0 512 512"},"child":[{"tag":"path","attr":{"d":"M304.844 280.232a43.459 43.459 0 0 0 15.51-2.94 57.63 57.63 0 0 0 3.14 17.82 83.92 83.92 0 0 0-18.65-14.88zm-55.49 32.54c.45 9.86 5 21.16 13.58 31.36 11.77 14 27.87 22.2 41.31 22.2a25.45 25.45 0 0 0 14.42-4.06c14.29-9.64 12.52-33.41-4-53.07-10.55-12.59-24.56-20.42-37-21.92zm-14.2-90.36a52.77 52.77 0 0 1-1.26-19.51 42 42 0 0 1-18.2 4.7 85.19 85.19 0 0 1 17.19 15.59l.43.54c.19-.17.39-.34.59-.5.39-.298.797-.572 1.22-.82zm-55.84-11.15a25.45 25.45 0 0 0-14.42 4.06c-14.3 9.65-12.52 33.4 4 53.07a68.72 68.72 0 0 0 16.65 14.42l39.93-42.87a68.08 68.08 0 0 0-4.81-6.48c-11.84-14.04-27.93-22.2-41.38-22.2zm53.74-158.67a20.88 20.88 0 0 0-3 2.47l-6.34 6.28 40.91-9a43.9 43.9 0 0 0-17.15-3.75 25.45 25.45 0 0 0-14.42 4zm-.08 67.47a48.823 48.823 0 0 1 .4-9.14 38.91 38.91 0 0 1 10.66-22.14l.27-.29 19.8-19.61-61.65 13.54-14.29 14.17c15.63 1.55 32.07 10.18 44.81 23.52zm-8.56 14.41c-11.78-14-27.87-22.2-41.31-22.2a25.45 25.45 0 0 0-14.42 4.06c-14.3 9.65-12.52 33.4 4 53.07 11.77 14 27.87 22.2 41.31 22.2a25.45 25.45 0 0 0 14.36-4.01c14.32-9.64 12.55-33.41-3.94-53.07zm101.94 61.47a60.14 60.14 0 0 1-2.72-11.92 39.41 39.41 0 0 1-6.54 2.7 85.77 85.77 0 0 1 9.26 9.22zm-65.86 6.31l.25-.27.26-.26a19 19 0 0 1 2.12-1.79 19.77 19.77 0 0 1 11.7-3.69c13 0 25.44 11.64 30.18 16.65a60.32 60.32 0 0 1 11.41 16.43c6.34 14.29 0 22.74-3 25.71l-.25.25-.26.23-9.61 8.66h1.14a25.45 25.45 0 0 0 14.42-4.06c14.29-9.64 12.52-33.41-4-53.07-11.78-14-27.87-22.2-41.31-22.2a25.45 25.45 0 0 0-14.42 4.06c-7.65 5.16-10.69 14.37-9.35 24.82zm212.64 59.22c9.77-9.68 8.64-28-2.63-44.81l-2.74 50.13zm-50.5-23.68a36.62 36.62 0 0 1-5.07 4.14 41 41 0 0 1-20.84 6.71 85.17 85.17 0 0 1 17.33 15.69c10.08 12 16 25.64 17.15 38.67l19.6-19.41 4.26-77.88-32.19 31.88zm-31.28 93.99a25.45 25.45 0 0 0 14.42-4.06c14.29-9.64 12.52-33.41-4-53.07-11.78-14-27.87-22.2-41.31-22.2a25.45 25.45 0 0 0-14.42 4.06c-14.3 9.65-12.52 33.4 4 53.07 11.73 14.01 27.83 22.2 41.31 22.2zm-12.67-279.95c-11.78-14.05-27.87-22.24-41.33-22.24a25.45 25.45 0 0 0-14.42 4.06 20.88 20.88 0 0 0-3 2.47l-15.33 15.21a195.28 195.28 0 0 1 25.18 11.26c16.89 8.81 36.82 21.12 56.45 34.85l5.1-5c3.09-11.5-1.27-27.03-12.65-40.61zm-100.63 25.76c17.19 1.72 35.38 12 48.52 27.66 9.08 10.83 14.81 22.94 16.68 34.75l31.42-31.11c-37.76-26.17-69.41-42.11-83-44.74zm36.3 38c-11.78-14-27.87-22.2-41.31-22.2a25.45 25.45 0 0 0-14.42 4.06c-14.3 9.65-12.52 33.4 4 53.07 11.77 14 27.87 22.2 41.31 22.2a25.45 25.45 0 0 0 14.42-4.06c14.22-9.66 12.44-33.43-4-53.07zm154.68-3.68c-11.78-14-27.87-22.2-41.31-22.2a25.45 25.45 0 0 0-14.42 4.06 20.88 20.88 0 0 0-3 2.47l-10.83 10.73c21.94 16.19 55.1 42.81 67.76 64.26l8.8-8.72c10.86-10.83 8.18-32.47-7.03-50.62zm-100.68 25.78c17.19 1.72 35.38 12 48.52 27.66 10.08 12 16 25.64 17.15 38.67l21.33-21.08c-4.86-11.5-26.79-34.9-67.43-64.59zm-15.49 72.9c11.77 14 27.87 22.2 41.31 22.2a25.45 25.45 0 0 0 14.42-4.06c14.29-9.64 12.52-33.41-4-53.07-11.78-14-27.87-22.2-41.31-22.2a25.45 25.45 0 0 0-14.42 4.06c-14.26 9.62-12.51 33.37 4 53.07zm-289.75 240.18a63.08 63.08 0 0 1 10.76 15l15.91-18.38a32.09 32.09 0 0 0-8-12.77 44.42 44.42 0 0 0-18.73-11l-16.81 14.08c7.55 3.89 13.81 9.84 16.87 13.07zm239-207.12c2.85-2.81-.95-11.44-8.79-19.73-6.68-7.06-14.21-11.65-18.56-11.65a3.86 3.86 0 0 0-2.31.64c-.11.082-.213.172-.31.27l-10.72 11.51a60.74 60.74 0 0 1 17.17 12 53.39 53.39 0 0 1 11.76 17.63zm-34.87 4.37a43.79 43.79 0 0 0-17.57-10.72l-127.09 136.53-45.6 38.15a61.46 61.46 0 0 1 16.17 11.38 50.49 50.49 0 0 1 8.2 10.71l37.5-43.33 138.14-124.42c-.31-3.84-2.79-11.35-9.7-18.3zm-215.75 213.75c-6.68-7.06-14.21-11.65-18.56-11.65a3.86 3.86 0 0 0-2.31.64c-3.42 2.48.37 11.44 8.47 20 6.69 7.06 14.21 11.65 18.56 11.65a3.86 3.86 0 0 0 2.31-.64c3.39-2.49-.36-11.44-8.47-20z"}}]})(props);
};
