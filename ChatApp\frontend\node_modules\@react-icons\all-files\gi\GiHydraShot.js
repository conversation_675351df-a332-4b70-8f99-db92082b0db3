// THIS FILE IS AUTO GENERATED
var GenIcon = require('../lib').GenIcon
module.exports.GiHydraShot = function GiHydraShot (props) {
  return GenIcon({"tag":"svg","attr":{"viewBox":"0 0 512 512"},"child":[{"tag":"path","attr":{"d":"M25.438 20.063c-6.856-.07-11.186 7.654-3.063 15.78l315.53 346.688c-4.782 6.952-10.63 12.444-16.5 16.783l81.69 94.875c11.577-6.95 22.384-14.636 32.592-22.907-6.568.54-13.36-.86-19.5-4.405-16.038-9.26-21.54-29.743-12.28-45.78 6.076-10.526 16.99-16.53 28.312-16.783 5.93-.132 11.986 1.317 17.5 4.5 12.5 7.218 18.583 21.275 16.28 34.72 11.174-11.344 21.762-23.25 32.03-35.47l-189.874-160.28c18.826 26.837 29.97 49.297 35.688 68.124L34.063 24.156c-2.86-2.858-5.943-4.067-8.626-4.093zm60.843 1.312c-10.29.46-13.115 12.897.44 16.53l254.468 98.876c-.642 8.503-3 16.25-5.938 23l118.156 41.314c6.554-11.81 12.07-23.886 16.78-36.156-5.424 3.762-11.995 5.968-19.092 5.968-18.52 0-33.53-15.01-33.53-33.53-.002-18.52 15.01-33.532 33.53-33.532 14.444 0 26.773 9.132 31.5 21.937 4.012-15.413 7.215-31.028 10-46.75L248 35.157c30.608 14.242 51.855 28.52 66.25 42.344L90.97 21.937c-1.303-.348-2.508-.527-3.657-.562-.348-.01-.7-.015-1.032 0zM367.595 77.25l49.125 10.28L401.186 108l-33.593-30.75zm-344.75.094c-4.748.095-8.76 4.893-6.157 13.5l53.75 268.937c-8.64 5.273-17.805 7.86-26.25 9.095l27.22 122.22C84.91 490.43 98.08 488.76 111 486.28c-6.06-2.62-11.41-7.057-15.156-13.092-9.77-15.734-4.953-36.388 10.78-46.157 5.41-3.357 11.412-5.004 17.345-5.06 11.324-.11 22.432 5.517 28.843 15.842 7.63 12.293 6.337 27.606-2.094 38.375 15.226-4.732 30.186-10.284 45.03-16.218l-91.72-230.94c3.786 31.363 3.21 55.78-.28 74.782L32.5 86.063c-1.82-6.018-5.963-8.793-9.656-8.718zm360.312 263.969l37.406 33.5-23.656 9.937-13.75-43.438zm-259.625 6.875l15.657 47.718-24.218-2.156 8.56-45.563z"}}]})(props);
};
