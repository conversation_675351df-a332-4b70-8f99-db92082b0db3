// THIS FILE IS AUTO GENERATED
var GenIcon = require('../lib').GenIcon
module.exports.BiBookHeart = function BiBookHeart (props) {
  return GenIcon({"tag":"svg","attr":{"viewBox":"0 0 24 24"},"child":[{"tag":"path","attr":{"d":"M3,8v6v3v2c0,2.201,1.794,3,3,3h15v-2H6.012C5.55,19.988,5,19.806,5,19s0.55-0.988,1.012-1H19h1h1v-1v-2V4 c0-1.103-0.897-2-2-2H6C4.794,2,3,2.799,3,5V8z M6,4h13v11v1H5v-2V8V5C5,4.194,5.55,4.012,6,4z"}},{"tag":"path","attr":{"d":"M11.997,14l3.35-3.289c0.871-0.854,0.871-2.21,0-3.069c-0.875-0.855-2.255-0.855-3.126,0L11.997,7.86l-0.224-0.219 c-0.87-0.855-2.25-0.855-3.125,0c-0.871,0.859-0.871,2.215,0,3.069L11.997,14z"}}]})(props);
};
