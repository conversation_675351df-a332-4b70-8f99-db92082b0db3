// THIS FILE IS AUTO GENERATED
var GenIcon = require('../lib').GenIcon
module.exports.GiBananaPeeled = function GiBananaPeeled (props) {
  return GenIcon({"tag":"svg","attr":{"viewBox":"0 0 512 512"},"child":[{"tag":"path","attr":{"d":"M389.7 23.58c-7.4 0-13.4 3.67-20.3 11.9-7.9 9.41-15.5 24.67-22.5 43.42-13.5 36.2-25.2 85.1-39.6 132 19.5 23.7 37.1 35.3 54.1 38.1 16.8 2.9 34.7-2.5 55.7-16.3 9.6-46.3 11.1-99.2 5.6-140.38-2.8-20.69-7.3-38.43-13-50.54-5.6-12.11-11.8-17.35-16.7-17.98-1.1-.15-2.2-.22-3.3-.22zM250.1 180.7c-2.7 0-5.4 0-8.1.1-7.2.2-14.6.8-21.8 1.7-29.2 3.5-57.9 12.3-77.5 24.8-18.5 11.8-28.3 25.3-27.1 41.9 3 .3 8.6.1 15.6-.9 14.2-1.7 34.3-5.7 55.7-8.5 4.8-.7 9.7-1.2 14.7-1.7 26.7-20.4 58-34.9 89.3-35.5 1.6-5.5 3.3-11 4.9-16.5-13.2-3.6-29-5.4-45.7-5.4zm190.2 21.2c-1.2 9.6-2.7 19.3-4.5 28.6 7.6 3.3 14.3 7.9 20.2 13.4 4.5-1.3 8.9-2.6 13-3.9 10.9-3.5 20-7.4 23.1-10 1.7-1.4 1.5-1.8 1.5-1.8 0 .1.2-.8-1.7-3.4l-.1-.1c-6.5-8.7-25.4-19.1-46.8-22.2-1.5-.2-3.1-.4-4.7-.6zm-148.1 18.8c-41.8.5-90.1 33.9-119 70-14.9 18.7-24.5 37.9-26.3 51.5-.9 6.7.1 11.7 2.2 15.2s5.5 6.2 12.5 8c4.7 1.1 14-2.5 26.2-12 12.2-9.6 26.5-23.8 42.3-38.2 30.3-27.7 66.9-57.3 109.4-54.4-16.2-7.4-31.8-20.8-47.3-40.1zm136 26.2c-22.7 15-44.6 22.8-66.5 20.2 9.8 5.7 17.6 14.6 23.5 24.7 8.9 15.3 14.8 33.7 20.5 51.5 5.7 17.7 11.2 34.7 17.8 46.4 6.7 11.6 12.9 17 22.7 16.9 8.7 0 15.6-6.1 21.1-19.1 5.5-13.1 8.3-32.1 7.1-51.8-1.1-19.8-6-40.2-14.5-56.7-7.8-15-18.3-26.3-31.7-32.1zm-95.6 31.7c-31.6.8-62.4 24.3-90.4 49.9-15.4 14-29.8 28.4-43.3 39-8.2 6.5-16.4 12.1-24.9 14.7-31.2 18.7-70 33.8-118.9 44.3.72 10 3.38 18.5 6.81 27.1 9.61-2.7 18.34-3.9 26.49-4.8l4.51-.5 3.08 3.3c37.81 41.2 83.41 45.3 132.01 24.7 48.6-20.6 98.8-67.5 138.5-128.6 5-7.7 9.6-15.2 14-22.7-3.3-9-6.8-17.2-10.9-24.1-5.4-9.4-11.2-15.8-18.5-19.3-6.9 19.7-17.8 45.5-36.5 71.2-30.3 41.5-81.6 83-168.9 100.1l-3.4-17.6c83.1-16.4 129.8-54.8 157.8-93.1 16.6-22.7 26.4-45.3 32.9-63.6zM37.35 430.1c-6.19 1.1-12.51 2.2-19 3.2 5.1 11 8.53 22.8 9.82 35.5 5.9-3.6 11.49-6.6 16.83-9-3.52-8.9-6.55-18.6-7.65-29.7z"}}]})(props);
};
