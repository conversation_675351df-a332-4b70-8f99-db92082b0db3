// THIS FILE IS AUTO GENERATED
var GenIcon = require('../lib').GenIcon
module.exports.GiDiamondsSmile = function GiDiamondsSmile (props) {
  return GenIcon({"tag":"svg","attr":{"viewBox":"0 0 512 512"},"child":[{"tag":"path","attr":{"d":"M115.877 46.832c-23.13.06-46.282 9.02-63.975 26.87-35.385 35.702-35.533 93.438-.33 128.956 14.105 14.23 31.746 22.73 50.1 25.518-10.1-3.56-19.568-9.39-27.604-17.496-28.77-29.03-28.628-76.343.24-105.467 14.432-14.562 33.43-21.878 52.417-21.926 18.986-.048 37.96 7.174 52.345 21.69 18.258 18.42 24.854 44.2 19.873 68.027 13.803-33.148 7.414-72.75-19.228-99.63-17.602-17.76-40.708-26.602-63.838-26.542zm282.79 0c-23.13-.06-46.235 8.782-63.837 26.54-26.642 26.88-33.03 66.484-19.228 99.632-4.98-23.826 1.615-49.606 19.873-68.027 14.385-14.516 33.36-21.738 52.345-21.69 18.987.048 37.985 7.364 52.418 21.926 28.867 29.124 29.01 76.437.24 105.467-8.037 8.107-17.504 13.935-27.605 17.496 18.354-2.787 35.995-11.287 50.1-25.518 35.203-35.518 35.055-93.254-.33-128.955-17.693-17.85-40.845-26.81-63.975-26.87zm-271.915 54.99c-14.127.04-28.275 5.553-39.172 16.547-21.793 21.987-21.863 57.334-.238 79.153 21.625 21.82 56.423 21.75 78.217-.238 21.792-21.988 21.862-57.335.237-79.154-10.813-10.91-24.918-16.346-39.045-16.308zm261.04 0c-14.126-.038-28.232 5.4-39.044 16.31-21.625 21.818-21.555 57.165.238 79.153 21.794 21.988 56.592 22.058 78.217.238s21.555-57.166-.238-79.154c-10.897-10.995-25.045-16.51-39.172-16.548zm-6.214 17.873c-5.868 5.834-9.502 13.913-9.502 22.842 0 17.79 14.42 32.21 32.21 32.21 9.17 0 17.44-3.836 23.306-9.985-1.113 8.36-4.84 16.422-11.196 22.834-15.46 15.6-40.596 15.538-56.14-.145-15.545-15.682-15.61-41.037-.147-56.64 6.057-6.11 13.6-9.808 21.468-11.115zm-264.564.61c-5.52 5.78-8.914 13.607-8.914 22.232 0 17.79 14.42 32.21 32.213 32.21 10.407 0 19.656-4.94 25.544-12.597-.717 9.212-4.567 18.238-11.568 25.3-15.545 15.684-40.68 15.745-56.142.146-15.46-15.6-15.4-40.958.145-56.64 5.362-5.41 11.867-8.96 18.72-10.65zM15.95 266.4v41.93l28.204-9.066 10.29-27.93c-12.823-1.508-25.654-3.14-38.493-4.934zm480.066.686c-17.328 2.382-34.67 4.487-52.022 6.348l.272 30.765 51.75 25.517v-62.63zm-422.448 6.4l-11.152 30.268L91.64 341.46l38.07-28.452 4.538-34.11c-20.207-1.45-40.434-3.253-60.68-5.412zm351.752 1.852c-24.378 2.35-48.78 4.185-73.21 5.504l-3.256 40.017 38.218 31.533 38.555-42.32-.307-34.735zm-272.384 4.805l-4.624 34.742 36.15 52.756 29.972-33.017-2.682-51.885c-19.588-.53-39.194-1.398-58.816-2.595zm180.35 1.62c-34.215 1.475-68.48 1.93-102.8 1.372l2.393 46.29 59.516 33.682 37.567-40.53 3.324-40.813zm-284.048 35.5L15.95 327.958v14.42c19.996 14.487 40.67 27.61 62.04 39.626V354.36l-28.752-37.098zm390.205 5.397l-40.61 44.572 3.935 36.21c32.464-15.395 62.304-32.803 91.048-53.973l-54.373-26.81zm-302.69 8.416l-40.073 29.95v31.107c25.3 13.236 51.554 25.073 78.806 35.887l-1.853-43.12-36.88-53.824zm204.01 7.338l-37.972 40.97-3.093 64.73c30.68-10.36 58.82-20.91 85.158-32.5l-4.396-40.45-39.696-32.75zm-113.156 9.502l-35.193 38.766 2.094 48.666c20.89 7.812 42.347 15.1 64.396 22.025 7.42-2.33 14.646-4.65 21.784-6.97l3.37-70.542-56.45-31.944z"}}]})(props);
};
