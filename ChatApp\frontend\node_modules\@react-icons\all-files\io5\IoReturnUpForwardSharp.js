// THIS FILE IS AUTO GENERATED
var GenIcon = require('../lib').GenIcon
module.exports.IoReturnUpForwardSharp = function IoReturnUpForwardSharp (props) {
  return GenIcon({"tag":"svg","attr":{"viewBox":"0 0 512 512"},"child":[{"tag":"path","attr":{"fill":"none","strokeLinecap":"square","strokeMiterlimit":"10","strokeWidth":"32","d":"M400 160l64 64-64 64"}},{"tag":"path","attr":{"fill":"none","strokeLinecap":"square","strokeMiterlimit":"10","strokeWidth":"32","d":"M448 224H48v128"}}]})(props);
};
