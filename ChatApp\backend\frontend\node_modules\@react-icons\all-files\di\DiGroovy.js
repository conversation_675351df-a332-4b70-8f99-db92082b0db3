// THIS FILE IS AUTO GENERATED
var GenIcon = require('../lib').GenIcon
module.exports.DiGroovy = function DiGroovy (props) {
  return GenIcon({"tag":"svg","attr":{"version":"1.1","viewBox":"0 0 32 32"},"child":[{"tag":"path","attr":{"d":"M15.997 10.084c0 0-1.742 2.887-2.57 4.29-0.16 0.060-0.177 0.043-0.353 0.215-0.424-0.486-0.929-0.043-1.065 0.229-0.059-0.587-0.157-0.588-0.288-0.72 0.076-0.514 0.327-1.351-0.25-1.952-1.117-0.753-2.409 1.088-3.129 2.053l-0.388 0.644c-1.601-0.033-2.862 0.009-4.382-0.022 1.854 0.695 1.93 0.711 4.17 1.561 0 0.068 0.167 0.385 0.352 0.571 0.153 0.155 0.32 0.242 0.403 0.294-0.11 0.056-0.332 0.144-0.398 0.267-0.303 0.565 0.143 0.696 0.432 1.146 0.326 0.589 0.396 0.979 1.166 0.648 0.264-0.113 0.582-0.154 0.785-0.217-0.717 1.132-0.902 1.441-2.158 3.391l0.013 0.004c5.023-1.961 5.152-2.011 7.636-2.993 3.571 1.386 3.776 1.5 7.698 2.994-0.548-0.855-0.922-1.391-1.32-2.067 0.157-0.013 0.5-0.047 0.806-0.241 0.22-0.14 0.479-0.298 0.712-0.595 0.459-0.584 0.571-1.322 0.403-2.243-0.080-0.438-0.243-0.864-0.22-0.879 2.135-0.806 2.386-0.894 4.377-1.645-2.393 0.035-2.508 0.012-4.755 0.017-0.037-0.428-0.252-0.649-0.913-0.628-0.247 0.041-0.403 0.125-0.519 0.462-0.369-0.681-0.88-0.481-1.096-0.146-0.285-0.292-0.951-0.39-1.285-0.176-0.246-0.115-0.533-0.116-0.784 0.184-0.182-0.152-0.238-0.195-0.636-0.354-0.917-1.551-1.68-2.788-2.443-4.092zM16.009 10.915c0.362 0.553 1.607 2.703 1.915 3.184-0.349 0.024-0.692 0.209-0.951 0.582-0.224-0.231-0.486-0.461-0.95-0.479-0.563-0.097-0.863 0.153-1.242 0.587-0.113-0.377-0.549-0.466-0.928-0.289 0.338-0.538 1.938-3.234 2.156-3.585zM11.286 13.311c-0.052 1.485-1.106 2.808-1.862 2.325-0.571-0.465 0.154-1.64 0.611-2.055-0.034 0.317 0.254 0.515 0.024 0.796-0.462 0.563-0.28 0.97-0.081 1.033 0.531 0.168 1.040-1.271 1.040-2.28 0-0.647-0.379-0.635-1.124 0.141-1.018 1.059-1.568 2.085-1.201 2.787 0.204 0.392 0.502 0.673 0.993 0.615 0.958-0.115 1.536-1.299 1.599-2.059 0.153-0.004 0.112 0.009 0.153-0.022 0 0.134 0.184 0.782 0.329 1.208 0.19 0.727 0.999 2.185-1.418 2.753-0.457 0.116-0.637 0.154-1.088 0.331-0.221-0.458-0.273-0.606-0.722-1.048 0.609-0.213 0.614-0.192 1.007-0.329 1.52-0.529 1.976-0.98 1.924-1.626 0 0 0.019-0.332-0.192-0.61-0.106 0.371-0.303 0.668-0.303 0.668-0.452 0.704-1.342 1.14-2.132 0.863-0.432-0.152-0.618-0.483-0.618-1.052 0-0.727 1.659-2.833 2.472-3.19 0.575-0.171 0.6 0.303 0.592 0.75zM18.020 14.658c0.269 0.005 0.563 0.137 0.762 0.351 0.615 0.664 0.548 1.468 0.169 1.973-0.451 0.602-1.587 0.335-1.855-0.506-0.125-0.391 0.098-1.358 0.492-1.682 0.119-0.098 0.271-0.14 0.433-0.137zM15.832 14.726c0.040 0.001 0.080 0.007 0.121 0.018 0.52 0.031 0.631 0.323 0.726 0.58 0.239 0.647 0.179 1.254-0.312 1.751-0.736 0.744-1.594 0.415-1.591-0.588 0.015-0.714 0.46-1.776 1.056-1.761zM17.767 14.765c-0.397 0.297-0.448 1.059-0.084 1.342 0.143 0.134 0.223 0.227 0.635 0.265 0.283 0.026 0.729-0.261 0.751-0.546 0.010-0.129-0.013-0.344-0.324-0.694-0.26-0.293-0.599-0.361-0.684-0.311-0.274 0.162 0.022 0.291 0.129 0.398 0.168 0.168 0.209 0.241 0.13 0.291-0.156 0.099-0.329 0.085-0.386 0.059-0.108-0.050-0.472-0.255-0.168-0.804v0zM21.66 14.768c0.173 0.27 0.223 0.328 0.416 0.999 0.25 0.868 0.566 1.205 0.846 1.047 0.767-0.433 0.152-1.53-0.118-2.045 0.173-0.002 0.139 0.007 0.296 0.005 0.124 0.488 0.090 0.401 0.422 1.878 0.398 1.767 0.458 2.237-0.547 3.030-0.462 0.364-1.142 0.384-1.726 0.351l-0.659-0.097c0.454-0.298 0.564-0.577 0.561-1.157 1.324 0.555 2.029 0.137 2.311-0.544 0.136-0.328 0.089-0.761-0.042-1.524 0.008 0.621-0.005 0.736-0.087 1.043-0.165 0.616-0.567 0.788-0.841 0.352-0.105-0.168-0.357-0.726-0.435-1.327s-0.137-1.445-0.519-1.798c0.042-0.083 0.085-0.177 0.122-0.213v0zM20.369 14.778c0.267 0.071 0.592 0.212 0.767 0.539 0.241 0.452 0.401 0.692 0.388 1.499-0.014 0.861-0.353 1.092-0.576 1.124-0.289 0.042-0.789 0.035-1.086-1.655-0.099-0.56-0.278-1.093-0.467-1.268 0.094-0.113 0.101-0.099 0.171-0.211 0.095 0.104 0.177 0.279 0.308 0.805 0.21 0.837 0.401 1.010 0.772 1.057 0.579 0.074 0.806-0.809 0.33-1.442-0.105-0.139-0.378-0.318-0.522-0.244 0.008 0.244 0.117 0.167 0.119 0.329-0.027 0.192-0.055 0.226-0.117 0.332-0.147-0.058-0.218-0.081-0.347-0.302-0.162-0.321-0.057-0.622 0.26-0.564v0zM15.607 14.857c-0.272 0.192-0.615 0.828-0.315 1.398 0.148 0.212 0.307 0.385 0.661 0.311 0.253-0.053 0.695-0.352 0.756-0.776 0.053-0.363-0.473-1.065-0.765-0.901-0.339 0.189-0.132 0.325 0.077 0.529 0.191 0.187 0.054 0.299-0.080 0.354-0.268 0.11-0.417 0.003-0.484-0.21s-0.009-0.394 0.149-0.705v0zM12.638 14.924c0.058 0.178 0.105 0.415 0.226 0.646 0.029 0.024 0.136 0.001 0.278-0.162 0.089-0.103 0.191-0.246 0.369-0.479 0.108 0.099 0.118 0.172 0.148 0.226 0.075 0.134 0.233 0.124 0.282 0.11 0.174-0.172 0.202-0.284 0.318-0.299 0.011 0.223 0.021 0.362-0.267 0.7-0.169 0.198-0.428 0.203-0.518 0.178-0.125-0.035-0.206-0.112-0.402-0.212-0.267 0.041-0.197 0.326-0.149 0.566 0.125 0.633 0.519 0.885 0.862 0.699 0.12-0.065 0.092-0.064 0.323-0.158-0.039 0.401-0.062 0.479 0.010 0.928-0.56 0.312-1.016 0.258-1.141-0.524-0.094-0.655-0.27-1.451-0.43-1.612-0.151-0.152-0.212-0.202-0.212-0.202s0.189-0.25 0.303-0.404zM5.27 15.008c0.78 0.010 1.659 0.010 2.595 0.018-0.134 0.329-0.181 0.559-0.2 0.884-0.437-0.169-1.943-0.708-2.395-0.902zM26.733 15.018c-1.033 0.372-1.776 0.66-2.772 1.040-0.109-0.467-0.129-0.609-0.226-1.030 1.658-0.005 1.686-0.002 2.999-0.010v0zM19.506 17.132c0.108 0.445 0.261 0.663 0.261 0.663 0.209 0.36 0.471 0.564 0.87 0.667 0.088 0.141 0.119 0.169 0.154 0.245 0.038 0.474 0.011 0.532-0.357 0.802-0.216 0.211-0.225 0.514-0.002 0.796 0.239 0.227 0.491 0.309 1.424 0.067 0.18 0.31 0.432 0.676 0.803 1.26-1.249-0.47-2.97-1.142-6.66-2.578 0 0-4.317 1.676-6.632 2.579 0.968-1.527 1.048-1.656 1.736-2.725 0.233-0.091 0.155-0.053 0.362-0.16 0.476-0.253 0.857-0.631 1.065-1.186 0.381 0.859 0.807 0.958 1.695 0.679 0.183-0.084 0.394-0.208 0.439-0.537 0.476 0.428 1.483 0.508 2.218-0.395 0.634 0.742 2.073 0.812 2.624-0.178v0zM21.74 18.027c0 0 0.042 0.134 0.067 0.175-0.105 0.003-0.221 0.001-0.221 0.001 0.053-0.043 0.095-0.096 0.154-0.176v0z"}}]})(props);
};
