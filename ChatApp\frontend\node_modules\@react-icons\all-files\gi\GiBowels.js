// THIS FILE IS AUTO GENERATED
var GenIcon = require('../lib').GenIcon
module.exports.GiBowels = function GiBowels (props) {
  return GenIcon({"tag":"svg","attr":{"viewBox":"0 0 512 512"},"child":[{"tag":"path","attr":{"d":"M107.643 25.715c-4.28.084-8.759 1.173-13.454 3.611C53.518 51.481 25.4 97.483 60.07 139.121c-14.984 19.347-20.219 35.593-9.502 53.365-16.915 16.155-12.66 32.309.266 48.463-11.026 14.52-5.226 21.561.158 28.787-11.725 13.449-37.788 54.228-8.14 70.23-14.488 18.706-4.632 80.856 49.146 69.048 9.032 26.311 42.663 26.504 63.016 4.968 10.512-.468 25.749-4.352 45.271-12.212-8.943-4.27-21.504-10.238-31.857-15.147-5.176-40.86-29.098-40.185-49.239-49.53 12.484-14.758 11.097-29.518 1.135-44.277 21.287-26.793 17.823-48.635 1.744-67.955 13.287-19.647 8.43-37.645 1.42-55.447 6.765-21.062 16.503-41.877 1.66-64.74 17.837 26.196 32.663 45.036 54.98 36.703 28.804 20.293 53.413 30.45 79.784 17.959 21.286 15.321 41.202 7.119 68.078-14.824 26.185 22.077 46.803-2.894 64.953-27.948-6.133 26.488-4.487 50.94 12.082 62.713-35.437 22.58-25.162 39.74-7.494 48.987-29.25 31.677-1.644 50.067 20.498 66.41-15.403 20.495-8.362 30.287 2.346 45.83-16.843-5.367-29.476 11.142-34.32 23.254-17.354-7.679-42.318-7.341-57.82-.211-19.54-8.827-31.82-6.08-47.395 8.877-17.465-4.666-34.928-11.631-52.393 5.392-27.894 47.569 4.222 190.502 58.602 64.563 27.827 15.997 50.26 4.89 58.61-4.861 27.733 8.952 57.144 19.38 64.501.546 16.027 6.017 99.283 18.287 71.356-54.894 10.057-21.226 11.246-40.839.71-58.32 11.805-15.245 16.055-35.104 1.337-53.485 22.257-27.526 3.886-54.386-11.842-68.355 12.884-18.203 17.436-25.168 4.177-39.86 4.8-12.253-1.38-36.408-12.873-46.474 23.733-40.438 9.553-94.849-52.086-72.436-23.91-19.77-69.494 8.79-69.716 28.656-35.772-8.603-51.965-5.588-62.233 14.553-24.282-24.11-48.205-16.262-69.814-3.744C192.508 64.108 191.934 47.4 153.8 52.953c-12.773-9.12-27.612-27.603-46.158-27.238z"}}]})(props);
};
