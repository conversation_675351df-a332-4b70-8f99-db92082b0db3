// THIS FILE IS AUTO GENERATED
var GenIcon = require('../lib').GenIcon
module.exports.GiCoolSpices = function GiCoolSpices (props) {
  return GenIcon({"tag":"svg","attr":{"viewBox":"0 0 512 512"},"child":[{"tag":"path","attr":{"d":"M170.344 13.625l-17 22.72 17 22.718 17.03-22.72-17.03-22.718zM375.25 21.22l-22.313 29.75 22.313 29.78 22.28-29.78-22.28-29.75zm-130.28 18.5l-22.282 29.75 22.28 29.75 22.313-29.75-22.31-29.75zM54.03 43.093l-17 22.718 17 22.72 17.032-22.72-17.03-22.718zm391.126 24.843l-13.594 29.407-29.437 13.594 29.438 14.156 13.593 28.844 13.563-29.407 29.436-13.593-28.875-13.593-14.124-29.406zm-300.812 13.22l-17.063 36.968-36.968 17.063 36.97 17.78 17.062 36.25 17.03-36.968 36.97-17.063-36.25-17.062-17.75-36.97zm208.594 9.218l-22.313 29.75 22.313 29.78 22.28-29.78-22.28-29.75zM252.78 153.5l-18.5 24.688 18.5 24.718 18.5-24.72-18.5-24.686zm103.032 31.063l-14 30.375-30.375 14 30.375 14.593 14 29.814 14.032-30.375 30.375-14.032-29.783-14-14.625-30.375zM85.406 191.53l-17.03 22.69 17.03 22.718 17-22.72-17-22.687zm94.125 27.064l-17 22.72 17 22.717 17.032-22.717-17.03-22.72zm72.657 24.062c-7.673.11-14.296 4.334-21.687 10.03-46.035 35.503-104.323 87.027-105.03 115.814-8.237.98-15.564 5.264-15.564 12.813 0 11.258 15.384 15.297 26.625 12.25-.138.687-.218 1.404-.218 2.156 0 16.076 31.4 17.454 37.25 4.467 34.6 9.25 77.038 10.805 115.813 6.157 2.352 14.95 33.956 15.173 37.688 1.03 10.247 7.523 28.91 7.963 40.218 1.376 10.675 6.772 32.408 3.443 32.408-9.813 0-1.963-.465-3.7-1.313-5.218 5.86-2.54 10.063-6.99 10.063-13.376 0-14.332-21.145-18.725-34.72-13.594-4.536-31.173-59.7-84.754-100.874-115.688-8.093-6.08-14.688-8.492-20.656-8.406zM76.72 355.062c-10.75.023-21.5 5.02-21.5 14.907 0 19.773 43 19.4 43 0 0-9.978-10.753-14.93-21.5-14.908zm390.436 13.032c-9.547.042-19.094 4.416-19.094 13.03 0 17.73 38.188 17.568 38.188 0 0-8.783-9.547-13.072-19.094-13.03zm-428.75 15.72c-9.547.018-19.093 4.434-19.093 13.217 0 17.57 38.187 17.234 38.187 0 0-8.862-9.547-13.237-19.094-13.217zm411.72 23.498c-10.75.023-21.5 5.02-21.5 14.907 0 19.775 42.968 19.4 42.968 0 0-9.98-10.72-14.93-21.47-14.908zm-273.845 6.594c-6 .013-11.994 1.765-15.624 5.25-11.234-4.884-29.875-1.086-29.875 11.188 0 14.306 25.317 16.71 34.72 7.812 11.234 4.8 29.875 1.01 29.875-11.03 0-8.864-9.547-13.24-19.094-13.22zm87.22 1.594c-9.547.042-19.094 4.415-19.094 13.03 0 17.727 38.188 17.57 38.188 0 0-8.782-9.547-13.072-19.094-13.03zm-192 9.375c-9.547.02-19.094 4.435-19.094 13.22 0 17.566 38.188 17.23 38.188 0 0-8.865-9.547-13.24-19.094-13.22zM370 438.063c-9.547.04-19.094 4.446-19.094 13.062 0 9.873 11.848 14.203 22.344 13.063-.717 1.552-1.094 3.284-1.094 5.187 0 18.818 36.526 20.522 43.75 5.53 10.313.833 21.625-3.56 21.625-13.124 0-14.715-26.8-16.88-35.5-7.06-4.325-.91-9.065-.96-13.467-.19.345-1.054.53-2.18.53-3.405 0-8.784-9.546-13.104-19.093-13.063zm-150.906 3.5c-10.948.048-21.875 5.09-21.875 14.968 0 20.33 43.78 20.146 43.78 0 0-10.07-10.958-15.016-21.906-14.967zm100.594 17.687c-12.55.026-25.094 5.86-25.094 17.406 0 23.09 50.187 22.65 50.187 0 0-11.65-12.544-17.432-25.092-17.406zm-153 6.72c-10.948.047-21.907 5.087-21.907 14.967 0 20.328 43.782 20.145 43.782 0 0-10.072-10.927-15.016-21.875-14.968z","fillRule":"evenodd"}}]})(props);
};
