// THIS FILE IS AUTO GENERATED
import { GenIcon } from '../lib';
export function BiAccessibility (props) {
  return GenIcon({"tag":"svg","attr":{"viewBox":"0 0 24 24"},"child":[{"tag":"circle","attr":{"cx":"18","cy":"4","r":"2"}},{"tag":"path","attr":{"d":"M17.836 12.014l-4.345.725 3.29-4.113c.175-.219.25-.501.207-.778s-.2-.523-.434-.679l-6-4c-.396-.265-.924-.213-1.262.125l-4 4 1.414 1.414 3.42-3.42 2.584 1.723-2.681 3.352C9.395 10.132 8.713 10 8 10c-1.294 0-2.49.416-3.471 1.115l1.451 1.451C6.575 12.216 7.261 12 8 12c2.206 0 4 1.794 4 4 0 .739-.216 1.425-.566 2.02l1.451 1.451C13.584 18.49 14 17.294 14 16c0-.445-.053-.878-.145-1.295L17 14.181V20h2v-7c0-.294-.129-.573-.354-.763C18.423 12.048 18.128 11.967 17.836 12.014zM8 20c-2.206 0-4-1.794-4-4 0-.739.216-1.425.566-2.02l-1.451-1.451C2.416 13.51 2 14.706 2 16c0 3.309 2.691 6 6 6 1.294 0 2.49-.416 3.471-1.115l-1.451-1.451C9.425 19.784 8.739 20 8 20z"}}]})(props);
};
