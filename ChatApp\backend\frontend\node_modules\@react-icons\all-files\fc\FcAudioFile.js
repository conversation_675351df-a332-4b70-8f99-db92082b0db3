// THIS FILE IS AUTO GENERATED
var GenIcon = require('../lib').GenIcon
module.exports.FcAudioFile = function FcAudioFile (props) {
  return GenIcon({"tag":"svg","attr":{"version":"1","viewBox":"0 0 48 48","enableBackground":"new 0 0 48 48"},"child":[{"tag":"rect","attr":{"x":"204","fill":"none","width":"48","height":"48"}},{"tag":"polygon","attr":{"fill":"#90CAF9","points":"244,45 212,45 212,3 234,3 244,13"}},{"tag":"polygon","attr":{"fill":"#E1F5FE","points":"242.5,14 233,14 233,4.5"}},{"tag":"g","attr":{"fill":"#1976D2"},"child":[{"tag":"circle","attr":{"cx":"227","cy":"30","r":"4"}},{"tag":"polygon","attr":{"points":"234,21 229,19 229,30 231,30 231,22.9 234,24"}}]},{"tag":"polygon","attr":{"fill":"#90CAF9","points":"40,45 8,45 8,3 30,3 40,13"}},{"tag":"polygon","attr":{"fill":"#E1F5FE","points":"38.5,14 29,14 29,4.5"}},{"tag":"g","attr":{"fill":"#1976D2"},"child":[{"tag":"circle","attr":{"cx":"23","cy":"30","r":"4"}},{"tag":"polygon","attr":{"points":"30,21 25,19 25,30 27,30 27,22.9 30,24"}}]}]})(props);
};
