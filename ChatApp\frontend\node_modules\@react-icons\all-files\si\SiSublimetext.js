// THIS FILE IS AUTO GENERATED
var GenIcon = require('../lib').GenIcon
module.exports.SiSublimetext = function SiSublimetext (props) {
  return GenIcon({"tag":"svg","attr":{"role":"img","viewBox":"0 0 24 24"},"child":[{"tag":"title","attr":{},"child":[]},{"tag":"path","attr":{"d":"M21.24,12.06a.72.72,0,0,0-.46-.65L13.4,9.07l7.37-2.34a.73.73,0,0,0,.47-.66V.38A.35.35,0,0,0,20.77,0L3.23,5.58a.68.68,0,0,0-.47.64v5.7a.65.65,0,0,0,.46.62l7.46,2.37L3.22,17.27a.73.73,0,0,0-.46.66v5.69a.34.34,0,0,0,.46.36l17.56-5.57a.65.65,0,0,0,.46-.62Z"}}]})(props);
};
