// THIS FILE IS AUTO GENERATED
var GenIcon = require('../lib').GenIcon
module.exports.IoBarChartOutline = function IoBarChartOutline (props) {
  return GenIcon({"tag":"svg","attr":{"viewBox":"0 0 512 512"},"child":[{"tag":"path","attr":{"fill":"none","strokeLinecap":"round","strokeLinejoin":"round","strokeWidth":"32","d":"M32 32v432a16 16 0 0016 16h432"}},{"tag":"rect","attr":{"width":"80","height":"192","x":"96","y":"224","fill":"none","strokeLinecap":"round","strokeLinejoin":"round","strokeWidth":"32","rx":"20","ry":"20"}},{"tag":"rect","attr":{"width":"80","height":"240","x":"240","y":"176","fill":"none","strokeLinecap":"round","strokeLinejoin":"round","strokeWidth":"32","rx":"20","ry":"20"}},{"tag":"rect","attr":{"width":"80","height":"304","x":"383.64","y":"112","fill":"none","strokeLinecap":"round","strokeLinejoin":"round","strokeWidth":"32","rx":"20","ry":"20"}}]})(props);
};
