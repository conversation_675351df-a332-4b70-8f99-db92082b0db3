// THIS FILE IS AUTO GENERATED
var GenIcon = require('../lib').GenIcon
module.exports.BiBullseye = function BiBullseye (props) {
  return GenIcon({"tag":"svg","attr":{"viewBox":"0 0 24 24"},"child":[{"tag":"path","attr":{"d":"M12,6c-3.309,0-6,2.691-6,6s2.691,6,6,6s6-2.691,6-6S15.309,6,12,6z M12,16c-2.206,0-4-1.794-4-4s1.794-4,4-4s4,1.794,4,4 S14.206,16,12,16z"}},{"tag":"path","attr":{"d":"M12,2C6.579,2,2,6.579,2,12s4.579,10,10,10s10-4.579,10-10S17.421,2,12,2z M12,20c-4.337,0-8-3.663-8-8s3.663-8,8-8 s8,3.663,8,8S16.337,20,12,20z"}},{"tag":"path","attr":{"d":"M12,10c-1.081,0-2,0.919-2,2s0.919,2,2,2s2-0.919,2-2S13.081,10,12,10z"}}]})(props);
};
