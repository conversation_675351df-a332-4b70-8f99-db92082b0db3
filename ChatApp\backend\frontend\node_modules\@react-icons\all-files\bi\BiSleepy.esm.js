// THIS FILE IS AUTO GENERATED
import { GenIcon } from '../lib';
export function BiSleepy (props) {
  return GenIcon({"tag":"svg","attr":{"viewBox":"0 0 24 24"},"child":[{"tag":"path","attr":{"d":"M12,2C6.486,2,2,6.486,2,12c0,5.514,4.486,10,10,10s10-4.486,10-10C22,6.486,17.514,2,12,2z M12,20c-4.411,0-8-3.589-8-8 s3.589-8,8-8s8,3.589,8,8S16.411,20,12,20z"}},{"tag":"ellipse","attr":{"cx":"12","cy":"15.5","rx":"3","ry":"2.5"}},{"tag":"path","attr":{"d":"M10 7c-2.905 0-3.983 2.386-4 3.99l2 .021C8.002 10.804 8.076 9 10 9V7zM14 7v2c1.826 0 1.992 1.537 2 2.007L17 11h1C18 9.392 16.935 7 14 7z"}}]})(props);
};
