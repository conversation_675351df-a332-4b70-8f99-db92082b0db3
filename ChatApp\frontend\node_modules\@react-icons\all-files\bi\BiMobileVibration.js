// THIS FILE IS AUTO GENERATED
var GenIcon = require('../lib').GenIcon
module.exports.BiMobileVibration = function BiMobileVibration (props) {
  return GenIcon({"tag":"svg","attr":{"viewBox":"0 0 24 24"},"child":[{"tag":"path","attr":{"d":"M15.535,2.808c-0.756-0.756-2.072-0.756-2.828,0l-9.899,9.899c-0.78,0.779-0.78,2.049,0,2.828l5.657,5.657 c0.378,0.378,0.88,0.586,1.414,0.586s1.036-0.208,1.414-0.586l9.899-9.899c0.378-0.378,0.586-0.88,0.586-1.414 s-0.208-1.036-0.586-1.414L15.535,2.808z M9.879,19.778L9.879,19.778v1V19.778l-5.657-5.657l9.899-9.899l5.657,5.657L9.879,19.778 z"}},{"tag":"circle","attr":{"cx":"9","cy":"15","r":"1"}},{"tag":"path","attr":{"transform":"rotate(-134.999 18 18)","d":"M17 13.757H19V22.241999999999997H17z"}},{"tag":"path","attr":{"transform":"rotate(45.001 6 6)","d":"M5 1.757H7V10.241999999999999H5z"}}]})(props);
};
