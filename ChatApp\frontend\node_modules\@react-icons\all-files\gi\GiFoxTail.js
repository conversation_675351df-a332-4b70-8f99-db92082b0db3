// THIS FILE IS AUTO GENERATED
var GenIcon = require('../lib').GenIcon
module.exports.GiFoxTail = function GiFoxTail (props) {
  return GenIcon({"tag":"svg","attr":{"viewBox":"0 0 512 512"},"child":[{"tag":"path","attr":{"d":"M144.734 22.04c-5.61-.035-11.163.12-16.634.456-43.77 2.7-82.904 17.003-103.862 44.996-7.506 10.027-5.682 23.91 2.95 31.42 8.635 7.51 23.004 8.053 36.234-.52 22.84-14.805 47.933-9.572 64.27 6.172 16.34 15.745 23.736 41.453 7.54 71.145-19.17 35.143-32.716 96.153-20.146 156.526 12.57 60.374 52.968 119.76 139.728 145.772 33.476 10.036 78.825 16.75 121.645 7.666 44.507-8.788 95.85-34.758 106.892-63.11-9.25 8.885-19.44 15.14-30.202 19.79 18.306-20.92 31.735-49.732 36.79-88.174l2.53-19.24-16.322 10.496c-10.503 6.755-20.585 13.403-30.093 18.396 2.638-5.872 5.038-13.22 7.73-22.777-11.097 15.19-23.73 25.355-38.598 31.472-9.234-.503-18.353-4.867-29.21-16.097-11.358-11.747-18.12-32.095-22.463-57.666-4.344-25.572-6.46-55.927-10.668-86.877-8.42-61.902-25.912-127.873-89.74-161.035-36.955-19.2-79.092-28.577-118.372-28.813zm-.123 18.01c36.462.255 76.11 9.065 110.197 26.774 56.393 29.3 71.994 87.14 80.203 147.488 4.104 30.175 6.186 60.554 10.758 87.465 1.316 7.753 2.835 15.242 4.693 22.385-15.448.04-27.254-8.307-41.704-24.717 7.385 30.41 11.99 36.534 25.705 55.55-28.22-8.235-60.64-34.74-80.95-64.063-3.274 40.047 20.223 71.574 33.275 83.93-25.176-14.196-60.713-41.536-84.623-88.655-1.016 41.426 11.93 87.732 36.45 116.465-34.515-11.536-64.97-99.472-85.42-127.633-13.04 33.217-2.948 89.085 16.072 130.122-19.628-22.838-30.887-49.375-36.555-76.596-11.524-55.342 1.75-113.847 18.325-144.238 19.55-35.842 10.915-71.75-10.85-92.726-21.768-20.976-56.854-27.564-86.554-8.315-8.56 5.55-12.688 3.732-14.626 2.045-1.94-1.687-2.76-3.84-.356-7.053 16.106-21.514 50.135-35.324 90.56-37.817 5.052-.312 10.195-.45 15.403-.414z"}}]})(props);
};
