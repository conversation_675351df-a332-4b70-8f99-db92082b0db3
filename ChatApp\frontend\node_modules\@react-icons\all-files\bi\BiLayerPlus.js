// THIS FILE IS AUTO GENERATED
var GenIcon = require('../lib').GenIcon
module.exports.BiLayerPlus = function BiLayerPlus (props) {
  return GenIcon({"tag":"svg","attr":{"viewBox":"0 0 24 24"},"child":[{"tag":"path","attr":{"d":"M21.484,11.125l-9.022-5c-0.301-0.167-0.667-0.167-0.968-0.001l-8.978,4.96C2.198,11.26,2.001,11.594,2,11.958 s0.195,0.698,0.513,0.875l9.022,5.04C11.687,17.958,11.854,18,12.022,18s0.335-0.042,0.486-0.126l8.978-5 C21.804,12.697,22,12.362,22,11.999S21.803,11.301,21.484,11.125z M12.023,15.855l-6.964-3.89l6.917-3.822l6.964,3.859 L12.023,15.855z"}},{"tag":"path","attr":{"d":"M12 22c.167 0 .335-.042.485-.126l9-5-.971-1.748L12 19.856l-8.515-4.73-.971 1.748 9 5C11.665 21.958 11.833 22 12 22zM20 2L18 2 18 4 16 4 16 6 18 6 18 8 20 8 20 6 22 6 22 4 20 4z"}}]})(props);
};
