// THIS FILE IS AUTO GENERATED
var GenIcon = require('../lib').GenIcon
module.exports.GiClockwork = function GiClockwork (props) {
  return GenIcon({"tag":"svg","attr":{"viewBox":"0 0 512 512"},"child":[{"tag":"path","attr":{"d":"M137.71 18.326L17.866 138.166l29.582 29.582c23.04-53.698 66.14-96.802 119.84-119.842l-29.58-29.58zm238.286.04L346.44 47.92c53.693 23.048 96.79 66.155 119.822 119.857l29.576-29.575-119.842-119.84zm-133.27 37.04C137.405 62.64 54.448 150.126 54.448 257.31c0 111.93 90.466 202.397 202.398 202.397 32.75 0 63.66-7.757 91.007-21.52l-22.26-15.78c-21.173 8.84-44.394 13.734-68.745 13.734-98.652 0-178.827-80.172-178.827-178.824 0-90.942 68.133-166.178 156.065-177.382l8.637-24.528zm29.432.076l8.555 24.604c87.397 11.69 154.96 86.67 154.96 177.23 0 48.234-19.17 92.043-50.29 124.23l8.633 24.686c40.12-36.963 65.23-89.96 65.23-148.923 0-106.782-82.332-194.023-187.088-201.828zm-14.75 14.49l-32.3 91.706h18.925v45.068c4.35-1.182 8.917-1.824 13.63-1.824 4.36 0 8.596.547 12.65 1.564v-44.808h18.98L257.408 69.97zm71.82 44.42l-17.197 29.79c-3.446-1.657-6.983-3.158-10.596-4.5l14.144 40.683h-26.576v35.057c12.702 9.562 20.95 24.756 20.95 41.793 0 8.697-2.16 16.91-5.956 24.142l40.705 54.186 19.466-14.662 4.924 14.08 20.603 11.897c3.477-5.153 6.657-10.522 9.526-16.08l-29.35-16.945c6.967-13.842 11.447-29.152 12.86-45.348h33.862c.253-3.69.392-7.412.392-11.168 0-2.52-.066-5.026-.18-7.52h-33.82c-.98-16.423-5.11-31.993-11.796-46.112l29.45-17.002c-2.76-5.616-5.835-11.046-9.208-16.266l-29.48 17.02c-8.97-13.35-20.434-24.88-33.73-33.922l17.153-29.71c-5.178-3.438-10.566-6.585-16.144-9.41zm-145.38.313c-5.567 2.85-10.94 6.023-16.104 9.483l17.62 30.523c-12.89 9.09-23.988 20.545-32.67 33.737l-30.645-17.695c-3.362 5.228-6.422 10.667-9.167 16.29l30.73 17.74c-6.385 13.828-10.33 29.016-11.285 45.015H96.895c-.115 2.493-.182 4.998-.182 7.52 0 3.755.14 7.477.392 11.167h35.477c1.377 15.775 5.664 30.708 12.324 44.264l-30.613 17.674c2.855 5.565 6.028 10.938 9.492 16.1l30.592-17.663c8.745 12.622 19.743 23.56 32.418 32.236l-17.527 30.353c5.202 3.4 10.616 6.505 16.216 9.292l17.473-30.265c13.535 6.554 28.428 10.746 44.147 12.052v34.62c3.224.193 6.472.303 9.746.303 3 0 5.98-.09 8.94-.252v-34.497c6.595-.42 13.048-1.358 19.325-2.756l10.682-8.047-47.52-63.257c-24.326-4.454-42.908-25.862-42.908-51.428 0-16.593 7.833-31.43 19.976-41.026v-35.825h-26.63l14.196-40.31c-3.934 1.5-7.777 3.184-11.51 5.056l-17.554-30.405zm73.814 108.906c-18.67 0-33.605 14.935-33.605 33.605 0 18.67 14.936 33.603 33.605 33.603 18.67 0 33.604-14.934 33.604-33.603 0-18.67-14.934-33.604-33.604-33.604zm34.363 72.927c-5.978 5.234-13.142 9.14-21.03 11.233l50.952 67.828-14.578 10.984 79.74 56.525-32.137-91.902-13.975 10.525-48.973-65.193zm174.223 50.33c-14.203 33.102-36.037 62.167-63.27 84.998l9.7 27.733 83.15-83.15-29.58-29.58zM47.46 346.9l-29.585 29.586 119.84 119.84 29.603-29.603C113.616 443.69 70.508 400.593 47.46 346.9zm323.343 107.553c-7.848 4.552-15.992 8.65-24.395 12.254l29.58 29.58 22.33-22.33-27.515-19.504z"}}]})(props);
};
