// THIS FILE IS AUTO GENERATED
import { GenIcon } from '../lib';
export function GiWingedLeg (props) {
  return GenIcon({"tag":"svg","attr":{"viewBox":"0 0 512 512"},"child":[{"tag":"path","attr":{"d":"M24.928 23.108c8.025 58.99 116.646 113.317 197.394 132.7-51.69.28-128.258-5.556-185.43-22.678 24.43 47.073 109.81 60.78 187.105 67.028-57.808 15.162-109.87 21.8-173.96 19.956C82.1 255.192 166.16 254.14 236.62 242.794c-38.85 19.96-83.113 32.74-129.636 39.588 40.913 20.362 106.803 1.757 147.45-4.43-19.043 16.43-41.836 28.653-66.856 37.932 25.075 10.61 64.635 2.653 92.165-11.408-3.48 11.993-15.64 25.648-31.718 35.095 20.82 4.43 46.642.674 66.817-6.833 27.207 10.518 36.235 23.727 37.968 38.06-41.966 13.17-57.812 106.545 29.825 105.43-41.212-17.458-48.907-61.13-19.812-76.848 44.16-23.86 65.382 48.02 55.51 86.106 33.642-12.11 36.518-88.94-7.634-108.167 46.837.503 67.127 32.147 54.327 72.508 53.774-63.696 9.178-103.04-46.535-99.114-82.715-5.958-156.808-121.435-127.108-150.472 22.79-19.817 22.544-51.31-6.26-65.664-72.81-36.302-120.06-22.37-260.198-111.466z"}}]})(props);
};
