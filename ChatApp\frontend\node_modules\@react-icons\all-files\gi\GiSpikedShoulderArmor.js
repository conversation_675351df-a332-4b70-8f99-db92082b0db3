// THIS FILE IS AUTO GENERATED
var GenIcon = require('../lib').GenIcon
module.exports.GiSpikedShoulderArmor = function GiSpikedShoulderArmor (props) {
  return GenIcon({"tag":"svg","attr":{"viewBox":"0 0 512 512"},"child":[{"tag":"path","attr":{"d":"M203.1 16.79L187.2 175.8c-1.2 10.9 2.5 15.7 9.8 19.9 7.3 4.1 19 5.8 30.6 4.6 11.6-1.3 23-5.5 29.5-10.7 6.5-5.3 8.5-9.2 5.8-16.3zm126.1 51.75C388 122.1 422.4 190.3 434.9 250.6c6.7 32.3 7.2 62.3 1.3 87.1-5.8 24.7-18.4 44.7-38.3 54-11.8 5.4-31.1 7.4-58.2 8.7-27.1 1.2-61 .8-97.1-1.7-66.2-4.6-139.8-16.2-192.9-39.4 1.12 8.5 2.79 17.1 5.03 25.9 1.37 5.3 7.05 12 17.99 18.4 10.94 6.4 26.53 12.4 44.98 17.5 37 10.1 85.7 16.8 134.3 19.5 48.7 2.7 97.5 1.4 134.8-3.7 18.7-2.6 34.5-6.2 45.6-10.5 11.1-4.3 16.5-9.5 17.4-12 40.1-110.3 25.1-198-6.7-257.7-16-29.9-36.2-52.8-55.8-67.81-19.4-12.6-36.1-22.79-58.1-20.35zm-23 3.87C289 76.16 270 82.1 250.4 90.1l29.3 76.8c5.3 14.2-1 28.4-11.4 36.7-10.3 8.3-24.4 13-38.8 14.6-14.4 1.5-29.3 0-41.4-6.9-12.1-6.8-20.6-20.5-18.9-37.3l4.3-43c-12.5 8.5-24.6 17.7-36.1 27.6-12.9 11.1-24.9 23.2-35.7 36l70.8 69.5c8.2 8.7-1.3 34.1-6.3 41.3-7.4 10.1-18 18-29.9 22.5-11.8 4.5-25.2 5.7-37.55 1.4-12.48-4.2-23.61-14.1-30.19-29.3l-.21-.5L59 267.8c-7.78 22.1-11.81 45.8-11 71.1 49.45 24 126.9 37.1 195.9 42 35.4 2.4 68.7 2.8 95 1.6 26.3-1.2 46.6-4.8 51.4-7.1 13.5-6.2 23.4-20.5 28.4-41.8 5-21.3 4.8-49-1.4-79.2-7.1-34.3-22.1-71.8-45.1-107.4l-16.4 180.9-.1.4c-1.3 7.2-5.2 13.5-10.4 17.6-5.2 4.2-11.5 6.3-18 7.2-13 1.8-27.3-1.2-40.6-6.8s-25.6-13.9-33.6-24.7c-8-10.8-10.9-25.9-2.9-38.9l.4-.7 119.9-137.4c-17-25.8-38.4-50.57-64.3-72.19zM48.16 167.2l37.09 125.9c4.87 11.1 11.6 16.6 19.15 19.2 7.8 2.6 16.9 2 25.5-1.3 8.7-3.2 16.6-9.2 21.7-16.2 4.5-6.3 6.6-13.1 5.9-20.2zm301.44 28.6l-84.2 96.7c-3.9 6.6-2.8 11.6 2.2 18.4 5.1 7 15.1 14.2 26.1 18.8 10.9 4.6 22.9 6.7 31.2 5.6 4.1-.6 7.2-1.9 9.2-3.5 2-1.5 3.2-3.4 3.9-6.7zm-299.22 214c-4.96.9-7.63 2.9-9.5 5.4-2.17 3-3.29 7.5-2.79 12.8 1.01 10.7 8.87 23.3 20.25 27.9 127.96 51.8 279.26 42.6 397.56 27.5 13.5-1.6 22-55.6-1-48.7-4.8 3.7-9.3 6-16 8.6-13.2 5.1-30.1 8.8-49.6 11.5-39 5.4-88.6 6.6-138.2 3.8-49.7-2.7-99.2-9.4-138.2-20.1-19.39-5.3-36.21-11.6-49.31-19.3-4.9-2.9-9.37-6-13.21-9.4z"}}]})(props);
};
