:root:has(input.theme-controller[value=wireframe]:checked),[data-theme="wireframe"] {
color-scheme: light;
--color-base-100: oklch(100% 0 0);
--color-base-200: oklch(97% 0 0);
--color-base-300: oklch(94% 0 0);
--color-base-content: oklch(20% 0 0);
--color-primary: oklch(87% 0 0);
--color-primary-content: oklch(26% 0 0);
--color-secondary: oklch(87% 0 0);
--color-secondary-content: oklch(26% 0 0);
--color-accent: oklch(87% 0 0);
--color-accent-content: oklch(26% 0 0);
--color-neutral: oklch(87% 0 0);
--color-neutral-content: oklch(26% 0 0);
--color-info: oklch(44% 0.11 240.79);
--color-info-content: oklch(90% 0.058 230.902);
--color-success: oklch(43% 0.095 166.913);
--color-success-content: oklch(90% 0.093 164.15);
--color-warning: oklch(47% 0.137 46.201);
--color-warning-content: oklch(92% 0.12 95.746);
--color-error: oklch(44% 0.177 26.899);
--color-error-content: oklch(88% 0.062 18.334);
--radius-selector: 0rem;
--radius-field: 0.25rem;
--radius-box: 0.25rem;
--size-selector: 0.25rem;
--size-field: 0.25rem;
--border: 1px;
--depth: 0;
--noise: 0;
}
