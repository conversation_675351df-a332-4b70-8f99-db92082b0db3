// THIS FILE IS AUTO GENERATED
var GenIcon = require('../lib').GenIcon
module.exports.<PERSON><PERSON><PERSON>hansen = function <PERSON><PERSON><PERSON><PERSON>sen (props) {
  return GenIcon({"tag":"svg","attr":{"role":"img","viewBox":"0 0 24 24"},"child":[{"tag":"title","attr":{},"child":[]},{"tag":"path","attr":{"d":"M4.27 10.09h4.08V5.63h4.11L9.98 18.37H8.34V14H4.26v4.37H0V5.63h4.27zm15.45 3.82h-4.07v4.46h-4.1l2.47-12.74h1.64V10h4.08V5.63H24v12.74h-4.28Z"}}]})(props);
};
