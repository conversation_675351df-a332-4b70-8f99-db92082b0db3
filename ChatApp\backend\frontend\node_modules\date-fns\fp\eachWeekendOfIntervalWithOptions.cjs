"use strict";
exports.eachWeekendOfIntervalWithOptions = void 0;

var _index = require("../eachWeekendOfInterval.cjs");
var _index2 = require("./_lib/convertToFP.cjs"); // This file is generated automatically by `scripts/build/fp.ts`. Please, don't change it.

const eachWeekendOfIntervalWithOptions =
  (exports.eachWeekendOfIntervalWithOptions = (0, _index2.convertToFP)(
    _index.eachWeekendOfInterval,
    2,
  ));
