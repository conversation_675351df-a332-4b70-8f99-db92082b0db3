// THIS FILE IS AUTO GENERATED
var GenIcon = require('../lib').GenIcon
module.exports.GiPrimitiveNecklace = function GiPrimitiveNecklace (props) {
  return GenIcon({"tag":"svg","attr":{"viewBox":"0 0 512 512"},"child":[{"tag":"path","attr":{"d":"M273.9 28.1c-20.7.1-41 3.02-60.1 9.42C144.3 60.96 107.5 136.6 88.83 198.9c-3.22 10.8-6.18 22.5-8.36 34.5 1.97-.1 3.92-.1 5.83 0 4.29.3 8.3 1.4 11.95 3 2.05-11.2 4.85-22.1 7.85-32.3 17.8-59.9 53.4-129.28 113.5-149.52 16.8-5.68 35.3-8.23 54.4-8.12 23.5.12 47.8 4.28 71 11.55 0 .14 0 .28-.1.42-5.1.84-11.3 1.94-17.9 3.4-17.4 3.84-37.5 9.26-50.4 20.72-12.6 11.21-20.1 29.95-25.8 46.35-5.6 16.3-8.8 30.4-8.8 30.4l17.6 4s2.9-13.2 8.3-28.5c5.3-15.3 13.7-32.6 20.7-38.81 7.3-6.49 26-13 42.3-16.6 6.1-1.34 11.8-2.38 16.7-3.16 1.8 3.09 4.4 5.76 7.3 7.86 6.1 4.48 13.9 6.9 22.4 6.9 1.3 0 2.6-.1 3.8-.18.8 2.01 1.6 4.09 2.3 6.29 3.9 11.8 5.9 26.2 4.1 31.3v.1c-2 5.8-13.2 16.4-24.3 23.8-11.1 7.4-21.4 12.4-21.4 12.4l7.8 16.2s11.3-5.4 23.6-13.6c12.2-8.2 26.2-18.3 31.3-33 4.9-14.1.3-29.6-4-42.83-.8-2.23-1.5-4.36-2.2-6.38.4-.32.9-.65 1.4-1 .5-.37 1-.76 1.5-1.16 11.5 6.88 22.2 14.54 31.5 22.77 30.1 26.6 45.8 74.7 36.3 111.4-15.6 59.6-71.4 117-134.6 143.5.6.7 1.2 1.4 1.7 2.1 2.9 4 5.1 8.7 6.3 14.1 68.1-28.5 126.7-88.7 144-155.1 11.7-44.7-6.2-98-41.7-129.44-10.3-9.13-21.9-17.55-34.4-25.1.1-.92.2-1.86.2-2.82 0-3.87-1-7.48-2.8-10.69 1.3-.34 2.6-.63 4-.87 12-2.07 27.9.19 46.5 8.96l7.6-16.28c-15.7-7.45-30.6-11.21-44.1-11.41-4.5-.1-8.9.27-13.1.99-5.8 1.01-11.3 2.86-16.3 5.45-4.6-1.84-9.6-2.8-15-2.8-7.2 0-13.9 1.74-19.5 5-27.1-9.35-55.8-14.66-83.9-14.59zM83.05 251.3c-4.72.1-10.6 1.9-17.86 7-16.24 11.3-31.61 33.7-43.15 54 13.06-3.9 27.43-7.9 40.83-12.3 10.71-3.4 20.64-7 28.09-10.6 7.45-3.6 12.14-7.7 12.64-8.8h.1c2.1-3.8.5-16.2-6.27-23.2-3.37-3.5-7.4-5.7-12.42-6-.63-.1-1.28-.1-1.95-.1zm16.16 54.1c-.14.1-.28.1-.42.2-4.93 2.4-10.43 4.6-16.25 6.7 2.03 6.3 4.66 12.4 7.99 18.1 2.99-3.3 6.43-6.3 10.17-8.8 1.3-.9 2.7-1.8 4.1-2.6-2.3-4.3-4.1-8.8-5.59-13.6zM126 330.9c-4.3.1-9.9 1.9-15.1 5.5-6.8 4.7-11.93 12.1-12.74 17.1-.35 2.3.92 7 3.94 10.9 3 3.9 7.2 6.2 9.4 6.4 1.4.1 3-.2 4.6-.8 1.1-3.1 2.1-5.9 3-8.3 3.5-8.8 8.7-15.8 14.8-20.9-.1-1.4-.3-2.7-.7-3.9-1.1-3.7-2.7-5.2-3.4-5.5-1.1-.3-2.4-.5-3.8-.5zm39.9 16.9c-5.3 0-10.6 1.1-15.2 3.4-6.1 3-11.3 8.1-14.8 17.1-5 12.6-13.3 42.2-17.2 70.6-2 14.2-2.8 28.3-1.9 39.7.2 1.9.5 3.6.8 5.3 10.3-21.4 26-42.8 40.5-62.4 6.7-9.1 13.2-17.9 18.8-25.9 0-2.8.2-5.6.6-8.3 1.6-11.3 6.3-22.3 14.4-30.1-.8-.7-1.6-1.6-2.8-2.4-4.1-3.1-10.8-5.8-17.8-6.7-1.8-.2-3.6-.3-5.4-.3zM303.4 365c-3.5 0-6.8.6-9.2 1.6-3.6 1.5-4.9 3.2-5.3 4.9.1 0 0 1.9.2 4.1.3 2.6.8 6.1 1.4 10.2 1.3 8.2 3.2 18.8 5.2 30.1 2.3 12.6 4.7 25.6 6.8 38 10.1-19.9 20-41.9 22.7-60.8 1.3-9.7-.6-15.6-3.6-19.7s-7.6-6.7-12.8-7.8c-1.3-.3-2.6-.5-3.9-.6h-1.5zm-14.5 6.5v0zm-74.4-4.7c-4-.1-7.8 1.3-9.4 2.7-4.4 3.8-8.6 12.2-9.8 20.4-1.2 8.3.6 15.6 3.9 19 2.1 2.1 7.7 3.7 14 2.9 6.4-.9 12.6-4.2 15-7.1 2.8-3.6 4.4-10.4 3.5-17.6-.9-7.1-4.2-14-7.8-17.3h-.1c-1.5-1.5-5.3-2.9-9.3-3zm56.6 9.5c-7.5.8-15.2 1.2-22.9 1.3.6 2.4 1.1 4.9 1.4 7.3.4 3.7.5 7.5.2 11.2 8-.2 16-.8 23.8-1.7-.3-2-.6-4-.9-5.8-.7-4.3-1.2-8.1-1.5-11.2 0-.4-.1-.7-.1-1.1z"}}]})(props);
};
