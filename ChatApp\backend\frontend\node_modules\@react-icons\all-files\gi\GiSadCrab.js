// THIS FILE IS AUTO GENERATED
var GenIcon = require('../lib').GenIcon
module.exports.GiSadCrab = function GiSadCrab (props) {
  return GenIcon({"tag":"svg","attr":{"viewBox":"0 0 512 512"},"child":[{"tag":"path","attr":{"d":"M406.125 63.5c-2.77-.006-5.507.245-8.22.75-.77.143-1.53.317-2.28.5 31.724 13.294 45.583 80.38 32.938 128.75-27.244-19-52.83-48.975-69.125-85.594-3.936 17.603-3.088 38.91 3.562 61.063 14.06 46.84 49.064 81.22 81.438 82.03 2.413 7.61 3.62 15.377 3.062 22.094-.574 6.91-2.73 12.567-6.75 16.937-2.736 2.975-6.457 5.54-11.813 7.345-11.156-26.96-54.428-49.023-111.875-58.406l7.782-26.94c8.59-1.858 15.03-9.477 15.03-18.624 0-10.544-8.55-19.094-19.093-19.094s-19.092 8.55-19.092 19.094c0 5.097 2.025 9.708 5.28 13.125l-8.656 29.907c-13.79-1.51-28.198-2.343-43.062-2.343-12.62 0-24.942.588-36.813 1.687l-8.312-28.75c4.017-3.5 6.563-8.628 6.563-14.374 0-10.544-8.55-19.062-19.094-19.062s-19.094 8.518-19.094 19.062c0 8.508 5.56 15.72 13.25 18.188l7.875 27.187c-60.053 8.706-105.796 31.093-117.78 58.72-6.512.095-11.185-1.353-14.72-3.5-4.69-2.85-7.91-7.31-10-13.53-1.948-5.8-2.573-13.033-2.094-20.533 30.84 5.723 70.265-21.42 91.19-64.125 7.964-16.257 12.164-32.583 12.874-47.406-22.317 26.172-51.152 45.68-79.5 56.5-2.78-45.638 26.937-103.885 62.53-103.687.704.003 1.42.043 2.126.093-4.063-4.068-8.93-7.238-14.563-9.282-4.936-1.79-10.156-2.61-15.562-2.56-29.19.264-63.354 26.15-82.313 64.843-19.433 39.662-16.518 79.807 5.063 97.343-1.662 11.733-1.12 23.82 2.563 34.78 3.15 9.38 8.978 18.053 18 23.533 7.033 4.27 15.684 6.4 25.562 6.155 9.402 9.173 18.922 17.656 29.375 25.22C92.165 360.824 83.33 393.616 84 429.5l18.688-.375c-.648-34.763 8.662-63.02 25.937-78.03 5.72 3.274 11.766 6.282 18.22 9.03-10.153 19.133-14.64 43.44-14.157 69.375l18.687-.375c-.466-25.03 4.23-46.694 13.344-62.47 5.153 1.6 10.558 3.06 16.25 4.345-3.35 16.865-4.853 36.836-4.657 58.406L195 429.22c-.187-20.688 1.315-39.628 4.344-54.783 16.303 2.444 34.742 3.75 55.906 3.75 25.547 0 47.04-1.858 65.625-5.312.082.386.17.765.25 1.156 3.083 15.22 4.595 34.32 4.406 55.19l18.69.186c.197-21.862-1.338-42.087-4.783-59.094-.108-.537-.23-1.062-.343-1.593 5.266-1.43 10.307-2.996 15.094-4.72 10.175 15.957 15.462 38.65 14.968 65.125l18.656.375c.514-27.556-4.59-53.282-16.125-72.906 5.594-2.73 10.886-5.675 15.938-8.844 20.01 13.876 30.888 43.84 30.188 81.375l18.687.375c.718-38.435-9.458-73.327-32.844-93.03 7.506-5.86 14.638-12.214 21.688-19 12.43-2.21 22.325-7.357 29.156-14.783 7.275-7.907 10.788-17.947 11.625-28.03.755-9.095-.477-18.37-3.063-27.25 28.068-12.473 39.56-55.32 25.594-101.845C474.44 98.2 438.8 63.566 406.126 63.5zm-71.188 206.188l51.532 43.53-45.626-22.28-32.156 35.187-5.782 6.344-6.812-5.25-41.97-32.314-32.905 34.28-6.69 7.002-6.75-6.97-32.905-33.968-40.063 20.125 47.032-39.75 1.656 1.688 30.938 31.937 32-33.344 5.812-6.062 6.625 5.125 41.813 32.217 31.062-34 3.188-3.5z"}}]})(props);
};
