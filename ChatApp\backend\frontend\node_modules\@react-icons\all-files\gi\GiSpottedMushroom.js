// THIS FILE IS AUTO GENERATED
var GenIcon = require('../lib').GenIcon
module.exports.GiSpottedMushroom = function GiSpottedMushroom (props) {
  return GenIcon({"tag":"svg","attr":{"viewBox":"0 0 512 512"},"child":[{"tag":"path","attr":{"d":"M363.79 19.94c-1.113.01-2.235.035-3.364.072-18.073.592-38.343 4.687-60.492 12.228-6.6 2.247-13.355 4.803-20.25 7.643 5.777 4.047 10.723 9.305 14.445 15.752 15.826 27.41 2.008 63.22-28.12 80.61-30.128 17.393-68.052 11.454-83.88-15.956-4.142-7.176-6.234-14.927-6.554-22.772-8.494 5.725-17.074 11.703-25.748 17.988-18.724 13.568-27.91 28.318-30.978 44.058-3.068 15.74.144 33.086 8.783 51.29 17.278 36.407 56.368 75.03 101.75 104.75 40.37 26.437 85.746 45.896 124.04 52.255.244-39.607 32.617-71.84 72.282-71.84 8.225 0 16.13 1.406 23.506 3.957 8.588-43.02 13.357-80.58 14.78-112.948-6.94 5.09-15.19 8.19-24.097 8.19-13.612 0-25.708-7.203-33.91-17.765-8.2-10.562-12.95-24.593-12.95-39.896 0-15.304 4.75-29.336 12.95-39.898 8.202-10.562 20.298-17.766 33.91-17.766 4.228 0 8.31.696 12.173 1.97-9.818-26.852-25-43.73-44.324-53.073-12.53-6.06-27.268-8.996-43.953-8.852zM257.235 49.913c-19.538 9.386-40.043 20.892-61.25 34.28-2.863 9.317-2.21 18.888 2.33 26.753 9.81 16.986 35.56 22.27 58.35 9.116 22.79-13.155 31.085-38.097 21.277-55.08-4.332-7.504-11.782-12.71-20.707-15.068zm182.657 48.674c-7.108 0-13.77 3.612-19.147 10.537-5.376 6.925-9.023 17.053-9.023 28.436 0 11.38 3.647 21.507 9.023 28.432 5.377 6.925 12.04 10.54 19.147 10.54 7.107 0 13.767-3.615 19.144-10.54 2.05-2.64 3.848-5.75 5.29-9.215-.363-14.41-1.513-27.562-3.4-39.478-.398-2.52-.833-4.966-1.292-7.37-.198-.27-.395-.545-.598-.807-5.376-6.925-12.037-10.537-19.144-10.537zm-150.15 65.525c36.473 0 66.24 29.765 66.24 66.234 0 36.467-29.767 66.232-66.24 66.232-36.47 0-66.24-29.763-66.24-66.232 0-36.47 29.77-66.233 66.24-66.233zm0 18.69c-26.373 0-47.55 21.175-47.55 47.544 0 26.368 21.177 47.543 47.55 47.543 26.375 0 47.55-21.177 47.55-47.543 0-26.37-21.175-47.543-47.55-47.543zM162.907 287.153c-3.926 3.102-7.687 6.536-11.23 10.328 5.333 13.34 14.93 27.43 29.56 42.08l-13.222 13.206c-4.352-4.358-8.37-8.725-12.053-13.108-23.56 20.345-47.254 38.95-70.065 54.627-18.356 12.616-26.258 26.65-27.946 39.777-1.69 13.127 2.887 25.97 12.222 36.278 18.67 20.613 55.9 30.58 96.8 3.648 25.25-16.628 49.28-45.38 70.432-80.142-6.425-2.704-12.522-5.898-17.853-9.532l10.526-15.443c11.646 7.94 34.012 14.59 47.914 15.76 3.664-5.888 6.762-11.554 9.362-17-23.126-9.617-46.314-22.054-68.207-36.392-20.27-13.274-39.415-28.157-56.24-44.086zm262.8 27.56c-29.717 0-53.604 23.884-53.604 53.595 0 .545.025 1.084.04 1.625 18.367.917 34.128-1.867 45.963-8.405 12.857-7.1 21.756-18.28 25.887-36.845.47-2.106.92-4.186 1.37-6.268-6.08-2.383-12.706-3.703-19.657-3.703z","fillRule":"evenodd"}}]})(props);
};
