// THIS FILE IS AUTO GENERATED
import { GenIcon } from '../lib';
export function BsLayoutSidebarReverse (props) {
  return GenIcon({"tag":"svg","attr":{"viewBox":"0 0 16 16","fill":"currentColor"},"child":[{"tag":"path","attr":{"fillRule":"evenodd","d":"M14 2H2a1 1 0 00-1 1v10a1 1 0 001 1h12a1 1 0 001-1V3a1 1 0 00-1-1zM2 1a2 2 0 00-2 2v10a2 2 0 002 2h12a2 2 0 002-2V3a2 2 0 00-2-2H2z","clipRule":"evenodd"}},{"tag":"path","attr":{"fillRule":"evenodd","d":"M11 14V2h1v12h-1z","clipRule":"evenodd"}}]})(props);
};
