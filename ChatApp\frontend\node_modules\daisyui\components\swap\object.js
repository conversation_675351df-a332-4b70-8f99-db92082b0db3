export default {".swap":{"position":"relative","display":"inline-grid","cursor":"pointer","place-content":"center","vertical-align":"middle","webkit-user-select":"none","user-select":"none","input":{"appearance":"none","border":"none"},"> *":{"grid-column-start":"1","grid-row-start":"1","transition-property":"transform, rotate, opacity","transition-duration":"0.2s","transition-timing-function":"cubic-bezier(0, 0, 0.2, 1)"},".swap-on, .swap-indeterminate, input:indeterminate ~ .swap-on":{"opacity":"0%"},"input:is(:checked, :indeterminate)":{"& ~ .swap-off":{"opacity":"0%"}},"input:checked ~ .swap-on, input:indeterminate ~ .swap-indeterminate":{"opacity":"100%","backface-visibility":"visible"}},".swap-active":{".swap-off":{"opacity":"0%"},".swap-on":{"opacity":"100%"}},".swap-rotate":{".swap-on, input:indeterminate ~ .swap-on":{"rotate":"45deg"},"input:is(:checked, :indeterminate) ~ .swap-on, &.swap-active .swap-on":{"rotate":"0deg"},"input:is(:checked, :indeterminate) ~ .swap-off, &.swap-active .swap-off":{"rotate":"calc(45deg * -1)"}},".swap-flip":{"transform-style":"preserve-3d","perspective":"20rem",".swap-on, .swap-indeterminate, input:indeterminate ~ .swap-on":{"transform":"rotateY(180deg)","backface-visibility":"hidden"},"input:is(:checked, :indeterminate) ~ .swap-on, &.swap-active .swap-on":{"transform":"rotateY(0deg)"},"input:is(:checked, :indeterminate) ~ .swap-off, &.swap-active .swap-off":{"transform":"rotateY(-180deg)","backface-visibility":"hidden","opacity":"100%"}}};