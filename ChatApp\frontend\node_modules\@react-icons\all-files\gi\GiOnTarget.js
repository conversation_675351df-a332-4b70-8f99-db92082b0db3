// THIS FILE IS AUTO GENERATED
var GenIcon = require('../lib').GenIcon
module.exports.GiOnTarget = function GiOnTarget (props) {
  return GenIcon({"tag":"svg","attr":{"viewBox":"0 0 512 512"},"child":[{"tag":"path","attr":{"d":"M107.563 21.406c122.47 187.613 107.72 216.17-74.97 55.813 83.344 103.73 183.05 185.66 287.876 260.75-47.685 54.762-65.51 116.2-39.283 141.374 27.95 26.827 95.317 2.292 150.25-54.938 54.934-57.23 76.668-125.547 48.72-152.375-26.4-25.338-87.652-4.57-140.657 46.033C267.523 213.846 194.602 110.56 107.562 21.406zm324.906 266.781c9.865-.14 18.234 2.508 24.25 8.282 21.386 20.528 4.908 72.768-37.126 116.56-42.035 43.794-93.957 62.81-115.344 42.283-19.952-19.15-6.325-65.992 29.72-107.75 3.706 2.625 7.405 5.264 11.124 7.875-26.5 31.075-36.24 65.542-21.406 79.78 16.03 15.39 54.65 1.294 86.156-31.53 31.507-32.825 44-71.99 27.97-87.375-15.205-14.592-50.578-2.533-81.064 26.75-2.594-3.763-5.187-7.52-7.78-11.282 28.526-27.297 60.18-43.263 83.5-43.592zm-22.033 37.375c5.66-.08 10.457 1.44 13.907 4.75 12.267 11.774 2.827 41.758-21.28 66.875-24.11 25.118-53.89 36.024-66.158 24.25-11.245-10.793-3.876-36.93 16-60.562 5.976 4.173 11.97 8.327 17.97 12.47-9.746 11-14.027 22.66-9.563 27.124 4.904 4.903 18.483-.735 30.343-12.595 11.86-11.86 17.498-25.47 12.594-30.375-4.778-4.778-17.8.476-29.438 11.72-4.21-6.092-8.42-12.186-12.625-18.283 16.46-15.875 34.774-25.184 48.25-25.375z"}}]})(props);
};
