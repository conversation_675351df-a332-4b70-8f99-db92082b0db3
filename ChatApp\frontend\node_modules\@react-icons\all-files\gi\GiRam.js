// THIS FILE IS AUTO GENERATED
var GenIcon = require('../lib').GenIcon
module.exports.GiRam = function GiRam (props) {
  return GenIcon({"tag":"svg","attr":{"viewBox":"0 0 512 512"},"child":[{"tag":"path","attr":{"d":"M464.416 141.915c-8.517-6.749-15.266-14.865-20.008-24.704-7.76-16.366-21.048-26.791-32.372-38.706-12.072-15.64-18.256-30.462-34.519-40.135-3.803 5.674-7.902 13.973-15.266 11.74-9.753-5.159-4.799-13.382-1.723-20.8-6.323-4.6-21.495-9.001-27.51-7.927-.877 7.956-2.21 17.492-10.83 17.094-14.185-4.655-2.406-19.973-13.426-19.324-9.922.559-18.975 2.55-27.63 4.884 2.44 9.422 5.662 22.255-1.65 25.316-10.817 1.642-12.804-10.195-15.81-17.98-8.258 1.711-24.16 11.704-27.841 17.473 3.875 4.623 9.156 10.605 7.362 15.914-8.156 11.432-15.077 3.058-21-3.138-6.818 6.158-14.911 20.468-15.336 27.11 7.826 3.064 17.978 8.615 13.283 17.035-6.205 7.284-12.993 2.988-19.654.626-2.204 10.244-2.3 21.296-2.076 30.885.094 3.94 1.038 4.4 5.12 4.07 6.443-.894 13.776.838 14.818 7.21-1.37 9.747-9.11 9.99-15.761 11.95 2.642 10.64 6.983 19.831 12.835 28.69 5.97-3.503 11.821-7.844 17.13-1.144 5.238 6.666.212 11.432-4.578 15.56 3.21 4.637 10.523 10.37 18.097 14.393 8.473 4.646 9.273-7.882 18.168-4.671 6.23 3.178 5.688 8.296 5.686 13.697 8.14 1.557 15.833 2.737 23.713 2.572 12.553-14.671 22.366-3.779 27.039-5.167 13.777-6.153 27.855-13.305 34.896-25.353-18.352 6.382-38.157 9.23-54.362 5.922-17.483-3.75-32.442-11.891-42.966-26.756-11.985-16.882-14.533-35.722-10.263-55.6C254 94.443 277.215 75.188 303.075 72.63c19.789-1.958 44.38-.67 62.998 4.235 4.554 1.204 6.843 5.84 5.923 10.052-1.038 4.766-4.743 7.42-9.863 7.043-15.667-1.109-34.203-6.065-46.834 2.678-4.6 3.245-6.159 10.582-3.752 15.997 10.95 12.205 29.449-7.963 38.341-1.262 2.926 2.63 3.776 5.84 2.785 9.697-5.679 27.277-17.848 48.394-33.433 66.313 19.04 5.586 40.788-12.957 45.82-20.893 11.75-14.394 19.446-2.49 17.767 10.712-3.28 24.385-15.738 42.86-36.595 55.565-18.876 11.468-39.521 15.679-61.464 14.358-25.482-1.498-47.85-10.476-67.08-27.193-21.574-7.273-63.404-9.548-84.776-9.615-16.257.165-31.876 3.08-47.071 8.66-18.569 6.818-33.859 18.155-45.75 33.857-10.237 14.746-7.113 21.63-.12 27.615 6.985-7.617 11.267-22.239 19.703-25.019 4.176-1.309 8.116-1.474 11.373 1.959 4.173 5.63 1.431 10.3-.448 15.407-5.698 16.825-3.75 30.001-1.157 45.809 3.516 20.976.26 40.595-12.199 58.031-3.35 4.66-3.09 8.33-1.108 13.213 5.355 13.202 10.547 26.497 15.194 39.946 7.488 19.595 5.05 37.714 9.202 55.671 1.44 6.218 2.454 7.067 8.683 7.29 9.153-.204 17.698.701 25.67.025 5.003-.65 6.088-2.525 3.918-7.268-13.188-19.395-19.861-44.062-26.002-64.424-3.54-11.927-3.776-24.22-2.195-36.572.944-7.338 4.72-11.573 11.868-14.062 9.06-3.138 18.073-6.807 26.355-11.55 30.415-19.251 43.9-48.955 49.926-80.858 17.825 2.95-.123.01 17.625 2.595-3.57 19.787-12.353 40.712-19.607 56.851 9.386 7.861 44.208 19.666 95.96 10.665-.692-16.073-7.468-25.21-15.574-37.233 14.401-11.08.873-.882 14.534-10.912 16.96 24.617 20.294 46.521 21.612 72.21-.687 13.063-8.602 23.308-15.242 32.95-1.77 2.572-1.77 4.578.165 7.209 15.01 23.955 9.382 46.794 8.895 71.396-.141 6.029 1.416 7.976 7.339 8.282 6.724.319 13.496-.118 20.22-.755 3.586-.354 5.12-2.737 3.94-6.158-5.876-11.926-13.934-16.95-11.16-31.133 6.395-29.423 12.152-59.105 29.517-84.717 4.414-8.05 8.163-17.779 10.476-25.587 1.44-4.896 3.54-8.542 7.857-11.444 21.472-14.275 34.874-34.236 40.607-59.364 4.837-21.14 6.797-42.12 4.367-63.437-1.038-9.19-2.784-18.297-3.893-27.487-.85-6.937 1.13-11.901 7.855-10.757 18.333 3.197 36.878 3.304 55.353 4.023 6.23.248 11.066-2.064 15.219-6.406 9.258-13.105-.847-19.362-10.403-26.923zm-40.536-23.572c-3.421 5.521-10.665 7.196-16.186 3.775-5.498-3.433-7.196-10.688-3.75-16.21 3.42-5.52 23.404 6.914 19.936 12.435z"}}]})(props);
};
