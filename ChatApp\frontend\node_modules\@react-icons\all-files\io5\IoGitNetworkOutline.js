// THIS FILE IS AUTO GENERATED
var GenIcon = require('../lib').GenIcon
module.exports.IoGitNetworkOutline = function IoGitNetworkOutline (props) {
  return GenIcon({"tag":"svg","attr":{"viewBox":"0 0 512 512"},"child":[{"tag":"circle","attr":{"cx":"128","cy":"96","r":"48","fill":"none","strokeLinecap":"round","strokeLinejoin":"round","strokeWidth":"32"}},{"tag":"circle","attr":{"cx":"256","cy":"416","r":"48","fill":"none","strokeLinecap":"round","strokeLinejoin":"round","strokeWidth":"32"}},{"tag":"path","attr":{"fill":"none","strokeLinecap":"round","strokeLinejoin":"round","strokeWidth":"32","d":"M256 256v112"}},{"tag":"circle","attr":{"cx":"384","cy":"96","r":"48","fill":"none","strokeLinecap":"round","strokeLinejoin":"round","strokeWidth":"32"}},{"tag":"path","attr":{"fill":"none","strokeLinecap":"round","strokeLinejoin":"round","strokeWidth":"32","d":"M128 144c0 74.67 68.92 112 128 112m128-112c0 74.67-68.92 112-128 112"}}]})(props);
};
