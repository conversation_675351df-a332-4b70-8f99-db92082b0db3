// THIS FILE IS AUTO GENERATED
var GenIcon = require('../lib').GenIcon
module.exports.GiFrogPrince = function GiFrogPrince (props) {
  return GenIcon({"tag":"svg","attr":{"viewBox":"0 0 512 512"},"child":[{"tag":"path","attr":{"d":"M176 22v86c80 32 80 32 160 0V22l-48 48-32-48-32 48zm159.543 108.31c-4.742.195-9.564 1.488-14.205 4.167-38.934 22.48-89.77 21.953-127.79.002-6.09-3.516-12.284-4.611-18.144-3.893a46.38 46.38 0 0 1 9.438 28.09c0 23.15-17.037 42.83-39.176 45.096-12.775 14.92-21.554 31.806-24.387 49.982 44.73-23.79 90.948-35.572 137.065-35.508 46.15.064 92.197 11.986 136.56 35.62-2.69-18.15-11.217-35.044-23.795-49.92-.585.025-1.17.048-1.76.048-24.18 0-43.447-20.7-43.447-45.318 0-10.64 3.601-20.542 9.641-28.363zm-194.148 3.215l-.002.002c-12.67 0-23.278 10.85-23.278 25.15 0 14.298 10.608 25.147 23.278 25.147 12.67 0 23.277-10.85 23.277-25.148s-10.607-25.15-23.276-25.15zm227.953 0v.002c-12.67 0-23.276 10.85-23.276 25.15 0 14.298 10.607 25.147 23.276 25.147 12.67 0 23.277-10.85 23.277-25.148s-10.608-25.15-23.277-25.15zm68.396 93.375c-9.361-.13-18.96 1.646-28.676 4.237 6.926 19.457 8.569 40.725 2.694 62.656-4.26 15.896.934 37.475 11.7 54.758l4.69 7.53-7.02 5.43c-19.764 15.28-36.439 25.107-46.103 35.264-9.664 10.158-13.888 19.59-10.916 40.875.484 12.745 6.068 17.054 13.668 24.97 19.876-14.55 36.01-23.888 68.344-4.095-6.738-18.804 15.937-29.76 46.719-29.779-36.91-15.88-64.98-25.62-86.438-30.377 67.492-72.188 97.182-127.96 66-159.187-10.985-8.794-22.626-12.114-34.662-12.282zm-364.074.118c-12.65.034-24.768 3.161-34.076 12.166-31.182 31.227-1.492 87 66 159.187-21.456 4.756-49.528 14.497-86.438 30.375 30.782.02 53.457 10.978 46.719 29.781 32.332-19.792 48.469-10.455 68.344 4.094 6.233-8.55 16.31-14.826 17.724-24.969 2.972-21.283-1.25-30.717-10.914-40.875-9.664-10.157-26.34-19.982-46.105-35.263l-7.02-5.428 4.692-7.53c10.73-17.227 15.857-39.232 11.699-54.76-5.782-21.571-4.184-42.44 2.537-61.56-10.425-2.871-22-5.249-33.162-5.218zm184.67 9.539c-46.096-.065-92.3 12.827-137.572 38.845a87.269 87.269 0 0 0 2.494 13.31v.003c5.453 20.354.592 42.93-9.485 62.297 15.89 11.634 30.344 20.524 41.479 32.228 10.36 10.89 16.795 25.133 16.955 43.713-1.096 16.308-9.158 39.273-22.348 59.244 24.59-14.237 42.135-15.333 45.291 3.492 14.097-17.783 25.698-20.386 38.985-8.035-3.745-31.452-11.117-52.887-17.258-65.097-14.896-36.567-42.816-61.484-73.742-83.424l11.359-16.014c38.788 27.517 76.799 62.664 89.125 119.567 9.628.705 19.25.65 28.85-.16 12.362-56.81 50.333-91.919 89.084-119.409l11.361 16.016c-31.19 22.127-59.334 47.28-74.13 84.363-6.046 12.357-13.14 33.493-16.794 64.158 13.29-12.35 24.891-9.747 38.988 8.036 3.153-18.825 20.697-17.73 45.288-3.493-13.51-20.455-21.645-44.058-22.42-60.424.415-18.01 6.809-31.872 16.949-42.533 11.135-11.705 25.586-20.595 41.474-32.23-10.064-19.29-14.99-41.737-9.48-62.303a88.607 88.607 0 0 0 2.51-13.266c-44.85-25.79-90.85-38.818-136.961-38.884z"}}]})(props);
};
