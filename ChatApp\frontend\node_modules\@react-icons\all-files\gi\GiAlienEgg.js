// THIS FILE IS AUTO GENERATED
var GenIcon = require('../lib').GenIcon
module.exports.GiAlienEgg = function GiAlienEgg (props) {
  return GenIcon({"tag":"svg","attr":{"viewBox":"0 0 512 512"},"child":[{"tag":"path","attr":{"d":"M274.871 20.028a225.334 225.334 0 0 0-6.521.01c-17.341.275-34.254 2.546-49.655 6.576-17.578 4.601-35.218 14.092-49.67 27.424-14.452 13.332-26.31 31.319-26.476 52.942-.123 16.037 7.219 29.58 16.113 41.185 4.972 6.487 10.628 12.487 16.55 18.06 10.051-6.959 19.603-11.335 28.825-13.544a61.846 61.846 0 0 1 8.91-1.453c-.305-.242-.611-.476-.916-.72-9.5-7.6-18.642-16.212-24.797-24.241-6.154-8.03-8.716-15.036-8.685-19.012.061-8.027 5.214-17.833 14.887-26.756 9.672-8.923 23.269-16.152 34.375-19.058 24.444-6.398 56.865-7.347 84.664-.928 27.798 6.418 49.811 19.656 59.605 38.56.72 1.39 1.442 7.908-.623 13.887s-6.349 9.994-7.361 10.395c-1.69.668-6.944.839-14.782-2.164-3.146-1.206-6.562-2.845-10.082-4.76-.851 5.973-3.116 11.495-6.533 16.158-3.85 5.256-8.766 9.62-14.336 13.469 5.762 3.333 11.797 6.346 18.07 8.75 12.364 4.737 26.584 7.689 40.91 2.02 15.186-6.01 23.58-18.911 28.141-32.112 4.56-13.201 5.855-28.125-1.44-42.203-16.474-31.8-49.242-49.175-83.47-57.078-14.975-3.458-30.45-5.207-45.703-5.407zM257.05 81.587s24.553 39.17 44.658 53.605c7.452-4.335 13.262-8.86 16.473-13.242 3.851-5.256 5.127-9.711 2.379-17.56-16.124-11.882-63.51-22.803-63.51-22.803zm-38.361 87.414c-3.396-.043-6.846.334-10.456 1.186-12.37 2.963-27.641 11.887-46.377 31.71-11.542 17.963-20.928 38.946-27.359 63.026 8.36 4.198 17.765 7.779 28.988 10.477 4.63-26.96 26.004-49.3 42.578-65.153l10.891-10.418 4.006 14.53c28.64 103.892 48.5 151.37 94.988 200.69a357.476 357.476 0 0 0 7.366-10.849c14.743-22.573 31.412-52.64 46.867-82.56 13.238-25.63 25.59-51.143 35.236-71.924-8.59-26.487-22.684-48.163-33.654-59.023-10.97-10.861-10.974-10.305-12.719-9.983-1.745.322-5.79 2.523-10.543 6.184-9.506 7.321-21.842 20.052-39.678 20.914a40.489 40.489 0 0 1-6.146-.19l-17.41 27.541 74.273 26.567-38.562 25.82 15.449 25.053-15.322 9.447-15.096-24.478-18.422 12.333-10.014-14.955 42.272-28.304-61.803-22.104 26.82-42.428c-4.211-2.098-8.242-4.585-12.14-7.15-10.686-7.032-20.88-15.01-31.34-20.144-8.023-3.593-15.224-5.72-22.693-5.815zm-10.692 65.158c-14.44 15.7-27.57 34.601-27.45 52.06l.079 10.827-10.658-1.904c-30.685-5.482-51.459-17.143-68.62-29.75-9.608-7.06-18.157-14.238-26.925-21.014 17.484 51.591 37.58 114.2 78.24 145.531l9.658 7.444-9.959 7.037c-27.956 19.753-56.551 36.69-79.838 53.066-14.175 9.968-26.193 19.77-35.066 29.483 20.231-6.628 40.28-15.856 60.477-22.725 27.76-9.441 57.234-13.995 85.726 2.72l1.037.61.842.861c14.982 15.318 30.852 22.309 59.965 23.448 25.861 1.011 62.587-3.585 113.867-12.399-87.106-73.382-115.421-119.016-151.375-245.295zm218.961 11.748c-.224.494-.305.715-.533 1.217-10.26 22.524-24.632 52.541-40.25 82.777-15.618 30.236-32.446 60.65-47.79 84.141-3.277 5.018-6.476 9.696-9.605 14.026 12.81 12.479 27.506 25.413 44.555 39.654 46.18 13.565 74.513 15.14 101.209 13.433-1.298-6.848-3.822-13.693-9.133-20.81-7.603-10.188-21.2-21.162-45.935-31.63l-8.114-3.433 3.26-8.185c23.039-57.845 16.709-116.65 12.336-171.19z"}}]})(props);
};
