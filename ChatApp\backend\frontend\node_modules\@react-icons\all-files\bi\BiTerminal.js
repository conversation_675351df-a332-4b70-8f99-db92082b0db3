// THIS FILE IS AUTO GENERATED
var GenIcon = require('../lib').GenIcon
module.exports.BiTerminal = function BiTerminal (props) {
  return GenIcon({"tag":"svg","attr":{"viewBox":"0 0 24 24"},"child":[{"tag":"path","attr":{"d":"M12 14H18V16H12zM6.293 9.707L8.586 12 6.293 14.293 7.707 15.707 11.414 12 7.707 8.293z"}},{"tag":"path","attr":{"d":"M20,4H4C2.897,4,2,4.897,2,6v12c0,1.103,0.897,2,2,2h16c1.103,0,2-0.897,2-2V6C22,4.897,21.103,4,20,4z M4,18V6h16 l0.002,12H4z"}}]})(props);
};
