// THIS FILE IS AUTO GENERATED
var GenIcon = require('../lib').GenIcon
module.exports.GiBolas = function GiBolas (props) {
  return GenIcon({"tag":"svg","attr":{"viewBox":"0 0 512 512"},"child":[{"tag":"path","attr":{"d":"M213.43 21.54c-46.155.27-91.59 9.917-126.184 27.747 35.44-4.564 67.766-4.785 96.442-1.312-14.38 5.39-28.077 12.13-40.72 20.013 45.897-11.947 96.025-13.915 138.58-4.474-39.536 4.256-73.158 12.716-100.536 24.37 70.373-10.442 131.843 17.4 167.558 73.458 6.998-9.104 15.812-17.02 26.29-23.135 13.58-7.923 28.375-11.89 43.064-12.26 2.498-.063 4.993-.008 7.478.135-24.215-41.376-57.388-67.313-93.865-80.943-19.67-8.972-41.355-15.86-64.205-19.525l.004-.015c-17.675-2.837-35.846-4.167-53.906-4.06zM420.912 144.49c-.8-.005-1.6.004-2.4.026-11.645.32-23.394 3.506-34.233 9.83-20.347 11.87-32.83 32.164-35.635 54.002 2.672-.85 5.51-1.31 8.447-1.31 15.503 0 28.27 12.77 28.27 28.273 0 13.33-9.44 24.63-21.94 27.54 21.866 28.454 61.394 36.848 93.125 18.334 34.684-20.236 46.558-64.938 26.494-100.058-13.363-23.393-37.35-36.482-62.128-36.638zm-63.82 81.238c-1.88 0-3.6.52-5.057 1.41l-30.672 10.604 6.037 17.686 30.592-10.578c4.96-.436 8.682-4.44 8.682-9.54 0-5.402-4.18-9.582-9.582-9.582zm-53.39 18.122l-36.835 12.736 6.037 17.687 36.834-12.738-6.037-17.685zM91.005 259.385c-.8-.005-1.6.003-2.4.025-11.646.32-23.394 3.504-34.233 9.828-34.684 20.237-46.558 64.94-26.494 100.06 10.792 18.892 28.517 31.05 47.99 35.114 17.308 17.118 36.44 31.05 56.63 42.098 20.84 14.38 43.545 22.827 66.818 26.23-30.582-20.555-50.59-46.054-52.867-69.185 53.748 60.732 145.74 81.982 216.568 61.55 32.046-11.658 57.986-29.305 73.025-51.087-61.803 27.515-125.787 31.27-181.826 16.44 19.368-5.92 36.6-13.626 51.6-22.862-58.266 9.763-109.74-12.115-144.66-59.055 3.792-17.256 1.442-35.952-8.022-52.52-13.364-23.39-37.35-36.48-62.13-36.635zm158.197 3.31l-34.383 11.89 6.037 17.686 34.383-11.887-6.037-17.688zM197.16 280.69l-26.17 9.05c2.952 5.635 5.264 11.454 6.96 17.37l25.247-8.73-6.037-17.69z"}}]})(props);
};
