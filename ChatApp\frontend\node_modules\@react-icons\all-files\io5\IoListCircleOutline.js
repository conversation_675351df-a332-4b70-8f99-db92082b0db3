// THIS FILE IS AUTO GENERATED
var GenIcon = require('../lib').GenIcon
module.exports.IoListCircleOutline = function IoListCircleOutline (props) {
  return GenIcon({"tag":"svg","attr":{"viewBox":"0 0 512 512"},"child":[{"tag":"path","attr":{"fill":"none","strokeLinecap":"round","strokeLinejoin":"round","strokeWidth":"32","d":"M224 184h128m-128 72h128m-128 71h128"}},{"tag":"path","attr":{"fill":"none","strokeMiterlimit":"10","strokeWidth":"32","d":"M448 258c0-106-86-192-192-192S64 152 64 258s86 192 192 192 192-86 192-192z"}},{"tag":"circle","attr":{"cx":"168","cy":"184","r":"8","fill":"none","strokeLinecap":"round","strokeLinejoin":"round","strokeWidth":"32"}},{"tag":"circle","attr":{"cx":"168","cy":"257","r":"8","fill":"none","strokeLinecap":"round","strokeLinejoin":"round","strokeWidth":"32"}},{"tag":"circle","attr":{"cx":"168","cy":"328","r":"8","fill":"none","strokeLinecap":"round","strokeLinejoin":"round","strokeWidth":"32"}}]})(props);
};
