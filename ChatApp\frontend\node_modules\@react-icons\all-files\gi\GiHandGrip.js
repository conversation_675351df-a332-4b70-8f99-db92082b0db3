// THIS FILE IS AUTO GENERATED
var GenIcon = require('../lib').GenIcon
module.exports.GiHandGrip = function GiHandGrip (props) {
  return GenIcon({"tag":"svg","attr":{"viewBox":"0 0 512 512"},"child":[{"tag":"path","attr":{"d":"M256 24.97c-34.037 0-62.75 23.497-70.766 55.1l-.234-.053-32 144 17.572 3.906 21.188-95.346c9.768 18.037 26.917 31.52 47.347 36.393h-37.236l-3.902 18h134.23l9.102 40.953 17.57-3.906-19.707-88.684c-3.55 7.233-8.086 14.218-13.717 21.25l2.752 12.387h-55.306c20.366-4.858 37.473-18.27 47.257-36.223l.11.49c6.44-10.535 9.595-21.02 10.767-34.52l-4.156-18.7-.111.025C318.734 48.453 290.027 24.97 256 24.97zm0 18c30.482 0 55 24.517 55 55 0 30.482-24.518 55-55 55s-55-24.518-55-55c0-30.483 24.518-55 55-55zM139.785 241.005c4.99 5.864 9.887 12.434 13.496 19.482 3.77 7.364 6.449 15.528 4.631 24.178-1.817 8.65-7.555 15.044-13.97 20.268-6.416 5.223-13.91 9.413-21.128 12.859-4.81 2.297-9.304 4.12-13.462 5.717 3.164 3.135 6.544 6.615 10.023 10.654 5.22 6.06 10.393 12.91 14.164 20.274 3.77 7.363 6.45 15.525 4.633 24.175-1.818 8.65-7.556 15.047-13.97 20.27-6.416 5.223-13.91 9.413-21.128 12.86-5.394 2.575-10.414 4.573-14.916 6.263 8.492 9.54 17.355 19.241 25.131 29.547 9.035 11.973 16.588 24.97 18.408 39.478a44.316 44.316 0 0 0 4.633-.718c3.908-.838 6.89-2.164 8.662-3.497 1.771-1.332 2.44-2.371 2.799-4.084l47.498-226.064-55.504-11.662zm232.43 0l-55.504 11.662 47.498 226.064c.36 1.713 1.028 2.752 2.799 4.084 1.771 1.333 4.754 2.659 8.662 3.497 1.437.307 2.99.546 4.633.718 1.82-14.509 9.373-27.505 18.408-39.478 7.776-10.306 16.64-20.007 25.13-29.547-4.501-1.69-9.521-3.688-14.915-6.264-7.218-3.446-14.712-7.636-21.127-12.86-6.415-5.222-12.153-11.617-13.97-20.267-1.818-8.65.861-16.814 4.632-24.177 3.77-7.364 8.944-14.214 14.164-20.274 3.48-4.039 6.86-7.52 10.023-10.654-4.158-1.597-8.653-3.42-13.462-5.717-7.218-3.446-14.712-7.636-21.127-12.86-6.416-5.223-12.154-11.617-13.971-20.267-1.818-8.65.86-16.814 4.63-24.178 3.61-7.048 8.506-13.618 13.497-19.482zM117.53 243.41l-13.187 62.766c3.358-1.324 7-2.856 10.715-4.63 6.412-3.06 12.838-6.764 17.517-10.573 4.68-3.81 7.212-7.588 7.72-10.01.51-2.422-.288-6.9-3.038-12.272-2.75-5.37-7.142-11.347-11.78-16.73-2.686-3.119-5.404-5.987-7.947-8.55zm276.938 0c-2.543 2.564-5.26 5.432-7.948 8.55-4.637 5.384-9.029 11.36-11.779 16.731-2.75 5.371-3.548 9.85-3.039 12.272.509 2.422 3.042 6.2 7.72 10.01 4.68 3.81 11.106 7.512 17.518 10.574 3.715 1.773 7.357 3.305 10.715 4.629L394.47 243.41zM97.79 337.361l-13.187 62.765c3.358-1.323 7-2.855 10.714-4.629 6.412-3.062 12.839-6.764 17.518-10.574 4.68-3.81 7.212-7.59 7.72-10.012.51-2.422-.288-6.899-3.038-12.27-2.75-5.37-7.142-11.346-11.78-16.73-2.686-3.119-5.405-5.987-7.947-8.55zm316.418 0c-2.542 2.563-5.26 5.431-7.947 8.55-4.638 5.384-9.03 11.36-11.78 16.73-2.75 5.371-3.548 9.848-3.039 12.27.51 2.422 3.042 6.202 7.721 10.012 4.68 3.81 11.106 7.512 17.518 10.574 3.714 1.774 7.356 3.306 10.714 4.629L414.21 337.36zM77.613 433.39l-6.156 29.301c-.36 1.713-.167 2.933.918 4.866 1.085 1.932 3.281 4.346 6.521 6.685 6.481 4.68 16.878 8.885 27.643 11.147 2.266.476 4.549.847 6.818 1.152-1.861-9.02-7.05-18.358-14.437-28.148-6.156-8.16-13.69-16.476-21.307-25.002zm356.774 0c-7.618 8.527-15.15 16.844-21.307 25.003-7.387 9.79-12.576 19.129-14.437 28.148a93.946 93.946 0 0 0 6.818-1.152c10.765-2.262 21.162-6.468 27.643-11.147 3.24-2.34 5.436-4.753 6.521-6.685 1.085-1.933 1.278-3.153.918-4.866l-6.156-29.3z"}}]})(props);
};
