// THIS FILE IS AUTO GENERATED
import { GenIcon } from '../lib';
export function GiThermometerCold (props) {
  return GenIcon({"tag":"svg","attr":{"viewBox":"0 0 512 512"},"child":[{"tag":"path","attr":{"d":"M160 36.5c-11.688 0-23 6.674-23 25.5v25h23v18h-23v14h7v18h-7v14h23v18h-23v14h7v18h-7v14h23v18h-23v14h7v18h-7v14h23v18h-23v14h7v18h-7v14h14v-23h18v81.313A32 32 0 0 1 192 432a32 32 0 0 1-32 32 32 32 0 0 1-32-32 32 32 0 0 1 23-30.688V361h-14v21h.01c-18.926 8.673-32.01 27.74-32.01 50 0 30.482 24.518 55 55 55s55-24.518 55-55c0-22.26-13.084-41.327-32.01-50h.01V62c0-18.826-11.313-25.5-23-25.5zm87 16.273v66.73l-46-23v20.124l46 23v50.246l-13.54 27.084-30.228-1.814-2.232-1.29v27.126l9.918 15.02L201 271.02v27.126l2.232-1.29 30.227-1.813L247 322.127v50.246l-29.51 14.754c3.703 4.73 6.834 9.922 9.293 15.478L247 392.498v66.73h18v-66.73l68.266 34.133 8.05-16.1L265 372.374v-50.246l13.54-27.084 30.228 1.814 43.513 25.123-5.11 85.172 17.97 1.078 4.57-76.187 57.79 33.365 9-15.588-57.79-33.365 63.694-42.053-9.918-15.02-71.205 47.01-43.514-25.124L301.082 256l16.684-25.268 43.515-25.125 71.206 47.012 9.918-15.022-63.693-42.053 57.79-33.365-9-15.588-57.79 33.365-4.57-76.187-17.97 1.078 5.11 85.172-43.512 25.123-30.227 1.814L265 189.873v-50.246l76.316-38.158-8.05-16.1L265 119.5v-66.73h-18zm-162.5 93.82l-9 15.587 43.5 25.115v-20.783l-34.5-19.92zm34.5 58.386l-49.404 32.618 9.918 15.02L119 226.55v-21.57zm-39.486 54.4l-9.918 15.022L119 307.022v-21.57l-39.486-26.07zM119 324.706L75.5 349.82l9 15.588 34.5-19.92v-20.783zm43.11 83.943c-25.186 0-25.186 26.678-25.186 26.678s7.05-10.4 11.31-14.904c4.195-4.435 13.877-11.774 13.877-11.774z"}}]})(props);
};
