// THIS FILE IS AUTO GENERATED
var GenIcon = require('../lib').GenIcon
module.exports.BiPalette = function BiPalette (props) {
  return GenIcon({"tag":"svg","attr":{"viewBox":"0 0 24 24"},"child":[{"tag":"path","attr":{"d":"M13.4,2.096c-3.358-0.458-6.729,0.804-8.937,3.331C2.565,7.603,1.702,10.509,2.096,13.4 c0.53,3.894,3.458,7.207,7.285,8.246C10.245,21.881,11.126,22,11.999,22l0.142-0.001c1.032-0.016,1.973-0.549,2.516-1.426 c0.541-0.876,0.599-1.953,0.153-2.879l-0.199-0.416c-0.418-0.868-0.121-1.57,0.094-1.912c0.539-0.858,1.627-1.21,2.576-0.755 l0.412,0.197c0.412,0.198,0.85,0.299,1.301,0.299c1.633,0,2.981-1.331,3.006-2.967c0.014-0.92-0.105-1.849-0.353-2.76 C20.607,5.554,17.294,2.626,13.4,2.096z M18.558,13.005l-0.412-0.197c-1.828-0.878-4.07-0.198-5.135,1.494 c-0.738,1.176-0.813,2.576-0.204,3.842l0.199,0.416c0.151,0.314,0.133,0.664-0.051,0.961c-0.185,0.299-0.492,0.474-0.844,0.479 L11.999,20c-0.696,0-1.401-0.096-2.095-0.283c-3.063-0.831-5.403-3.479-5.826-6.586C3.757,10.776,4.43,8.508,5.971,6.742 C7.491,5,9.689,4,12,4c0.374,0,0.754,0.026,1.131,0.078c3.107,0.423,5.755,2.764,6.586,5.826c0.198,0.73,0.293,1.474,0.282,2.207 C19.987,12.918,19.154,13.294,18.558,13.005z"}},{"tag":"circle","attr":{"cx":"7.5","cy":"14.5","r":"1.5"}},{"tag":"circle","attr":{"cx":"7.5","cy":"10.5","r":"1.5"}},{"tag":"circle","attr":{"cx":"10.5","cy":"7.5","r":"1.5"}},{"tag":"circle","attr":{"cx":"14.5","cy":"7.5","r":"1.5"}}]})(props);
};
