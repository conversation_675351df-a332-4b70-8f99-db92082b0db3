// THIS FILE IS AUTO GENERATED
import { GenIcon } from '../lib';
export function BiConversation (props) {
  return GenIcon({"tag":"svg","attr":{"viewBox":"0 0 24 24"},"child":[{"tag":"path","attr":{"d":"M16,14h0.5c0.827,0,1.5-0.673,1.5-1.5v-9C18,2.673,17.327,2,16.5,2h-13C2.673,2,2,2.673,2,3.5V13v1v4l5.333-4H13H16z M6.667,12L4,14v-1V4h12v8h-3H6.667z"}},{"tag":"path","attr":{"d":"M20.5,8H20v2v2.586v1.415c0,1.1-0.893,1.993-1.99,1.999h-0.677H16h-5h-1H8v0.5C8,17.327,8.673,18,9.5,18H10h1h5h0.667 L22,22v-4v-1v-1.999v-2.002V9.5C22,8.673,21.327,8,20.5,8z"}}]})(props);
};
