// THIS FILE IS AUTO GENERATED
var GenIcon = require('../lib').GenIcon
module.exports.GiGauntlet = function GiGauntlet (props) {
  return GenIcon({"tag":"svg","attr":{"viewBox":"0 0 512 512"},"child":[{"tag":"path","attr":{"d":"M123.153 24.602c-11.349.764-48.792 83.005-63.545 132.174-8.046 26.818 2.983 74.734 41.723 106.45 8.813-1.502 16.946-3.047 24.434-4.626-22.473-24.22-39.048-50.488-47.772-82.059l-1.021-3.699 1.963-3.299c26.135-43.925 37.681-68.548 50.85-112.24l3.849-12.773 10.402 8.351c14.624 11.743 23.72 18.084 32.098 21.809-14.428-22.99-31.841-41.36-52.46-50.06a2.164 2.164 0 0 0-.52-.028zm19.791 50.203c-11.724 36.176-24.141 62.49-46.508 100.379 9.004 29.978 25.746 54.616 49.733 78.65 18.744-4.857 32.588-9.929 43.383-14.978 16.875-7.894 26.514-15.73 36.92-23.701-6.532-34.91-18.944-80.14-38.018-118.375-14.754-2.769-27.196-8.373-45.51-21.975zm90.094 158.008c-9.156 7.022-19.796 14.833-35.861 22.347-17.365 8.123-40.947 15.887-76.29 22.793 35.349 28.759 64.905 62.213 112.643 82.157 2.79-15.613 10.509-29.532 20.61-39.782 9.547-9.688 21.609-16.383 34.252-16.82 2.202-5.202 5.378-10.557 10.593-14.93 6.41-5.374 15.626-8.323 26.932-8.156a35.046 35.046 0 0 1 4.807-5.424c-7.384-1.603-16.19-3.168-27.145-5.586zm120.275 50.299c-.04.009-.084.012-.125.021-11.264 3.729-12.514 6.776-16.947 16.078-8.484-1.005-19.247-2.081-25.69 3.16-6.017 6.313-7.279 12.696-9.685 20.715-14.012-3.276-25.77 1.842-33.904 9.877-8.032 8.15-14.35 20.016-16.082 32.65 5.926 2.628 12.109 4.214 18.139 5.727 12.69-15.498 32.27-33.513 50.66-36.851 12.995-13.444 28.669-18.08 41.183-19.891a60.105 60.105 0 0 1 11.764-8.4c-6.632-11.786-12.405-18.622-19.313-23.086zm-232.011 18.882c-1.967 12.934-7.997 24.573-.64 46.305 32.36 70.655 41.042 23.73 93.735 22.953-38.944-18.938-66.126-45.737-93.095-69.258zm267.765 17.102c-4.87.972-9.343 3.536-13.062 6.28 9.197 12.249 16.987 27.313 23.28 43.81 7.916-2.052 14.75-4.612 21.103-7.506-6.473-26.762-18.951-37.976-31.32-42.584zm-29.935 13.906c-7.676 1.509-15.588 4.36-22.774 10.35 8.367 8.844 20.976 24.773 26.053 43.283 7.632-1.268 15.91-3.698 21.006-8.576-6.608-17.858-15.058-33.73-24.285-45.057zm-38.139 19.668c-14.313 5.357-25.257 14.981-34.674 25.938l19.383 26.324c13.696-1.49 26.639-6.254 39.39-13.299-4.69-15.098-18.62-32.912-24.447-38.607zm108.848 24.365c-6.679 3.099-13.973 5.985-22.237 8.375 6.323 10.32 11.618 21.138 15.602 32.543 7.703-3.07 14.902-6.372 21.264-10.082-3.964-11.28-8.68-22.055-14.63-30.836zM164.687 408.39l49.638 43.377c11.274-13.516 27.044-23.94 42.492-33.942l-22.02-25.32c-24.334-8.01-52.756 2.355-70.11 15.885zM391.9 394.153c-6.866 5.056-14.143 7.727-21.795 9.377 5.371 9.31 10.375 19.502 14.354 29.755 7.946-2.139 15.451-4.432 22.344-7.775-3.648-10.897-8.68-21.303-14.903-31.357zm-39.88 14.105c-11.759 6.313-24.191 11.162-37.42 13.545l13.316 27.447c14.663-2.177 28.099-3.684 39.666-9.746-4.195-10.706-9.777-21.706-15.563-31.246zm98.017 17.156c-6.627 3.604-13.647 6.703-20.846 9.534 2.83 7.167 5.28 14.119 7.21 20.757 10.397-1.435 20.263-5.912 18.179-13.869-1.423-5.296-2.923-10.83-4.543-16.422zM268.82 431.54c-16.062 10.564-31.09 20.975-40.728 32.256l1.392 1.217c16.648 14.548 36.256 20.475 44.95 9.13 8.522-11.12 10.536-24.116-5.614-42.603zm144.01 10.957c-7.166 3.334-14.4 5.634-21.465 7.58 3.051 6.991 5.842 13.849 8.244 20.48 9.2-.457 17.732-4.025 19.33-10.442-1.622-5.49-3.688-11.409-6.109-17.618zm-38.437 13.69c-12.724 6.396-25.866 8.467-38.184 10.154l6 12.367c10.038 10.705 32.656 11.336 42.21 2.944-2.654-7.907-6.1-16.516-10.026-25.465z"}}]})(props);
};
