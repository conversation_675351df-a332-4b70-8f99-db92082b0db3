// THIS FILE IS AUTO GENERATED
var GenIcon = require('../lib').GenIcon
module.exports.GiTreeDoor = function GiTreeDoor (props) {
  return GenIcon({"tag":"svg","attr":{"viewBox":"0 0 512 512"},"child":[{"tag":"path","attr":{"d":"M197 39.09c-4.1.06-8 .47-11.6 1.25-11.8 2.52-25.8 9.73-35.8 19.27-9.9 9.54-15.5 20.67-14.2 32.01l1.8 14.48-13.8-4.9c-28.53-10.39-50.02.1-66.12 20.3-16.11 20.3-24.77 50.8-22.34 76.2 1.77 18.6 14.69 37.1 32.82 51.5 18.12 14.3 41.14 24.2 59.94 26.4 16.6 1.9 38.4-9.4 55.4-22.4 17.1-13 29.2-26.6 29.2-26.6l4.7-5.3 6.2 3.4s11.2 5.9 25 12.2c13.8 6.4 30.7 13 39 14.3 6.8 1.1 21.2-2.7 32.8-7.6 11.6-5 20.7-10.3 20.7-10.3l8-4.7 4.4 8.2s8 14.8 21.2 29.4c13.2 14.7 31 28.1 48.8 27.9 17.6-.2 32.4-13.8 43.8-34.5 11.4-20.7 18.4-47.6 20.1-68.4 2.7-31.3-11.7-72-37-86.7-6.1-3.5-23.5-3-38.5.3-15 3.4-27.6 8.3-27.6 8.3l-17.4 6.7L372 102s2.2-7.14.3-15.91c-1.9-8.76-6.8-18.62-25.6-25.37-26.2-9.46-39.6-4.66-47.6 1.16-8 5.83-10.3 13.15-10.3 13.15l-6.2 19.09-10.2-17.33c-6.5-11.24-20.8-22.55-37.3-29.63-12.4-5.31-25.9-8.25-38.1-8.07zm23.8 204.61c-2.5 2.6-6.1 6.2-10.8 10.5.9 7.7.6 15.7-2 24.7-1.2 3.9-10.2.1-21.3-6-9.9 6.9-20.9 13.3-32.7 17.2 10.2 5.3 21.6 10.3 35.2 15.7-4.7 99.7 2.3 162.4-77.2 190.2h106V384c0-32 76-32 76 0v112h122c-15-11.1-38.9-17.8-77.4-24.1-30.3-93.6-17.6-128.8 39.8-177.1-6.6-5.1-12.4-10.9-17.5-16.5-2.2-2.4-4.1-4.8-5.9-7-9.2 9.7-19.4 16.4-29 13.9-7.9-2.1-12.8-9.4-15.7-18.9-8.5 2.5-17.3 4-25.9 2.7-8.1-1.2-17.5-4.4-26.8-8.3-1.3 7.2-4.8 14.3-11.8 21.8-4.3 4.6-10.6-14.7-16.9-34.8-3.1-1.5-5.8-2.9-8.1-4zM273 424.4a9 9 0 0 0-9 9 9 9 0 0 0 9 9 9 9 0 0 0 9-9 9 9 0 0 0-9-9z"}}]})(props);
};
