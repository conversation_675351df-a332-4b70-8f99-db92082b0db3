// THIS FILE IS AUTO GENERATED
var GenIcon = require('../lib').GenIcon
module.exports.GiOrbitalRays = function GiOrbitalRays (props) {
  return GenIcon({"tag":"svg","attr":{"viewBox":"0 0 512 512"},"child":[{"tag":"path","attr":{"d":"M312.598 17.617c-11.352.495-22.753 1.162-34.504 3.447-7.382 1.438-14.92 3.48-22.397 5.63h-.002l.582 102.386c-4.14 1.2-8.277 2.578-12.398 4.123 14.078 1.1 27.395 4.973 39.405 11.068 2.518-.268 5.01-.452 7.47-.54.864-.033 1.724-.053 2.58-.062 12.852-.14 24.76 2.234 34.995 7.217 21.838 10.63 33.073 30.607 34.164 56.81 1.09 26.204-8.932 58.226-31.22 88.748-22.29 30.523-52.376 53.464-82.374 65.647-29.997 12.182-59.435 13.43-81.273 2.8-21.838-10.63-33.07-30.608-34.162-56.812-.404-9.69.718-20.176 3.412-31.088-4.622-11.87-7.164-24.77-7.164-38.248 0-.232.008-.463.01-.695-5.353 10.91-9.423 21.863-12.19 32.66l-93.67-3.203c-3.03 11.91-5.398 23.594-6.728 35.37-.858 7.613-1.447 15.52-1.567 23.003l97.918-30.83c-.345 4.694-.433 9.327-.244 13.875.448 10.748 2.51 21.08 6.133 30.592l-95.55 46.945h-.003c2.456 8.035 5.49 16.144 9.094 23.692 3.755 7.87 7.882 15.017 12.637 21.844l83.783-73.775c6.932 9.733 16.053 17.892 27.305 23.784L84.57 470.47c13.744 9.173 29.25 15.45 45.707 20.024l45.454-101.21c13.947 3.576 28.93 3.992 44.2 1.74l-29.576 106.328c18.565-.79 37.977-3.862 57.455-9.463l-6.248-101.753c5-1.514 9.993-3.28 14.954-5.293 10.273-4.172 20.45-9.406 30.332-15.606l25.332 95.213c9.426-5.314 18.51-11.05 27.785-17.528 9.086-6.347 18.08-13.29 26.44-20.348l-60.594-70.726c13.438-10.623 26-23.116 37.09-37.24l75.883 53.853c13.686-17.525 25.127-36.118 34.947-55.083l-97.558-17.61c9.69-15.447 16.76-31.22 21.176-46.722l101.742-1.578c3.09-12.056 5.713-24.31 7.058-36.23.858-7.61 1.67-15.05 1.79-22.532l-106.094 38.4c.848-6.886 1.147-13.656.873-20.25-.345-8.29-1.656-16.326-3.895-23.935l101.086-54.33c-2.51-8.397-5.562-16.293-9.316-24.162-3.603-7.55-7.888-14.786-12.412-21.375h-.002l-86.9 81.843c-7.2-12.837-17.724-23.557-31.462-30.873l79.122-89.908c-5.416-3.616-11.37-6.953-17.247-9.86-9.134-4.516-19.145-7.464-29.235-10.247l-50.832 102.857c-10.007-2.73-20.56-3.822-31.39-3.437-4.118.146-8.275.507-12.458 1.068l34.823-106.88zm-121.827 36.9c-9.23 5.23-18.697 11.185-27.784 17.532-9.086 6.347-17.527 12.9-25.886 19.96l42.332 57.045c8.634-5.428 18.102-9.644 28.162-12.41L190.77 54.52zM84.72 146.126c-5.277 6.76-10.507 13.635-15.226 20.652l-.553.387c-7.44 11.105-13.74 22.86-19.72 34.432l81.932 19.754c1.723-10.357 4.954-20.212 9.463-29.33L84.72 146.125zm150.856 9.193c-46.207 0-83.424 37.217-83.424 83.424 0 46.207 37.217 83.424 83.424 83.424 46.207 0 83.424-37.217 83.424-83.424 0-46.207-37.217-83.424-83.424-83.424z"}}]})(props);
};
