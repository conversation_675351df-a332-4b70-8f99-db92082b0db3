// THIS FILE IS AUTO GENERATED
var GenIcon = require('../lib').GenIcon
module.exports.GiTireTracks = function GiTireTracks (props) {
  return GenIcon({"tag":"svg","attr":{"viewBox":"0 0 512 512"},"child":[{"tag":"path","attr":{"d":"M484.148 41.3l-14.12 11.165L494 82.78V53.76zm-30.09 10.893l-14.12 11.164 31.119 39.354-16.149.414.446-.352-31.383-39.687L409.85 74.25l31.119 39.354-16.15.414.447-.352-31.385-39.687-14.121 11.164 31.119 39.353-16.149.414.446-.351-31.383-39.688-7.06 5.582-7.061 5.582 31.119 39.354-16.15.414.445-.352-31.383-39.687-14.121 11.164 31.12 39.353-16.15.414.446-.351-31.385-39.688-14.12 11.164 31.118 39.354-16.148.414.445-.352-31.383-39.687-14.12 11.164 31.118 39.353-16.15.414.447-.351-31.384-39.688-14.122 11.164 31.12 39.354-16.149.414.445-.352-31.382-39.687-14.121 11.164 31.119 39.354-16.15.414.445-.352-31.383-39.687-14.121 11.164 31.119 39.353-16.149.414.446-.351-31.385-39.688-14.121 11.164 31.385 39.688 13.117-10.371 1.283 50.152 17.996-.461-1.295-50.58-.566.016 12.672-10.02 1.283 50.152 17.996-.46-1.295-50.58-.568.015 12.672-10.02 1.285 50.153 17.994-.461-1.293-50.58-.568.013 12.671-10.017 1.284 50.152 17.996-.46-1.295-50.58-.569.013 12.674-10.018 1.283 50.153 17.997-.461-1.295-50.58-.569.013 12.672-10.017 1.283 50.152 17.996-.46-1.294-50.58-.567.013 12.672-10.02 1.283 50.155 17.996-.461-1.295-50.58-.568.013 12.672-10.02 1.285 50.155 17.994-.46-1.293-50.581-.568.014 12.672-10.02 1.283 50.154 17.996-.46-1.295-50.58-.568.013 12.674-10.02 1.283 50.155 17.996-.461-1.295-50.58-.568.014 12.671-10.02 1.284 50.154 8.277-.213V92.002l-9.002.23.445-.351zM170.433 211.971l-17.428.447.445-.352-31.383-39.687-14.119 11.164 31.12 39.352-16.153.416.445-.352-31.382-39.688-14.12 11.165 31.12 39.351-16.15.416.444-.351-31.382-39.688-14.12 11.164 31.12 39.352-16.153.416.446-.352-31.383-39.687L18 215.967v.658l31.063 39.283 13.119-10.373 1.283 50.154 17.994-.46-1.295-50.58-.566.013 12.672-10.02 1.285 50.155 17.992-.461-1.293-50.58-.567.014 12.672-10.02 1.284 50.154 17.994-.46-1.295-50.58-.567.013 12.674-10.02 1.283 50.155 17.995-.461zm-121.37 43.937l-17.425.446.445-.352L18 238.195v28.942l13.08-10.342 1.283 50.154 17.994-.463zM494 279.061l-13.95 11.029L494 307.729zm-29.918 10.755l-14.121 11.166 31.12 39.352-16.15.416.446-.354-31.383-39.687c-4.708 3.721-9.414 7.444-14.121 11.166l31.12 39.352-16.151.416.445-.354-31.383-39.687-14.12 11.166 31.118 39.351-16.148.416.445-.353-31.383-39.688-14.12 11.166 31.118 39.352-16.15.416.445-.354-31.382-39.687-14.122 11.166 31.12 39.351-16.15.416.446-.353-31.384-39.688-14.121 11.166 31.119 39.352-16.149.416.446-.354-31.383-39.687-14.121 11.166 31.119 39.351-16.15.416.445-.353-31.383-39.688-14.121 11.166 31.119 39.352-16.148.416.445-.353-31.383-39.688-14.121 11.166 31.383 39.688 13.117-10.374 1.283 50.155 17.996-.461-1.293-50.58-.568.013 12.672-10.02 1.283 50.155 17.996-.463-1.295-50.578-.568.014 12.674-10.02 1.283 50.155 17.994-.463-1.293-50.578-.568.013 12.672-10.02 1.283 50.155 17.996-.463-1.295-50.578-.568.014 12.673-10.02 1.284 50.155 17.996-.463-1.295-50.578-.569.013 12.672-10.02 1.284 50.155 17.996-.463-1.293-50.578-.569.014 12.672-10.02 1.283 50.154 17.996-.462-1.294-50.58-.569.015 12.674-10.02 1.283 50.155 17.994-.463-1.293-50.58-.568.016L494 330.662v-3.014zm-223.45 137.995l-17.427.445.445-.352-31.382-39.687-14.12 11.164 31.12 39.353-16.153.414.447-.351-31.384-39.688-14.12 11.164 31.12 39.354-16.15.414.445-.352-31.383-39.687-14.12 11.164 31.12 39.354-16.153.414.446-.352L102 420.895l-14.12 11.164 31.384 39.687 13.119-10.371.834 32.625h18.006l-.858-33.512-.566.014 12.672-10.02L163.586 494h18.004l-1.135-44.406-.566.015 12.672-10.02 1.283 50.153 17.994-.46-1.295-50.58-.566.015 12.673-10.02 1.284 50.153 17.994-.461zm-121.368 43.935l-17.426.447.445-.351-31.385-39.688-14.119 11.164 31.385 39.688 13.117-10.373.547 21.367h18.006zm-31.1 11.26l-17.428.447.446-.353-31.383-39.688-14.12 11.164L56.8 493.93c.709-.018-.29.039.709-.018l12.672-10.02.26 10.108h18.005zM18 464.789v29.02l.15.191h22.932l.01-.008z"}}]})(props);
};
