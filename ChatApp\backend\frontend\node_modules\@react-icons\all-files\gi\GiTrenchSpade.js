// THIS FILE IS AUTO GENERATED
var GenIcon = require('../lib').GenIcon
module.exports.GiTrenchSpade = function GiTrenchSpade (props) {
  return GenIcon({"tag":"svg","attr":{"viewBox":"0 0 512 512"},"child":[{"tag":"path","attr":{"d":"M388.951 25.986a57.09 57.09 0 0 1 18.193 2.98l43.24 14.542a12.871 12.871 0 0 1 8.109 8.108l14.53 43.208a57.136 57.136 0 0 1-13.757 58.602l-81.652 81.651a11.507 11.507 0 0 1-16.271 0l-3.9-3.9a18.193 18.193 0 0 0 2.025-2.364 454.475 454.475 0 0 0 27.107-44.312c7.482-14.201 12.666-26.618 15.395-36.931 4.73-17.863 2.558-31.586-6.447-40.796-4.162-4.241-11.735-9.314-24.253-9.314-15.225 0-35.999 7.608-63.504 23.276-18.897 10.756-33.985 21.389-34.622 21.844a18.124 18.124 0 0 0-2.343 2.012l-3.9-3.9a11.507 11.507 0 0 1 0-16.272l81.639-81.696a57.136 57.136 0 0 1 40.411-16.738zm-17.715 89.622c4.742 0 8.62 1.136 11.257 3.842 18.477 18.875-37.933 98.923-37.933 98.923-4.89-4.89-12.325-10.347-19.716-10.347a14.372 14.372 0 0 0-10.438 4.548l-78.091 78.092-20.354-4.64-4.639-20.35 78.092-78.092c10.017-10.04 1.49-22.935-5.81-30.2 0 0 59.127-41.776 87.632-41.776zM196.11 280.548l2.172 9.494a18.193 18.193 0 0 0 13.689 13.689l9.494 2.172L66.827 460.54a17.931 17.931 0 1 1-25.354-25.355L196.11 280.548z"}}]})(props);
};
