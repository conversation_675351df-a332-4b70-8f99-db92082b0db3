// THIS FILE IS AUTO GENERATED
var GenIcon = require('../lib').GenIcon
module.exports.GiEel = function GiEel (props) {
  return GenIcon({"tag":"svg","attr":{"viewBox":"0 0 512 512"},"child":[{"tag":"path","attr":{"d":"M290.4 27.83C219 81.44 130.3 113.9 52.29 159.4c-5.97 3.5-7.78 6.9-8.57 12.3s.37 13.1 2.91 21.6c1.4 4.6 3.19 9.5 5.1 14.5 6.06-11.5 15.22-22.5 26.17-32.9 23.1-22.1 55.4-42.7 89.2-61.2 67.8-36.97 142.8-65.34 165.3-74.39-15.1-2.99-29.9-6.48-42-11.48zm94.1 18.23c-14.6 0-28.6 3.2-44.9 9.78-23 9.25-97.4 37.37-163.9 73.66-33.2 18.1-64.3 38.2-85.37 58.4-21.14 20.2-31.24 39.2-27.8 55.8 4.07 19.6 23.14 34.1 52.97 44.7 30 10.6 69.4 16.6 108.4 20.3 39.1 3.6 77.9 5 107.6 6.9 14.9 1 27.5 2.1 37.1 3.8 4.8.9 8.9 1.9 12.7 3.4 3.6 1.5 8 3.8 9.6 9.7 3.2 11.7-1.4 23.6-9.6 32.6-8.4 9.1-20.3 16.7-34.7 23.9-28.7 14.5-68 26.9-108.1 38-40.3 11-81.4 20.5-113.3 28.6-15.9 4.1-29.51 7.7-39.36 11-3.97 1.3-6.78 2.4-9.33 3.5 7.74 2.1 21.67 3.6 39.19 3.8 36.3.5 88.4-3.8 141.6-13.5 53.1-9.7 107.2-24.8 148-45.1 40.7-20.3 66.5-45.1 68.2-73.1 1.9-27.4-12.1-44.3-37.1-57.9-25-13.5-60.9-21.7-97.1-26.9-36.4-5.2-73.4-7.6-101.8-10.8-14.4-1.7-26.4-3.4-35.6-6.1-4.8-1.4-8.8-2.9-12.4-5.5-3.5-2.5-7.1-6.9-7.5-12.4-.4-8 4-14.7 9.4-20.2 5.3-5.6 12.3-10.5 20.6-15.3 16.5-9.5 38-18.3 59.4-26.1 42.7-15.5 85.5-26.4 85.5-26.4l1.5-.4 1.4.1c29.1 2.1 64.3-3.8 89.7-15.7 12.8-5.9 22.9-13.3 29-21.23 3.9-5.05 6-10.16 6.6-15.76-36.8 12.56-48.6 18.26-78.3 19.49l-.9-18.03c24.9-.98 34.1-4.88 62-14.57-26.1-10.97-46.5-18.01-64.3-20.85-5.6-.91-11-1.43-16.2-1.56h-2.9zm-11 17.11a16 16 0 0 1 16 16 16 16 0 0 1-16 16 16 16 0 0 1-16-16 16 16 0 0 1 16-16zM214.7 206.6c-4.3 2.9-8.1 5.8-10.6 8.3-2.6 2.7-3.4 4.5-3.6 5.7 1 .6 3.1 1.6 6.4 2.6 7.2 2.1 18.7 3.9 32.6 5.5 27.6 3.1 64.9 5.6 102.3 10.9 37.2 5.4 74.6 13.4 103.1 28.9 16.5 8.9 30 20.8 38.4 35.9.8-10.4.1-23.4-3.7-41.2C406 230 298.9 224 214.7 206.6zm157.8 140c-86.6 25.6-158.5 61.1-270.6 74.1-44.78 5.3-56.15 28-75.05 50.3 2.59 3.2 6.97 6.1 13.79 8.4 6.07 2.2 13.65 3.7 22.06 4.8-2.15-1.2-4.12-2.7-5.9-4.8-1.89-2.3-3.31-5.8-3.18-9.2.13-3.4 1.55-6.3 3.23-8.6 2.95-3.9 6.03-5.2 9.79-7 3.77-1.7 8.28-3.4 13.61-5.2 10.65-3.4 24.55-7.2 40.55-11.2 32.2-8.2 73.2-17.7 112.9-28.6 39.7-10.9 78.2-23.3 104.9-36.7 13.3-6.6 23.5-13.6 29.3-19.9 2.2-2.4 3.7-4.5 4.6-6.4z"}}]})(props);
};
