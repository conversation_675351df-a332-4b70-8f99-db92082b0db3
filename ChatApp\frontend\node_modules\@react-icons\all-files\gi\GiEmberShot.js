// THIS FILE IS AUTO GENERATED
var GenIcon = require('../lib').GenIcon
module.exports.GiEmberShot = function GiEmberShot (props) {
  return GenIcon({"tag":"svg","attr":{"viewBox":"0 0 512 512"},"child":[{"tag":"path","attr":{"d":"M105.113 21.303c-1.034-.015-2.07-.013-3.105.006v-.003c-9.466.168-19.305 1.977-28.83 5.19 52.08 2.52 85.738 22.703 96.87 58.812C109.856 51.663 23.21 50.762 17.825 218.503c4.654-24.187 12.24-42.658 21.91-56.506 1.452 60.194 39.912 153.326 126.848 191.432C25.53 238.15 46.6 12.83 226.555 190.828c-17.528 3.248-33.786 13.22-46.705 28.832 40.82 3.046 79.194 32.62 114.744 86.49-13.16-10.622-39.578-4.165-52.47 4.038 34.233 13.687 60.515 46.612 63.425 80.722 2.653 31.13 9.728 52.008 24.436 64.543 16.382 19.37 40.845 31.7 68.116 31.7 44.674 0 81.825-33.08 88.226-76.014 13.92-59.877-7.103-150.928-42.392-205.32-1.33 33.756-9.516 48.257-24.795 64.004 4.843-45.456-4.917-82.556-37.48-127.43-2.958 20.896-6.127 41.78-23.062 50.74-17.134-47.644-70.686-97.153-122.24-140.113 16.883 28.56 35.38 56.712 39.785 92.257C232.59 88.597 170.257 22.23 105.113 21.303zM323.387 222.07c9.62 0 17.422 7.8 17.422 17.422s-7.802 17.422-17.423 17.422c-9.622 0-17.42-7.8-17.42-17.422 0-9.62 7.798-17.422 17.42-17.422zm50.71 11.39c12.203 0 22.094 9.893 22.094 22.095 0 12.202-9.89 22.093-22.092 22.093-12.202 0-22.092-9.89-22.092-22.093 0-12.202 9.89-22.094 22.092-22.094zm-35.95 50.07c10.45 0 19.38 6.474 23.02 15.626 6.004-6.568 14.64-10.69 24.24-10.69 18.13 0 32.83 14.7 32.83 32.83 0 3.02-.417 5.944-1.18 8.722 29.79 8.246 51.56 35.455 51.56 67.933 0 39.056-31.462 70.515-70.515 70.515s-70.514-31.46-70.514-70.514c0-24.234 12.122-45.538 30.65-58.22-2.384-3.508-4.11-7.498-4.992-11.796-4.18 3.22-9.415 5.14-15.1 5.14-13.68 0-24.77-11.09-24.77-24.77 0-13.682 11.09-24.773 24.77-24.773zm109.052 8.038c9.62 0 17.42 7.8 17.42 17.422s-7.8 17.422-17.42 17.422c-9.622 0-17.423-7.8-17.423-17.422 0-9.62 7.8-17.422 17.422-17.422z","fillRule":"evenodd"}}]})(props);
};
