// THIS FILE IS AUTO GENERATED
var GenIcon = require('../lib').GenIcon
module.exports.GiFleshyMass = function GiFleshyMass (props) {
  return GenIcon({"tag":"svg","attr":{"viewBox":"0 0 512 512"},"child":[{"tag":"path","attr":{"d":"M193.24 19.008c-39.99.03-90.725 18.933-136.98 73.293-42.623 50.09-43.956 96.654-26.955 130.233 16.154 31.91 49.733 51.928 83.863 50.05.817-1.036 1.644-2.064 2.49-3.075 1.606-1.92 3.267-3.794 4.973-5.63l9.415-13.358c-11.113-4.64-20.094-11.292-26.785-19.377-10.676-12.9-15.37-29.04-15.246-45.217.244-32.353 18.907-65.897 50.19-81.666 11.97-6.034 24.344-8.83 36.542-9.024 17.343-.273 34.322 4.732 49.28 13.174 6.798-22.15 22.078-39.673 41.333-51.707-2.25-8.447-8.483-16.68-18.71-23.467-12.038-7.987-29.3-13.57-49.574-14.173-1.267-.038-2.546-.056-3.836-.055zm147.996 35.508c-22.5.316-44.8 5.874-62.57 15.996-19.727 11.237-33.782 27.5-38.28 49.093 12.067 9.827 22.26 21.963 29.555 35.385 2.558-3.954 5.64-7.448 9.134-10.39 11.407-9.61 26.41-13.88 41.313-13.82 14.902.062 29.985 4.45 41.77 13.773 8.204 6.49 14.6 15.655 17.564 26.703 16.263-15.822 37.02-23.22 57.08-23.04 4.85.044 9.654.546 14.348 1.462-.035-27.86-9.53-48.44-24.28-63.62-17.65-18.17-43.575-28.536-70.86-30.99-3.41-.306-6.84-.488-10.27-.547-1.5-.024-3.002-.025-4.502-.004zm-165.87 59.28c-9.606.146-19.3 2.388-28.75 7.15-24.218 12.21-39.725 40.084-39.915 65.12-.094 12.52 3.43 24.066 10.956 33.16 7.526 9.095 19.21 16.14 37.235 18.83h.003c39.574 5.908 82.127 9.612 116.025 27.868 33.898 18.255 57.493 52.813 56.3 112.822-.868 43.678 24.482 67.034 59.085 69.498h.002c19.04 1.36 39.016-14.747 46.504-32.055 3.742-8.654 4.214-16.855 1.68-22.807-2.534-5.952-7.93-11.126-20.965-13.76l.024-.12c-6.08-1.215-11.806-3.26-17.07-6.023-16.734-8.785-28.737-24.21-35.093-41.584-6.357-17.372-7.19-36.947-.66-54.644 6.528-17.697 20.85-33.24 42.79-41.17 8.17-2.953 16.144-4.486 23.797-4.805 23.534-.98 44.027 9.512 57.936 25.38 13.79-23.95 7.996-59.225-13.37-77.257h-.003c-24.878-20.997-72.19-18.427-93.607 25.56-3.247 9.45-8.724 17.39-15.586 23.38-10.917 9.528-24.99 14.332-39.108 14.64-14.118.31-28.494-3.886-39.974-13.093-11.48-9.208-19.725-23.583-21.36-41.75 0-.017-.002-.033-.004-.05-4.264-22.568-20.17-45.425-41.107-59.532-13.99-9.43-29.756-15.005-45.765-14.76zM320.31 149.47c-11.18-.045-22.01 3.368-29.197 9.423-7.188 6.054-11.454 14.276-10.258 27.568v.003c1.228 13.645 6.782 22.704 14.44 28.846 7.658 6.14 17.763 9.208 27.873 8.987 10.11-.22 20.013-3.74 27.227-10.037 7.213-6.296 12.077-15.165 12.435-28.06.347-12.515-4.45-20.804-12.27-26.99-7.818-6.184-19.07-9.693-30.25-9.74zm111.083 100.186c-6.72.005-13.922 1.254-21.526 4.002-17.23 6.23-26.834 17.125-31.607 30.063-4.773 12.94-4.285 28.194.678 41.757 4.962 13.562 14.274 25.18 26.23 31.457 11.956 6.277 26.617 7.814 44.85.226 18.36-7.642 28.286-19.104 32.937-31.795 4.65-12.69 3.814-27.015-1.46-39.84-7.91-19.237-24.69-34.61-47.253-35.795-.94-.05-1.89-.074-2.85-.074zm-274.05 7.643c-10.213 6.585-19.498 14.807-27.35 24.196-17.158 20.52-27.33 46.345-26.304 71.936 1.027 25.59 12.765 51.3 41.572 73.33 28.917 22.114 57.135 27.782 82.322 23.398 25.187-4.384 47.588-19.206 63.344-39.63 7.505-9.727 13.508-20.607 17.605-32.128v-.027c1.1-55.318-17.576-80.434-46.476-95.998-27.654-14.893-66.18-19.45-104.715-25.078zm-119.345 6.647c-17.504 21.283-15.83 46.66-4.68 66.543 11.265 20.09 31.345 32.706 51.97 28.002-.116-1.437-.215-2.875-.272-4.31-.89-22.146 5.054-43.816 15.732-63.008-23.247-1.824-45.287-11.65-62.75-27.227zm275.188 147.24c-2.31 3.714-4.8 7.308-7.46 10.758-16.392 21.247-39.18 37.576-65.53 44.557 7.864 17.405 27.497 26.744 48.827 26.312 23.31-.47 46.56-12.385 55.635-39.52-14.386-9.634-25.56-23.998-31.472-42.106z"}}]})(props);
};
