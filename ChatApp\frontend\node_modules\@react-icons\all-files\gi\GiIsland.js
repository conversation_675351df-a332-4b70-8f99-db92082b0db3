// THIS FILE IS AUTO GENERATED
var GenIcon = require('../lib').GenIcon
module.exports.GiIsland = function GiIsland (props) {
  return GenIcon({"tag":"svg","attr":{"viewBox":"0 0 512 512"},"child":[{"tag":"path","attr":{"d":"M80.87 28.32c-10.027.162-20.065 3.47-29.706 11.055C79.26 31.458 116.008 60.67 128.582 94.5c-33.088 2.865-77.492 21.193-92.373 60.79 45.182-35.396 77.437-49.508 97.192-28.644-20.36 20.232-37.693 49.855-34.722 77.06 8.497-19.502 30.642-47.206 53.763-56.956-.017.246-.03.493-.037.74 0 9.698 7.86 17.56 17.56 17.56 5.507-.01 10.692-2.603 14.003-7.005 43.084 62.306 46.703 103.963 46.99 171.13 1.22 3.765 3.31 13.657 8.712 13.323 2.138-.15 7.886-4.198 9.24-14.906-.658-72.08-6.662-120.87-59.648-192.89.088-.437.17-.88.244-1.335 12.77-25.514 63.138-12.534 85.207-7.342-19.952-24.276-63.064-33.383-91.26-30.154 6.987-23.99 41.58-35.786 79.522-39.88-35.283-14.532-83.623-2.6-108.498 18.582-18.92-23.63-46.22-46.692-73.61-46.252zM316.444 88.3c-14.417-.27-30.606 5.297-47.838 19.68 55.587-9.758 66.225 13.936 65.26 41.247-27.864-3.965-65.48 2.288-83.724 24.488 15.247-3.588 43.993-5.876 64.527 1.6-2.136 2.976-3.288 6.547-3.293 10.21 0 9.697 7.86 17.558 17.557 17.56 1.158-.007 2.312-.127 3.447-.36-29.184 40.13-43.586 77.41-49.65 109.765 5.913.638 11.845 1.472 17.78 2.49 6.267-33.1 22.157-72.1 56.822-115.246 3.327 3.807 8.134 5.994 13.19 6.002 8.54-.01 15.833-6.162 17.283-14.578 24.362 2.404 52.773 19.613 66.91 34.192-6.48-25.342-31.1-46.236-56.117-58.325 20.007-20.112 64.557-27.84 85.123-26.85-48.212-22.24-87.34-20.276-110.062-9.238-9.94-21.647-30.544-42.133-57.213-42.636zM18 327v18h100.234c14.542-6.786 29.8-12.894 45.434-18zm330.69 0c15.736 5.106 31.102 11.213 45.736 18H494v-18zm-81.858 2.29c-1.966 17.012-11.84 30.178-25.898 31.165-17.093-1.086-24.48-13.605-27.6-27.437-33.38 5.94-67.274 18.015-97.31 33.033-36.807 18.405-67.758 41.478-84.942 61.233 4.887 1.483 10.322 3.123 17 4.844 16.234 4.183 36.103 7.82 47.176 6.904 8.815-.73 18.05-5.583 28.39-11.27 10.34-5.687 21.82-12.22 35.834-13.026 19-1.092 36.012 5.71 51.84 12.04 15.828 6.332 30.557 12.207 44.69 12.226 8.875.012 18.36-3.293 28.83-7.22 10.47-3.925 21.902-8.468 34.943-8.778 30.896-.735 56.652 15.618 80.36 16 14.596.235 38.53-3.61 58.222-7.625 8.712-1.776 16.05-3.47 22.18-4.91-16.61-19.392-47.196-42.19-83.774-60.38-39.91-19.846-86.81-34.618-129.94-36.798zm-97.768 109.66c-17.693.86-35.45 8.61-51.22 16.005-9.012 4.226-17.343 8.447-24.168 11.486C86.85 469.48 81.11 471 80 471c-25.66 0-48.943-12.707-62-21.492v21.472C33.352 479.837 55.207 489 80 489c7.268 0 13.51-2.78 20.998-6.115 7.488-3.335 15.8-7.56 24.488-11.633 17.376-8.147 36.382-15.234 49.875-14.275 8.73.62 17.46 6.266 27.45 13.51 9.993 7.246 21.062 16.013 35.75 18.396 21.05 3.416 40.977-2.01 59.72-7.215 18.745-5.204 36.403-10.194 52.91-8.705 6 .54 11.362 3.603 18.867 7.564C377.562 484.487 387.252 489 400 489c14.94 0 38.64-4.13 59.537-8.164 15.083-2.91 28.2-5.772 34.463-7.166v-18.39l-.012-.05c0-.002-17.313 3.968-37.863 7.934C435.575 467.13 411.277 471 400 471c-8.694 0-14.606-2.73-21.54-6.39-6.936-3.66-14.852-8.6-25.65-9.573-21.053-1.898-40.784 4.134-59.343 9.287-18.56 5.153-35.852 9.418-52.026 6.793-8.856-1.437-17.89-7.824-28.063-15.2-10.174-7.378-21.676-15.823-36.738-16.894-2.6-.177-5.16-.19-7.576-.074z"}}]})(props);
};
