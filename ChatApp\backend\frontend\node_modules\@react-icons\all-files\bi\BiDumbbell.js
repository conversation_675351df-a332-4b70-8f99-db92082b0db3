// THIS FILE IS AUTO GENERATED
var GenIcon = require('../lib').GenIcon
module.exports.BiDumbbell = function BiDumbbell (props) {
  return GenIcon({"tag":"svg","attr":{"viewBox":"0 0 24 24"},"child":[{"tag":"path","attr":{"d":"M6 5L6 11 6 13 6 19 9 19 9 13 15 13 15 19 18 19 18 5 15 5 15 11 9 11 9 5zM3 15c0 .553.447 1 1 1h1V8H4C3.447 8 3 8.447 3 9v2H2v2h1V15zM21 9c0-.553-.447-1-1-1h-1v8h1c.553 0 1-.447 1-1v-2h1v-2h-1V9z"}}]})(props);
};
