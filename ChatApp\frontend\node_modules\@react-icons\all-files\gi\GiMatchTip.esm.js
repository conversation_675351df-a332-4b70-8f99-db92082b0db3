// THIS FILE IS AUTO GENERATED
import { GenIcon } from '../lib';
export function GiMatchTip (props) {
  return GenIcon({"tag":"svg","attr":{"viewBox":"0 0 512 512"},"child":[{"tag":"path","attr":{"d":"M310.867 16.785L258.89 101.47l-51.976-58.402 2.188 102.407 19.962-46.123L251.2 171.03l23.19-28.46 3.16 60.08 80.108-64.298-48.486 91.705 47.433 21.08-38.93 15.894 69.548 110.654-80.776-52.3 5.45 36.505-44.467-49.445-30.987 73.61-12.482-85.963-41.273 26.937 1.053-46.378-83.27 6.323 82.218-40.055-88.542-29.512 43.772-19.898-117.313 11.42 89.94 43.8-79.426 55.48 95.778 4.675-34.46 57.232 57.82-26.863L131 453.262l77.66-93.657 18.69 129.067 33.287-100.45 93.285 84.055-23.957-83.273 136.207 68.642-78.377-138.935 81.11 9.827-75.325-68.94 80.225-31.98-94.026-21.025 43.8-50.225-61.32 16.936 43.803-112.13-98.7 85.85 3.504-130.238zM18.854 18.537v42.535c38.127 46.536 115.7 118.404 184.445 166.92-2.49 15.173 4.22 33.952 18.917 48.65 21.02 21.02 50.402 25.723 65.623 10.503 15.222-15.223 10.518-44.604-10.504-65.625-13.795-13.795-31.19-20.562-45.82-19.284-49.03-67.528-121.705-143.93-172.71-183.7H18.855z"}}]})(props);
};
