import properties from './base/properties/index.js';
import scrollbar from './base/scrollbar/index.js';
import svg from './base/svg/index.js';
import rootscrolllock from './base/rootscrolllock/index.js';
import rootscrollgutter from './base/rootscrollgutter/index.js';
import rootcolor from './base/rootcolor/index.js';
import table from './components/table/index.js';
import radio from './components/radio/index.js';
import drawer from './components/drawer/index.js';
import tab from './components/tab/index.js';
import hero from './components/hero/index.js';
import select from './components/select/index.js';
import avatar from './components/avatar/index.js';
import skeleton from './components/skeleton/index.js';
import progress from './components/progress/index.js';
import fileinput from './components/fileinput/index.js';
import stack from './components/stack/index.js';
import footer from './components/footer/index.js';
import menu from './components/menu/index.js';
import button from './components/button/index.js';
import tooltip from './components/tooltip/index.js';
import dock from './components/dock/index.js';
import timeline from './components/timeline/index.js';
import textarea from './components/textarea/index.js';
import label from './components/label/index.js';
import fieldset from './components/fieldset/index.js';
import carousel from './components/carousel/index.js';
import navbar from './components/navbar/index.js';
import card from './components/card/index.js';
import toast from './components/toast/index.js';
import modal from './components/modal/index.js';
import countdown from './components/countdown/index.js';
import filter from './components/filter/index.js';
import checkbox from './components/checkbox/index.js';
import divider from './components/divider/index.js';
import toggle from './components/toggle/index.js';
import status from './components/status/index.js';
import steps from './components/steps/index.js';
import range from './components/range/index.js';
import badge from './components/badge/index.js';
import list from './components/list/index.js';
import mockup from './components/mockup/index.js';
import calendar from './components/calendar/index.js';
import dropdown from './components/dropdown/index.js';
import input from './components/input/index.js';
import chat from './components/chat/index.js';
import indicator from './components/indicator/index.js';
import swap from './components/swap/index.js';
import rating from './components/rating/index.js';
import breadcrumbs from './components/breadcrumbs/index.js';
import validator from './components/validator/index.js';
import stat from './components/stat/index.js';
import loading from './components/loading/index.js';
import link from './components/link/index.js';
import collapse from './components/collapse/index.js';
import mask from './components/mask/index.js';
import alert from './components/alert/index.js';
import kbd from './components/kbd/index.js';
import radialprogress from './components/radialprogress/index.js';
import diff from './components/diff/index.js';
import radius from './utilities/radius/index.js';
import join from './utilities/join/index.js';
import typography from './utilities/typography/index.js';
import glass from './utilities/glass/index.js';

export const base = {properties,scrollbar,svg,rootscrolllock,rootscrollgutter,rootcolor};
export const components = {table,radio,drawer,tab,hero,select,avatar,skeleton,progress,fileinput,stack,footer,menu,button,tooltip,dock,timeline,textarea,label,fieldset,carousel,navbar,card,toast,modal,countdown,filter,checkbox,divider,toggle,status,steps,range,badge,list,mockup,calendar,dropdown,input,chat,indicator,swap,rating,breadcrumbs,validator,stat,loading,link,collapse,mask,alert,kbd,radialprogress,diff};
export const utilities = {radius,join,typography,glass};
