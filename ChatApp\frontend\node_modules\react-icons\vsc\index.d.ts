// THIS FILE IS AUTO GENERATED
import type { IconType } from '../lib/index'
export declare const VscAccount: IconType;
export declare const VscActivateBreakpoints: IconType;
export declare const VscAdd: IconType;
export declare const VscArchive: IconType;
export declare const VscArrowBoth: IconType;
export declare const VscArrowCircleDown: IconType;
export declare const VscArrowCircleLeft: IconType;
export declare const VscArrowCircleRight: IconType;
export declare const VscArrowCircleUp: IconType;
export declare const VscArrowDown: IconType;
export declare const VscArrowLeft: IconType;
export declare const VscArrowRight: IconType;
export declare const VscArrowSmallDown: IconType;
export declare const VscArrowSmallLeft: IconType;
export declare const VscArrowSmallRight: IconType;
export declare const VscArrowSmallUp: IconType;
export declare const VscArrowSwap: IconType;
export declare const VscArrowUp: IconType;
export declare const VscAttach: IconType;
export declare const VscAzureDevops: IconType;
export declare const VscAzure: IconType;
export declare const VscBeakerStop: IconType;
export declare const VscBeaker: IconType;
export declare const VscBellDot: IconType;
export declare const VscBellSlashDot: IconType;
export declare const VscBellSlash: IconType;
export declare const VscBell: IconType;
export declare const VscBlank: IconType;
export declare const VscBold: IconType;
export declare const VscBook: IconType;
export declare const VscBookmark: IconType;
export declare const VscBracketDot: IconType;
export declare const VscBracketError: IconType;
export declare const VscBriefcase: IconType;
export declare const VscBroadcast: IconType;
export declare const VscBrowser: IconType;
export declare const VscBug: IconType;
export declare const VscCalendar: IconType;
export declare const VscCallIncoming: IconType;
export declare const VscCallOutgoing: IconType;
export declare const VscCaseSensitive: IconType;
export declare const VscCheckAll: IconType;
export declare const VscCheck: IconType;
export declare const VscChecklist: IconType;
export declare const VscChevronDown: IconType;
export declare const VscChevronLeft: IconType;
export declare const VscChevronRight: IconType;
export declare const VscChevronUp: IconType;
export declare const VscChip: IconType;
export declare const VscChromeClose: IconType;
export declare const VscChromeMaximize: IconType;
export declare const VscChromeMinimize: IconType;
export declare const VscChromeRestore: IconType;
export declare const VscCircleFilled: IconType;
export declare const VscCircleLargeFilled: IconType;
export declare const VscCircleLarge: IconType;
export declare const VscCircleSlash: IconType;
export declare const VscCircleSmallFilled: IconType;
export declare const VscCircleSmall: IconType;
export declare const VscCircle: IconType;
export declare const VscCircuitBoard: IconType;
export declare const VscClearAll: IconType;
export declare const VscClippy: IconType;
export declare const VscCloseAll: IconType;
export declare const VscClose: IconType;
export declare const VscCloudDownload: IconType;
export declare const VscCloudUpload: IconType;
export declare const VscCloud: IconType;
export declare const VscCodeOss: IconType;
export declare const VscCodeReview: IconType;
export declare const VscCode: IconType;
export declare const VscCoffee: IconType;
export declare const VscCollapseAll: IconType;
export declare const VscColorMode: IconType;
export declare const VscCombine: IconType;
export declare const VscCommentDiscussion: IconType;
export declare const VscCommentDraft: IconType;
export declare const VscCommentUnresolved: IconType;
export declare const VscComment: IconType;
export declare const VscCompassActive: IconType;
export declare const VscCompassDot: IconType;
export declare const VscCompass: IconType;
export declare const VscCopilotWarning: IconType;
export declare const VscCopilot: IconType;
export declare const VscCopy: IconType;
export declare const VscCoverage: IconType;
export declare const VscCreditCard: IconType;
export declare const VscDash: IconType;
export declare const VscDashboard: IconType;
export declare const VscDatabase: IconType;
export declare const VscDebugAll: IconType;
export declare const VscDebugAltSmall: IconType;
export declare const VscDebugAlt: IconType;
export declare const VscDebugBreakpointConditionalUnverified: IconType;
export declare const VscDebugBreakpointConditional: IconType;
export declare const VscDebugBreakpointDataUnverified: IconType;
export declare const VscDebugBreakpointData: IconType;
export declare const VscDebugBreakpointFunctionUnverified: IconType;
export declare const VscDebugBreakpointFunction: IconType;
export declare const VscDebugBreakpointLogUnverified: IconType;
export declare const VscDebugBreakpointLog: IconType;
export declare const VscDebugBreakpointUnsupported: IconType;
export declare const VscDebugConsole: IconType;
export declare const VscDebugContinueSmall: IconType;
export declare const VscDebugContinue: IconType;
export declare const VscDebugCoverage: IconType;
export declare const VscDebugDisconnect: IconType;
export declare const VscDebugLineByLine: IconType;
export declare const VscDebugPause: IconType;
export declare const VscDebugRerun: IconType;
export declare const VscDebugRestartFrame: IconType;
export declare const VscDebugRestart: IconType;
export declare const VscDebugReverseContinue: IconType;
export declare const VscDebugStackframeActive: IconType;
export declare const VscDebugStackframe: IconType;
export declare const VscDebugStart: IconType;
export declare const VscDebugStepBack: IconType;
export declare const VscDebugStepInto: IconType;
export declare const VscDebugStepOut: IconType;
export declare const VscDebugStepOver: IconType;
export declare const VscDebugStop: IconType;
export declare const VscDebug: IconType;
export declare const VscDesktopDownload: IconType;
export declare const VscDeviceCameraVideo: IconType;
export declare const VscDeviceCamera: IconType;
export declare const VscDeviceMobile: IconType;
export declare const VscDiffAdded: IconType;
export declare const VscDiffIgnored: IconType;
export declare const VscDiffModified: IconType;
export declare const VscDiffMultiple: IconType;
export declare const VscDiffRemoved: IconType;
export declare const VscDiffRenamed: IconType;
export declare const VscDiffSingle: IconType;
export declare const VscDiff: IconType;
export declare const VscDiscard: IconType;
export declare const VscEditSession: IconType;
export declare const VscEdit: IconType;
export declare const VscEditorLayout: IconType;
export declare const VscEllipsis: IconType;
export declare const VscEmptyWindow: IconType;
export declare const VscErrorSmall: IconType;
export declare const VscError: IconType;
export declare const VscExclude: IconType;
export declare const VscExpandAll: IconType;
export declare const VscExport: IconType;
export declare const VscExtensions: IconType;
export declare const VscEyeClosed: IconType;
export declare const VscEye: IconType;
export declare const VscFeedback: IconType;
export declare const VscFileBinary: IconType;
export declare const VscFileCode: IconType;
export declare const VscFileMedia: IconType;
export declare const VscFilePdf: IconType;
export declare const VscFileSubmodule: IconType;
export declare const VscFileSymlinkDirectory: IconType;
export declare const VscFileSymlinkFile: IconType;
export declare const VscFileZip: IconType;
export declare const VscFile: IconType;
export declare const VscFiles: IconType;
export declare const VscFilterFilled: IconType;
export declare const VscFilter: IconType;
export declare const VscFlame: IconType;
export declare const VscFoldDown: IconType;
export declare const VscFoldUp: IconType;
export declare const VscFold: IconType;
export declare const VscFolderActive: IconType;
export declare const VscFolderLibrary: IconType;
export declare const VscFolderOpened: IconType;
export declare const VscFolder: IconType;
export declare const VscGame: IconType;
export declare const VscGear: IconType;
export declare const VscGift: IconType;
export declare const VscGistSecret: IconType;
export declare const VscGist: IconType;
export declare const VscGitCommit: IconType;
export declare const VscGitCompare: IconType;
export declare const VscGitFetch: IconType;
export declare const VscGitMerge: IconType;
export declare const VscGitPullRequestClosed: IconType;
export declare const VscGitPullRequestCreate: IconType;
export declare const VscGitPullRequestDraft: IconType;
export declare const VscGitPullRequestGoToChanges: IconType;
export declare const VscGitPullRequestNewChanges: IconType;
export declare const VscGitPullRequest: IconType;
export declare const VscGitStashApply: IconType;
export declare const VscGitStashPop: IconType;
export declare const VscGitStash: IconType;
export declare const VscGithubAction: IconType;
export declare const VscGithubAlt: IconType;
export declare const VscGithubInverted: IconType;
export declare const VscGithubProject: IconType;
export declare const VscGithub: IconType;
export declare const VscGlobe: IconType;
export declare const VscGoToEditingSession: IconType;
export declare const VscGoToFile: IconType;
export declare const VscGoToSearch: IconType;
export declare const VscGrabber: IconType;
export declare const VscGraphLeft: IconType;
export declare const VscGraphLine: IconType;
export declare const VscGraphScatter: IconType;
export declare const VscGraph: IconType;
export declare const VscGripper: IconType;
export declare const VscGroupByRefType: IconType;
export declare const VscHeartFilled: IconType;
export declare const VscHeart: IconType;
export declare const VscHistory: IconType;
export declare const VscHome: IconType;
export declare const VscHorizontalRule: IconType;
export declare const VscHubot: IconType;
export declare const VscInbox: IconType;
export declare const VscIndent: IconType;
export declare const VscInfo: IconType;
export declare const VscInsert: IconType;
export declare const VscInspect: IconType;
export declare const VscIssueDraft: IconType;
export declare const VscIssueReopened: IconType;
export declare const VscIssues: IconType;
export declare const VscItalic: IconType;
export declare const VscJersey: IconType;
export declare const VscJson: IconType;
export declare const VscKebabVertical: IconType;
export declare const VscKey: IconType;
export declare const VscLaw: IconType;
export declare const VscLayersActive: IconType;
export declare const VscLayersDot: IconType;
export declare const VscLayers: IconType;
export declare const VscLayoutActivitybarLeft: IconType;
export declare const VscLayoutActivitybarRight: IconType;
export declare const VscLayoutCentered: IconType;
export declare const VscLayoutMenubar: IconType;
export declare const VscLayoutPanelCenter: IconType;
export declare const VscLayoutPanelJustify: IconType;
export declare const VscLayoutPanelLeft: IconType;
export declare const VscLayoutPanelOff: IconType;
export declare const VscLayoutPanelRight: IconType;
export declare const VscLayoutPanel: IconType;
export declare const VscLayoutSidebarLeftOff: IconType;
export declare const VscLayoutSidebarLeft: IconType;
export declare const VscLayoutSidebarRightOff: IconType;
export declare const VscLayoutSidebarRight: IconType;
export declare const VscLayoutStatusbar: IconType;
export declare const VscLayout: IconType;
export declare const VscLibrary: IconType;
export declare const VscLightbulbAutofix: IconType;
export declare const VscLightbulbSparkle: IconType;
export declare const VscLightbulb: IconType;
export declare const VscLinkExternal: IconType;
export declare const VscLink: IconType;
export declare const VscListFilter: IconType;
export declare const VscListFlat: IconType;
export declare const VscListOrdered: IconType;
export declare const VscListSelection: IconType;
export declare const VscListTree: IconType;
export declare const VscListUnordered: IconType;
export declare const VscLiveShare: IconType;
export declare const VscLoading: IconType;
export declare const VscLocation: IconType;
export declare const VscLockSmall: IconType;
export declare const VscLock: IconType;
export declare const VscMagnet: IconType;
export declare const VscMailRead: IconType;
export declare const VscMail: IconType;
export declare const VscMapFilled: IconType;
export declare const VscMapVerticalFilled: IconType;
export declare const VscMapVertical: IconType;
export declare const VscMap: IconType;
export declare const VscMarkdown: IconType;
export declare const VscMegaphone: IconType;
export declare const VscMention: IconType;
export declare const VscMenu: IconType;
export declare const VscMerge: IconType;
export declare const VscMicFilled: IconType;
export declare const VscMic: IconType;
export declare const VscMilestone: IconType;
export declare const VscMirror: IconType;
export declare const VscMortarBoard: IconType;
export declare const VscMove: IconType;
export declare const VscMultipleWindows: IconType;
export declare const VscMusic: IconType;
export declare const VscMute: IconType;
export declare const VscNewFile: IconType;
export declare const VscNewFolder: IconType;
export declare const VscNewline: IconType;
export declare const VscNoNewline: IconType;
export declare const VscNote: IconType;
export declare const VscNotebookTemplate: IconType;
export declare const VscNotebook: IconType;
export declare const VscOctoface: IconType;
export declare const VscOpenPreview: IconType;
export declare const VscOrganization: IconType;
export declare const VscOutput: IconType;
export declare const VscPackage: IconType;
export declare const VscPaintcan: IconType;
export declare const VscPassFilled: IconType;
export declare const VscPass: IconType;
export declare const VscPercentage: IconType;
export declare const VscPersonAdd: IconType;
export declare const VscPerson: IconType;
export declare const VscPiano: IconType;
export declare const VscPieChart: IconType;
export declare const VscPin: IconType;
export declare const VscPinnedDirty: IconType;
export declare const VscPinned: IconType;
export declare const VscPlayCircle: IconType;
export declare const VscPlay: IconType;
export declare const VscPlug: IconType;
export declare const VscPreserveCase: IconType;
export declare const VscPreview: IconType;
export declare const VscPrimitiveSquare: IconType;
export declare const VscProject: IconType;
export declare const VscPulse: IconType;
export declare const VscQuestion: IconType;
export declare const VscQuote: IconType;
export declare const VscRadioTower: IconType;
export declare const VscReactions: IconType;
export declare const VscRecordKeys: IconType;
export declare const VscRecordSmall: IconType;
export declare const VscRecord: IconType;
export declare const VscRedo: IconType;
export declare const VscReferences: IconType;
export declare const VscRefresh: IconType;
export declare const VscRegex: IconType;
export declare const VscRemoteExplorer: IconType;
export declare const VscRemote: IconType;
export declare const VscRemove: IconType;
export declare const VscReplaceAll: IconType;
export declare const VscReplace: IconType;
export declare const VscReply: IconType;
export declare const VscRepoClone: IconType;
export declare const VscRepoFetch: IconType;
export declare const VscRepoForcePush: IconType;
export declare const VscRepoForked: IconType;
export declare const VscRepoPull: IconType;
export declare const VscRepoPush: IconType;
export declare const VscRepo: IconType;
export declare const VscReport: IconType;
export declare const VscRequestChanges: IconType;
export declare const VscRobot: IconType;
export declare const VscRocket: IconType;
export declare const VscRootFolderOpened: IconType;
export declare const VscRootFolder: IconType;
export declare const VscRss: IconType;
export declare const VscRuby: IconType;
export declare const VscRunAbove: IconType;
export declare const VscRunAllCoverage: IconType;
export declare const VscRunAll: IconType;
export declare const VscRunBelow: IconType;
export declare const VscRunCoverage: IconType;
export declare const VscRunErrors: IconType;
export declare const VscSaveAll: IconType;
export declare const VscSaveAs: IconType;
export declare const VscSave: IconType;
export declare const VscScreenFull: IconType;
export declare const VscScreenNormal: IconType;
export declare const VscSearchFuzzy: IconType;
export declare const VscSearchStop: IconType;
export declare const VscSearch: IconType;
export declare const VscSend: IconType;
export declare const VscServerEnvironment: IconType;
export declare const VscServerProcess: IconType;
export declare const VscServer: IconType;
export declare const VscSettingsGear: IconType;
export declare const VscSettings: IconType;
export declare const VscShare: IconType;
export declare const VscShield: IconType;
export declare const VscSignIn: IconType;
export declare const VscSignOut: IconType;
export declare const VscSmiley: IconType;
export declare const VscSnake: IconType;
export declare const VscSortPrecedence: IconType;
export declare const VscSourceControl: IconType;
export declare const VscSparkleFilled: IconType;
export declare const VscSparkle: IconType;
export declare const VscSplitHorizontal: IconType;
export declare const VscSplitVertical: IconType;
export declare const VscSquirrel: IconType;
export declare const VscStarEmpty: IconType;
export declare const VscStarFull: IconType;
export declare const VscStarHalf: IconType;
export declare const VscStopCircle: IconType;
export declare const VscSurroundWith: IconType;
export declare const VscSymbolArray: IconType;
export declare const VscSymbolBoolean: IconType;
export declare const VscSymbolClass: IconType;
export declare const VscSymbolColor: IconType;
export declare const VscSymbolConstant: IconType;
export declare const VscSymbolEnumMember: IconType;
export declare const VscSymbolEnum: IconType;
export declare const VscSymbolEvent: IconType;
export declare const VscSymbolField: IconType;
export declare const VscSymbolFile: IconType;
export declare const VscSymbolInterface: IconType;
export declare const VscSymbolKey: IconType;
export declare const VscSymbolKeyword: IconType;
export declare const VscSymbolMethod: IconType;
export declare const VscSymbolMisc: IconType;
export declare const VscSymbolNamespace: IconType;
export declare const VscSymbolNumeric: IconType;
export declare const VscSymbolOperator: IconType;
export declare const VscSymbolParameter: IconType;
export declare const VscSymbolProperty: IconType;
export declare const VscSymbolRuler: IconType;
export declare const VscSymbolSnippet: IconType;
export declare const VscSymbolString: IconType;
export declare const VscSymbolStructure: IconType;
export declare const VscSymbolVariable: IconType;
export declare const VscSyncIgnored: IconType;
export declare const VscSync: IconType;
export declare const VscTable: IconType;
export declare const VscTag: IconType;
export declare const VscTarget: IconType;
export declare const VscTasklist: IconType;
export declare const VscTelescope: IconType;
export declare const VscTerminalBash: IconType;
export declare const VscTerminalCmd: IconType;
export declare const VscTerminalDebian: IconType;
export declare const VscTerminalLinux: IconType;
export declare const VscTerminalPowershell: IconType;
export declare const VscTerminalTmux: IconType;
export declare const VscTerminalUbuntu: IconType;
export declare const VscTerminal: IconType;
export declare const VscTextSize: IconType;
export declare const VscThreeBars: IconType;
export declare const VscThumbsdownFilled: IconType;
export declare const VscThumbsdown: IconType;
export declare const VscThumbsupFilled: IconType;
export declare const VscThumbsup: IconType;
export declare const VscTools: IconType;
export declare const VscTrash: IconType;
export declare const VscTriangleDown: IconType;
export declare const VscTriangleLeft: IconType;
export declare const VscTriangleRight: IconType;
export declare const VscTriangleUp: IconType;
export declare const VscTwitter: IconType;
export declare const VscTypeHierarchySub: IconType;
export declare const VscTypeHierarchySuper: IconType;
export declare const VscTypeHierarchy: IconType;
export declare const VscUnfold: IconType;
export declare const VscUngroupByRefType: IconType;
export declare const VscUnlock: IconType;
export declare const VscUnmute: IconType;
export declare const VscUnverified: IconType;
export declare const VscVariableGroup: IconType;
export declare const VscVerifiedFilled: IconType;
export declare const VscVerified: IconType;
export declare const VscVersions: IconType;
export declare const VscVmActive: IconType;
export declare const VscVmConnect: IconType;
export declare const VscVmOutline: IconType;
export declare const VscVmRunning: IconType;
export declare const VscVm: IconType;
export declare const VscVr: IconType;
export declare const VscVscodeInsiders: IconType;
export declare const VscVscode: IconType;
export declare const VscWand: IconType;
export declare const VscWarning: IconType;
export declare const VscWatch: IconType;
export declare const VscWhitespace: IconType;
export declare const VscWholeWord: IconType;
export declare const VscWindow: IconType;
export declare const VscWordWrap: IconType;
export declare const VscWorkspaceTrusted: IconType;
export declare const VscWorkspaceUnknown: IconType;
export declare const VscWorkspaceUntrusted: IconType;
export declare const VscZoomIn: IconType;
export declare const VscZoomOut: IconType;
