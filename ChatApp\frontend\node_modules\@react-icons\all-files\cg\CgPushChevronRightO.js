// THIS FILE IS AUTO GENERATED
var GenIcon = require('../lib').GenIcon
module.exports.CgPushChevronRightO = function CgPushChevronRightO (props) {
  return GenIcon({"tag":"svg","attr":{"viewBox":"0 0 24 24","fill":"none"},"child":[{"tag":"path","attr":{"d":"M14 8H16V16H14V8Z","fill":"currentColor"}},{"tag":"path","attr":{"d":"M8.70261 16.2427L7.2884 14.8285L10.1168 12L7.28842 9.17164L8.70264 7.75743L12.9453 12.0001L8.70261 16.2427Z","fill":"currentColor"}},{"tag":"path","attr":{"fillRule":"evenodd","clipRule":"evenodd","d":"<PERSON>23 12C23 5.92487 18.0751 1 12 1C5.92487 1 1 5.92487 1 12C1 18.0751 5.92487 23 12 23C18.0751 23 23 18.0751 23 12ZM21 12C21 7.02944 16.9706 3 12 3C7.02944 3 3 7.02944 3 12C3 16.9706 7.02944 21 12 21C16.9706 21 21 16.9706 21 12Z","fill":"currentColor"}}]})(props);
};
