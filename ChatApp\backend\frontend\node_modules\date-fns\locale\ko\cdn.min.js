(()=>{var $;function B(G){return B=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(H){return typeof H}:function(H){return H&&typeof Symbol=="function"&&H.constructor===Symbol&&H!==Symbol.prototype?"symbol":typeof H},B(G)}function K(G,H){var J=Object.keys(G);if(Object.getOwnPropertySymbols){var X=Object.getOwnPropertySymbols(G);H&&(X=X.filter(function(Y){return Object.getOwnPropertyDescriptor(G,Y).enumerable})),J.push.apply(J,X)}return J}function Q(G){for(var H=1;H<arguments.length;H++){var J=arguments[H]!=null?arguments[H]:{};H%2?K(Object(J),!0).forEach(function(X){x(G,X,J[X])}):Object.getOwnPropertyDescriptors?Object.defineProperties(G,Object.getOwnPropertyDescriptors(J)):K(Object(J)).forEach(function(X){Object.defineProperty(G,X,Object.getOwnPropertyDescriptor(J,X))})}return G}function x(G,H,J){if(H=N(H),H in G)Object.defineProperty(G,H,{value:J,enumerable:!0,configurable:!0,writable:!0});else G[H]=J;return G}function N(G){var H=z(G,"string");return B(H)=="symbol"?H:String(H)}function z(G,H){if(B(G)!="object"||!G)return G;var J=G[Symbol.toPrimitive];if(J!==void 0){var X=J.call(G,H||"default");if(B(X)!="object")return X;throw new TypeError("@@toPrimitive must return a primitive value.")}return(H==="string"?String:Number)(G)}var W=Object.defineProperty,JG=function G(H,J){for(var X in J)W(H,X,{get:J[X],enumerable:!0,configurable:!0,set:function Y(Z){return J[X]=function(){return Z}}})},D={lessThanXSeconds:{one:"1\uCD08 \uBBF8\uB9CC",other:"{{count}}\uCD08 \uBBF8\uB9CC"},xSeconds:{one:"1\uCD08",other:"{{count}}\uCD08"},halfAMinute:"30\uCD08",lessThanXMinutes:{one:"1\uBD84 \uBBF8\uB9CC",other:"{{count}}\uBD84 \uBBF8\uB9CC"},xMinutes:{one:"1\uBD84",other:"{{count}}\uBD84"},aboutXHours:{one:"\uC57D 1\uC2DC\uAC04",other:"\uC57D {{count}}\uC2DC\uAC04"},xHours:{one:"1\uC2DC\uAC04",other:"{{count}}\uC2DC\uAC04"},xDays:{one:"1\uC77C",other:"{{count}}\uC77C"},aboutXWeeks:{one:"\uC57D 1\uC8FC",other:"\uC57D {{count}}\uC8FC"},xWeeks:{one:"1\uC8FC",other:"{{count}}\uC8FC"},aboutXMonths:{one:"\uC57D 1\uAC1C\uC6D4",other:"\uC57D {{count}}\uAC1C\uC6D4"},xMonths:{one:"1\uAC1C\uC6D4",other:"{{count}}\uAC1C\uC6D4"},aboutXYears:{one:"\uC57D 1\uB144",other:"\uC57D {{count}}\uB144"},xYears:{one:"1\uB144",other:"{{count}}\uB144"},overXYears:{one:"1\uB144 \uC774\uC0C1",other:"{{count}}\uB144 \uC774\uC0C1"},almostXYears:{one:"\uAC70\uC758 1\uB144",other:"\uAC70\uC758 {{count}}\uB144"}},S=function G(H,J,X){var Y,Z=D[H];if(typeof Z==="string")Y=Z;else if(J===1)Y=Z.one;else Y=Z.other.replace("{{count}}",J.toString());if(X!==null&&X!==void 0&&X.addSuffix)if(X.comparison&&X.comparison>0)return Y+" \uD6C4";else return Y+" \uC804";return Y};function A(G){return function(){var H=arguments.length>0&&arguments[0]!==void 0?arguments[0]:{},J=H.width?String(H.width):G.defaultWidth,X=G.formats[J]||G.formats[G.defaultWidth];return X}}var M={full:"y\uB144 M\uC6D4 d\uC77C EEEE",long:"y\uB144 M\uC6D4 d\uC77C",medium:"y.MM.dd",short:"y.MM.dd"},R={full:"a H\uC2DC mm\uBD84 ss\uCD08 zzzz",long:"a H:mm:ss z",medium:"HH:mm:ss",short:"HH:mm"},L={full:"{{date}} {{time}}",long:"{{date}} {{time}}",medium:"{{date}} {{time}}",short:"{{date}} {{time}}"},V={date:A({formats:M,defaultWidth:"full"}),time:A({formats:R,defaultWidth:"full"}),dateTime:A({formats:L,defaultWidth:"full"})},j={lastWeek:"'\uC9C0\uB09C' eeee p",yesterday:"'\uC5B4\uC81C' p",today:"'\uC624\uB298' p",tomorrow:"'\uB0B4\uC77C' p",nextWeek:"'\uB2E4\uC74C' eeee p",other:"P"},w=function G(H,J,X,Y){return j[H]};function I(G){return function(H,J){var X=J!==null&&J!==void 0&&J.context?String(J.context):"standalone",Y;if(X==="formatting"&&G.formattingValues){var Z=G.defaultFormattingWidth||G.defaultWidth,C=J!==null&&J!==void 0&&J.width?String(J.width):Z;Y=G.formattingValues[C]||G.formattingValues[Z]}else{var T=G.defaultWidth,q=J!==null&&J!==void 0&&J.width?String(J.width):G.defaultWidth;Y=G.values[q]||G.values[T]}var U=G.argumentCallback?G.argumentCallback(H):H;return Y[U]}}var _={narrow:["BC","AD"],abbreviated:["BC","AD"],wide:["\uAE30\uC6D0\uC804","\uC11C\uAE30"]},f={narrow:["1","2","3","4"],abbreviated:["Q1","Q2","Q3","Q4"],wide:["1\uBD84\uAE30","2\uBD84\uAE30","3\uBD84\uAE30","4\uBD84\uAE30"]},F={narrow:["1","2","3","4","5","6","7","8","9","10","11","12"],abbreviated:["1\uC6D4","2\uC6D4","3\uC6D4","4\uC6D4","5\uC6D4","6\uC6D4","7\uC6D4","8\uC6D4","9\uC6D4","10\uC6D4","11\uC6D4","12\uC6D4"],wide:["1\uC6D4","2\uC6D4","3\uC6D4","4\uC6D4","5\uC6D4","6\uC6D4","7\uC6D4","8\uC6D4","9\uC6D4","10\uC6D4","11\uC6D4","12\uC6D4"]},v={narrow:["\uC77C","\uC6D4","\uD654","\uC218","\uBAA9","\uAE08","\uD1A0"],short:["\uC77C","\uC6D4","\uD654","\uC218","\uBAA9","\uAE08","\uD1A0"],abbreviated:["\uC77C","\uC6D4","\uD654","\uC218","\uBAA9","\uAE08","\uD1A0"],wide:["\uC77C\uC694\uC77C","\uC6D4\uC694\uC77C","\uD654\uC694\uC77C","\uC218\uC694\uC77C","\uBAA9\uC694\uC77C","\uAE08\uC694\uC77C","\uD1A0\uC694\uC77C"]},P={narrow:{am:"\uC624\uC804",pm:"\uC624\uD6C4",midnight:"\uC790\uC815",noon:"\uC815\uC624",morning:"\uC544\uCE68",afternoon:"\uC624\uD6C4",evening:"\uC800\uB141",night:"\uBC24"},abbreviated:{am:"\uC624\uC804",pm:"\uC624\uD6C4",midnight:"\uC790\uC815",noon:"\uC815\uC624",morning:"\uC544\uCE68",afternoon:"\uC624\uD6C4",evening:"\uC800\uB141",night:"\uBC24"},wide:{am:"\uC624\uC804",pm:"\uC624\uD6C4",midnight:"\uC790\uC815",noon:"\uC815\uC624",morning:"\uC544\uCE68",afternoon:"\uC624\uD6C4",evening:"\uC800\uB141",night:"\uBC24"}},k={narrow:{am:"\uC624\uC804",pm:"\uC624\uD6C4",midnight:"\uC790\uC815",noon:"\uC815\uC624",morning:"\uC544\uCE68",afternoon:"\uC624\uD6C4",evening:"\uC800\uB141",night:"\uBC24"},abbreviated:{am:"\uC624\uC804",pm:"\uC624\uD6C4",midnight:"\uC790\uC815",noon:"\uC815\uC624",morning:"\uC544\uCE68",afternoon:"\uC624\uD6C4",evening:"\uC800\uB141",night:"\uBC24"},wide:{am:"\uC624\uC804",pm:"\uC624\uD6C4",midnight:"\uC790\uC815",noon:"\uC815\uC624",morning:"\uC544\uCE68",afternoon:"\uC624\uD6C4",evening:"\uC800\uB141",night:"\uBC24"}},h=function G(H,J){var X=Number(H),Y=String(J===null||J===void 0?void 0:J.unit);switch(Y){case"minute":case"second":return String(X);case"date":return X+"\uC77C";default:return X+"\uBC88\uC9F8"}},b={ordinalNumber:h,era:I({values:_,defaultWidth:"wide"}),quarter:I({values:f,defaultWidth:"wide",argumentCallback:function G(H){return H-1}}),month:I({values:F,defaultWidth:"wide"}),day:I({values:v,defaultWidth:"wide"}),dayPeriod:I({values:P,defaultWidth:"wide",formattingValues:k,defaultFormattingWidth:"wide"})};function O(G){return function(H){var J=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{},X=J.width,Y=X&&G.matchPatterns[X]||G.matchPatterns[G.defaultMatchWidth],Z=H.match(Y);if(!Z)return null;var C=Z[0],T=X&&G.parsePatterns[X]||G.parsePatterns[G.defaultParseWidth],q=Array.isArray(T)?c(T,function(E){return E.test(C)}):m(T,function(E){return E.test(C)}),U;U=G.valueCallback?G.valueCallback(q):q,U=J.valueCallback?J.valueCallback(U):U;var HG=H.slice(C.length);return{value:U,rest:HG}}}function m(G,H){for(var J in G)if(Object.prototype.hasOwnProperty.call(G,J)&&H(G[J]))return J;return}function c(G,H){for(var J=0;J<G.length;J++)if(H(G[J]))return J;return}function y(G){return function(H){var J=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{},X=H.match(G.matchPattern);if(!X)return null;var Y=X[0],Z=H.match(G.parsePattern);if(!Z)return null;var C=G.valueCallback?G.valueCallback(Z[0]):Z[0];C=J.valueCallback?J.valueCallback(C):C;var T=H.slice(Y.length);return{value:C,rest:T}}}var d=/^(\d+)(일|번째)?/i,g=/\d+/i,p={narrow:/^(b\.?\s?c\.?|b\.?\s?c\.?\s?e\.?|a\.?\s?d\.?|c\.?\s?e\.?)/i,abbreviated:/^(b\.?\s?c\.?|b\.?\s?c\.?\s?e\.?|a\.?\s?d\.?|c\.?\s?e\.?)/i,wide:/^(기원전|서기)/i},u={any:[/^(bc|기원전)/i,/^(ad|서기)/i]},l={narrow:/^[1234]/i,abbreviated:/^q[1234]/i,wide:/^[1234]사?분기/i},i={any:[/1/i,/2/i,/3/i,/4/i]},n={narrow:/^(1[012]|[123456789])/,abbreviated:/^(1[012]|[123456789])월/i,wide:/^(1[012]|[123456789])월/i},s={any:[/^1월?$/,/^2/,/^3/,/^4/,/^5/,/^6/,/^7/,/^8/,/^9/,/^10/,/^11/,/^12/]},o={narrow:/^[일월화수목금토]/,short:/^[일월화수목금토]/,abbreviated:/^[일월화수목금토]/,wide:/^[일월화수목금토]요일/},r={any:[/^일/,/^월/,/^화/,/^수/,/^목/,/^금/,/^토/]},a={any:/^(am|pm|오전|오후|자정|정오|아침|저녁|밤)/i},e={any:{am:/^(am|오전)/i,pm:/^(pm|오후)/i,midnight:/^자정/i,noon:/^정오/i,morning:/^아침/i,afternoon:/^오후/i,evening:/^저녁/i,night:/^밤/i}},t={ordinalNumber:y({matchPattern:d,parsePattern:g,valueCallback:function G(H){return parseInt(H,10)}}),era:O({matchPatterns:p,defaultMatchWidth:"wide",parsePatterns:u,defaultParseWidth:"any"}),quarter:O({matchPatterns:l,defaultMatchWidth:"wide",parsePatterns:i,defaultParseWidth:"any",valueCallback:function G(H){return H+1}}),month:O({matchPatterns:n,defaultMatchWidth:"wide",parsePatterns:s,defaultParseWidth:"any"}),day:O({matchPatterns:o,defaultMatchWidth:"wide",parsePatterns:r,defaultParseWidth:"any"}),dayPeriod:O({matchPatterns:a,defaultMatchWidth:"any",parsePatterns:e,defaultParseWidth:"any"})},GG={code:"ko",formatDistance:S,formatLong:V,formatRelative:w,localize:b,match:t,options:{weekStartsOn:0,firstWeekContainsDate:1}};window.dateFns=Q(Q({},window.dateFns),{},{locale:Q(Q({},($=window.dateFns)===null||$===void 0?void 0:$.locale),{},{ko:GG})})})();

//# debugId=26A67348B9DB24B764756E2164756E21
