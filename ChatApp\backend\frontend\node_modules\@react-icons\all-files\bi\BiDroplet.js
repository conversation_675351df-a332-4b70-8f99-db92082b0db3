// THIS FILE IS AUTO GENERATED
var GenIcon = require('../lib').GenIcon
module.exports.BiDroplet = function BiDroplet (props) {
  return GenIcon({"tag":"svg","attr":{"viewBox":"0 0 24 24"},"child":[{"tag":"path","attr":{"d":"M12,22c4.636,0,8-3.468,8-8.246c0-6.232-7.118-11.354-7.421-11.569c-0.348-0.246-0.811-0.246-1.156-0.001 C11.12,2.397,4,7.503,4,13.75C4,18.53,7.364,22,12,22z M11.999,4.26C13.604,5.55,18,9.474,18,13.754C18,17.432,15.532,20,12,20 s-6-2.57-6-6.25C6,9.46,10.394,5.547,11.999,4.26z"}}]})(props);
};
